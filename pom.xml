<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.centerm</groupId>
	<artifactId>cpay-coms</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<modules>
		<module>cpay-commons</module><!--公共库 -->
		<module>cpay-config</module><!--配置库 -->
		<module>cpay-coms-view</module><!--运维系统前端 -->
		<module>cpay-coms-controller</module><!--运维系统控制器 -->
		<module>cpay-system-service</module><!--系统管理 -->
		<module>cpay-flow-service</module><!-- 流量管理服务 -->
		<!--<module>cpay-sms-facade</module>--><!--短信dubbo服务接口 -->
		<!--<module>cpay-sms-provider</module>--><!--短信dubbo服务提供者 -->
		<module>cpay-terminal-service</module><!-- 终端管理服务 -->
		<!--<module>cpay-cas-gate</module>&lt;!&ndash; 云POS助手接口 &ndash;&gt;-->
		<module>cpay-cas-service</module><!-- 云POS助手服务 -->
		<module>cpay-validate-service</module><!-- 公共服务 -->
		<module>cpay-taskScheduling-service</module><!-- 任务调度服务 -->
		<module>cpay-security</module>     <!-- 安全验证服务 -->
		<module>cpay-ams-gate</module><!-- 应用商城服务 -->
		<module>cpay-task</module><!-- 定时服务 -->
		<module>coms-apkstore-service</module>
		<module>cpay-tms-service</module>
		<module>cpay-tms-gate</module>
    	<module>cpay-developer-service</module><!-- 开发者管理服务 -->
		<module>cpay-cis-gate</module>
        <!--<module>cpay-theme-service</module>-->
        <!--<module>cpay-afterSale-service</module>--><!-- 售后管理服务 -->
  </modules>
	<!-- 集中定义依赖版本号 -->
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<log4j.leve>debug</log4j.leve>
		<log4j.ale>debug</log4j.ale>
		<junit.version>4.12</junit.version>
		<spring.version>4.2.0.RELEASE</spring.version>
		<mybatis.version>3.2.8</mybatis.version>
		<mybatis.spring.version>1.2.2</mybatis.spring.version>
		<mybatis.paginator.version>1.2.15</mybatis.paginator.version>
		<mysql.version>5.1.32</mysql.version>
		<slf4j.version>1.6.4</slf4j.version>
		<jackson.version>2.9.4</jackson.version>
		<druid.version>1.0.9</druid.version>
		<httpclient.version>4.5.2</httpclient.version>
		<jstl.version>1.2</jstl.version>
		<servlet-api.version>2.5</servlet-api.version>
		<cxf.version>3.1.6</cxf.version>
		<jsp-api.version>2.0</jsp-api.version>
		<joda-time.version>2.5</joda-time.version>
		<commons-lang3.version>3.3.2</commons-lang3.version>
		<commons-io.version>1.3.2</commons-io.version>
		<commons-net.version>3.3</commons-net.version>
		<pagehelper.version>5.0.0</pagehelper.version>
		<jsqlparser.version>0.9.5</jsqlparser.version>
		<commons-fileupload.version>1.3.1</commons-fileupload.version>
		<kryo.version>2.24.0</kryo.version>
		<kryo-serializers.version>0.26</kryo-serializers.version>
		<wss4j.version>2.1.6</wss4j.version>
		<hibernateValidator.version>5.2.2.Final</hibernateValidator.version>
		<json.version>1.0.2</json.version>
		<xstream.version>1.4.1</xstream.version>
		<emoji.version>0.0.1</emoji.version>
		<fastjson.version>1.2.7</fastjson.version>
		<lombok.version>1.18.8</lombok.version>
		<aws-s3.version>1.12.677</aws-s3.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
			  <groupId>com.github.binarywang</groupId>
			  <artifactId>java-emoji-converter</artifactId>
			  <version>${emoji.version}</version>
			</dependency>
			<!-- 时间操作组件 -->
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>${joda-time.version}</version>
			</dependency>
			<!-- hibernate-validator -->
			<dependency>
				<groupId>org.hibernate</groupId>
				<artifactId>hibernate-validator</artifactId>
				<version>${hibernateValidator.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sf.json-lib</groupId>
				<artifactId>json-lib-ext-spring</artifactId>
				<version>${json.version}</version>
			</dependency>
			<!-- cxf -->
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-frontend-jaxws</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-transports-http</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-bindings-soap</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-ws-security</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.wss4j</groupId>
				<artifactId>wss4j-ws-security-common</artifactId>
				<version>${wss4j.version}</version>
			</dependency>
			<!-- Apache工具组件 -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-net</groupId>
				<artifactId>commons-net</artifactId>
				<version>${commons-net.version}</version>
			</dependency>
			<!-- Jackson Json处理工具包 -->
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<!-- httpclient -->
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>${httpclient.version}</version>
			</dependency>
			<dependency>
    			<groupId>com.thoughtworks.xstream</groupId>
   			 	<artifactId>xstream</artifactId>
    			<version>${xstream.version}</version>
			</dependency>
			<!-- 单元测试 -->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>
			<!-- 日志处理 -->
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<!-- Mybatis -->
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${mybatis.spring.version}</version>
			</dependency>
			<!-- github -->
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.jsqlparser</groupId>
				<artifactId>jsqlparser</artifactId>
				<version>${jsqlparser.version}</version>
			</dependency>
			<!-- MySql -->
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<!-- 连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<!-- Spring -->
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context-support</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-beans</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aspects</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<!-- JSP相关 -->
			<dependency>
				<groupId>jstl</groupId>
				<artifactId>jstl</artifactId>
				<version>${jstl.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>servlet-api</artifactId>
				<version>${servlet-api.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>jsp-api</artifactId>
				<version>${jsp-api.version}</version>
				<scope>provided</scope>
			</dependency>
			<!-- 文件上传组件 -->
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<!-- dubbo高效序列化 -->
			<dependency>
				<groupId>com.esotericsoftware.kryo</groupId>
				<artifactId>kryo</artifactId>
				<version>${kryo.version}</version>
			</dependency>
			<dependency>
				<groupId>de.javakaffee</groupId>
				<artifactId>kryo-serializers</artifactId>
				<version>${kryo-serializers.version}</version>
			</dependency>

			<dependency>
		        <groupId>org.springframework.data</groupId>  
		        <artifactId>spring-data-redis</artifactId>  
		        <version>1.7.2.RELEASE</version>  
		    </dependency>

			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-s3</artifactId>
				<version>${aws-s3.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>
	 <profiles>
		<profile><!-- 本地开发环境 -->
			<id>dev</id>
			<properties>
				<profiles.active>dev</profiles.active>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile><!-- 测试环境 -->
			<id>test</id>
			<properties>
				<profiles.active>test</profiles.active>
			</properties>
		</profile>
		
		<profile><!-- 生产环境 -->
			<id>pro</id>
			<properties>
				<profiles.active>pro</profiles.active>
			</properties>
		</profile>
	</profiles>
	<build>
		<finalName>${project.artifactId}</finalName>
		<resources>
				<resource>
					<directory>src/main/resources</directory><!-- 资源根目录排除各环境的配置，防止在生成目录中多余其它目录 -->
					<excludes>
						<exclude>test/*</exclude>
						<exclude>pro/*</exclude>
						<exclude>dev/*</exclude>
					</excludes>
				</resource>
				<resource>
					<directory>src/main/resources/${profiles.active}</directory>
				</resource>
			</resources>
		<plugins>
			<!-- 资源文件拷贝插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.7</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- java编译插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.2</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!-- 配置Tomcat插件 -->
				<plugin>
					<groupId>org.apache.tomcat.maven</groupId>
					<artifactId>tomcat7-maven-plugin</artifactId>
					<version>2.2</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>