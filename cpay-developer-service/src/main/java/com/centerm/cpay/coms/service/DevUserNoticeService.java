package com.centerm.cpay.coms.service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.DevUserNotice;

public interface DevUserNoticeService {

	EUDataGridResult getList(DevUserNotice userNotice, int page, int rows);

	ResultMsg insert(DevUserNotice userNotice);

	DevUserNotice selectByPrimaryKey(int id);

	ResultMsg updateByPrimaryKey(DevUserNotice userNotice);

	ResultMsg deleteByPrimaryKey(int id);

	ResultMsg stick(Integer id);

	ResultMsg cancelStick(Integer id);

}
