package com.centerm.cpay.coms.dao.pojo;

import java.util.Date;

public class DevFeedBackAnswer {
    private Integer id;

    private String answerPerson;

    private String answerPersonEmail;

    private Date createTime;

    private Integer questionId;

    private String answerContent;
    
    private String questionContent;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAnswerPerson() {
        return answerPerson;
    }

    public void setAnswerPerson(String answerPerson) {
        this.answerPerson = answerPerson == null ? null : answerPerson.trim();
    }

    public String getAnswerPersonEmail() {
        return answerPersonEmail;
    }

    public void setAnswerPersonEmail(String answerPersonEmail) {
        this.answerPersonEmail = answerPersonEmail == null ? null : answerPersonEmail.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent == null ? null : answerContent.trim();
    }

	public String getQuestionContent() {
		return questionContent;
	}

	public void setQuestionContent(String questionContent) {
		this.questionContent = questionContent;
	}
    
}