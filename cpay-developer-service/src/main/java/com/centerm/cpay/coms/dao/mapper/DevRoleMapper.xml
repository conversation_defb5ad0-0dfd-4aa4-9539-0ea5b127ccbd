<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.DevRoleMapper">
	<resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.Role">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="name" property="name" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="treeMap" type="com.centerm.cpay.coms.dao.pojo.Tree">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="name" property="name" jdbcType="VARCHAR" />
		<result column="pid" property="pid" jdbcType="VARCHAR" />
		<result column="seq" property="seq" jdbcType="VARCHAR" />
		<result column="open" property="open" jdbcType="VARCHAR" />
		<result column="checked" property="checked" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List">
		type as id, name 
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from cpay_role
		where id = #{id,jdbcType=INTEGER}
	</select>
	<select id="getRoletree" resultMap="treeMap" parameterType="java.lang.Integer">
		select t1.*,IF(t2.menu_id!='',true,false) as checked
		from
		(select
		id,name,parent_id as
		pid,1 as open,seq as seq
		from
		cpay_dev_resource_menu) as
		t1 left join (select
		menu_id from
		cpay_dev_role_menu
		where role_id=
		#{roleId,jdbcType=INTEGER}
		) t2
		on(t1.id=t2.menu_id) order by
		pid,seq
	</select>

	<select id="getRoleList" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from cpay_dict where parent_id=(select id from cpay_dict where type='devrole')
	</select>

	<delete id="deleteById" parameterType="java.lang.Integer">
		delete from
		cpay_dev_role_menu
		where
		id = #{id,jdbcType=INTEGER}
	</delete>

	<delete id="deleteByIds" parameterType="java.util.List">
		delete from cpay_dev_role_menu where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
	
	<delete id="deleteRoleMenu" parameterType="java.lang.Integer">
		delete from
		cpay_dev_role_menu 
		where
		role_id = #{id,jdbcType=INTEGER}
	</delete>
	
	<insert id="insertRoleMenu" parameterType="com.centerm.cpay.coms.dao.pojo.RoleMenu">
		insert into cpay_dev_role_menu
		(role_id, menu_id)
		values (
		#{roleId,jdbcType=INTEGER},
		#{menuId,jdbcType=INTEGER}
		)
	</insert>
	<select id="getRoleMenuList" parameterType="java.lang.Integer"
		resultType="java.lang.String">
		select menu_id from cpay_dev_role_menu where
		role_id=#{roleId,jdbcType=INTEGER}
	</select>
</mapper>