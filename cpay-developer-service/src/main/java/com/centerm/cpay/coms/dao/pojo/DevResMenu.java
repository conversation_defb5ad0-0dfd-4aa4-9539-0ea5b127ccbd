package com.centerm.cpay.coms.dao.pojo;

import java.util.List;

public class DevResMenu {
	private Integer id;

	private String resourceId;

	private String name;

	private Integer parentId;

	private Byte position;

	private Short seq;

	private String url;

	private List<DevResMenu> childMenus;// 子菜单集合

	private Byte enabled;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId == null ? null : resourceId.trim();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}

	public Byte getPosition() {
		return position;
	}

	public void setPosition(Byte position) {
		this.position = position;
	}

	public Short getSeq() {
		return seq;
	}

	public void setSeq(Short seq) {
		this.seq = seq;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url == null ? null : url.trim();
	}

	public Byte getEnabled() {
		return enabled;
	}

	public void setEnabled(Byte enabled) {
		this.enabled = enabled;
	}

	public List<DevResMenu> getChildMenus() {
		return childMenus;
	}

	public void setChildMenus(List<DevResMenu> childMenus) {
		this.childMenus = childMenus;
	}


}