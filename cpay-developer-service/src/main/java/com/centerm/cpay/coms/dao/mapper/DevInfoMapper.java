package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.DevInfo;

public interface DevInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DevInfo record);

    DevInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKey(DevInfo record);

	List<DevInfo> selectByCondition(DevInfo devInfo);

	Integer selectUniqueByTitle(String title);

	void stick(Integer id);

	void cancelStick(Integer id);

	void display(Integer id);

	void cancelDisplay(Integer id);
}