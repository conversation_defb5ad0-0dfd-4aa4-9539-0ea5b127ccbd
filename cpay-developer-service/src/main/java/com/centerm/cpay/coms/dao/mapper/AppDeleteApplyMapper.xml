<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.AppDeleteApplyMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
    <result column="app_version_name" property="appVersionName" jdbcType="VARCHAR" />
    <result column="developer_id" property="developerId" jdbcType="INTEGER" />
    <result column="developer_name" property="developerName" jdbcType="VARCHAR" />
    <result column="deal_user_id" property="dealUserId" jdbcType="INTEGER" />
    <result column="deal_status" property="dealStatus" jdbcType="CHAR" />
    <result column="deal_time" property="dealTime" jdbcType="TIMESTAMP" />
    <result column="realname" property="auditUserName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, app_code, app_name, app_version, app_version_name, developer_id, developer_name, 
    deal_user_id, deal_status, deal_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from cpay_dev_app_delete_apply
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from cpay_dev_app_delete_apply
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" >
    insert into cpay_dev_app_delete_apply (id, app_code, app_name, 
      app_version, app_version_name, developer_id, 
      developer_name, deal_user_id, deal_status, 
      deal_time)
    values (#{id,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{appVersion,jdbcType=VARCHAR}, #{appVersionName,jdbcType=VARCHAR}, #{developerId,jdbcType=INTEGER}, 
      #{developerName,jdbcType=VARCHAR}, #{dealUserId,jdbcType=INTEGER}, #{dealStatus,jdbcType=CHAR}, 
      #{dealTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" >
    insert into cpay_dev_app_delete_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="appName != null" >
        app_name,
      </if>
      <if test="appVersion != null" >
        app_version,
      </if>
      <if test="appVersionName != null" >
        app_version_name,
      </if>
      <if test="developerId != null" >
        developer_id,
      </if>
      <if test="developerName != null" >
        developer_name,
      </if>
      <if test="dealUserId != null" >
        deal_user_id,
      </if>
      <if test="dealStatus != null" >
        deal_status,
      </if>
      <if test="dealTime != null" >
        deal_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="developerId != null" >
        #{developerId,jdbcType=INTEGER},
      </if>
      <if test="developerName != null" >
        #{developerName,jdbcType=VARCHAR},
      </if>
      <if test="dealUserId != null" >
        #{dealUserId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null" >
        #{dealStatus,jdbcType=CHAR},
      </if>
      <if test="dealTime != null" >
        #{dealTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" >
    update cpay_dev_app_delete_apply
    <set >
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        app_version_name = #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="developerId != null" >
        developer_id = #{developerId,jdbcType=INTEGER},
      </if>
      <if test="developerName != null" >
        developer_name = #{developerName,jdbcType=VARCHAR},
      </if>
      <if test="dealUserId != null" >
        deal_user_id = #{dealUserId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null" >
        deal_status = #{dealStatus,jdbcType=CHAR},
      </if>
      <if test="dealTime != null" >
        deal_time = #{dealTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" >
    update cpay_dev_app_delete_apply
    set app_code = #{appCode,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      app_version = #{appVersion,jdbcType=VARCHAR},
      app_version_name = #{appVersionName,jdbcType=VARCHAR},
      developer_id = #{developerId,jdbcType=INTEGER},
      developer_name = #{developerName,jdbcType=VARCHAR},
      deal_user_id = #{dealUserId,jdbcType=INTEGER},
      deal_status = #{dealStatus,jdbcType=CHAR},
      deal_time = #{dealTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByCondition" 
  	parameterType="com.centerm.cpay.coms.dao.pojo.AppDeleteApply" resultMap="BaseResultMap">
  		select a.*,b.realname from cpay_dev_app_delete_apply a
  		left join cpay_user b on a.deal_user_id=b.id
  		where 1=1 
  	  <if test="appCode != null" >
        and a.app_code like concat('%',#{appCode},'%')
      </if>
      <if test="appName != null" >
        and a.app_name like concat('%',#{appName},'%')
      </if>
      <if test="developerName != null" >
        and a.developer_name like concat('%',#{developerName},'%') order by a.deal_status asc
      </if>
  </select>
</mapper>