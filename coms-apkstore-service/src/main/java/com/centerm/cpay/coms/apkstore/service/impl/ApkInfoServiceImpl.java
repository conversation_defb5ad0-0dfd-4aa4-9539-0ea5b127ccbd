package com.centerm.cpay.coms.apkstore.service.impl;


import java.util.*;

import com.centerm.cpay.coms.apkstore.service.StoreApkService;
import com.centerm.cpay.coms.dao.mapper.TerminalTypeMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.apkstore.dao.mapper.ApkRangeMapper;
import com.centerm.cpay.coms.apkstore.dao.mapper.ApkTemporaryMapper;
import com.centerm.cpay.coms.apkstore.dao.mapper.ApkTypeInfoMapper;
import com.centerm.cpay.coms.apkstore.dao.mapper.StoreApkInfMapper;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkCountInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTypeInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo;
import com.centerm.cpay.coms.apkstore.service.ApkInfoService;
import com.centerm.cpay.coms.dao.mapper.FileInfoMapper;
import com.centerm.cpay.coms.dao.mapper.InstitutionMapper;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class ApkInfoServiceImpl implements ApkInfoService {
    @Autowired
    private StoreApkInfMapper storeApkInfMapper;
    @Autowired
    private ApkTypeInfoMapper apkTypeInfoMapper;
    @Autowired
    private FileInfoMapper fileInfoMapper;
    @Autowired
    private InstitutionMapper institutionMapper;
    @Autowired
    private ApkTemporaryMapper apkTemporaryMapper;
    @Autowired
    private ApkRangeMapper apkRangeMapper;

    @Override
    public EUDataGridResult getPubApk(StoreApkInfo apkInfo, Integer page,
                                      Integer rows) {
        // 分页处理
        PageHelper.startPage(page, rows);
        List<StoreApkInfo> list = storeApkInfMapper.getPubApk(apkInfo);
        // 创建一个返回值对象
        EUDataGridResult result = new EUDataGridResult();
        result.setRows(list);
        // 取记录总条数
        PageInfo<StoreApkInfo> pageInfo = new PageInfo<>(list);
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    @Override
    public List<StoreApkInfo> getPubApkList(StoreApkInfo apkInfo) {
        List<StoreApkInfo> list = storeApkInfMapper.getDriverJobPubApk(apkInfo);

        return list;
    }
    @Override
    public StoreApkInfo getApkInfo(Integer id) {
        return storeApkInfMapper.getApkById(id);
    }
    @Override
    public List<StoreApkInfo> getTermTypeName(StoreApkInfo apkinfo) {
        return storeApkInfMapper.getTermTypeName(apkinfo);
    }

    @Override
    public EUDataGridResult getApkTypePage(ApkTypeInfo apkTypeInfo, Integer page, Integer rows) {
        // 分页处理
        PageHelper.startPage(page, rows);
        List<ApkTypeInfo> list = apkTypeInfoMapper.getApkTypePage(apkTypeInfo);
        // 创建一个返回值对象
        EUDataGridResult result = new EUDataGridResult();
        result.setRows(list);
        // 取记录总条数
        PageInfo<ApkTypeInfo> pageInfo = new PageInfo<>(list);
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    @Override
    public boolean isExist(ApkTypeInfo apkTypeInfo) {
        ApkTypeInfo apkTypeIf = apkTypeInfoMapper.getApkTypeSelective(apkTypeInfo);
        if (apkTypeIf == null) {
            return false;
        }
        return true;
    }

    @Override
    public ResultMsg addApkType(ApkTypeInfo apkTypeInfo) {
        String resuletMsg1 = "Each organization can only have a maximum of 10 categories";
        String resuletMsg2 = "Newly added classification";
        Integer insId = apkTypeInfo.getInsId();
        List list = apkTypeInfoMapper.selectByInsId(insId);
        if (list != null && list.size() > 10) {
            return new ResultMsg(2, resuletMsg1, null);
        }
        apkTypeInfoMapper.insertSelective(apkTypeInfo);
        return new ResultMsg(3, resuletMsg2, null);
    }

    @Override
    public ApkTypeInfo getApkTypeById(Integer id) {
        ApkTypeInfo apkTypeInfo = apkTypeInfoMapper.selectByPrimaryKey(id);
        String iconId = apkTypeInfo.getIconId();
        FileInfo fileInfo = fileInfoMapper.getFileByuuid(iconId);
        String iconPath = fileInfo.getFileSavePath();
        apkTypeInfo.setIconPath(iconPath);
        return apkTypeInfo;
    }

    @Override
    public int updateApkType(ApkTypeInfo apkTypeInfo) {
        apkTypeInfoMapper.updateByPrimaryKeySelective(apkTypeInfo);
        return 0;
    }

    @Override
    public int deleteApkTypeById(Integer id) {
        StoreApkInfo storeApkInfo = new StoreApkInfo();
        storeApkInfo.setAppTypeId(id);

        apkTypeInfoMapper.deleteByPrimaryKey(id);
        return 0;
    }

    @Override
    public int isUsed(Integer id) {
        List list1 = apkTemporaryMapper.getApkByTypeId(id);
        List list2 = storeApkInfMapper.getApkByTypeId(id);
        if ((list1 != null && list1.size() > 0) || (list2 != null && list2.size() > 0)) {
            return 1;
        }
        return 0;
    }

    @Override
    public ApkCountInfo getApkCount(Integer insId) {
        Institution institution = storeApkInfMapper.getInsInfoById(insId);
        List<StoreApkInfo> apk = storeApkInfMapper.getCount(institution.getCode());
        List<StoreApkInfo> recApk = storeApkInfMapper.getRecCount(institution.getCode());
        ApkCountInfo apkCountInfo = new ApkCountInfo();
        List<String> month = new ArrayList<String>();
        List<Integer> apkmount = new ArrayList<>();
        List<Integer> recmount = new ArrayList<>();
        String[] strs = getLast12Months();
        if (apk != null && apk.size() > 0) {
            for (int i = 0; i < strs.length; i++) {
                for (int j = 0; j < apk.size(); j++) {
                    if (apk.get(j).getApkMonth().equals(strs[i])) {
                        apkmount.add(apk.get(j).getApkAmount());
                        break;
                    }
                    if (j == (apk.size() - 1)) {
                        apkmount.add(0);
                    }
                }
            }
        } else {
            for (int i = 0; i < strs.length; i++) {
                apkmount.add(0);
            }
        }
        if (recApk != null && recApk.size() > 0) {
            for (int i = 0; i < strs.length; i++) {
                for (int j = 0; j < recApk.size(); j++) {
                    if (recApk.get(j).getRecMonth().equals(strs[i])) {
                        recmount.add(recApk.get(j).getRecAmount());
                        break;
                    }
                    if (j == (recApk.size() - 1)) {
                        recmount.add(0);
                    }
                }
            }
        } else {
            for (int i = 0; i < strs.length; i++) {
                recmount.add(0);
            }
        }
        for (int i = 0; i < strs.length; i++) {
            month.add(strs[i]);
        }
        apkCountInfo.setMonth(month);
        apkCountInfo.setApkCount(apkmount);
        apkCountInfo.setRecCount(recmount);
        return apkCountInfo;
    }

    @Override
    public List getApkAmount(Integer insId) {
        Institution institution = storeApkInfMapper.getInsInfoById(insId);
        String insCd = institution.getCode();
        List list = apkTemporaryMapper.getApkAmount(insCd);
        return list;
    }

    @Override
    public int deleteApkTemById(Integer id) {
        ApkTemporary apkInfo = new ApkTemporary();
        apkInfo.setId(id);
        apkInfo.setIsDelete("1");
        apkTemporaryMapper.updateByPrimaryKeySelective(apkInfo);
        return 1;
    }

    public String[] getLast12Months() {
        String[] last12Months = new String[12];
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1); // 要先+1,才能把本月的算进去</span>
        for (int i = 0; i < 12; i++) {
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 1); // 逐次往前推1个月
            last12Months[11 - i] = cal.get(Calendar.YEAR)
                    + fillZero((cal.get(Calendar.MONTH) + 1) + "", 2);
        }

        return last12Months;
    }

    public String fillZero(String str, int len) {
        int tmp = str.length();
        int t;
        String str1 = str;
        if (tmp >= len) {
            return str1;
        }
        t = len - tmp;
        for (int i = 0; i < t; i++)
            str1 = "0" + str1;
        return str1;
    }

    @Override
    public ResultMsg reCycleApkInfoById(Integer id) {
        storeApkInfMapper.reCycleApkInfoById(id);
        return ResultMsg.success();
    }

    @Override
    public ResultMsg restoreApkById(ApkTemporary apkTemp) {
        //storeApkInfMapper.restoreApkById(id);
        apkTemp.setRecSt("0");
        apkTemporaryMapper.insert(apkTemp);
        storeApkInfMapper.deletePubedApkById(apkTemp.getId());
        return ResultMsg.success();
    }

    @Override
    public ResultMsg offLineApk(ApkTemporary apkTemp) {
		/*apkTemp.setRecSt("0");
		apkTemporaryMapper.insert(apkTemp);
		storeApkInfMapper.deletePubedApkById(apkTemp.getId());*/
        storeApkInfMapper.offLineApk(apkTemp.getId());
        apkRangeMapper.deleteByAppId(apkTemp.getId());
        return ResultMsg.success();
    }

    @Override
    public void appYL(Map map) {
        storeApkInfMapper.appYL(map);
    }

    @Override
    public ResultMsg calcelApkYL(String appId) {
        Integer appYlId = storeApkInfMapper.queryAppYlpackageId(appId);
        if (CtUtils.isEmpty(appYlId) || appYlId == 0) {
            return ResultMsg.build(ResultMsg.ERROR_CODE, "The current app does not depend on other apps!");
        } else {
            storeApkInfMapper.calcelApkYL(appId);
            return ResultMsg.build(ResultMsg.SUCCESS_CODE, "Successfully canceled!");
        }
    }

    @Override
    public List<StoreApkInfo> getApkByAppYlpackageId(Integer ylId) {

        return storeApkInfMapper.getApkByAppYlpackageId(ylId);
    }

    @Override
    public List<ApkTypeInfo> getApkTypeList() {
        List<ApkTypeInfo> apkTypeInfo = apkTypeInfoMapper.selectTypesList();
        return apkTypeInfo;
    }

    @Override
    public StoreApkInfo getStoreApkInfobyID(Integer Id) {
        return storeApkInfMapper.getApkById(Id);
    }

    @Override
    public EUDataGridResult getPubApkForApkRange(StoreApkInfo apkInfo, Integer page, Integer rows) {
        // 分页处理
        PageHelper.startPage(page, rows);
        List<StoreApkInfo> list = new ArrayList<StoreApkInfo>();
        Institution institution = institutionMapper.selectByInsCode(apkInfo.getInsCd());
        if (!CtUtils.isEmpty(institution)) {
            String[] detailArr = institution.getDetail().split(",");
            String detail = "";
            if (detailArr.length == 1) {
                detail = detailArr[0];
                StoreApkInfo apkInfoW = new StoreApkInfo();
                list.add(apkInfoW);
            } else {
                for (int i = 0; i < detailArr.length - 1; i++) {
                    detail += detailArr[i] + ",";
                }
                detail = detail.substring(0, detail.length() - 1);
                apkInfo.setInsCd(detail);
                list = storeApkInfMapper.getPubApkForApkRange(apkInfo);
            }
        } else {
            list = storeApkInfMapper.getPubApkForApkRange(apkInfo);
        }
        // 创建一个返回值对象
        EUDataGridResult result = new EUDataGridResult();
        result.setRows(list);
        // 取记录总条数
        PageInfo<StoreApkInfo> pageInfo = new PageInfo<>(list);
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    @Override
    public Integer getApkAuditCount(Integer insId) {
        // TODO
        return apkTemporaryMapper.getApkAuditCount(insId);
    }

    @Override
    public Integer getApkPubCount(Integer insId) {
        // TODO
        return apkTemporaryMapper.getApkPubCount(insId);
    }
}
