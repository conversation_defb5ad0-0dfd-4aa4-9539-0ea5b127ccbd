<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.apkstore.dao.mapper.StoreApkInfMapper">

   <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_code" jdbcType="VARCHAR" property="appCode" />
    <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="apk_id" jdbcType="VARCHAR" property="apkId" />
    <result column="icon_id" jdbcType="VARCHAR" property="iconId" />
    <result column="pic_id" jdbcType="VARCHAR" property="picId" />
    <result column="key_word" jdbcType="VARCHAR" property="keyWord" />
    <result column="app_desc" jdbcType="VARCHAR" property="appDesc" />
    <result column="ver_desc" jdbcType="VARCHAR" property="verDesc" />
    <result column="pub_time"  property="pubTime" />
    <result column="developer_id" jdbcType="INTEGER" property="developerId" />
    <result column="rec_st" jdbcType="CHAR" property="recSt" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="audit_user_id" jdbcType="INTEGER" property="auditUserId" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="audit_msg" jdbcType="VARCHAR" property="auditMsg" />
    <result column="dl_num" jdbcType="INTEGER" property="dlNum" />
    <result column="up_flag" jdbcType="CHAR" property="upFlag" />
    <result column="uninstall_flag" jdbcType="CHAR" property="uninstallFlag" />
    <result column="re_degree" jdbcType="INTEGER" property="reDegree" />
    <result column="app_screen" jdbcType="CHAR" property="appScreen" />
    <result column="app_url" jdbcType="VARCHAR" property="appUrl" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="app_size" jdbcType="INTEGER" property="appSize" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="app_version_name" jdbcType="VARCHAR" property="appVersionName" />
    <result column="sign_md5" jdbcType="VARCHAR" property="signMd5" />
    <result column="apk_md5" jdbcType="VARCHAR" property="apkMd5" />
    <result column="ins_id" jdbcType="INTEGER" property="insId" />
    <result column="ins_cd" jdbcType="VARCHAR" property="insCd" />
    <result column="group_id" property="groupId" jdbcType="INTEGER" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="app_type_id" jdbcType="INTEGER" property="appTypeId" />
    <result column="apkAmount"  property="apkAmount" />
    <result column="apkMonth"  property="apkMonth" />
    <result column="recAmount"  property="recAmount" />
    <result column="recMonth"  property="recMonth" />
    <result column="name"  property="ins_nm" />
    <result column="ins_name"  property="insName" />
    <result column="file_save_path"  property="filePath" />
	<result column="factory_id" property="factoryId" jdbcType="INTEGER"/>
	<result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
	<result column="term_type_code" property="termTypeCode" jdbcType="VARCHAR"/>
	<result column="term_type_codestr" property="termTypeCodeStr" jdbcType="VARCHAR" />
	<result column="term_type_namestr" property="termTypeNameStr" jdbcType="VARCHAR" />

   </resultMap>

  <resultMap id="InsMap" type="com.centerm.cpay.coms.dao.pojo.Institution" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="detail" property="detail" jdbcType="VARCHAR" />
    <result column="enabled" property="enabled" jdbcType="TINYINT" />
  </resultMap>

  <resultMap type="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkPublish"
			   id="StoreApkReportMap">
		<result column="appPublishCount" property="appPublishCount" />
		<result column="factoryName" property="factoryName" />
		<result column="month" property="month" />
	</resultMap>

  	<sql id="factoryCondition">
		<if test="factoryId != 0" >
        	and tb.factory_id=#{factoryId,jdbcType=INTEGER}
      	</if>
	</sql>
   <update id="updateApkInfo" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
    update coms_store_apk_info
    <set>
      <if test="appCode != null">
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null">
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="apkId != null">
        apk_id = #{apkId,jdbcType=VARCHAR},
      </if>
      <if test="iconId != null">
        icon_id = #{iconId,jdbcType=VARCHAR},
      </if>
      <if test="picId != null">
        pic_id = #{picId,jdbcType=VARCHAR},
      </if>
      <if test="keyWord != null">
        key_word = #{keyWord,jdbcType=VARCHAR},
      </if>
      <if test="appDesc != null">
        app_desc = #{appDesc,jdbcType=VARCHAR},
      </if>
      <if test="verDesc != null">
        ver_desc = #{verDesc,jdbcType=VARCHAR},
      </if>
      <if test="pubTime != null">
        pub_time = #{pubTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recSt != null">
        rec_st = #{recSt,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditMsg != null">
        audit_msg = #{auditMsg,jdbcType=VARCHAR},
      </if>
      <if test="dlNum != null">
        dl_num = #{dlNum,jdbcType=INTEGER},
      </if>
      <if test="uninstallFlag != null">
        uninstall_flag = #{uninstallFlag,jdbcType=CHAR},
      </if>
      <if test="upFlag != null">
        up_flag = #{upFlag,jdbcType=CHAR},
      </if>
      <if test="reDegree != null">
        re_degree = #{reDegree,jdbcType=INTEGER},
      </if>
      <if test="appScreen != null">
        app_screen = #{appScreen,jdbcType=CHAR},
      </if>
      <if test="appUrl != null">
        app_url = #{appUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="appSize != null">
        app_size = #{appSize,jdbcType=INTEGER},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null">
        app_version_name = #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="signMd5 != null">
        sign_md5 = #{signMd5,jdbcType=VARCHAR},
      </if>
      <if test="apkMd5 != null">
        apk_md5 = #{apkMd5,jdbcType=VARCHAR},
      </if>
      <if test="insId != null">
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="insCd != null">
        ins_cd = #{insCd,jdbcType=VARCHAR},
      </if>
      <if test="appTypeId != null">
        app_type_id = #{appTypeId,jdbcType=INTEGER},
      </if>
		<if test="factoryId != null">
			factory_id = #{factoryId,jdbcType=INTEGER},
		</if>
		<if test="termTypeCodeStr != null">
			term_type_codestr = #{termTypeCodeStr,jdbcType=VARCHAR},
		</if>
		<if test="termTypeNameStr != null">
			term_type_namestr = #{termTypeNameStr,jdbcType=VARCHAR},
		</if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
   <select id="getApkList"  parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
	   SELECT
	   info.*,
	   ins1.name AS insName,g.name AS group_name ,tp.tp_nm AS appTypeName, CONCAT(#{ngsPicPath},file.file_save_path) AS
	   file_save_path,
	   fa.name AS factory_name
	   FROM
	   (
	   (SELECT
	   t1.*
	   FROM
	   coms_store_apk_info_temporary t1)
	   UNION
	   ALL
	   (SELECT
	   t2.id,
	   t2.app_code,
	   t2.app_version,
	   t2.app_name,
	   t2.apk_id,
	   t2.icon_id,
	   t2.pic_id,
	   t2.key_word,
	   t2.app_desc,
	   t2.ver_desc,
	   t2.rec_st,
	   t2.user_id,
	   t2.developer_id,
	   t2.create_time,
	   t2.audit_user_id,
	   t2.pub_time,
	   t2.audit_time,
	   t2.audit_msg,
	   t2.app_screen,
	   t2.app_version_name,
	   t2.sign_md5,
	   t2.app_type_id,
	   t2.group_id,
	   t2.ins_id,
	   t2.ins_cd,
	   t2.is_delete,
	   t2.factory_id,
	   t2.term_type_codestr,
	   t2.term_type_namestr
	   FROM
	   coms_store_apk_info t2)
	   ) info
	   LEFT JOIN cpay_institution ins1
	   ON info.ins_id = ins1.id
	   LEFT JOIN coms_store_file_info file
	   ON info.icon_id = file.uuid
	   LEFT JOIN coms_terminal_group g
	   ON g.id = info.group_id
	   LEFT JOIN coms_store_apk_type_info tp
	   ON info.app_type_id = tp.id
	   LEFT JOIN coms_terminal_manufacturer fa
	   ON info.factory_id = fa.id
	   WHERE info.is_delete = 0
	   AND info.ins_cd IN
	   (SELECT
	   ins2.code
	   FROM
	   cpay_institution ins2
	   WHERE locate(( SELECT ins3.detail from cpay_institution ins3 WHERE ins3.id = #{insId}),ins2.detail)>0
	   )
	   <if test="appName != null">
            AND info.app_name LIKE '%${appName}%'
        </if>
         <if test="appCode != null">
            AND info.app_code LIKE '%${appCode}%'
        </if>
        <if test="appScreen != null ">
            AND info.app_screen = #{appScreen}
        </if>
        <if test="appTypeId != null ">
            AND info.app_type_id = #{appTypeId}
        </if>
        <if test="appVersion != null">
            AND info.app_version=#{appVersion}
        </if>
         <if test="groupId != null">
            AND info.group_id=#{groupId}
        </if>
        <if test="recSt != null">
            AND info.rec_st = #{recSt}
        </if>
	   <if test="factoryId != null">
		   AND info.factory_id=#{factoryId}
	   </if>
		ORDER BY info.create_time DESC
   </select>
   <select id="getApkByAppYlpackageId"  parameterType="java.lang.Integer" resultMap="BaseResultMap">
       select * from coms_store_apk_info where app_ylpackage_id = #{appYlpackageId}
   </select>

   <select id="getApkListByAppCode"  parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
     SELECT app_version from( 
					 select A.app_version from 
					 coms_store_apk_info_temporary A 
					 WHERE  TRIM(A.app_code)=#{appCode}  and A.ins_id=#{insId,jdbcType=INTEGER}
					 union  
					 select B.app_version from 
					 coms_store_apk_info B 
				 	 WHERE  TRIM(B.app_code)=#{appCode}  and B.ins_id=#{insId,jdbcType=INTEGER}
					) t 
   </select>
   <select id="getDriverJobPubApk" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
   		SELECT
        ID,APP_CODE,APP_NAME,APP_VERSION,term_type_codestr,term_type_namestr
        FROM
        coms_store_apk_info
        WHERE rec_st = 3
        and is_delete = '0'
	   and GROUP_ID = 0
	   <if test="insId != null and insId != 0">
		   and ins_id =#{insId,jdbcType=INTEGER}
	   </if>
	   <if test="factoryId != null and factoryId !='0'">
		   and factory_id = #{factoryId,jdbcType=INTEGER}
	   </if>

   </select>
   <select id="getPubApk" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
	   SELECT
	   info.*,
	   ins1.name AS insName,g.name AS group_name,fa.name AS factory_name ,tp.tp_nm AS
	   appTypeName,CONCAT(#{ngsPicPath},file.file_save_path) AS file_save_path
	   FROM
	   (
	   (SELECT
	   t1.*
	   FROM
	   coms_store_apk_info_temporary t1
	   WHERE t1.rec_st = 2)
	   UNION
	   ALL
	   (SELECT
	   t2.id,
	   t2.app_code,
	   t2.app_version,
	   t2.app_name,
	   t2.apk_id,
	   t2.icon_id,
	   t2.pic_id,
	   t2.key_word,
	   t2.app_desc,
	   t2.ver_desc,
	   t2.rec_st,
	   t2.user_id,
	   t2.developer_id,
	   t2.create_time,
	   t2.audit_user_id,
	   t2.pub_time,
	   t2.audit_time,
	   t2.audit_msg,
	   t2.app_screen,
	   t2.app_version_name,
	   t2.sign_md5,
	   t2.app_type_id,
	   t2.group_id,
	   t2.ins_id,
	   t2.ins_cd,
	   t2.is_delete,
	   t2.factory_id,
	   t2.term_type_codestr,
	   t2.term_type_namestr
	   FROM
	   coms_store_apk_info t2
	   WHERE t2.rec_st = 3
	   OR t2.rec_st = 4)
	   ) info
	   LEFT JOIN cpay_institution ins1
	   ON info.ins_id = ins1.id
	   LEFT JOIN coms_store_file_info file
	   ON info.icon_id = file.uuid
	   LEFT JOIN coms_terminal_group g
	   ON g.id = info.group_id
	   LEFT JOIN coms_store_apk_type_info tp
	   ON info.app_type_id = tp.id
	   LEFT JOIN coms_terminal_manufacturer fa
	   ON info.factory_id = fa.id
	   WHERE info.is_delete = '0' AND info.ins_cd IN
	   (SELECT
	   ins2.code
	   FROM
	   cpay_institution ins2
	   WHERE locate(( SELECT ins3.detail from cpay_institution ins3 WHERE ins3.id = #{insId}),ins2.detail)>0
	   )
	   <if test="appName != null">
	       AND info.app_name LIKE '%${appName}%'
	    </if>
	    <if test="groupId != null">
	       AND info.group_id=#{groupId,jdbcType=INTEGER}
	    </if>
	    <if test="appCode != null">
	       AND info.app_code LIKE '%${appCode}%'
	    </if>
	    <if test="appTypeId != null">
	       AND info.app_type_id=#{appTypeId,jdbcType=INTEGER}
	    </if>
	ORDER BY info.create_time DESC
  </select>

   <select id="getApkRecycleList" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
	   SELECT
	   info.*,
	   ins1.name AS insName,g.name AS group_name,fa.name AS factory_name,tp.tp_nm AS
	   appTypeName, CONCAT(#{ngsPicPath},file.file_save_path) AS file_save_path
	   FROM
	   (
	   (SELECT
	   t1.*
	   FROM
	   coms_store_apk_info_temporary t1)
	   UNION
	   ALL
	   (SELECT
	   t2.id,
	   t2.app_code,
	   t2.app_version,
	   t2.app_name,
	   t2.apk_id,
	   t2.icon_id,
	   t2.pic_id,
	   t2.key_word,
	   t2.app_desc,
	   t2.ver_desc,
	   t2.rec_st,
	   t2.user_id,
	   t2.developer_id,
	   t2.create_time,
	   t2.audit_user_id,
	   t2.pub_time,
	   t2.audit_time,
	   t2.audit_msg,
	   t2.app_screen,
	   t2.app_version_name,
	   t2.sign_md5,
	   t2.app_type_id,
	   t2.group_id,
	   t2.ins_id,
	   t2.ins_cd,
	   t2.is_delete,
	   t2.factory_id,
	   t2.term_type_codestr,
	   t2.term_type_namestr
	   FROM
	   coms_store_apk_info t2)
	   ) info
	   LEFT JOIN cpay_institution ins1
	   ON info.ins_id = ins1.id
	   LEFT JOIN coms_terminal_group g
	   ON g.id = info.group_id
	   LEFT JOIN coms_store_file_info file
	   ON info.icon_id = file.uuid
	   LEFT JOIN coms_store_apk_type_info tp
	   ON info.app_type_id = tp.id
	   LEFT JOIN coms_terminal_manufacturer fa
	   ON info.factory_id = fa.id
	   WHERE info.is_delete = '1' AND info.ins_cd IN
	   (SELECT
	   ins2.code
	   FROM
	   cpay_institution ins2
	   WHERE locate(( SELECT ins3.detail from cpay_institution ins3 WHERE ins3.id = #{insId}),ins2.detail)>0
	   )
	   <if test="appName != null">
	       AND info.app_name LIKE '%${appName}%'
	    </if>
	ORDER BY info.create_time DESC
   </select>

   <select id="getApkPublishedList"  parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
        select t3.up_flag,t2.name,t3.id,t3.ins_id,t3.app_name,t3.app_code,t3.app_version,t3.app_screen,t3.rec_st,t3.create_time,t3.pub_time from coms_store_apk_info t3
        left join  cpay_institution t2  on t3.ins_id = t2.id
        where  t3.ins_cd in
        (select t1.`code` from cpay_institution t1 WHERE t1.detail LIKE '%${insCd}%'
        and rec_st = '3'
        <if test="appName != null">
            and app_name LIKE '%${appName}%'
        </if>
        UNION
        select 2 up_flag,t2.name,t6.id,t6.ins_id,t6.app_name,t6.app_code,t6.app_version,t6.app_screen,t6.rec_st,t6.create_time,t6.pub_time
        from coms_store_apk_info_temporary t6
        left join  cpay_institution t2  on t6.ins_id = t2.id
        where  t6.ins_cd in
        (select t4.`code` from cpay_institution t4 WHERE t4.detail LIKE '%${insCd}%'
        and rec_st = '3'
        <if test="appName != null">
            and app_name LIKE '%${appName}%'
        </if>
        ORDER BY create_time desc
   </select>

   <select id="getVersion" resultType="String" parameterType="java.util.Map">
     		SELECT max(app_version) app_version from( 
				   select A.app_version from 
				   coms_store_apk_info_temporary A 
			WHERE  TRIM(A.app_code)=#{appCode} and ins_cd=#{insCd}) t 
   </select>
   <select id="isUploded" resultType="Integer" parameterType="java.util.Map">
                     select A.id from 
					 coms_store_apk_info_temporary A 
					 WHERE  TRIM(A.app_code)=#{appCode} and ins_id!=#{insId} 
					 union  
					 select B.id from 
					 coms_store_apk_info B 
				 	 WHERE  TRIM(B.app_code)=#{appCode} and ins_id!=#{insId}        
   </select>
   <select id="getPubVersion" resultType="String" parameterType="java.util.Map">
     select B.app_version from 
					 coms_store_apk_info B 
				 	 WHERE  TRIM(B.app_code)=#{appCode} and ins_cd=#{insCd}
   </select>
   <select id="getApkById" resultMap="BaseResultMap">
        SELECT
		a.*,fa.name AS factory_name FROM
		coms_store_apk_info a
		LEFT JOIN coms_terminal_manufacturer fa ON a.factory_id = fa.id
		where a.id=#{id}
   </select>

   <select id="getInsInfoById" resultMap="InsMap">
        select * from cpay_institution where id=#{insId}
   </select>

   <select parameterType="String" id="getCount" resultMap="BaseResultMap">
     select DATE_FORMAT(A.pub_time,'%Y%m') apkMonth, count(id) apkAmount from  coms_store_apk_info A
     where
	 DATE_FORMAT(A.pub_time,'%Y-%m')>
	 DATE_FORMAT(date_sub(curdate(), interval 12 month),'%Y-%m') 
	 and A.ins_cd in (select t1.`code` from cpay_institution t1 WHERE t1.detail LIKE concat ('%',#{insCd},'%'))
	 and A.rec_st in ('0', '1', '2', '3', '4', '5')
     group by apkMonth 
	</select>
	<select id="getApkByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
	       select * from coms_store_apk_info where 1=1 
	   	   and app_version=#{appVersion,jdbcType=VARCHAR}
		   and app_code = #{appCode,jdbcType=VARCHAR}
		   and ins_id = #{insId,jdbcType=INTEGER}
	</select>
	<select id="getApkByTypeId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
	      select * from coms_store_apk_info  where  app_type_id = #{appTypeId}  and rec_st not in('2','3')
	</select>
	<select  parameterType="String" id="getRecCount"  resultMap="BaseResultMap">
     select DATE_FORMAT(A.pub_time,'%Y%m') recMonth, count(id) recAmount from  coms_store_apk_info A
     where
	 DATE_FORMAT(A.pub_time,'%Y-%m')>
	 DATE_FORMAT(date_sub(curdate(), interval 12 month),'%Y-%m') 
	 and A.ins_cd in (select t1.`code` from cpay_institution t1 WHERE t1.detail LIKE concat ('%',#{insCd},'%'))
	 and A.rec_st='3'
     group by recMonth 
   </select>
   <!-- edit by xiarp start 20160706-->
   <sql id="Base_Column_List" >
    id,app_code,app_version,app_name,apk_id,icon_id,pic_id,key_word,app_desc,ver_desc,pub_time,rec_st,
    user_id,create_time,audit_user_id,audit_time,audit_msg,dl_num,up_flag,re_degree,app_screen,app_url,icon_url,
    app_size,pic_url,app_version_name,sign_md5,apk_md5,ins_cd,app_type_id,group_id,ins_id
  </sql>

  <resultMap id="ContainTypeInfoMap" type="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" extends="BaseResultMap" >
  		<association property="apkTypeInfo" javaType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTypeInfo">
			<result column="tp_nm" jdbcType="VARCHAR" property="tpNm" />
		</association>
  </resultMap>
   <select id="selectByAppCode" resultMap="ContainTypeInfoMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
   SELECT
		tb.*, b.tp_nm
	from (
   		select t.*
    from coms_store_apk_info t
    where t.app_code = #{appCode,jdbcType=VARCHAR}
    	and t.ins_id in (select t2.id from cpay_institution t2 where t2.code IN (${codeListStr}))
	    
	   	and t.rec_st = '3'
	   	<if test="groupId != 0" >
        	and ( t.group_id = #{groupId} or t.group_id = 0)
      	</if>
      	<if test="groupId == 0" >
        	and t.group_id = 0
      	</if>
	   	<!-- and t.id not in (select app_id from coms_store_apk_range where ins_id = #{insId}
	   	<if test="groupId == 0" >
        	 and group_id = 0
      	</if>
	   	<if test="groupId != 0" >
        	 and (group_id = #{groupId} or group_id = 0)
      	</if>
	   	) -->
	   	and t.id in (select apk.id from coms_store_apk_info apk,(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info t where 1 = 1
	   	and t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)
	   	group BY app_code) tb
			where apk.app_code = tb.app_code
				<!-- and apk.ins_id = tb.ins_id -->
			)
	   	) tb
	    left join coms_store_apk_type_info b on  tb.app_type_id = b.id
		LEFT JOIN coms_store_apk_info_temporary t3 ON tb.app_ylpackage_id = t3.id
		LEFT JOIN coms_store_file_info t4 ON t3.apk_id = t4.uuid order by ins_id,group_id asc
   </select>

   <select id="queryPageByMap" resultMap="ContainTypeInfoMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
   SELECT
		tb.*, b.tp_nm
	from (
   		select t.*
   		 from coms_store_apk_info t
   where t.app_type_id = #{apkTypeId}
   		and t.ins_id in (select t2.id from cpay_institution t2 where t2.code IN (${codeListStr}))
   		
   		and t.rec_st = '3'
   		<if test="groupId != 0" >
        	 and (group_id = #{groupId} or group_id = 0)
      	</if>
      	<if test="groupId == 0" >
        	and t.group_id = 0
      	</if>
      	and t.id in (select apk.id from coms_store_apk_info apk,
      	(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info t
      	where 1 = 1 and
      	t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)

      	group BY app_code) tb
			where apk.app_code = tb.app_code
			<!-- and apk.ins_id = tb.ins_id -->
			)
      	) tb
      	left join coms_store_apk_type_info b on  tb.app_type_id = b.id
		LEFT JOIN coms_store_apk_info_temporary t3 ON tb.app_ylpackage_id = t3.id
		LEFT JOIN coms_store_file_info t4 ON t3.apk_id = t4.uuid
		where 1=1 and locate(#{termTypeCode},tb.term_type_codestr)>0
   		 <include refid="factoryCondition" />
		

		ORDER BY tb.re_degree desc,tb.group_id  asc
   </select>

   <select id="queryListOrderByRecommended" resultMap="ContainTypeInfoMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
   SELECT
		tb.*, b.tp_nm
	from (
   		select t.*
   		 from coms_store_apk_info t
   where t.ins_id in (select t2.id from cpay_institution t2 where t2.code IN (${codeListStr}))
   		
   		and t.rec_st = '3'
   		<if test="groupId != 0" >
        	and ( t.group_id = #{groupId} or t.group_id = 0)
      	</if>
      	<if test="groupId == 0" >
        	and t.group_id = 0
      	</if>
		and t.id in (select apk.id from coms_store_apk_info apk,
		(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info
		t where 1 = 1 and
		t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)
		group BY app_code) tb
			where apk.app_code = tb.app_code
			<!-- and apk.ins_id = tb.ins_id -->
			)
   		 ) tb
   		 left join coms_store_apk_type_info b on  tb.app_type_id = b.id
   		 where 1=1 and locate(#{termTypeCode},tb.term_type_codestr)>0
   		 <include refid="factoryCondition" />
		 order by tb.re_degree desc,tb.group_id asc
   </select>

   <select id="queryListOrderByDownload" resultMap="ContainTypeInfoMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
   SELECT
		tb.*, b.tp_nm
	from (
   		select t.*
   		 from coms_store_apk_info t
   where t.ins_id in (select t2.id from cpay_institution t2 where t2.code IN (${codeListStr}))
   		
   		and t.rec_st = '3'
   		<if test="groupId != 0" >
        	and ( t.group_id = #{groupId} or t.group_id = 0)
      	</if>
      	<if test="groupId == 0" >
        	and t.group_id = 0
      	</if>
		and t.id in (select apk.id from coms_store_apk_info apk,
		(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info t where 1 = 1

		and t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)
		group BY app_code) tb
			where apk.app_code = tb.app_code
			<!-- and apk.ins_id = tb.ins_id -->
			)
   		 ) tb
   		 left join coms_store_apk_type_info b on  tb.app_type_id = b.id
   		  where 1=1 and locate(#{termTypeCode},tb.term_type_codestr)>0
   		 <include refid="factoryCondition" />
   		 order by tb.dl_num desc,tb.group_id asc
   </select>

   <select id="queryListByIdList" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
   select
   	<include refid="Base_Column_List" />
   from coms_store_apk_info t
   where 1=1
	and app_code in ('${idListStr}')
	and up_flag = '1'
	and rec_st = '3'
	and t.id in (select apk.id from coms_store_apk_info apk,
	(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info t where 1 = 1
	and t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)
	group BY app_code) tb
	where apk.app_code = tb.app_code  and locate(#{termTypeCode},tb.term_type_codestr)>0
   	<include refid="factoryCondition" />
		<!-- and apk.ins_id = tb.ins_id -->
	)
   </select>

   <select id="queryListByKeyWord" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkSelectCondition">
    SELECT
		tb.*, b.tp_nm
	from (
   		select t.*
   		 from coms_store_apk_info t
   where t.ins_id in (select t2.id from cpay_institution t2 where t2.code IN (${codeListStr}))
   		
	    and t.rec_st = '3'
	    and t.id not in (select app_id from coms_store_apk_range where ins_id = #{insId}
	    <if test="groupId == 0" >
        	 and group_id = 0
      	</if>
	    <if test="groupId != 0" >
        	and (group_id = #{groupId} or group_id = 0)
      	</if>
	    )
	    <if test="groupId != 0" >
        	and ( t.group_id = #{groupId} or t.group_id = 0)
      	</if>
      	<if test="groupId == 0" >
        	and t.group_id = 0
      	</if>
      	and t.id in (select apk.id from coms_store_apk_info apk,(select app_code,max(t.ins_id) as ins_id from coms_store_apk_info t where 1 = 1
      	and t.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${codeListStr})
		)
      	group BY app_code) tb
					where apk.app_code = tb.app_code
					<!-- and apk.ins_id = tb.ins_id -->
					)
   	    and ( t.app_name like '%${keyword}%'
	    or
	      t.key_word like '%${keyword}%')
	     ) tb
		 left join coms_store_apk_type_info b on  tb.app_type_id = b.id
		 where 1=1 and locate(#{termTypeCode},tb.term_type_codestr)>0
   		 <include refid="factoryCondition" />
   </select>

   <select id="queryNoUninstallApkCode" resultType="java.lang.String" >
   select 
   		app_code
   from coms_store_apk_info
   where uninstall_flag = '2'
   </select>
   <!-- edit by xiarp end 20160706-->
    <insert id="insertSelective" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" >
    insert into coms_store_apk_info
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="appCode != null" >
        app_code,
      </if>
      <if test="appVersion != null" >
        app_version,
      </if>
      <if test="appName != null" >
        app_name,
      </if>
      <if test="apkId != null" >
        apk_id,
      </if>
      <if test="iconId != null" >
        icon_id,
      </if>
      <if test="picId != null" >
        pic_id,
      </if>
      <if test="keyWord != null" >
        key_word,
      </if>
      <if test="appDesc != null" >
        app_desc,
      </if>
      <if test="verDesc != null" >
        ver_desc,
      </if>
      <if test="pubTime != null" >
        pub_time,
      </if>
      <if test="recSt != null" >
        rec_st,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="auditUserId != null" >
        audit_user_id,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditMsg != null" >
        audit_msg,
      </if>
      <if test="dlNum != null" >
        dl_num,
      </if>
      <if test="uninstallFlag != null" >
        uninstall_flag,
      </if>
      <if test="upFlag != null" >
        up_flag,
      </if>
      <if test="reDegree != null" >
        re_degree,
      </if>
      <if test="appScreen != null" >
        app_screen,
      </if>
      <if test="appUrl != null" >
        app_url,
      </if>
      <if test="iconUrl != null" >
        icon_url,
      </if>
      <if test="appSize != null" >
        app_size,
      </if>
      <if test="picUrl != null" >
        pic_url,
      </if>
      <if test="appVersionName != null" >
        app_version_name,
      </if>
      <if test="signMd5 != null" >
        sign_md5,
      </if>
      <if test="apkMd5 != null" >
        apk_md5,
      </if>
      <if test="insId != null" >
        ins_id,
      </if>
      <if test="insCd != null" >
        ins_cd,
      </if>
      <if test="groupId != null">
         group_id,
      </if>
      <if test="appTypeId != null" >
        app_type_id,
      </if>
      <if test="developerId != null" >
        developer_id,
      </if>
      <if test="appPromulgator != null" >
        app_promulgator,
      </if>
		<if test="factoryId != null" >
			factory_id,
		</if>
		<if test="termTypeCodeStr != null" >
			term_type_codestr,
		</if>
		<if test="termTypeNameStr != null" >
			term_type_namestr,
		</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appName != null" >
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="apkId != null" >
        #{apkId,jdbcType=VARCHAR},
      </if>
      <if test="iconId != null" >
        #{iconId,jdbcType=VARCHAR},
      </if>
      <if test="picId != null" >
        #{picId,jdbcType=VARCHAR},
      </if>
      <if test="keyWord != null" >
        #{keyWord,jdbcType=VARCHAR},
      </if>
      <if test="appDesc != null" >
        #{appDesc,jdbcType=VARCHAR},
      </if>
      <if test="verDesc != null" >
        #{verDesc,jdbcType=VARCHAR},
      </if>
      <if test="pubTime != null" >
        #{pubTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recSt != null" >
        #{recSt,jdbcType=CHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUserId != null" >
        #{auditUserId,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditMsg != null" >
        #{auditMsg,jdbcType=VARCHAR},
      </if>
      <if test="dlNum != null" >
        #{dlNum,jdbcType=INTEGER},
      </if>
      <if test="uninstallFlag != null" >
        #{uninstallFlag,jdbcType=CHAR},
      </if>
      <if test="upFlag != null" >
        #{upFlag,jdbcType=CHAR},
      </if>
      <if test="reDegree != null" >
        #{reDegree,jdbcType=INTEGER},
      </if>
      <if test="appScreen != null" >
        #{appScreen,jdbcType=CHAR},
      </if>
      <if test="appUrl != null" >
        #{appUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null" >
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="appSize != null" >
        #{appSize,jdbcType=INTEGER},
      </if>
      <if test="picUrl != null" >
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="signMd5 != null" >
        #{signMd5,jdbcType=VARCHAR},
      </if>
      <if test="apkMd5 != null" >
        #{apkMd5,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        #{insId,jdbcType=INTEGER},
      </if>
       <if test="insCd != null" >
        #{insCd,jdbcType=VARCHAR},
       </if>
      <if test="groupId != null">
         #{groupId,jdbcType=INTEGER},
      </if>
      <if test="appTypeId != null" >
        #{appTypeId,jdbcType=INTEGER},
      </if>
      <if test="developerId != null" >
         #{developerId,jdbcType=CHAR},
      </if>
      <if test="appPromulgator != null" >
         #{appPromulgator,jdbcType=VARCHAR},
      </if>
		<if test="factoryId != null" >
			#{factoryId,jdbcType=INTEGER},
		</if>
		<if test="termTypeCodeStr != null" >
			#{termTypeCodeStr,jdbcType=VARCHAR},
		</if>
		<if test="termTypeNameStr != null" >
			#{termTypeNameStr,jdbcType=VARCHAR},
		</if>
    </trim>
  </insert>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
   delete from  coms_store_apk_info where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deletePubedApkById" parameterType="java.lang.Integer">
   delete from  coms_store_apk_info where id = #{id,jdbcType=VARCHAR}
  </delete>

  <update id="updateDownloadNumber" >
  UPDATE coms_store_apk_info csai, (SELECT
    ctar.app_code,
    COUNT(ctar.term_seq) AS dl_num
  FROM coms_terminal_app_relation ctar
  	GROUP BY ctar.app_code) AS tmp_tb
  SET csai.dl_num = tmp_tb.dl_num
	WHERE csai.app_code = tmp_tb.app_code
	AND csai.dl_num <![CDATA[ < ]]> tmp_tb.dl_num
  </update>
  <select id="getApkDownLoadList"  parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
        SELECT
        b.name as ins_name,
		a.app_code,
		a.app_name,
		a.app_version,
		a.pub_Time,
		ifnull(a.dl_num, 0) AS dl_num
		FROM
		coms_store_apk_info a
		LEFT JOIN cpay_institution b ON a.ins_id = b.id where 1=1
		<if test="appName != null" >
			and a.app_name like '%${appName}%'
     	</if>
		<if test="insId != null and insId != 0" >
        	and a.ins_id in(SELECT s1.id from cpay_institution s1 where locate(( SELECT s2.detail from cpay_institution s2 WHERE s2.id = #{insId}),s1.detail)>0)
     	</if>
   </select>

   <update id="reCycleApkInfoById" parameterType="java.lang.Integer">
  	UPDATE coms_store_apk_info 
  	SET is_delete = 1
	WHERE id = #{id,jdbcType=INTEGER}
  </update>

  <update id="restoreApkById" parameterType="java.lang.Integer">
  	UPDATE coms_store_apk_info 
  	SET is_delete = 0
	WHERE id = #{id,jdbcType=INTEGER}
  </update>

  <update id="offLineApk" parameterType="java.lang.Integer">
  	UPDATE coms_store_apk_info 
  	SET up_flag = 0,rec_st = 4
	WHERE id = #{id,jdbcType=INTEGER}
  </update>
  <select id="checkApkPubEd" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultType="java.util.HashMap">
  		select ins_id from coms_store_apk_info where app_code =#{appCode} and app_version=#{appVersion,jdbcType=VARCHAR}
  </select>
  <update id="appYL" parameterType="java.util.Map">
  	UPDATE coms_store_apk_info 
  	SET app_ylpackage_id = #{appIdYl}
	WHERE id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryAppYlpackageId" parameterType="java.lang.String" resultType="java.lang.Integer">
  	select app_ylpackage_id from coms_store_apk_info where id=#{appId}
  </select>
  <update id="calcelApkYL" parameterType="java.lang.String">
  	update coms_store_apk_info set app_ylpackage_id = 0 where id=#{appId}
  </update>
  <select id="queryDlNumByApkId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
  	select dl_num from coms_store_apk_info where id=#{id,jdbcType=INTEGER}
  </select>
  <select id="isDelectZS" resultType="java.lang.Integer" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  	select count(*) from coms_store_apk_info 
  	where 
  		app_code=#{appCode,jdbcType=VARCHAR} 
  	 	and <![CDATA[app_version >=#{appVersion,jdbcType=VARCHAR}]]>
  		and is_delete='1' and ins_id = #{insId,jdbcType=INTEGER}
  </select>
  <select id="isDelectTemp" resultType="java.lang.Integer" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  	select count(*) from coms_store_apk_info_temporary 
  	where 
  		app_code=#{appCode,jdbcType=VARCHAR} 
  		and <![CDATA[app_version >=#{appVersion,jdbcType=VARCHAR}]]>
  		and is_delete='1' and ins_id = #{insId,jdbcType=INTEGER}
  </select>
  <update id="updateAppDeleteApply" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  	update cpay_dev_app_delete_apply set deal_status='1' where 1=1
  	  <if test="appCode != null" >
       and app_code = #{appCode,jdbcType=VARCHAR}
      </if>
      <if test="appVersion != null" >
       and app_version = #{appVersion,jdbcType=VARCHAR}
      </if>
  </update>
  <insert id="insertUserNotice" parameterType="java.util.Map">
  	insert into cpay_dev_user_notice(title, content,create_time, 
      stick, author_id, modify_time, 
      is_display, user_type, 
      user_id) values(#{title},#{content},now(),'0',#{authorId},now(),'1','',#{userId})
  </insert>
  <select id="isSameCodeVersion" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultType="java.lang.Integer">
		select sum(count) from(
		select count(*) as count from coms_store_apk_info_temporary a where a.app_code=#{appCode,jdbcType=VARCHAR} 
		and a.app_version =#{appVersion,jdbcType=VARCHAR} and a.ins_id=#{insId,jdbcType=INTEGER}
		union 
		select count(*) as count from coms_store_apk_info b where b.app_code=#{appCode,jdbcType=VARCHAR} 
		and b.app_version =#{appVersion,jdbcType=VARCHAR} and b.ins_id=#{insId,jdbcType=INTEGER}
		)t
  </select>
  <select id="selectExistWhere" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
  		select a.* from coms_store_apk_info a
	  	where a.app_code=#{appCode}  and a.ins_id=#{insId}
	  	and a.factory_id = #{factoryId}
		  <if test="groupId != null and groupId !='0'" >
			  and a.group_id = #{groupId,jdbcType=INTEGER}
		  </if>
		  <if test="groupId == null or groupId =='0'" >
			  and a.group_id = '0'
		  </if>
  		and a.rec_st='3'
  </select>
  <delete id="deleteOffLineApk" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  		delete from coms_store_apk_info where app_code=#{appCode}  and ins_id=#{insId}
  		<if test="groupId != null and groupId !='0'" >
  			and group_id = #{groupId,jdbcType=INTEGER}
  		</if>
  		<if test="groupId == null or groupId =='0'" >
  			and group_id = '0'
  		</if>
  		and rec_st='4' and <![CDATA[app_version <=#{appVersion,jdbcType=VARCHAR}]]>
  </delete>
  <select id="getPubApkForApkRange" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
  	select a.*,b.name as ins_name,c.name as group_name,
  	d.name as  factory_name,e.name as term_type_name from coms_store_apk_info
  	a left join cpay_institution b on a.ins_Id = b.id
  	left join coms_terminal_group c on a.group_id = c.id
  	left join coms_terminal_manufacturer d on a.factory_id = d.id
  	left join coms_terminal_type e on a.term_type_id = e.id
  	where rec_st='3'
  	<if test="insCd != null">
		and a.ins_cd in (${insCd})
    </if>
  </select>
  <delete id="cleanCurrentRecycle" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  	delete from coms_store_apk_info where is_delete='1' and app_code=#{appCode,jdbcType=VARCHAR} and ins_cd=#{insCd,jdbcType=VARCHAR}
  </delete>
  <select id="getTermTypeName" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo" resultMap="BaseResultMap">
  	 SELECT a.*, b.id as term_type_id,b.code as term_type_code,b.name as term_type_name
  	 FROM coms_store_apk_info a  LEFT JOIN coms_terminal_type b ON a.term_type_id = b.id
	   WHERE 1 = 1
	   <if test="factoryId != null and factoryId !='0'">
	    and a.factory_id = #{factoryId,jdbcType=INTEGER}
	   </if>
	   <if test="id != null and id !='0'">
	    and a.id = #{id,jdbcType=INTEGER}
	   </if>
	   <if test="appVersion != null ">
	    and a.app_version = #{appVersion,jdbcType=VARCHAR}
	   </if>
	   <if test="appCode != null">
	   	AND a.app_code= #{appCode,jdbcType=VARCHAR}
	   	AND a.id IN (select max(id) from coms_store_apk_info where app_code= #{appCode,jdbcType=VARCHAR} group by term_type_id)
	   </if>
  </select>
  <select id="getAppPublishReport" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkPublishCount" resultMap="StoreApkReportMap">
  	SELECT
	count(*) AS appPublishCount,DATE_FORMAT(a.create_time,'%Y%m') AS month,
	b.name as factoryName
	FROM
	coms_store_apk_info a
	LEFT JOIN coms_terminal_manufacturer b ON a.factory_id = b.id
	where a.factory_id=#{factoryId,jdbcType=INTEGER}
	<if test="isShowAll == 1">
		and a.ins_id in (SELECT s1.id from cpay_institution s1
		where locate((SELECT s2.detail from cpay_institution
		s2 where
		s2.id=#{insId}),s1.detail)>0)
	</if>
    <if test="isShowAll == 0">
	    and a.ins_id = #{insId,jdbcType=INTEGER}
	</if>
	  <if test="startTime != null">
		  and  <![CDATA[ DATE_FORMAT(a.create_time,'%Y%m') >= #{startTime,jdbcType=VARCHAR} ]]>
	  </if>
	  <if test="endTime != null">
		  and  <![CDATA[ DATE_FORMAT(a.create_time,'%Y%m') <= #{endTime,jdbcType=VARCHAR} ]]>
	  </if>
	GROUP BY
	b.name,DATE_FORMAT(a.create_time,'%Y%m')
  </select>
</mapper>