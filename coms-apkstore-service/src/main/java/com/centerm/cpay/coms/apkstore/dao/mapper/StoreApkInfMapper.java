package com.centerm.cpay.coms.apkstore.dao.mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.centerm.cpay.coms.apkstore.dao.pojo.*;
import com.centerm.cpay.coms.dao.pojo.Institution;

public interface StoreApkInfMapper {

	List<StoreApkInfo> getCount(String insCd);

	List<StoreApkInfo> getRecCount(String insCd);

	List<StoreApkInfo> getApkList(StoreApkInfo storeApkInfo);
	
	List<StoreApkInfo> getApkListByAppCode(StoreApkInfo storeApkInfo);
	
	List<StoreApkInfo> getApkPublishedList(StoreApkInfo storeApkInfo);
	
	List<StoreApkInfo> getApkList(HashMap<String, String> map);

	Institution getInsInfoById(Integer insId);

	StoreApkInfo getApkById(Integer id);
    
	StoreApkInfo getApkByCondition(StoreApkInfo storeApkInfo);


	int updateApkInfo(StoreApkInfo storeApkInfo);

	List<String> getVersion(HashMap<String, String> map);

	List<StoreApkInfo> queryPageByMap(ApkSelectCondition condition);

	List<StoreApkInfo> queryListOrderByRecommended(ApkSelectCondition condition);

	List<StoreApkInfo> queryListOrderByDownload(ApkSelectCondition condition);

	List<StoreApkInfo> queryListByIdList(ApkSelectCondition condition);

	List<StoreApkInfo> queryListByKeyWord(ApkSelectCondition condition);

	List<StoreApkInfo> selectByAppCode(ApkSelectCondition condition);

	List<String> queryNoUninstallApkCode();

	int add(StoreApkInfo storeApkInfo);

	int insertSelective(StoreApkInfo storeApkInfo);
	
	void deletePubedApkById(Integer id); 
	
	List<StoreApkInfo> getApkByTypeId(Integer appTypeId);
	
	int updateDownloadNumber();

	List<String> getPubVersion(HashMap<String, String> map);

	List<StoreApkInfo> getApkDownLoadList(StoreApkInfo storeApkInfo);

	List<Integer> isUploded(Map map);

	List<StoreApkInfo> getApkRecycleList(StoreApkInfo storeApkInfo);

	int reCycleApkInfoById(Integer id);

	int restoreApkById(Integer id);

	int offLineApk(Integer id);

	List<StoreApkInfo> getPubApk(StoreApkInfo apkInfo);
	List<StoreApkInfo> getDriverJobPubApk(StoreApkInfo apkInfo);

	List checkApkPubEd(StoreApkInfo apkInfo);

	void appYL(Map map);

	Integer queryAppYlpackageId(String appId);

	void calcelApkYL(String appId);
	
	List<StoreApkInfo> getApkByAppYlpackageId(Integer ylId);

	Integer queryDlNumByApkId(Integer id);

	Integer isDelectZS(StoreApkInfo storeApkInfo);

	Integer isDelectTemp(StoreApkInfo storeApkInfo);

	void updateAppDeleteApply(StoreApkInfo apk);

	void insertUserNotice(Map map);

	int isSameCodeVersion(StoreApkInfo apk);

	StoreApkInfo selectExistWhere(StoreApkInfo apkinfo);

	void deleteOffLineApk(StoreApkInfo apkinfo);

	List<StoreApkInfo> getPubApkForApkRange(StoreApkInfo apkInfo);

	void deleteByPrimaryKey(Integer id);

	void cleanCurrentRecycle(StoreApkInfo apkinfo);
	
	List<StoreApkInfo> getTermTypeName(StoreApkInfo apkinfo);

	List<StoreApkPublish> getAppPublishReport(StoreApkPublishCount storeApkPublishCount);
	
}
