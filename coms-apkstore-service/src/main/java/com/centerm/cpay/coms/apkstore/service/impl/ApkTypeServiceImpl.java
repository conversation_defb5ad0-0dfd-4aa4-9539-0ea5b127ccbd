package com.centerm.cpay.coms.apkstore.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.apkstore.dao.mapper.ApkTypeInfoMapper;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTypeInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo;
import com.centerm.cpay.coms.apkstore.service.ApkTypeService;
import com.centerm.cpay.coms.apkstore.service.StoreApkService;
import com.centerm.cpay.coms.apkstore.util.StoreUtil;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalType;
import com.centerm.cpay.coms.service.FileInfoService;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.service.TerminalService;
import com.centerm.cpay.coms.service.TerminalTypeService;

@Service
public class ApkTypeServiceImpl implements ApkTypeService {


	@Autowired
	private StoreApkService storeApkService;
	
	@Autowired
	private TerminalService terminalService;
	
	@Autowired
	private SysConfigureService sysConfigureService;
	
	@Autowired
	private InstitutionService institutionService;
	
	@Autowired
	private FileInfoService fileInfoService;
	
	@Autowired
	private ApkTypeInfoMapper apkTypeInfoMapper;
	
	@Autowired
	private TerminalTypeService terminalTypeService;
	
	@Override
	public ResultMsg queryApkTpesContainAppInfo(String termSeq,String screenType) {
		String faile1 = "Query terminal information is abnormal";
		String faile2 = "Terminal affiliate does not exist";
		String faile3 = "Check no application category information";
		Terminal terminal = terminalService.selectByTermSeq(termSeq);
		if(terminal == null){
			return ResultMsg.fail(faile1);
		}
		
		List<TerminalType> terminalTypeList = terminalTypeService.selectTermTypeByCode(terminal.getTermTypeCode());
		if(CtUtils.isEmpty(terminalTypeList)){
			return ResultMsg.fail(faile1);
		}
		
		Institution institution = institutionService.selectByInsId(terminal.getInsId());
		if(institution == null){
			return ResultMsg.fail(faile2);
		}
		Map<String,String> paramMap = new HashMap<String,String>();
		paramMap.put("codeListStr",institution.getDetail());
		List<ApkTypeInfo> apkTypeList = apkTypeInfoMapper.selectByCodeList(paramMap);
		if(apkTypeList== null || apkTypeList.size() == 0){
			return ResultMsg.fail(faile3);
		}
		List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
		for(ApkTypeInfo tempTypeInfo:apkTypeList){
			Map<String,Object> map = new HashMap<String,Object>();
			FileInfo fileinfo = fileInfoService.getFileByuuid(tempTypeInfo.getIconId());
			map.put("apkTypeId", tempTypeInfo.getId());
			map.put("apkTypeName", tempTypeInfo.getTpNm());
			map.put("iconUrl", fileinfo == null?"":getDownload()+fileinfo.getFileSavePath());
			List<StoreApkInfo> apkList = storeApkService.queryByApkType(tempTypeInfo.getId(),screenType,institution.getDetail(),termSeq);
			map.put("apps", StoreUtil.changeApkInfoListToAppInfoList(apkList,getDownload()));
			resultList.add(map);
		}
		Map<String,Object> returnMap = new HashMap<String,Object>();
		returnMap.put("types", resultList);
		return ResultMsg.success(returnMap);
	}
	
	private String getDownload() {

		SysConfigure sysConfigure = sysConfigureService.getSysConfByKey("download_url");
		return sysConfigure.getValue();
	}
}
