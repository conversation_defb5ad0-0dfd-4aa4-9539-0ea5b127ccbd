<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.apkstore.dao.mapper.ApkTemporaryMapper" >

    <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="app_code" property="appCode" jdbcType="VARCHAR" />
        <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
        <result column="app_name" property="appName" jdbcType="VARCHAR" />
        <result column="apk_id" property="apkId" jdbcType="VARCHAR" />
        <result column="icon_id" property="iconId" jdbcType="VARCHAR" />
        <result column="pic_id" property="picId" jdbcType="VARCHAR" />
        <result column="key_word" property="keyWord" jdbcType="VARCHAR" />
        <result column="app_desc" property="appDesc" jdbcType="VARCHAR" />
        <result column="ver_desc" property="verDesc" jdbcType="VARCHAR" />
        <result column="rec_st" property="recSt" jdbcType="CHAR" />
        <result column="developer_id" property="developerId" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="pub_time" property="pubTime" jdbcType="TIMESTAMP" />
        <result column="audit_user_id" property="auditUserId" jdbcType="INTEGER" />
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        <result column="audit_msg" property="auditMsg" jdbcType="VARCHAR" />
        <result column="app_screen" property="appScreen" jdbcType="CHAR" />
        <result column="app_version_name" property="appVersionName" jdbcType="VARCHAR" />
        <result column="sign_md5" property="signMd5" jdbcType="VARCHAR" />
        <result column="app_type_id" property="appTypeId" jdbcType="INTEGER" />
        <result column="ins_id" jdbcType="INTEGER" property="insId" />
        <result column="ins_cd" property="insCd" jdbcType="VARCHAR" />
        <result column="user_name" property="developerName" jdbcType="VARCHAR" />
        <result column="company" property="company" jdbcType="VARCHAR" />
        <result column="link_tel" property="linkTel" jdbcType="VARCHAR" />
        <result column="name" property="insName" jdbcType="VARCHAR" />
        <result column="group_id" property="groupId" jdbcType="INTEGER"/>
        <result column="file_save_path"  property="filePath" />
        <result column="factory_id" property="factoryId" jdbcType="INTEGER"/>
        <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
        <result column="group_name" property="groupName" jdbcType="VARCHAR" />
        <result column="term_type_codestr" property="termTypeCodeStr" jdbcType="VARCHAR" />
        <result column="term_type_namestr" property="termTypeNameStr" jdbcType="VARCHAR" />

    </resultMap>

    <select id="getPubApk" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary" resultMap="BaseResultMap">
        SELECT
        info.*,
        ins1.name AS insName
        FROM
        (
        (SELECT
        t1.*
        FROM
        coms_store_apk_info_temporary t1
        WHERE t1.rec_st = 2)
        UNION
        ALL
        (SELECT
        t2.id,
        t2.app_code,
        t2.app_version,
        t2.app_name,
        t2.apk_id,
        t2.icon_id,
        t2.pic_id,
        t2.key_word,
        t2.app_desc,
        t2.ver_desc,
        t2.rec_st,
        t2.user_id,
        t2.developer_id,
        t2.create_time,
        t2.audit_user_id,
        t2.pub_time,
        t2.audit_time,
        t2.audit_msg,
        t2.app_screen,
        t2.app_version_name,
        t2.sign_md5,
        t2.app_type_id,
        t2.group_id,
        t2.ins_id,
        t2.ins_cd,
        t2.is_delete,
        t2.factory_id,
        t2.term_type_id,
        t2.term_type_codestr
        FROM
        coms_store_apk_info t2
        WHERE t2.rec_st = 3
        OR t2.rec_st = 4)
        ) info
        LEFT JOIN cpay_institution ins1
        ON info.ins_id = ins1.id
        WHERE info.is_delete = '0' AND info.ins_cd IN
        (SELECT
        ins2.code
        FROM
        cpay_institution ins2
        WHERE ins2.detail LIKE CONCAT(concat(
        '%',
        (SELECT
        ins3.detail
        FROM
        cpay_institution ins3
        WHERE ins3.id = #{insId})),
        '%'
        ))
        <if test="appName != null">
            AND info.app_name LIKE concat(concat('%',#{appName}),'%')
        </if>
        ORDER BY info.create_time DESC
    </select>

    <select id="getApkByTypeId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
         select * from coms_store_apk_info_temporary  where  app_type_id = #{appTypeId}  and rec_st not in('2','3')
    </select>

    <select id="getAuditList" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary">
        SELECT
        A.*,
        B.file_save_path AS icon_path,
        D.name,
        tp.tp_nm AS appTypeName,
        CONCAT(#{ngsPicPath},B.file_save_path) AS file_save_path,
        g.name AS group_name,fa.name AS factory_name
        FROM
        coms_store_apk_info_temporary A
        LEFT JOIN coms_store_file_info B
        ON B.uuid = A.icon_id
        LEFT JOIN coms_terminal_group g
        ON g.id = A.group_id
        LEFT JOIN coms_terminal_manufacturer fa
        ON A.factory_id = fa.id
        LEFT JOIN cpay_institution D
        ON A.ins_id = D.id
        LEFT JOIN coms_store_apk_type_info tp
        ON tp.id = A.app_type_id
        WHERE 1=1 and A.rec_st='1'
        and A.ins_cd in
        (SELECT s1.code from cpay_institution s1 where s1.detail like
        CONCAT(concat('%',(SELECT s2.detail from cpay_institution s2 where s2.id=#{insId})),'%'))
        <if test="appName != null">
            and app_name LIKE concat (concat('%',#{appName}),'%')
        </if>
        <if test="appScreen != null ">
            and app_screen = #{appScreen}
        </if>
        <if test="appTypeId != null ">
            and APP_TYPE_ID = #{appTypeId,jdbcType=INTEGER}
        </if>
        <if test="appVersion != null">
            and app_version LIKE concat (concat('%',#{appVersion}),'%')
        </if>
        order by A.create_time desc
    </select>
    <select id="getApkTempById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
      select A.*,fa.name AS factory_name from coms_store_apk_info_temporary A
      LEFT JOIN coms_terminal_manufacturer fa ON A.factory_id = fa.id
      where 1=1 and A.id = #{id,jdbcType=INTEGER}
  </select>

    <select id="getApkTempByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary">
        select * from coms_store_apk_info_temporary A where 1=1
        <if test="appCode != null">
            and A.app_code = #{appCode}
        </if>
        <if test="insId != null">
            and A.ins_id = #{insId}
        </if>
        <if test="appVersion != null">
            and A.app_version = #{appVersion}
        </if>
    </select>

    <select id="getApkAmount" resultType="java.lang.Integer" parameterType="java.lang.String">
   select count(*)  from (select a.id from coms_store_apk_info_temporary a inner  JOIN cpay_institution b on a.ins_id=b.id WHERE a.rec_st='1' and a.is_delete='0' and b.detail like concat(concat('%',#{insCd}),'%'))c
     UNION all
   SELECT COUNT(*) FROM (SELECT t1.ins_id,t1.is_delete FROM coms_store_apk_info_temporary t1 WHERE t1.rec_st = 2 UNION ALL SELECT t2.ins_id,t2.is_delete FROM coms_store_apk_info t2 WHERE t2.rec_st = 4) info LEFT JOIN cpay_institution ins1 ON info.ins_id = ins1.id WHERE info.is_delete = '0' and ins1.detail like concat(concat('%',#{insCd}),'%')
 </select>
    <select id="getApkAuditCount" resultType="java.lang.Integer"
            parameterType="java.lang.String">
		select count(*) from (select a.id from coms_store_apk_info_temporary a inner
		JOIN cpay_institution b on a.ins_id=b.id WHERE a.rec_st='1' and
		a.is_delete='0' and
		b.detail like '%${_parameter}%'
		)t
	</select>
    <select id="getApkPubCount" resultType="java.lang.Integer"
            parameterType="java.lang.String">
		select count(*) from
		(SELECT t1.ins_id,t1.is_delete FROM
		coms_store_apk_info_temporary t1 WHERE t1.rec_st = 2 UNION ALL SELECT
		t2.ins_id,t2.is_delete FROM coms_store_apk_info t2 WHERE t2.rec_st =
		4) info LEFT JOIN cpay_institution ins1 ON info.ins_id = ins1.id WHERE
		info.is_delete = '0' and  	ins1.detail like '%${_parameter}%'
	</select>
    <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary">
        update coms_store_apk_info_temporary
        <set >
            <if test="appCode != null" >
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="appVersion != null" >
                app_version = #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="appName != null" >
                app_name = #{appName,jdbcType=VARCHAR},
            </if>
            <if test="apkId != null" >
                apk_id = #{apkId,jdbcType=VARCHAR},
            </if>
            <if test="iconId != null" >
                icon_id = #{iconId,jdbcType=VARCHAR},
            </if>
            <if test="picId != null" >
                pic_id = #{picId,jdbcType=VARCHAR},
            </if>
            <if test="keyWord != null" >
                key_word = #{keyWord,jdbcType=VARCHAR},
            </if>
            <if test="appDesc != null" >
                app_desc = #{appDesc,jdbcType=VARCHAR},
            </if>
            <if test="verDesc != null" >
                ver_desc = #{verDesc,jdbcType=VARCHAR},
            </if>
            <if test="recSt != null" >
                rec_st = #{recSt,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUserId != null" >
                audit_user_id = #{auditUserId,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null" >
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMsg != null" >
                audit_msg = #{auditMsg,jdbcType=VARCHAR},
            </if>
            <if test="appScreen != null" >
                app_screen = #{appScreen,jdbcType=CHAR},
            </if>
            <if test="appVersionName != null" >
                app_version_name = #{appVersionName,jdbcType=VARCHAR},
            </if>
            <if test="signMd5 != null" >
                sign_md5 = #{signMd5,jdbcType=VARCHAR},
            </if>
            <if test="appTypeId != null" >
                app_type_id = #{appTypeId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null" >
                group_id = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="insId != null" >
                ins_id = #{insId,jdbcType=INTEGER},
            </if>
            <if test="insCd != null" >
                ins_cd = #{insCd,jdbcType=VARCHAR},
            </if>
            <if test="factoryId != null" >
                factory_id = #{factoryId,jdbcType=INTEGER},
            </if>
            <if test="termTypeCodeStr != null" >
                term_type_codestr = #{termTypeCodeStr,jdbcType=VARCHAR},
            </if>
            <if test="termTypeNameStr != null" >
                term_type_namestr = #{termTypeNameStr,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <insert id="insert" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary" >
        insert into coms_store_apk_info_temporary
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="appCode != null" >
                app_code,
            </if>
            <if test="appVersion != null" >
                app_version,
            </if>
            <if test="appName != null" >
                app_name,
            </if>
            <if test="apkId != null" >
                apk_id,
            </if>
            <if test="iconId != null" >
                icon_id,
            </if>
            <if test="picId != null" >
                pic_id,
            </if>
            <if test="keyWord != null" >
                key_word,
            </if>
            <if test="appDesc != null" >
                app_desc,
            </if>
            <if test="verDesc != null" >
                ver_desc,
            </if>
            <if test="recSt != null" >
                rec_st,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="auditUserId != null" >
                audit_user_id,
            </if>
            <if test="auditTime != null" >
                audit_time,
            </if>
            <if test="auditMsg != null" >
                audit_msg,
            </if>
            <if test="appScreen != null" >
                app_screen,
            </if>
            <if test="appVersionName != null" >
                app_version_name,
            </if>
            <if test="signMd5 != null" >
                sign_md5,
            </if>
            <if test="appTypeId != null" >
                app_type_id,
            </if>
            <if test="groupId != null" >
                group_id,
            </if>
            <if test="insCd != null" >
                ins_cd,
            </if>
            <if test="insId != null">
                ins_id,
            </if>
            <if test="developerId != null">
                developer_id,
            </if>
            <if test="factoryId != null">
                factory_id,
            </if>
            <if test="termTypeCodeStr != null">
                term_type_codestr,
            </if>
            <if test="termTypeNameStr != null">
                term_type_namestr,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <!-- <if test="id != null" >
              #{id,jdbcType=INTEGER},
            </if> -->
            <if test="appCode != null" >
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="appVersion != null" >
                #{appVersion,jdbcType=VARCHAR},
            </if>
            <if test="appName != null" >
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="apkId != null" >
                #{apkId,jdbcType=VARCHAR},
            </if>
            <if test="iconId != null" >
                #{iconId,jdbcType=VARCHAR},
            </if>
            <if test="picId != null" >
                #{picId,jdbcType=VARCHAR},
            </if>
            <if test="keyWord != null" >
                #{keyWord,jdbcType=VARCHAR},
            </if>
            <if test="appDesc != null" >
                #{appDesc,jdbcType=VARCHAR},
            </if>
            <if test="verDesc != null" >
                #{verDesc,jdbcType=VARCHAR},
            </if>
            <if test="recSt != null" >
                #{recSt,jdbcType=CHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUserId != null" >
                #{auditUserId,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null" >
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMsg != null" >
                #{auditMsg,jdbcType=VARCHAR},
            </if>
            <if test="appScreen != null" >
                #{appScreen,jdbcType=CHAR},
            </if>
            <if test="appVersionName != null" >
                #{appVersionName,jdbcType=VARCHAR},
            </if>
            <if test="signMd5 != null" >
                #{signMd5,jdbcType=VARCHAR},
            </if>
            <if test="appTypeId != null" >
                #{appTypeId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null" >
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="insCd != null" >
                #{insCd,jdbcType=VARCHAR},
            </if>
            <if test="insId  != null">
                #{insId,jdbcType=INTEGER},
            </if>
            <if test="developerId != null">
                #{developerId,jdbcType=INTEGER},
            </if>
            <if test="factoryId != null">
                #{factoryId,jdbcType=INTEGER},
            </if>
            <if test="termTypeCodeStr != null" >
                #{termTypeCodeStr,jdbcType=VARCHAR},
            </if>
            <if test="termTypeNameStr != null" >
                #{termTypeNameStr,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <delete id="deleteById" parameterType="java.lang.Integer">
     delete from  coms_store_apk_info_temporary where id = #{id}
  </delete>

    <insert id="addLog" parameterType="java.util.Map">
	insert into cpay_log (user_name,module,action,op_desc,op_time) 
	values(#{userName},#{module},#{action},#{opDesc},#{opTime})
  </insert>

    <update id="reCycleApkTempById" parameterType="java.lang.Integer">
    update coms_store_apk_info_temporary
	set is_delete = 1
    where id = #{id,jdbcType=INTEGER}
  </update>

    <update id="restoreApkTempById" parameterType="java.lang.Integer">
    update coms_store_apk_info_temporary
	set is_delete = 0,rec_st = 0,audit_msg = NULL,audit_user_id = NULL,audit_time = NULL
    where id = #{id,jdbcType=INTEGER}
  </update>

    <update id="toAuditApks" parameterType="java.lang.Integer">
    update coms_store_apk_info_temporary
	set rec_st = 1
    where id = #{id,jdbcType=INTEGER}
  </update>

    <delete id="deleteApkInfoTempById" parameterType="java.lang.Integer">
     delete from  coms_store_apk_info_temporary where id = #{id}
  </delete>
    <update id="updateAppDeleteApplyTemp" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary">
        update cpay_dev_app_delete_apply set deal_status='1' where 1=1
        <if test="appCode != null" >
            and app_code = #{appCode,jdbcType=VARCHAR}
        </if>
        <if test="appVersion != null" >
            and app_version = #{appVersion,jdbcType=VARCHAR}
        </if>
    </update>
    <delete id="cleanCurrentRecycle" parameterType="com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo">
  	delete from coms_store_apk_info_temporary where is_delete='1' and app_code=#{appCode,jdbcType=VARCHAR} and ins_cd=#{insCd,jdbcType=VARCHAR}
  </delete>
</mapper>