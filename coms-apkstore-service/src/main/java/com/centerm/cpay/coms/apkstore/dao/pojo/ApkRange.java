package com.centerm.cpay.coms.apkstore.dao.pojo;

import java.util.Date;
import java.util.List;

public class ApkRange {
    private Integer id;

    private Integer appId;

    private Integer insId;

    private String insCode;

    private Date createTime;
    
    private String appCode;
    
    private String appName;
    
    private String appVersionName;
    
    private String insName;
    
    private String idstemp;
    
    private Integer groupId;
    
    private List<String> idstempList;
    
    private String groupName;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public Integer getInsId() {
        return insId;
    }

    public void setInsId(Integer insId) {
        this.insId = insId;
    }

    public String getInsCode() {
        return insCode;
    }

    public void setInsCode(String insCode) {
        this.insCode = insCode == null ? null : insCode.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public String getAppCode() {
		return appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getAppVersionName() {
		return appVersionName;
	}

	public void setAppVersionName(String appVersionName) {
		this.appVersionName = appVersionName;
	}

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public String getIdstemp() {
		return idstemp;
	}

	public void setIdstemp(String idstemp) {
		this.idstemp = idstemp;
	}

	public List<String> getIdstempList() {
		return idstempList;
	}

	public void setIdstempList(List<String> idstempList) {
		this.idstempList = idstempList;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
    
}