package com.centerm.cpay.coms.apkstore.dao.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApkTemporary {
    private Integer id;

    private String appCode;

    private String appVersion;

    private String appName;

    private String apkId;

    private String iconId;

    private String picId;

    private String keyWord;

    private String appDesc;

    private String verDesc;

    private String recSt;

    private Integer userId;
    
    private Integer developerId;

    private Date createTime;
    
    private Date pubTime;

    private Integer auditUserId;

    private Date auditTime;

    private String auditMsg;

    private String appScreen;

    private String appVersionName;

    private String signMd5;

    private Integer appTypeId;
    
    private String appTypeName;

    private String insCd;
    
    private Integer insId;
    private Integer groupId;
    private Integer factoryId;
    private String groupName;
    private String factoryName;
    private String termTypeCodeStr;
    private String termTypeNameStr;

	public String getTermTypeNameStr() {
		return termTypeNameStr;
	}

	public void setTermTypeNameStr(String termTypeNameStr) {
		this.termTypeNameStr = termTypeNameStr;
	}

	public String getTermTypeCodeStr() {
		return termTypeCodeStr;
	}

	public void setTermTypeCodeStr(String termTypeCodeStr) {
		this.termTypeCodeStr = termTypeCodeStr;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public Integer getFactoryId() {
		return factoryId;
	}

	public void setFactoryId(Integer factoryId) {
		this.factoryId = factoryId;
	}

	//非数据库字段
    private List app_pms = new ArrayList();     
    
    private String icon_path = "";
    
    private List picList = new ArrayList(); 
    
    private String file_absolute_path;
    
    private String file_save_name;
    
    private String ngsPicPath;
    
    private String userName;
    
    private String developerName;
    
    private String company;
    
    private String linkTel;
    
    private Integer reDegree;
    
    private String isDelete;
    
    private String insName;
    
    private String apkPath;
    
  //图片地址
    private String filePath;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }



    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion == null ? null : appVersion.trim();
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public String getApkId() {
        return apkId;
    }

    public void setApkId(String apkId) {
        this.apkId = apkId == null ? null : apkId.trim();
    }

    public String getIconId() {
        return iconId;
    }

    public void setIconId(String iconId) {
        this.iconId = iconId == null ? null : iconId.trim();
    }

    public String getPicId() {
        return picId;
    }

    public void setPicId(String picId) {
        this.picId = picId == null ? null : picId.trim();
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord == null ? null : keyWord.trim();
    }

    public String getAppDesc() {
        return appDesc;
    }

    public void setAppDesc(String appDesc) {
        this.appDesc = appDesc == null ? null : appDesc.trim();
    }

    public String getVerDesc() {
        return verDesc;
    }

    public void setVerDesc(String verDesc) {
        this.verDesc = verDesc == null ? null : verDesc.trim();
    }

    public String getRecSt() {
        return recSt;
    }

    public void setRecSt(String recSt) {
        this.recSt = recSt == null ? null : recSt.trim();
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId ;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public Integer getAuditUserId() {
		return auditUserId;
	}

	public void setAuditUserId(Integer auditUserId) {
		this.auditUserId = auditUserId;
	}

	public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditMsg() {
        return auditMsg;
    }

    public void setAuditMsg(String auditMsg) {
        this.auditMsg = auditMsg == null ? null : auditMsg.trim();
    }

    public String getAppScreen() {
        return appScreen;
    }

    public void setAppScreen(String appScreen) {
        this.appScreen = appScreen == null ? null : appScreen.trim();
    }


    public String getAppVersionName() {
		return appVersionName;
	}

	public void setAppVersionName(String appVersionName) {
		this.appVersionName = appVersionName;
	}

	public String getSignMd5() {
        return signMd5;
    }

    public void setSignMd5(String signMd5) {
        this.signMd5 = signMd5 == null ? null : signMd5.trim();
    }


	public Integer getAppTypeId() {
		return appTypeId;
	}

	public void setAppTypeId(Integer appTypeId) {
		this.appTypeId = appTypeId;
	}

	public String getInsCd() {
        return insCd;
    }

    public void setInsCd(String insCd) {
        this.insCd = insCd == null ? null : insCd.trim();
    }

	

	public String getAppCode() {
		return appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public List getApp_pms() {
		return app_pms;
	}

	public void setApp_pms(List app_pms) {
		this.app_pms = app_pms;
	}
	public String getIcon_path() {
		return icon_path;
	}

	public void setIcon_path(String icon_path) {
		this.icon_path = icon_path;
	}
	

	public List getPicList() {
		return picList;
	}

	public void setPicList(List picList) {
		this.picList = picList;
	}

	public Integer getInsId() {
		return insId;
	}

	public void setInsId(Integer insId) {
		this.insId = insId;
	}

	public String getFile_absolute_path() {
		return file_absolute_path;
	}

	public void setFile_absolute_path(String file_absolute_path) {
		this.file_absolute_path = file_absolute_path;
	}

	public String getFile_save_name() {
		return file_save_name;
	}

	public void setFile_save_name(String file_save_name) {
		this.file_save_name = file_save_name;
	}

	public String getNgsPicPath() {
		return ngsPicPath;
	}

	public void setNgsPicPath(String ngsPicPath) {
		this.ngsPicPath = ngsPicPath;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getLinkTel() {
		return linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}

	public Integer getReDegree() {
		return reDegree;
	}

	public void setReDegree(Integer reDegree) {
		this.reDegree = reDegree;
	}

	public Date getPubTime() {
		return pubTime;
	}

	public void setPubTime(Date pubTime) {
		this.pubTime = pubTime;
	}

	public Integer getDeveloperId() {
		return developerId;
	}

	public void setDeveloperId(Integer developerId) {
		this.developerId = developerId;
	}

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public String getDeveloperName() {
		return developerName;
	}

	public void setDeveloperName(String developerName) {
		this.developerName = developerName;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getAppTypeName() {
		return appTypeName;
	}

	public void setAppTypeName(String appTypeName) {
		this.appTypeName = appTypeName;
	}

	public String getApkPath() {
		return apkPath;
	}

	public void setApkPath(String apkPath) {
		this.apkPath = apkPath;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}
	
	
}