<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.centerm</groupId>
    <artifactId>cpay-coms</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>coms-apkstore-service</artifactId>
  <!-- 依赖管理 -->
  <dependencies>
         <dependency>
		    <groupId>com.centerm</groupId>
		    <artifactId>cpay-commons</artifactId>
		    <version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
		    <groupId>com.centerm</groupId>
		    <artifactId>cpay-developer-service</artifactId>
		    <version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
		    <groupId>com.centerm</groupId>
		    <artifactId>cpay-system-service</artifactId>
		    <version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
		    <groupId>com.centerm</groupId>
		    <artifactId>cpay-terminal-service</artifactId>
		    <version>0.0.1-SNAPSHOT</version>
		</dependency>
        <dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- Spring -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>

		<!-- Mybatis -->
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
		</dependency>
	    <dependency>
			<groupId>com.github.jsqlparser</groupId>
			<artifactId>jsqlparser</artifactId>
		</dependency>
		<!-- MySql -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!-- 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>
  </dependencies>
  	<distributionManagement>
		<snapshotRepository>
			<id>cpay-snapshots</id>
			<name>cpay-snapshots Repository</name>
			<url>http://192.168.48.66:8081/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement> 
  <!-- 如果不添加此节点mybatis的mapper.xml文件都会被漏掉。 -->
	<build>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>
	</build>
</project>