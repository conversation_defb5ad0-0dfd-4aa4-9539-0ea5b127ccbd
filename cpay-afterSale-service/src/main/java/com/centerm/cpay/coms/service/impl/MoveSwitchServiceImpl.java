package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.MoveSwitchMapper;
import com.centerm.cpay.coms.dao.pojo.MoveSwitch;
import com.centerm.cpay.coms.service.MoveSwitchService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class MoveSwitchServiceImpl implements MoveSwitchService {
	@Autowired
	MoveSwitchMapper mapper;
	
	
	@Override
	public EUDataGridResult getList(MoveSwitch moveSwitch,int page, int rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<MoveSwitch> list = mapper.selectList(moveSwitch);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<MoveSwitch> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
        return result;
	}
	
	@Override
	public ResultMsg insert(MoveSwitch moveSwitch) {
		mapper.insert(moveSwitch);
		return ResultMsg.success();
	}
	
	@Override
	public ResultMsg update(MoveSwitch moveSwitch) {
		mapper.updateByPrimaryKey(moveSwitch);
		return ResultMsg.success();
	}
	
	@Override
	public ResultMsg delete(Integer id) {
		 mapper.deleteByPrimaryKey(id);
		 return ResultMsg.success();
	}
	
	@Override
	public MoveSwitch selectById(Integer id) {
		MoveSwitch moveSwitch = mapper.selectByPrimaryKey(id);
		return moveSwitch;
	}

	@Override
	public List<MoveSwitch> getListByCondition(MoveSwitch moveSwitch) {
		List<MoveSwitch> movelist = mapper.selectList(moveSwitch);
		return movelist;
	}
}
