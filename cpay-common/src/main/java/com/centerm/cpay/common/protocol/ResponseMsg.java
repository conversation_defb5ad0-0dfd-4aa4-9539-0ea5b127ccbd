package com.centerm.cpay.common.protocol;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CommConstant;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ResponseMsg {
	static Logger logger = LoggerFactory.getLogger(ResponseMsg.class);

	public static String SUCCESS_CODE = "00";

	public static String ERROR_CODE = "01";

	public static String FAIL_CODE = "99";

	@Override
	public String toString() {
		return "ResponseMsg [header=" + header + ", body=" + body + ", sign=" + sign + "]";
	}

	private ResponseHeader header;

	private Object body;
	@JsonProperty("sign")
	private String sign;

	public static ResponseMsg fail(String functionId, String msg, RequestMsg<?> requestMsg) throws Exception {
		return new ResponseMsg(functionId, msg, null, requestMsg);
	}

	public static ResponseMsg build(String functionId, ResultMsg msg, RequestMsg<?> requestMsg) throws Exception {
		return new ResponseMsg(functionId, msg, requestMsg);
	}

	public ResponseMsg(String functionId, ResultMsg msg, RequestMsg<?> requestMsg) throws Exception {
		this.body = null;
		this.sign = requestMsg.getSign();
		if (msg.getStatus() == ResultMsg.SUCCESS_CODE) {
			this.header = new ResponseHeader(functionId + CommConstant.ERQUST_SUCCESS_CODE, msg.getMsg(), requestMsg);
			this.body = msg.getData();
		} else if (msg.getStatus() == ResultMsg.FAIL_CODE) {
			this.header = new ResponseHeader(functionId + CommConstant.ERQUST_FAIL_CODE, msg.getData().toString(), requestMsg);
		} else if(msg.getStatus() == ResultMsg.BUS_CODE){
			this.header = new ResponseHeader(functionId + CommConstant.ERQUST_BUS_ERROR, msg.getMsg().toString(), requestMsg);
		}else {
			this.header = new ResponseHeader(functionId + CommConstant.ERQUST_ERROR_CODE, msg.getMsg(), requestMsg);
		}
	}

	public ResponseMsg(String status, String msg, Object data, RequestMsg<?> requestMsg) throws Exception {
		this.header = new ResponseHeader(status, msg, requestMsg);
		this.body = data;
		this.sign = "";
		logger.debug("响应报文为"+toString());
	}
	

	public void setHeader(ResponseHeader header) {
		this.header = header;
	}

	public ResponseHeader getHeader() {
		return this.header;
	}

	public void setBody(Object body) {
		this.body = body;
	}

	public Object getBody() {
		return this.body;
	}
	@JsonIgnore
	public void setSign(String sign) {
		this.sign = sign;
	}
	@JsonIgnore
	public String getSign() {
		return this.sign;
	}
}
