/**
 *  copyright(c) 2013 FuJian star-net Information Corp.Ltd
 *
 * @File name:  Constants.java
 * @Version : 1.0
 * @Create on:  2013-07-17
 * <AUTHOR>  lujunming
 *
 * @ChangeList
 * ---------------------------------------------------
 * Date         Editor              ChangeReasons
 *
 *
 */
package com.centerm.cpay.common.utils;

import java.util.List;
import java.util.Map;
import java.util.Properties;


/**
 * 功能: 全局常量
 * <AUTHOR>
 */
public class Constants {

	/****连接池名称****/
	public static final String DATASOURCE_JNDI = "jdbc/mtms";
	/****数据库类型****/
    public static final String DATABASE_TYPE = "mysql";  

	public static final String APPLICATION_BEAN = "applicationBean";

	public static final String SESSION_BEAN = "sessionBean";

	public static final int PAGE_SIZE = 20;

	public static final String SUCCESS = "success";

	public static final String FAILURE = "failure";

	public static final String UNAUTHORIZED = "unauthorized";

	public static final String NOLOGIN = "nologin";
	
	public static final String LOGIN = "登录";

	public static final String RESUBMIT = "resubmit";

	public static final String LIST = "list";

	public static final String EDIT = "edit";

	public static final String LIST_PAGE = "listPage";
	
	public static final String CHART_PAGE = "chartPage";

	public static final String EDIT_PAGE = "editPage";

	public static final String ADD_PAGE = "addPage";

	public static final String UNVISITED = "unvisited";

	public static final String VIEW = "view";

	public static final String AUDIT = "audit";

	public static final String TREE = "tree";
			
	public static Properties properties = null;

	/****已上传****/
	public static final String FLOWFLAG1 = "1"; 
	/****已提交测试****/
	public static final String FLOWFLAG2 = "2"; 
	/**** 测试通过 ****/
	public static final String FLOWFLAG3 = "3"; 
	/**** 测试不通过 ****/
	public static final String FLOWFLAG4 = "4"; 
	/****  审核通过 ****/
	public static final String FLOWFLAG5 = "5";
	/****  审核不通过 ****/
	public static final String FLOWFLAG6 = "6"; 
	/****  已发布 ****/
	public static final String FLOWFLAG7 = "7"; 


	/**** 已上传 ****/
	public static final String ADFLAG1 = "1"; 
	/**** 待审核 ****/
	public static final String ADFLAG2 = "2"; 
	/**** 审核通过 ****/
	public static final String ADFLAG3 = "3"; 
	/**** 审核不通过 ****/
	public static final String ADFLAG4 = "4"; 
	/**** 已发布 ****/
	public static final String ADFLAG5 = "5"; 


	
	/**** 待审核 ****/
	
	public static final String ORGST1 = "0"; 
	/**** 审核通过 ****/
	public static final String ORGST2 = "1"; 
	/****  审核不通过  ****/
	public static final String ORGST3 = "2";
	/****  ****/
	public static final String[] ORGST_TITLES = { "待审核", "审核通过", " 审核不通过" };


	
	/**** 新增 ****/
	public static final String OPER1 = "0"; 
	/****  修改 ****/
	public static final String OPER2 = "1"; 
	/****  删除  ****/
	public static final String OPER3 = "2";
	/****  ****/
	public static final String[] OPER_TITLES = { "新增", "修改", "删除" };


	
	
	/**** 广告位置开始   屏保广告****/
	public static final String ADPOSITION1 = "01"; 
	/**** 广告位置开始   底部广告 ****/
	public static final String ADPOSITION2 = "02"; 


	public static final String APPINF = "应用管理-应用信息管理";

	public static final String APPVERSION = "应用管理-应用上传";

	public static final String APPAUDIT = "应用管理-应用审核";

	public static final String APPISSUE = "应用管理-应用发布";

	public static final String OFFLINEPACK = "应用管理-脱机包制作";

	public static final String ADINF = "广告管理-广告上传";

	public static final String ADAUDIT = "广告管理-广告审核";

	public static final String ADAISSUE = "广告管理-广告发布";

	public static final String TERMINFO = "终端信息管理-终端静态信息";

	public static final String TERMAUDIT = "终端信息管理-终端静态信息审核";

	public static final String INSGPARE = "终端参数管理-机构参数管理";

	public static final String INSGPAREAPPLY = "终端参数管理-机构参数申请";

	public static final String TERMPAREAPPLY = "终端参数管理-终端参数申请";

	public static final String INSGPAREVERFIFY = "终端参数管理-机构参数审核";

	public static final String TERMPAREVERIFY = "终端参数管理-终端参数审核";

	public static final String TERMPARE = "终端参数管理-终端参数管理";

	public static final String TERMAPPGROUP = "应用管理-终端应用组别";

	public static final String PASEINS = "参数管理-机构管理";

	public static final String PASMIRROR = "参数管理-镜像点管理";

	public static final String TERMFACTOR = "参数管理-终端厂商管理";

	public static final String TYPEINF = "参数管理-终端型号管理";

	public static final String PERDON = "系统管理-用户管理";

	public static final String ROSE = "系统管理-角色管理";

	public static final String MENUM = "系统管理-菜单管理";

	public static final String FUNCTION = "系统管理-功能管理";

	public static final String CODE = "系统管理-系统代码管理";

	public static final String CODETYPE = "系统管理-系统代码管理-代码类型";

	public static final String LOG = "系统管理-操作日志";

	public static final String AIDAPPLY = "IC卡参数管理-IC卡参数申请";

	public static final String AIDVERIFY = "IC卡参数管理-IC卡参数审核";

	public static final String PKPARAMAPPLY = "公钥参数管理-公钥参数申请";

	public static final String PKPARAMVERIFY = "公钥参数管理-公钥参数审核";

	public static final String MANUAL = "金融信息发布-理财购买说明书上传";

	public static final String MANUALAUDIT = "金融信息发布-理财购买说明书审核";
	
	public static final String SYNCBASEINFO = "参数管理-基础数据同步";
	
	public static final String OSINF = "系统OS管理-系统OS信息管理";
	
	public static final String OSVERSION = "系统OS管理-系统OS上传";
	
	public static final String OSAUDIT = "系统OS管理-系统OS审核";
	
	public static final String OSISSUE = "系统OS管理-系统OS发布";
	
	public static final String AGENTINF = "参数管理-代理商管理";
	
	public static final String TERMTEMP = "终端信息管理-终端临时信息";


	public static final int TERMFLAG1 = 0;

	public static final int TERMFLAG2 = 1;

	public static final int TERMFLAG3 = 2;

	public static final String[] TERM_TITLES = { "待审核", "审核通过", "审核不通过" };

	/*********启用终端********/
	public static final int TERM_STATE_ENABLED = 1;  
	/*********注销********/
	public static final int TERM_STATE_CANCEL = 0;  


	/*********移动********/
	public static final String MIRRORST1 = "00"; 
	/*********联通********/
	public static final String MIRRORST2 = "01"; 
	/*********电信********/
	public static final String MIRRORST3 = "02"; 

	/*********添加********/
	public static final String OPER_ADD = "I"; 
	/*********修改********/
	public static final String OPER_UPDATE = "U"; 
	/********* 删除********/
	public static final String OPER_DELETE = "D";
	
	/********* 等待发布********/
	public static final String RELEASEST1 = "1"; 
	/********* 发布失败********/
	public static final String RELEASEST2 = "2"; 
	/********* 发布失败********/
	public static final String RELEASEST3 = "3"; 
	/********* /发布中********/
	public static final String RELEASEST4 = "4"; 
	/********* 取消发布********/
	public static final String RELEASEST5 = "5"; 
	/********* 提交发布********/
	public static final String RELEASEST6 = "6"; 
	/**
	 * 对应认证等级权限
	 */
	public static Map<String,List<String>> levelPermissions = null;
	/**
	 * 所有需认证的权限
	 */
	public static List authPermissions = null;

	public static String SET_FILE_PERMISSIONS = "chmod 777 -R ";

	public static String DATABASE_PWD_HASH = "e10adc3949ba59abbe56e057f20f883e";
	
	public static String DATABASE_PWD_KEY = "e99a18c428cb38d5f260853678922e03";
	
	public static String FLOW_BOOT_STATUS="1";
	
	public static String FLOW_NORMAL_STATUS="0";
	
	public static String FLOW_SHUNDOWN_STATUS="2";
	
}
