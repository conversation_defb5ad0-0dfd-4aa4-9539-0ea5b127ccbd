package com.centerm.cpay.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 * @version $Id: OrderWarrStatus.java, v 0.1 2016年7月14日 上午9:33:37 sup Exp $
 */
public enum OrderWarrStatus {
	INEFFECTIVE ("0", "未生效"),
	
	EFFECTIVE("1", "已生效"),
	
	EXPIRED("2", "已过期"),
    ;
	/* 编码 */
    private String code;

    /* 描述 */
    private String text;

    /**
     * 构造函数
     * @param code
     * @param text
     */
    OrderWarrStatus(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    /**
     * 根据编码返回枚举值
     * @param code
     * @return
     */
    public static OrderWarrStatus getEnums(String code) {
        for (OrderWarrStatus enums : values()) {
            if (StringUtils.equalsIgnoreCase(code, enums.getCode())) {
                return enums;
            }
        }
        return null;
    }
}
