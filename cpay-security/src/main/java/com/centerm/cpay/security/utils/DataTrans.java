package com.centerm.cpay.security.utils;

import java.io.UnsupportedEncodingException;

public class DataTrans
{

	public static byte[] str2Bcd(String asc)
	{

		// 原数据的长度
		int len = asc.length();
		int mod = len % 2;

		if (mod != 0)
		{
			asc = asc + "0";
			len = asc.length();
		}

		// 原数据
		byte bOriginalData[] = new byte[len];
		if (len >= 2)
		{
			len = len / 2;
		}

		// 将字符串数据转换成字节数据
		bOriginalData = asc.getBytes();

		// 转换后的BCD码
		byte bBCD[] = new byte[len];

		int sH, sL;

		for (int p = 0; p < asc.length() / 2; p++)
		{

			if ((bOriginalData[2 * p] >= 'a') && (bOriginalData[2 * p] <= 'f'))
			{
				sH = bOriginalData[2 * p] - 'a' + 10;
			}
			else if ((bOriginalData[2 * p] >= 'A') && (bOriginalData[2 * p] <= 'F'))
			{
				sH = bOriginalData[2 * p] - 'A' + 10;
			}
			else
			{
				sH = bOriginalData[2 * p] & 0x0f;
			}

			if ((bOriginalData[2 * p + 1] >= 'a') && (bOriginalData[2 * p + 1] <= 'f'))
			{
				sL = bOriginalData[2 * p + 1] - 'a' + 10;
			}
			else if ((bOriginalData[2 * p + 1] >= 'A') && (bOriginalData[2 * p + 1] <= 'F'))
			{
				sL = bOriginalData[2 * p + 1] - 'A' + 10;
			}
			else
			{
				sL = bOriginalData[2 * p + 1] & 0x0f;
			}

			bBCD[p] = (byte) ((sH << 4) + sL);
		}
		return bBCD;
	}

	/**
	 * @函数功能: BCD码串转化为字符串
	 * @输入参数: BCD码
	 * @输出结果: 10进制串
	 */
	public static String bcd2Str(byte[] bytes)
	{
		char temp[] = new char[bytes.length * 2], val;

		for (int i = 0; i < bytes.length; i++)
		{
			val = (char) (((bytes[i] & 0xf0) >> 4) & 0x0f);
			temp[i * 2] = (char) (val > 9 ? val + 'A' - 10 : val + '0');

			val = (char) (bytes[i] & 0x0f);
			temp[i * 2 + 1] = (char) (val > 9 ? val + 'A' - 10 : val + '0');
		}
		return new String(temp);
	}

	/**
	 * @函数功能: BCD码串转化为字符串
	 * @输入参数: 开始位置iPos iLen转换长度
	 * @输出结果: 字符串
	 */
	public static String bcd2StrA(byte[] bytes, int iPos, int iLen)
	{
		byte[] bytmp = new byte[iLen];

		System.arraycopy(bytes, iPos, bytmp, 0, iLen);
		return DataTrans.bcd2Str(bytmp);
	}

	/*
	 * 把一个byte转化为int型
	 */
	public static int byte2int(byte by)
	{
		return (int) ((by + 256) % 256);
	}

	/*
	 * 取左边开始iPos(从0开始)的多少长度得到新的byte
	 */
	public static byte[] byteleft(byte[] bysrc, int iPos, int iLen)
	{
		byte bydest[] = new byte[iLen];
		System.arraycopy(bysrc, iPos, bydest, 0, iLen);
		return bydest;
	}

	/*
	 * 在byte iPos后面增加str 返回增加的str长度
	 */
	public static int byteaddstr(byte[] bybuf, int iPos, String str)
	{
		byte[] bystr = null;
		try
		{
			bystr = str.getBytes("gbk");
		}
		catch (UnsupportedEncodingException e)
		{
			// TODO
			e.printStackTrace();
		}
		int iLen = bystr.length;
		if (iLen > 0)
			System.arraycopy(bystr, 0, bybuf, iPos, iLen);
		return iLen;
	}

	/**
	 * 数组转换成十六进制字符串
	 * 
	 * @param byte[]
	 * @return HexString
	 */
	public static final String bytesToHexString(byte[] bArray)
	{
		StringBuffer sb = new StringBuffer(bArray.length);
		String sTemp;
		for (int i = 0; i < bArray.length; i++)
		{
			sTemp = Integer.toHexString(0xFF & bArray[i]);
			if (sTemp.length() < 2)
				sb.append(0);
			sb.append(sTemp.toUpperCase());
		}
		return sb.toString();
	}

	/**
	 * 十六进制字符串转换字符串
	 * 
	 * @param HexString
	 * @return String
	 */
	public static String hextoString(String s)
	{
		byte[] baKeyword = new byte[s.length() / 2];
		for (int i = 0; i < baKeyword.length; i++)
		{
			try
			{
				baKeyword[i] = (byte) (0xff & Integer.parseInt(s.substring(i * 2, i * 2 + 2), 16));
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		try
		{
			s = new String(baKeyword, "utf-8");// UTF-16le:Not
		}
		catch (Exception e1)
		{
			e1.printStackTrace();
		}
		return s;
	}

	public static byte[] hexStringToByte(String hex)
	{
		hex = hex.toUpperCase();
		int len = (hex.length() / 2);
		byte[] result = new byte[len];
		char[] achar = hex.toCharArray();
		for (int i = 0; i < len; i++)
		{
			int pos = i * 2;
			result[i] = (byte) (toByte(achar[pos]) << 4 | toByte(achar[pos + 1]));
		}
		return result;
	}

	private static byte toByte(char c)
	{
		byte b = (byte) "0123456789ABCDEF".indexOf(c);
		return b;
	}

	/**
	 * 异或运算
	 * 
	 * @param xor1
	 *            操作数1
	 * @param xor2
	 *            操作数2
	 * @return byte[] 异或结果(16进制数字符串)
	 * @throws Exception
	 */
	public static byte[] xor(byte[] hexSource1, byte[] hexSource2) throws Exception
	{
		int length = hexSource1.length;
		byte[] xor = new byte[length];
		for (int i = 0; i < length; i++)
		{
			xor[i] = (byte) (hexSource1[i] ^ hexSource2[i]);
		}
		return xor;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args)
	{

	}
}
