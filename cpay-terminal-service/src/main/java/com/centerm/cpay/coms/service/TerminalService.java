
package com.centerm.cpay.coms.service;

import java.io.File;
import java.util.List;
import java.util.Map;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.*;

public interface TerminalService {
	EUDataGridResult getTermInfList(Terminal termInf, int page, int rows);

	List<Terminal> getTermInfAll(Terminal terminal);

	Terminal selectByPrimaryKey(Integer id);

	ResultMsg termExcelAdd(File file, String fileFileName);
	
	Terminal selectByTermSeq(String termSeq);
	
	Terminal selectByImei(String imei);
	
	Terminal selectByImsi(String imsi);

	EUDataGridResult selectTerminalByNoGroupId(Terminal terminal, Integer page, Integer rows);

	EUDataGridResult selectTerminalByGroupId(Terminal terminal, Integer page, Integer rows);

	EUDataGridResult selectApp(TerminalApp terminalApp, Integer page, Integer rows);

	TermSysDetail sysDetail(Integer id);

	void addLog(Map map);

	EUDataGridResult basicApp(TerminalApp terminalApp, Integer page, Integer rows);

	TerminalCountInfo getTerminalCount(Integer insId);

	public Terminal queryTerminalInfoByTermSeq(String termSeq) ;
	
	public int insertTerminalInfo(Terminal terminalInfo) ;
	
	public int updateTerminalInfo(Terminal terminalInfo);
	public int updateTermGroupId(Terminal terminalInfo);

	ResultMsg update(Terminal terminal);
	
	List<Terminal> queryTermBelong(Map map);

	int saveImportTerminal(Terminal terminal);

	ResultMsg testAuto(Terminal terminal,Integer userId);

	ResultMsg delTestAuto(Integer id,Integer userId);

	TerminalPosition getTerminalCurrentPosition(Integer id);

	TerminalPosition getCheckTerminalPosition(String termSeq);

	List<Terminal> selectTermList(Terminal terminal);
	List<Terminal> selectTermListExcel(Terminal terminal);

	TerminalRealTimeStatus getTermPosiByTermSeq(String termSeq);

	TerminalCircle getCircleByTermSeq(String termSeq);

	ResultMsg lockTerm(String termSeq);

	ResultMsg unlockTerm(String termSeq);

	ResultMsg changeMerchant(TerminalChangeMerchant entity);

	TermInfoReportCount getTermInfoReport(TermInfoReportCount termInfoReportCount);

	List<Terminal> selectTermListForUpload(Terminal terminalParam);

}