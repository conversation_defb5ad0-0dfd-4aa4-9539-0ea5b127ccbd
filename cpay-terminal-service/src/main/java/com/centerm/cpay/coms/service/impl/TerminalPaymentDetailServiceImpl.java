package com.centerm.cpay.coms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.TerminalPayMethodDetailMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalPayMethodDetail;
import com.centerm.cpay.coms.service.TerminalPaymentDetailService;

@Service
public class TerminalPaymentDetailServiceImpl implements TerminalPaymentDetailService {
	@Autowired
	private TerminalPayMethodDetailMapper mapper;
	@Override
	public ResultMsg record(String termSeq, String payCode) {
		TerminalPayMethodDetail detail = mapper.selectByTermSeq(termSeq);
		if(detail != null){
			if(!payCode.trim().equals(detail.getSendPayCode())){
				Map<String,String> result = new HashMap<String,String>();
				result.put("payCode",detail.getSendPayCode());
				detail.setChangeStatus("4");
				detail.setReceivePayCode(payCode);
				mapper.updateByPrimaryKey(detail);
				return ResultMsg.success(result);
			}else{
				detail.setReceivePayCode(payCode);
				mapper.updateByPrimaryKey(detail);
			}
		}else{
			detail = new TerminalPayMethodDetail();
			detail.setTermSeq(termSeq);
			detail.setReceivePayCode(payCode);
			detail.setChangeStatus("0");
			detail.setCreateTime(new Date());
			detail.setSendPayCode(payCode);
			detail.setMethodId(0);
			mapper.insert(detail);
		}
		return ResultMsg.success();
	}
	
	@Override
	public ResultMsg update(String termSeq, String payCode) {
		TerminalPayMethodDetail detail = mapper.selectByTermSeq(termSeq);
		if(detail != null){
			detail.setChangeStatus(payCode);
			mapper.updateByPrimaryKey(detail);
		}
		return ResultMsg.success();
	}

}
