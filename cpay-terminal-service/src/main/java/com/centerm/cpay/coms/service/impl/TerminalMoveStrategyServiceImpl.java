package com.centerm.cpay.coms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.JsonUtils;
import com.centerm.cpay.coms.dao.mapper.TerminalMoveStrategyMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalMoveStrategy;
import com.centerm.cpay.coms.dao.pojo.TerminalMoveStrategyJson;
import com.centerm.cpay.coms.service.TerminalMoveStrategyService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class TerminalMoveStrategyServiceImpl implements TerminalMoveStrategyService{

	@Autowired
	private TerminalMoveStrategyMapper terminalMoveStrategyMapper;
	
	@Override
	public EUDataGridResult getTerminalParamList(TerminalMoveStrategy TerminalMoveStrategy, Integer page,
			Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		
		List<TerminalMoveStrategy>list = terminalMoveStrategyMapper.selectByCondition(TerminalMoveStrategy);
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<TerminalMoveStrategy> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
		
	}

	@Override
	public TerminalMoveStrategy selectByPrimaryKey(Integer id) {
		TerminalMoveStrategy terminalMoveStrategy = terminalMoveStrategyMapper.selectByPrimaryKey(id);
		String pageContent = terminalMoveStrategy.getParaContent();
		if(CtUtils.isEmpty(pageContent)){
			return null;
		}else{
			TerminalMoveStrategyJson json = JsonUtils.jsonToPojo(pageContent,TerminalMoveStrategyJson.class);
			terminalMoveStrategy.setRadius(json.getRadius());
			terminalMoveStrategy.setMonitorType(json.getMonitorType());
			terminalMoveStrategy.setReturnUnlock(json.getReturnUnlock());
			terminalMoveStrategy.setMoveLock(json.getMoveLock());
		}
		return terminalMoveStrategy;
	}

	@Override
	public ResultMsg insert(TerminalMoveStrategy terminalMoveStrategy) {
		List<TerminalMoveStrategy> list = terminalMoveStrategyMapper.queryParamExistAdd(terminalMoveStrategy);
		if(!list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"当前这套参数已存在");
		}else{
			TerminalMoveStrategyJson json = new TerminalMoveStrategyJson();
			json.setRadius(terminalMoveStrategy.getRadius() == null ?"":terminalMoveStrategy.getRadius());
			json.setMonitorType(terminalMoveStrategy.getMonitorType() == null ? "":terminalMoveStrategy.getMonitorType());
			json.setReturnUnlock(terminalMoveStrategy.getReturnUnlock() == null ?"": terminalMoveStrategy.getReturnUnlock());
			json.setMoveLock(terminalMoveStrategy.getMoveLock() == null ? "":terminalMoveStrategy.getMoveLock());
			String paraContent = JsonUtils.objectToJson(json);
			terminalMoveStrategy.setParaContent(paraContent);
			String paraVersion = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(System.currentTimeMillis()));
			terminalMoveStrategy.setParaVersion(paraVersion);
			terminalMoveStrategyMapper.insert(terminalMoveStrategy);
			return ResultMsg.success();
		}
	}

	@Override
	public ResultMsg updateByPrimaryKey(TerminalMoveStrategy terminalMoveStrategy) {
		List<TerminalMoveStrategy> list = terminalMoveStrategyMapper.queryParamExistEdit(terminalMoveStrategy);
		if(!list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"当前这套参数已存在");
		}else{
			TerminalMoveStrategyJson json = new TerminalMoveStrategyJson();
			json.setRadius(terminalMoveStrategy.getRadius() == null ?"":terminalMoveStrategy.getRadius());
			json.setMonitorType(terminalMoveStrategy.getMonitorType() == null ? "":terminalMoveStrategy.getMonitorType());
			json.setReturnUnlock(terminalMoveStrategy.getReturnUnlock() == null ?"": terminalMoveStrategy.getReturnUnlock());
			json.setMoveLock(terminalMoveStrategy.getMoveLock() == null ? "":terminalMoveStrategy.getMoveLock());
			String paraContent = JsonUtils.objectToJson(json);
			terminalMoveStrategy.setParaContent(paraContent);
			String paraVersion = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(System.currentTimeMillis()));
			terminalMoveStrategy.setParaVersion(paraVersion);
			terminalMoveStrategyMapper.updateByPrimaryKey(terminalMoveStrategy);
			return ResultMsg.success();
		}
	}

	@Override
	public ResultMsg deleteByPrimaryKey(Integer id) {
		terminalMoveStrategyMapper.deleteByPrimaryKey(id);
		return ResultMsg.success();
	}

}
