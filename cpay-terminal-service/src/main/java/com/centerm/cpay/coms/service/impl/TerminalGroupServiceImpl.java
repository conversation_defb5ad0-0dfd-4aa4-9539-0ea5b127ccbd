package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;

import com.centerm.cpay.coms.dao.mapper.TerminalGroupMapper;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalGroup;
import com.centerm.cpay.coms.service.TerminalGroupService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class TerminalGroupServiceImpl implements TerminalGroupService {
	@Autowired
	private TerminalGroupMapper terminalGroupMapper;

	@Override
	public EUDataGridResult getTerminalGroupList(TerminalGroup terminalGroup, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);

		List<TerminalGroup> list = terminalGroupMapper.selectByCondition(terminalGroup);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<TerminalGroup> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg deleteByPrimaryKey(Integer id) {
		terminalGroupMapper.deleteByPrimaryKey(id);//删除当前组别
		return ResultMsg.success();
	}

	@Override
	public TerminalGroup selectByPrimaryKey(Integer id) {

		TerminalGroup terminalGroup = terminalGroupMapper.selectByPrimaryKey(id);
		
		return terminalGroup;
	}

	@Override
	public ResultMsg insert(TerminalGroup terminalGroup) {
		List list = terminalGroupMapper.checkNameExist(terminalGroup);
		if(list != null && !list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"组别名称已存在");
		}
		terminalGroupMapper.insert(terminalGroup);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg updateByPrimaryKey(TerminalGroup terminalGroup) {
		List list = terminalGroupMapper.checkNameExist(terminalGroup);//判断当前组别是否存在
		TerminalGroup tg = terminalGroupMapper.selectByPrimaryKey(terminalGroup.getId());//查询出当前组别的机构，如果组别机构有修改，那么删除这套组别的终端参数
		if(list != null && !list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"组别名称已存在");
		}
		terminalGroupMapper.updateByPrimaryKey(terminalGroup);
		return ResultMsg.success();

	}

	@Override
	public List<TerminalGroup> getTerminalGroupTree(TerminalGroup terminalGroup) {
		return terminalGroupMapper.getTerminalGroupTree(terminalGroup);
	}

	@Override
	public ResultMsg updateTerminal(Terminal terminal) {
		terminalGroupMapper.updateTerminal(terminal);
		return ResultMsg.success();
	}

	@Override
	public void groupJZ(Terminal terminal) {
		terminalGroupMapper.groupJZ(terminal);
	}

	@Override
	public void groupGZ(Terminal terminal) {

		terminalGroupMapper.groupGZ(terminal);
	}

	@Override
	public List<TerminalGroup> selectGroup(TerminalGroup terminalGroup) {

		return terminalGroupMapper.selectGroup(terminalGroup);
	}
}
