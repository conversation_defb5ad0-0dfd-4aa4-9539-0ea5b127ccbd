<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalSystemInfoMapper">
	<resultMap id="BaseResultMap"
		type="com.centerm.cpay.coms.dao.pojo.TerminalSystemInfo">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="term_id" property="termId" jdbcType="INTEGER" />
		<result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
		<result column="comm_para_version" property="commParaVersion"
			jdbcType="VARCHAR" />
		<result column="launcher_para_version" property="launcherParaVersion"
			jdbcType="VARCHAR" />
		<result column="os_version" property="osVersion" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="VARCHAR" />
		<result column="tms_sdk" property="tmsSDK" jdbcType="VARCHAR" />
		<result column="pay_sdk" property="paySDK" jdbcType="VARCHAR" />
		<result column="emv_ver" property="emvVer" jdbcType="VARCHAR" />
		<result column="pay_app_code" property="payAppCode" jdbcType="VARCHAR" />
		<result column="pay_app_name" property="payAppName" jdbcType="VARCHAR" />
		<result column="pay_app_version" property="payAppVersion" jdbcType="VARCHAR" />
		<result column="tms_app_version" property="tmsAppVersion" jdbcType="VARCHAR" />
		<result column="tms_app_version_outside" property="tmsAppVersionOutSide" jdbcType="VARCHAR" />
		<result column="pay_app_version_outside" property="payAppVersionOutSide" jdbcType="VARCHAR" />
		<result column="safe_mod_ver" property="safeModVer" jdbcType="VARCHAR" />
		<result column="os_ver" property="osVer" jdbcType="VARCHAR" />
		<result column="android_ver" property="androidVer" jdbcType="VARCHAR" />
		<result column="network_type" property="networkType" jdbcType="VARCHAR" />

	</resultMap>
	<resultMap id="extendResultMap"
		type="com.centerm.cpay.coms.dao.pojo.TerminalApp">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
		<result column="app_code" property="appCode" jdbcType="VARCHAR" />
		<result column="app_name" property="appName" jdbcType="VARCHAR" />
		<result column="app_version" property="appVersion" jdbcType="VARCHAR" />
		<result column="app_version_outside" property="appVersionOutSide" jdbcType="VARCHAR" />
		<result column="para_version" property="paraVersion" jdbcType="VARCHAR" />
	</resultMap>
	<resultMap id="TerminalSysDetailMap" type="com.centerm.cpay.coms.dao.pojo.TermSysDetail">
		<result column="term_id" property="termId" jdbcType="INTEGER" />
		<result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
		<result column="comm_para_version" property="commParaVersion" jdbcType="VARCHAR" />
		<result column="launcher_para_version" property="launcherParaVersion" jdbcType="VARCHAR" />
		<result column="os_version" property="osVersion" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="VARCHAR" />
		<result column="tms_sdk" property="tmsSDK" jdbcType="VARCHAR" />
		<result column="pay_sdk" property="paySDK" jdbcType="VARCHAR" />
		<result column="emv_ver" property="emvVer" jdbcType="VARCHAR" />
		<result column="pay_app_code" property="payAppCode" jdbcType="VARCHAR" />
		<result column="pay_app_name" property="payAppName" jdbcType="VARCHAR" />
		<result column="pay_app_version" property="payAppVersion" jdbcType="VARCHAR" />
		<result column="pay_app_version_outside" property="payAppVersionOutSide" jdbcType="VARCHAR" />
		<result column="safe_mod_ver" property="safeModVer" jdbcType="VARCHAR" />
		<result column="os_ver" property="osVer" jdbcType="VARCHAR" />
		<result column="android_ver" property="androidVer" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List">
		id, term_id, term_seq,tms_sdk, pay_sdk, emv_ver, pay_app_code, pay_app_name,
		pay_app_version, pay_app_version_outside,tms_app_version, tms_app_version_outside,safe_mod_ver, os_ver, android_ver, comm_para_version,
		launcher_para_version, os_version,network_type,
		update_time
	</sql>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from coms_terminal_sysdetail
		where id = #{id,jdbcType=INTEGER}
	</select>
	<select id="selectByCondition" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalSystemInfo" resultMap="BaseResultMap">
	select * from (select
		a.term_seq,tms_sdk, pay_sdk, emv_ver, pay_app_code, pay_app_name,
		pay_app_version, pay_app_version_outside,tms_app_version, tms_app_version_outside,
		safe_mod_ver, os_ver, android_ver, comm_para_version, os_version,
		case when network_type = '1' then 'Public'
		when network_type = '2' then 'Private'
		else 'Unknown' end as network_type,update_time
		from coms_terminal_sysdetail a left join coms_terminal_info b on a.term_seq = b.term_seq
		where 1=1

		<if test="termSeq != null">
			and a.term_seq LIKE '%${termSeq}%'
		</if>
		<if test="osVer != null">
			and a.os_ver LIKE '%${osVer}%'
		</if>
		<if test="androidVer != null">
			and a.android_ver LIKE '%${androidVer}%'
		</if>
		<if test="safeModVer != null">
			and a.safe_mod_ver LIKE '%${safeModVer}%'
		</if>
		<if test="paySDK != null">
			and a.pay_sdk LIKE '%${paySDK}%'
		</if>
		<if test="tmsSDK != null">
			and a.tms_sdk LIKE '%${tmsSDK}%'
		</if>
		<if test="emvVer != null">
			and a.emv_ver LIKE '%${emvVer}%'
		</if>
		<if test="payAppCode != null">
			and a.pay_app_code LIKE '%${payAppCode}%'
		</if>
		<if test="payAppName != null">
			and a.pay_app_name LIKE '%${payAppName}%'
		</if>
		<if test="payAppVersion != null">
			and a.pay_app_version LIKE '%${payAppVersion}%'
		</if>
		<if test="tmsAppVersion != null">
			and a.tms_app_version LIKE '%${tmsAppVersion}%'
		</if>
		<if test="payAppVersionOutSide != null">
			and a.pay_app_version_outside LIKE '%${payAppVersionOutSide}%'
		</if>
		<if test="payAppVersionOutSide != null">
			and a.pay_app_version_outside LIKE '%${payAppVersionOutSide}%'
		</if>
		<if test="insId != null and insId != 0">
			and b.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
		order by a.term_seq asc) t
		where 1=1
		<if test="networkType != null">
			and network_type LIKE '%${networkType}%'
		</if>

	</select>
	<select id="selectTerminalAppInfoList" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" resultMap="extendResultMap">
	select
    a.*
    from coms_terminal_app_relation a
    left join coms_terminal_info b on a.term_seq = b.term_seq
    where 1=1
    <if test="termSeq != null">
			and a.term_seq LIKE '%${termSeq}%'
		</if>
		<if test="appCode != null">
			and a.app_code LIKE '%${appCode}%'
		</if>
		<if test="appName != null">
			and a.app_name LIKE '%${appName}%'
		</if>
		<if test="appVersion != null">
			and a.app_version LIKE '%${appVersion}%'
		</if>
		<if test="appVersionOutSide != null">
			and a.app_version_outside LIKE '%${appVersionOutSide}%'
		</if>
		<if test="insId != null and insId != 0">
			and b.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
		order by a.term_seq asc
	</select>
</mapper>