<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalBootMapper">
	<resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="term_seq" property="termSeqId" jdbcType="CHAR" />
		<result column="last_close_time" property="lastCloseTime"
			jdbcType="VARCHAR" />
		<result column="last_open_time" property="lastOpenTime"
			jdbcType="VARCHAR" />
	</resultMap>

	<sql id="INS_LIST">
		(SELECT id from cpay_institution
		where detail like
		CONCAT(concat('%',(SELECT detail from cpay_institution where
		id=#{insId})),'%'))
	</sql>

	<sql id="Base_Column_List">
		id, term_seq, last_close_time, last_open_time
	</sql>

	<sql id="Base_Acolumn_List">
		a.id, a.term_seq, a.last_close_time, a.last_open_time
	</sql>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from coms_terminal_boot_info
		where id = #{id,jdbcType=INTEGER}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from
		coms_terminal_boot_info
		where id = #{id,jdbcType=INTEGER}
	</delete>

	<select id="selectBySample" resultMap="BaseResultMap"
		parameterType="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		select
		<include refid="Base_Acolumn_List" />
		from coms_terminal_boot_info a where 1=1
		<!-- INNER JOIN coms_terminal_info b on a.term_seq = b.term_seq where b.ins_id 
			in <include refid="INS_LIST" /> -->
		<if test="termSeqId != null">
			and a.term_seq LIKE concat (concat('%',#{termSeqId}),'%')
		</if>
		<if test="closeStartTime != null and closeStartTime != '' ">
	<![CDATA[
        and 
        last_close_time >=closeStartTime  
      ]]>
		</if>
		<if test="closeEndTime != null and closeEndTime != '' ">
	<![CDATA[
		 and 
    	last_close_time <= closeEndTime
      ]]>
		</if>
		<if test="openStartTime != null and openStartTime != '' ">
	<![CDATA[
          and 
         last_open_time >= openStartTime
      ]]>
		</if>
		<if test="openEndTime != null and openEndTime!= '' ">
	<![CDATA[
       and 
    	last_open_time<= openEndTime
      ]]>
		</if>
		 order by last_open_time desc,last_close_time desc
	</select>

	<insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		insert into
		coms_terminal_boot_info (id, term_seq_id, last_close_time,
		last_open_time)
		values (
		#{termSeqId,jdbcType=CHAR},
		#{lastCloseTime,jdbcType=VARCHAR},
		#{lastOpenTime,jdbcType=VARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		insert into coms_terminal_boot_info
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="termSeqId != null">
				term_seq_id,
			</if>
			<if test="lastCloseTime != null">
				last_close_time,
			</if>
			<if test="lastOpenTime != null">
				last_open_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=INTEGER},
			</if>
			<if test="termSeqId != null">
				#{termSeqId,jdbcType=CHAR},
			</if>
			<if test="lastCloseTime != null">
				#{lastCloseTime,jdbcType=VARCHAR},
			</if>
			<if test="lastOpenTime != null">
				#{lastOpenTime,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		update coms_terminal_boot_info
		<set>
			<if test="termSeqId != null">
				term_seq_id = #{termSeqId,jdbcType=CHAR},
			</if>
			<if test="lastCloseTime != null">
				last_close_time = #{lastCloseTime,jdbcType=VARCHAR},
			</if>
			<if test="lastOpenTime != null">
				last_open_time = #{lastOpenTime,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalBoot">
		update
		coms_terminal_boot_info
		set term_seq_id = #{termSeqId,jdbcType=CHAR},
		last_close_time = #{lastCloseTime,jdbcType=VARCHAR},
		last_open_time =
		#{lastOpenTime,jdbcType=VARCHAR}
		where id = #{id,jdbcType=INTEGER}
	</update>
	<delete id="clearSelectSwitchrecord" parameterType="java.util.List">
  	delete from coms_terminal_boot_info where id in 
  		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
  </delete>
  <delete id="clearAllSwitchrecord" parameterType="java.util.List">
  	delete from coms_terminal_boot_info where term_seq = #{termSeq,jdbcType=VARCHAR}
  </delete>
</mapper>