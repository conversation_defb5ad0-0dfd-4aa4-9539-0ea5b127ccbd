package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.coms.dao.pojo.TerminalApp;
import com.centerm.cpay.coms.dao.pojo.TerminalSystemInfo;

public interface TerminalSystemInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalSystemInfo record);

    int insertSelective(TerminalSystemInfo record);

    TerminalSystemInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalSystemInfo record);

    int updateByPrimaryKey(TerminalSystemInfo record);

	List<TerminalSystemInfo> selectByCondition(TerminalSystemInfo terminalSystemInfo);

	List<TerminalApp> selectTerminalAppInfoList(TerminalApp terminalApp);
}