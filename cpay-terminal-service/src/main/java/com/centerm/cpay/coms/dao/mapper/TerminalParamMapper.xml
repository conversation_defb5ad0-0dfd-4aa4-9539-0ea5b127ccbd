<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalParamMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="para_content" property="paraContent" jdbcType="VARCHAR" />
    <result column="para_version" property="paraVersion" jdbcType="CHAR" />
    <result column="terminal_group_id" property="terminalGroupId" jdbcType="INTEGER" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
    <result column="ins_code" property="insCode" jdbcType="VARCHAR" />
    <result column="terminal_group_name" property="terminalGroupName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    a.id, 
    a.ins_id,
    a.para_content, 
    a.para_version, 
    a.terminal_group_id,
    b.name as ins_name,
    b.code as ins_code,
    c.name as terminal_group_name 
  </sql>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    select
    <include refid="Base_Column_List" />
        from cpay_institution_param a 
    	left join cpay_institution b on a.ins_id = b.id
    	left join coms_terminal_group c on a.terminal_group_id = c.id
    	where 1=1  
    	<if test="insName != null">
			and b.name LIKE concat (concat('%',#{insName}),'%')
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from cpay_institution_param a 
    left join cpay_institution b on a.ins_id = b.id
    left join coms_terminal_group c on a.terminal_group_id = c.id
    where a.id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from cpay_institution_param
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    insert into cpay_institution_param ( ins_id, para_content, 
      para_version, terminal_group_id)
    values ( #{insId,jdbcType=INTEGER}, #{paraContent,jdbcType=VARCHAR}, 
      #{paraVersion,jdbcType=VARCHAR}, #{terminalGroupId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    insert into cpay_institution_param
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="insId != null" >
        ins_id,
      </if>
      <if test="paraContent != null" >
        para_content,
      </if>
      <if test="paraVersion != null" >
        para_version,
      </if>
      <if test="terminalGroupId != null" >
        terminal_group_id,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="insId != null" >
        #{insId,jdbcType=INTEGER},
      </if>
      <if test="paraContent != null" >
        #{paraContent,jdbcType=VARCHAR},
      </if>
      <if test="paraVersion != null" >
        #{paraVersion,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    update cpay_institution_param
    <set >
      <if test="insId != null" >
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
      <if test="paraContent != null" >
        para_content = #{paraContent,jdbcType=VARCHAR},
      </if>
      <if test="paraVersion != null" >
        para_version = #{paraVersion,jdbcType=VARCHAR},
      </if>
      <if test="terminalGroupId != null" >
        terminal_group_id = #{terminalGroupId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    update cpay_institution_param
      	set ins_id = #{insId,jdbcType=INTEGER},
      	para_content = #{paraContent,jdbcType=VARCHAR},
      	para_version = #{paraVersion,jdbcType=VARCHAR},
      	terminal_group_id = #{terminalGroupId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="queryParamExistAdd" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    select
    <include refid="Base_Column_List" />
        from cpay_institution_param a 
    	left join cpay_institution b on a.ins_id = b.id
    	left join coms_terminal_group c on a.terminal_group_id = c.id
    	where 1=1  
    	<if test="insId != null">
			and b.id=#{insId,jdbcType=INTEGER}
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and c.id=#{terminalGroupId,jdbcType=INTEGER}
		</if>
		<if test="terminalGroupId == 0">
			and a.terminal_group_id = '0'
		</if>
  </select>
   <select id="queryParamExistEdit" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam" >
    select
    <include refid="Base_Column_List" />
        from cpay_institution_param a 
    	left join cpay_institution b on a.ins_id = b.id
    	left join coms_terminal_group c on a.terminal_group_id = c.id
    	where 1=1 and a.id !=#{id,jdbcType=INTEGER}  
    	<if test="insId != null">
			and b.id=#{insId,jdbcType=INTEGER}
		</if>
		<if test="terminalGroupId != null">
			and a.terminal_group_id=#{terminalGroupId,jdbcType=INTEGER}
		</if>
  </select>
    <!-- 传入参数借用paraVersion字段存储termSeq -->
    <select id="selectByInstIdAndGroupId" resultMap="BaseResultMap"
            parameterType="com.centerm.cpay.coms.dao.pojo.TerminalParam">
        select
            id, ins_id, para_content, para_version, terminal_group_id
        from cpay_institution_param
        where ins_id = #{insId,jdbcType=INTEGER}
        and (terminal_group_id = 0 or terminal_group_id=(select term_group_id from coms_terminal_info where
        term_seq=#{paraVersion,jdbcType=VARCHAR}))
        order by terminal_group_id desc limit 1
    </select>

    <select id="selectByCond" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
            id, ins_id, para_content, para_version, terminal_group_id
        from
        cpay_institution_param
        where
        ins_id in (
        SELECT
        s1.id
        from
        cpay_institution s1
        where
        instr((SELECT s2.detail from cpay_institution s2 where  s2.id = #{insId,jdbcType=INTEGER}),s1.code)>0)
        and terminal_group_id = 0
        order by
        ins_id desc
    </select>
</mapper>