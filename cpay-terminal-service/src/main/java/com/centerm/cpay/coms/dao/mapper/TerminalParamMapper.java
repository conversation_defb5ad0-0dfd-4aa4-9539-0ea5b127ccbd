package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.coms.dao.pojo.TerminalParam;

public interface TerminalParamMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalParam terminalParam);

    int insertSelective(TerminalParam terminalParam);

    TerminalParam selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalParam terminalParam);

    int updateByPrimaryKey(TerminalParam terminalParam);

	List<TerminalParam> selectByCondition(TerminalParam terminalParam);

	List<TerminalParam> queryParamExistAdd(TerminalParam terminalParam);

	List<TerminalParam> queryParamExistEdit(TerminalParam terminalParam);

    TerminalParam selectByInstIdAndGroupId(TerminalParam institutionParam);

    List<TerminalParam> selectByCond(Integer insId);
}