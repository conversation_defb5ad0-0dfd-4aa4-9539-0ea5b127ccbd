package com.centerm.cpay.coms.dao.pojo;

import java.util.Date;

public class TerminalOperationJob {
    private Integer id;
    //任务名称
    private String jobName;
    //操作指令
    private String operateType;
    //厂商id
    private Integer manufacturerId;
    //型号code
    private String termTypeCode;

    private String factoryName;

    private String termTypeName;
    //发布类型
    private String releaseType;
    //发布机构
    private Integer releaseIns;
    //用户id
    private Integer userId;
    //创建时间
    private Date createTime;

    private String insName;
    //用户机构id
    private Integer insId;
    //组别更新方式
    private String isGroupUpdate;
    //组别id
    private String groupIds;
    //终端序列号集合
    private String termSeqs;
    //指令状态
    private String jobStatus;

    private String content;

    private String appPakage;

    private String appActivity;

    private String appPwd;

    public String getTermTypeCode() {
        return termTypeCode;
    }

    public void setTermTypeCode(String termTypeCode) {
        this.termTypeCode = termTypeCode;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getTermSeqs() {
        return termSeqs;
    }

    public void setTermSeqs(String termSeqs) {
        this.termSeqs = termSeqs;
    }

    public String getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(String groupIds) {
        this.groupIds = groupIds;
    }
    public String getIsGroupUpdate() {
        return isGroupUpdate;
    }

    public void setIsGroupUpdate(String isGroupUpdate) {
        this.isGroupUpdate = isGroupUpdate;
    }
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName == null ? null : jobName.trim();
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType == null ? null : operateType.trim();
    }

    public Integer getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(Integer manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getReleaseType() {
        return releaseType;
    }

    public void setReleaseType(String releaseType) {
        this.releaseType = releaseType == null ? null : releaseType.trim();
    }

    public Integer getReleaseIns() {
        return releaseIns;
    }

    public void setReleaseIns(Integer releaseIns) {
        this.releaseIns = releaseIns;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public String getTermTypeName() {
		return termTypeName;
	}

	public void setTermTypeName(String termTypeName) {
		this.termTypeName = termTypeName;
	}

	public Integer getInsId() {
		return insId;
	}

	public void setInsId(Integer insId) {
		this.insId = insId;
	}

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAppPakage() {
        return appPakage;
    }

    public void setAppPakage(String appPakage) {
        this.appPakage = appPakage;
    }

    public String getAppActivity() {
        return appActivity;
    }

    public void setAppActivity(String appActivity) {
        this.appActivity = appActivity;
    }

    public String getAppPwd() {
        return appPwd;
    }

    public void setAppPwd(String appPwd) {
        this.appPwd = appPwd;
    }
}