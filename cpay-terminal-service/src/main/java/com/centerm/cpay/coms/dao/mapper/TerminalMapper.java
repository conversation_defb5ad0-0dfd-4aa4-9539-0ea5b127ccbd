package com.centerm.cpay.coms.dao.mapper;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.coms.dao.pojo.*;

public interface TerminalMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Terminal terminal);

    int insertSelective(Terminal terminal);

    int updateTermGroupId(Terminal terminal);
    int updateByPrimaryKeySelective(Terminal terminal);

    int updateByPrimaryKey(Terminal terminal);
    
	List<Terminal> selectByCondition(Terminal terminal);
	List<Terminal> selectByConditionExcel(Terminal terminal);

	Terminal selectByPrimaryKey(Integer id);
	
	List<TerminalCount> getCount(Integer insId);

	List<TerminalCount> getActCount(Integer insId);
	
	Terminal selectByTermSeq(String termSeq);
	
	Terminal selectByImei(String imei);
	
	Terminal selectByImsi(String imsi);

	List<Terminal> selectTerminalByNoGroupId(Terminal terminal);

	List<Terminal> selectTerminalByGroupId(Terminal terminal);


	void addLog(Map map);

	void updateTerminal(Terminal terminal);

	List<Terminal> queryTermBelong(Map map);

	String queryTermOrgAuto(Integer id);

	int queryTermOrgMerExist(Integer id);

	int selectByMerchantId(Integer id);

	void testAuto(Terminal terminal);

	Integer getDefMerchant(Integer id);

	void insertTestAutoRel(Map map);

	Terminal queryTerminalExist(String termSeq);

	String queryTerminalAsycn(String string);

	String queryMchntCd(String string);

	String queryTermId(String string);

	void cleanTerm(String termSeq);

	Integer getFactoryId(Integer id);

	List<Terminal> queryTerminalCollection(Terminal terminal);

	Terminal getTerminalCurrentPosition(Integer id);

	List<Terminal> selectByTerminalAdvQueryOne(TerminalAdvQuery terminalAdvQuery);

	List<Terminal> selectByTerminalAdvQueryTwo(TerminalAdvQuery terminalAdvQuery);

	List<Terminal> selectByTerminalAdvQueryThree(TerminalAdvQuery terminalAdvQuery);

	List<Terminal> selectByTerminalAdvQueryFour(TerminalAdvQuery terminalAdvQuery);

	String queryTermSeqByImsi(String imsi);

	List<Terminal> selectByInsId(Integer insId);

	String queryIsStart();

	List<TermInfoReport> getTermInfoReport(TermInfoReportCount reportCount);

	List<Terminal> selectTermListForUpload(Terminal terminalParam);

	List<Terminal> getAllTerminalDyfInfo(Terminal terminal);
}