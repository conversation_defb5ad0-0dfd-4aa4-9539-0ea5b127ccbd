package com.centerm.cpay.coms.dao.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TerminalCertExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TerminalCertExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNull() {
            addCriterion("upload_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNotNull() {
            addCriterion("upload_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdEqualTo(Integer value) {
            addCriterion("upload_user_id =", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotEqualTo(Integer value) {
            addCriterion("upload_user_id <>", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThan(Integer value) {
            addCriterion("upload_user_id >", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("upload_user_id >=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThan(Integer value) {
            addCriterion("upload_user_id <", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("upload_user_id <=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIn(List<Integer> values) {
            addCriterion("upload_user_id in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotIn(List<Integer> values) {
            addCriterion("upload_user_id not in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdBetween(Integer value1, Integer value2) {
            addCriterion("upload_user_id between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("upload_user_id not between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameIsNull() {
            addCriterion("upload_user_name is null");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameIsNotNull() {
            addCriterion("upload_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameEqualTo(String value) {
            addCriterion("upload_user_name =", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameNotEqualTo(String value) {
            addCriterion("upload_user_name <>", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameGreaterThan(String value) {
            addCriterion("upload_user_name >", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("upload_user_name >=", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameLessThan(String value) {
            addCriterion("upload_user_name <", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameLessThanOrEqualTo(String value) {
            addCriterion("upload_user_name <=", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameLike(String value) {
            addCriterion("upload_user_name like", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameNotLike(String value) {
            addCriterion("upload_user_name not like", value, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameIn(List<String> values) {
            addCriterion("upload_user_name in", values, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameNotIn(List<String> values) {
            addCriterion("upload_user_name not in", values, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameBetween(String value1, String value2) {
            addCriterion("upload_user_name between", value1, value2, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andUploadUserNameNotBetween(String value1, String value2) {
            addCriterion("upload_user_name not between", value1, value2, "uploadUserName");
            return (Criteria) this;
        }

        public Criteria andInsIdIsNull() {
            addCriterion("ins_id is null");
            return (Criteria) this;
        }

        public Criteria andInsIdIsNotNull() {
            addCriterion("ins_id is not null");
            return (Criteria) this;
        }

        public Criteria andInsIdEqualTo(Integer value) {
            addCriterion("ins_id =", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdNotEqualTo(Integer value) {
            addCriterion("ins_id <>", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdGreaterThan(Integer value) {
            addCriterion("ins_id >", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ins_id >=", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdLessThan(Integer value) {
            addCriterion("ins_id <", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdLessThanOrEqualTo(Integer value) {
            addCriterion("ins_id <=", value, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdIn(List<Integer> values) {
            addCriterion("ins_id in", values, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdNotIn(List<Integer> values) {
            addCriterion("ins_id not in", values, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdBetween(Integer value1, Integer value2) {
            addCriterion("ins_id between", value1, value2, "insId");
            return (Criteria) this;
        }

        public Criteria andInsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ins_id not between", value1, value2, "insId");
            return (Criteria) this;
        }

        public Criteria andCertSavePathIsNull() {
            addCriterion("cert_save_path is null");
            return (Criteria) this;
        }

        public Criteria andCertSavePathIsNotNull() {
            addCriterion("cert_save_path is not null");
            return (Criteria) this;
        }

        public Criteria andCertSavePathEqualTo(String value) {
            addCriterion("cert_save_path =", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathNotEqualTo(String value) {
            addCriterion("cert_save_path <>", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathGreaterThan(String value) {
            addCriterion("cert_save_path >", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathGreaterThanOrEqualTo(String value) {
            addCriterion("cert_save_path >=", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathLessThan(String value) {
            addCriterion("cert_save_path <", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathLessThanOrEqualTo(String value) {
            addCriterion("cert_save_path <=", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathLike(String value) {
            addCriterion("cert_save_path like", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathNotLike(String value) {
            addCriterion("cert_save_path not like", value, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathIn(List<String> values) {
            addCriterion("cert_save_path in", values, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathNotIn(List<String> values) {
            addCriterion("cert_save_path not in", values, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathBetween(String value1, String value2) {
            addCriterion("cert_save_path between", value1, value2, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertSavePathNotBetween(String value1, String value2) {
            addCriterion("cert_save_path not between", value1, value2, "certSavePath");
            return (Criteria) this;
        }

        public Criteria andCertStateIsNull() {
            addCriterion("cert_state is null");
            return (Criteria) this;
        }

        public Criteria andCertStateIsNotNull() {
            addCriterion("cert_state is not null");
            return (Criteria) this;
        }

        public Criteria andCertStateEqualTo(String value) {
            addCriterion("cert_state =", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateNotEqualTo(String value) {
            addCriterion("cert_state <>", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateGreaterThan(String value) {
            addCriterion("cert_state >", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateGreaterThanOrEqualTo(String value) {
            addCriterion("cert_state >=", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateLessThan(String value) {
            addCriterion("cert_state <", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateLessThanOrEqualTo(String value) {
            addCriterion("cert_state <=", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateLike(String value) {
            addCriterion("cert_state like", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateNotLike(String value) {
            addCriterion("cert_state not like", value, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateIn(List<String> values) {
            addCriterion("cert_state in", values, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateNotIn(List<String> values) {
            addCriterion("cert_state not in", values, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateBetween(String value1, String value2) {
            addCriterion("cert_state between", value1, value2, "certState");
            return (Criteria) this;
        }

        public Criteria andCertStateNotBetween(String value1, String value2) {
            addCriterion("cert_state not between", value1, value2, "certState");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateIsNull() {
            addCriterion("validate_endDate is null");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateIsNotNull() {
            addCriterion("validate_endDate is not null");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateEqualTo(Date value) {
            addCriterion("validate_endDate =", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateNotEqualTo(Date value) {
            addCriterion("validate_endDate <>", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateGreaterThan(Date value) {
            addCriterion("validate_endDate >", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateGreaterThanOrEqualTo(Date value) {
            addCriterion("validate_endDate >=", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateLessThan(Date value) {
            addCriterion("validate_endDate <", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateLessThanOrEqualTo(Date value) {
            addCriterion("validate_endDate <=", value, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateIn(List<Date> values) {
            addCriterion("validate_endDate in", values, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateNotIn(List<Date> values) {
            addCriterion("validate_endDate not in", values, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateBetween(Date value1, Date value2) {
            addCriterion("validate_endDate between", value1, value2, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andValidateEnddateNotBetween(Date value1, Date value2) {
            addCriterion("validate_endDate not between", value1, value2, "validateEnddate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}