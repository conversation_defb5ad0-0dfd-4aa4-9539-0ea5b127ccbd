package com.centerm.cpay.coms.dao.pojo;

public class TerminalAlarmInfo {
    private Integer id;

    private String termSeq;

    private String hardwareStatBmp;

    private String expInfoList;

    private String recordUploadTime;

    private String baseInfo;

    private String ipPort;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTermSeq() {
        return termSeq;
    }

    public void setTermSeq(String termSeq) {
        this.termSeq = termSeq == null ? null : termSeq.trim();
    }

    public String getHardwareStatBmp() {
        return hardwareStatBmp;
    }

    public void setHardwareStatBmp(String hardwareStatBmp) {
        this.hardwareStatBmp = hardwareStatBmp == null ? null : hardwareStatBmp.trim();
    }

    public String getExpInfoList() {
        return expInfoList;
    }

    public void setExpInfoList(String expInfoList) {
        this.expInfoList = expInfoList == null ? null : expInfoList.trim();
    }

    public String getRecordUploadTime() {
        return recordUploadTime;
    }

    public void setRecordUploadTime(String recordUploadTime) {
        this.recordUploadTime = recordUploadTime == null ? null : recordUploadTime.trim();
    }

    public String getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(String baseInfo) {
        this.baseInfo = baseInfo == null ? null : baseInfo.trim();
    }

    public String getIpPort() {
        return ipPort;
    }

    public void setIpPort(String ipPort) {
        this.ipPort = ipPort == null ? null : ipPort.trim();
    }
}