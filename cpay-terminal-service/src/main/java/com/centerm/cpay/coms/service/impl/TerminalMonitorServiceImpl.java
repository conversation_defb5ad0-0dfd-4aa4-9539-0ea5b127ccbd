
package com.centerm.cpay.coms.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.enums.BitMapElementStatus;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.mapper.TerminalMonitorMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalRealTimeStatusMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalMapInfo;
import com.centerm.cpay.coms.dao.pojo.TerminalMonitor;
import com.centerm.cpay.coms.dao.pojo.TerminalRealTimeStatus;
import com.centerm.cpay.coms.service.TerminalMonitorService;
import com.github.pagehelper.PageHelper;

/**
 * 
 * <AUTHOR>
 * @version $Id: TerminalMonitorServiceImpl.java, v 0.1 2016年7月16日 下午3:22:08 sup
 *          Exp $
 */
@Service
public class TerminalMonitorServiceImpl implements TerminalMonitorService {
	
	@Autowired
	private TerminalRealTimeStatusMapper terminalRealTimeStatusMapper;

	@Override
	public ResultMsg getAllTerminalPosition(TerminalRealTimeStatus terminalRealTimeStatus) {
		
		List<TerminalRealTimeStatus> list = terminalRealTimeStatusMapper.queryTerminalPosition(terminalRealTimeStatus.getTermSeq());		
		return ResultMsg.success(list);
	}
	
	@Override
	public ResultMsg getMoveTerminalCount(String instId){
		return ResultMsg.success(terminalRealTimeStatusMapper.getMoveTerminalCountByInsId(instId));
	}
}
