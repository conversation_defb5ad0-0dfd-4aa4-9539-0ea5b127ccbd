
package com.centerm.cpay.coms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.event.ListSelectionEvent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CommUtil;
import com.centerm.cpay.common.utils.PropertyUtil;
import com.centerm.cpay.coms.dao.mapper.TerminalPositionMapper;
import com.centerm.cpay.coms.dao.pojo.MapData;
import com.centerm.cpay.coms.dao.pojo.TerminalPosition;
import com.centerm.cpay.coms.service.TerminalPositionService;

/**
 * 
 * <AUTHOR>
 * @version $Id: TerminalPositionServiceImpl.java, v 0.1 2016年7月16日 下午3:22:00
 *          sup Exp $
 */
@Service
public class TerminalPositionServiceImpl implements TerminalPositionService {
	@Autowired
	private TerminalPositionMapper terminalPositionMapper;

	@Override
	public ResultMsg getPositionList(TerminalPosition terminalPosition) {
		Map resultMap = new HashMap();
		List<List<MapData>> markLineDataList = new ArrayList<>();
		Map map = new HashMap();
		List<TerminalPosition> listTerminalPosition = terminalPositionMapper.selectBySample(terminalPosition);
		int j = 0;
		String latitude = null;
		String longitude = null;
		String[] latLon = new String[2];
		for (int i = 0; i < listTerminalPosition.size(); i++) {
			if (listTerminalPosition.get(i).getLatitude() == null
					|| listTerminalPosition.get(i).getLatitude().trim().equals("")
					|| listTerminalPosition.get(i).getLongitude() == null
					|| listTerminalPosition.get(i).getLongitude().trim().equals("")) {
			} else {
				// 高德地图坐标坐标转百度地图
				if (i == 0) {
					latitude = listTerminalPosition.get(0).getLatitude();
					longitude = listTerminalPosition.get(0).getLongitude();
					latLon = CommUtil.bd_encrypt(latitude, longitude);
					listTerminalPosition.get(0).setLongitude(latLon[1]);
					listTerminalPosition.get(0).setLatitude(latLon[0]);
				}

				latitude = listTerminalPosition.get(i).getLatitude();
				longitude = listTerminalPosition.get(i).getLongitude();
				latLon = CommUtil.bd_encrypt(latitude, longitude);
				listTerminalPosition.get(i).setLongitude(latLon[1]);
				listTerminalPosition.get(i).setLatitude(latLon[0]);

				double aToB = CommUtil.distanceLogLat(Double.parseDouble(listTerminalPosition.get(0).getLongitude()),
						Double.parseDouble(listTerminalPosition.get(0).getLatitude()),
						Double.parseDouble(listTerminalPosition.get(i).getLongitude()),
						Double.parseDouble(listTerminalPosition.get(i).getLatitude()));
				int data1 = (int) aToB;
				int data2 = Integer.parseInt(terminalPositionMapper.getSysConfByKey("task.max.distance"));
				if (data1 > data2 || i == 0) {// 移动轨迹的各个点
					j++;
					if (i != 0) {
						if (j == 2) {
							List<MapData> listMapData = new ArrayList();
							MapData mapData3 = new MapData();
							MapData mapData4 = new MapData();
							
							mapData3.setName("position" + (j - 1 - 1));
							listMapData.add(mapData3);
							
							mapData4.setName("position" + (j - 1));
							mapData4.setValue("60");
							listMapData.add(mapData4);
							
							markLineDataList.add(listMapData);
						}
						List<MapData> listMapData = new ArrayList();
						MapData mapData = new MapData();
						MapData mapData2 = new MapData();
						
						mapData.setName("position" + (j - 1));
						listMapData.add(mapData);

						mapData2.setName("position" + j);
						mapData2.setValue("60");
						listMapData.add(mapData2);

						markLineDataList.add(listMapData);
					}
					List longLatiArray = new ArrayList();
					longLatiArray.add(listTerminalPosition.get(i).getLongitude());
					longLatiArray.add(listTerminalPosition.get(i).getLatitude());
					map.put("position" + j, longLatiArray);
					resultMap.put("map", map);
					resultMap.put("markLineDataList", markLineDataList);
					// 如果距离大于配置中规定的距离，先该点设置为下一个比对点
					listTerminalPosition.get(0).setLongitude(listTerminalPosition.get(i).getLongitude());
					listTerminalPosition.get(0).setLatitude(listTerminalPosition.get(i).getLatitude());
				}
			}

		}
		return ResultMsg.success(resultMap);
	}

	@Override
	public ResultMsg getDetailPositionList(TerminalPosition terminalPosition) {
		return ResultMsg.success(terminalPositionMapper.selectPoByTermId(terminalPosition));
	}

}
