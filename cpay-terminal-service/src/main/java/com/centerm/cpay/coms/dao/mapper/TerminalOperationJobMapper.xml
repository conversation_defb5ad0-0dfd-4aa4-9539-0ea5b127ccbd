<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalOperationJobMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="job_name" property="jobName" jdbcType="VARCHAR" />
    <result column="operate_type" property="operateType" jdbcType="CHAR" />
    <result column="manufacturer_id" property="manufacturerId" jdbcType="INTEGER" />
    <result column="term_type_code" property="termTypeCode" jdbcType="VARCHAR" />
    <result column="release_type" property="releaseType" jdbcType="CHAR" />
    <result column="release_ins" property="releaseIns" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
    <result column="term_type_name" property="termTypeName" jdbcType="VARCHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="is_group_update" property="isGroupUpdate" jdbcType="CHAR" />
    <result column="group_ids" property="groupIds" jdbcType="VARCHAR" />
    <result column="TERM_SEQS" property="termSeqs" jdbcType="VARCHAR" />
    <result column="job_status" property="jobStatus" jdbcType="CHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, job_name, operate_type, manufacturer_id, term_type_code, release_type, release_ins,
    user_id, create_time,ins_id,is_group_update,group_ids,TERM_SEQS,job_status,content
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    a.id, a.job_name, operate_type, manufacturer_id, term_type_code, release_type, release_ins,
    user_id, create_time,a.ins_id,is_group_update,group_ids,TERM_SEQS,job_status
    ,e.name as ins_name,a.content
    from coms_terminal_operation_job a
    LEFT JOIN cpay_institution e ON a.ins_id = e.id
    where a.id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob">
  	 select
    a.id, a.job_name, operate_type, manufacturer_id, term_type_code, release_type, release_ins,
    user_id, create_time,a.ins_id,is_group_update,group_ids,TERM_SEQS,c.name as term_type_name,
    d.name as factory_name,e.name as ins_name, case when f.wait_send=0 then 7 ELSE a.job_status end as job_status,a.content
    from coms_terminal_operation_job a
     LEFT JOIN coms_terminal_type c ON c.code =a.term_type_code
	 LEFT JOIN coms_terminal_manufacturer d ON a.manufacturer_id = d.id
	 LEFT JOIN cpay_institution e ON a.ins_id = e.id
     LEFT jOIN (select g.job_id,
      sum(case when g.operate_status='0' or g.operate_status='2' then 1 else 0 end) as wait_send
      from coms_terminal_operation g group by g.JOB_ID) f
    on f.job_id = a.id
     where 1 = 1
  	 <if test="jobName != null">
		and a.job_name  LIKE concat (concat('%',#{jobName}),'%')
	 </if>
	 <if test="manufacturerId != null and manufacturerId != 0">
		and a.manufacturer_id = #{manufacturerId,jdbcType=INTEGER}
	</if>
	<if test="termTypeCode != null and termTypeCode != ''">
		and a.term_type_code = #{termTypeCode,jdbcType=VARCHAR}
	</if>
    <if test="jobStatus != null ">
      and a.job_status = #{jobStatus,jdbcType=CHAR}
    </if>
	<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
	</if>
    ORDER BY CREATE_TIME desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_terminal_operation_job
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob" >
    insert into coms_terminal_operation_job ( job_name, operate_type, 
      manufacturer_id, term_type_code, release_type,
      release_ins, user_id,
      create_time,insId,TERM_SEQS,JOB_STATUS,content)
    values ( #{jobName,jdbcType=VARCHAR}, #{operateType,jdbcType=CHAR}, 
      #{manufacturerId,jdbcType=INTEGER}, #{termTypeCode,jdbcType=VARCHAR}, #{releaseType,jdbcType=CHAR},
      #{releaseIns,jdbcType=INTEGER},  #{userId,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP},#{insId,jdbcType=INTEGER},#{termSeqs,jdbcType=VARCHAR},#{jobStatus,jdbcType=VARCHAR},#{content,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob" 
  useGeneratedKeys="true" keyProperty="id">
    insert into coms_terminal_operation_job
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jobName != null" >
        job_name,
      </if>
      <if test="operateType != null" >
        operate_type,
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id,
      </if>
      <if test="termTypeCode != null" >
        term_type_code,
      </if>
      <if test="releaseType != null" >
        release_type,
      </if>
      <if test="releaseIns != null" >
        release_ins,
      </if>
      <if test="groupIds != null" >
        group_ids,
      </if>
      <if test="isGroupUpdate != null" >
        is_group_update,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="insId != null" >
        ins_id,
      </if>
      <if test="termSeqs != null" >
        term_seqs,
      </if>
      <if test="jobStatus != null" >
        job_Status,
      </if>
      <if test="content != null" >
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jobName != null" >
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=CHAR},
      </if>
      <if test="manufacturerId != null" >
        #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="termTypeCode != null" >
        #{termTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="releaseType != null" >
        #{releaseType,jdbcType=CHAR},
      </if>
      <if test="releaseIns != null" >
        #{releaseIns,jdbcType=INTEGER},
      </if>
      <if test="groupIds != null" >
        #{groupIds,jdbcType=VARCHAR},
      </if>
      <if test="isGroupUpdate != null" >
        #{isGroupUpdate,jdbcType=CHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="insId != null" >
        #{insId,jdbcType=INTEGER},
      </if>
      <if test="termSeqs != null" >
        #{termSeqs,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null" >
        #{jobStatus,jdbcType=CHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob" >
    update coms_terminal_operation_job
    <set >
      <if test="jobName != null" >
        job_name = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null" >
        operate_type = #{operateType,jdbcType=CHAR},
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="termTypeCode != null" >
        term_type_code = #{termTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="releaseType != null" >
        release_type = #{releaseType,jdbcType=CHAR},
      </if>
      <if test="releaseIns != null" >
        release_ins = #{releaseIns,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="insId != null" >
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
      <if test="termSeqs != null" >
        term_seqs = #{termSeqs,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null" >
        job_status = #{jobStatus,jdbcType=CHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalOperationJob" >
    update coms_terminal_operation_job
    set job_name = #{jobName,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=CHAR},
      manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      term_type_code = #{termTypeCode,jdbcType=VARCHAR},
       group_ids = #{groupIds,jdbcType=VARCHAR},
       is_group_update =  #{isGroupUpdate,jdbcType=CHAR},
      release_type = #{releaseType,jdbcType=CHAR},
      release_ins = #{releaseIns,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      ins_id = #{insId,jdbcType=INTEGER},
      term_seqs = #{termSeqs,jdbcType=VARCHAR},
      job_status = #{jobStatus,jdbcType=CHAR},
      content = #{content,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateJobStatus" parameterType="java.util.Map" >
    update coms_terminal_operation_job
    set
      job_status = #{jobStatus,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>