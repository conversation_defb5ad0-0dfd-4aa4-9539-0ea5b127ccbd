package com.centerm.cpay.coms.service;

import java.util.List;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.TerminalParam;

public interface TerminalParamService {

	ResultMsg deleteByPrimaryKey(Integer id);

	TerminalParam selectByPrimaryKey(Integer id);

	ResultMsg insert(TerminalParam terminalParam);

	ResultMsg updateByPrimaryKey(TerminalParam terminalParam);

	EUDataGridResult getTerminalParamList(TerminalParam terminalParam, Integer page, Integer rows);

	String queryAlarmInfoByCode(List<String> codesList);

	TerminalParam getInstitutionParam(TerminalParam institutionParam);

	TerminalParam getInstitutionParamByInsList(int insId);
}