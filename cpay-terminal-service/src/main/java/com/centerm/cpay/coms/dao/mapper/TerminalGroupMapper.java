package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalGroup;

public interface TerminalGroupMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalGroup terminalGroup);

    int insertSelective(TerminalGroup terminalGroup);

    TerminalGroup selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalGroup terminalGroup);

    int updateByPrimaryKey(TerminalGroup terminalGroup);

	List<TerminalGroup> selectByCondition(TerminalGroup terminalGroup);

	List<TerminalGroup> getTerminalGroupTree(TerminalGroup terminalGroup);

	void updateTerminal(Terminal terminal);

	void groupJZ(Terminal terminal);

	void groupGZ(Terminal terminal);

	List<TerminalGroup> selectGroup(TerminalGroup terminalGroup);

	List checkNameExist(TerminalGroup terminalGroup);

	void deleteGParamById(Integer id);
}