package com.centerm.cpay.coms.service.impl;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.centerm.cpay.common.enums.OffLineStatus;
import com.centerm.cpay.common.enums.TermActivateStatus;
import com.centerm.cpay.coms.dao.mapper.*;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.service.FactoryService;
import com.centerm.cpay.coms.service.TerminalParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CipherUtil;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.HttpClientUtil;
import com.centerm.cpay.common.utils.JsonUtils;

import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.coms.service.TerminalService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import net.sf.json.JSONObject;
/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TerminalServiceImpl implements TerminalService {
	private static final Logger logger = LoggerFactory.getLogger(TerminalServiceImpl.class);
	@Autowired
	private TerminalMapper terminalMapper;
	@Autowired
	private TerminalAppMapper terminalAppMapper;
	@Autowired
	private TermSysDetailMapper termSysDetailMapper;
	@Autowired
	private OqcChcekHistoryMapper OqcChcekHistoryMapper;
	@Autowired
	private TerminalPositionMapper terminalPositionMapper;
	@Autowired
	private TerminalRealTimeStatusMapper terminalRealTimeStatusMapper;
	@Autowired
	private TerminalCircleMapper terminalCircleMapper;
	@Autowired
	private TerminalLockStateMapper terminalLockStateMapper;
	@Autowired
	private TerminalChangeMerchantMapper terminalChangeMerchantMapper;
	@Autowired
	private FactoryService factoryService;
	@Autowired
	private TerminalParamService terminalParamService;


	//【终端】在线状态控制（在线）
	private static final String TERMINAL_ONLINE = "1";

	@Override
	public EUDataGridResult getTermInfList(Terminal terminal, int page, int rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<Terminal> list = terminalMapper.selectByCondition(terminal);

		//【处理】终端在线状态控制
		list.forEach(this::getTerminalOnlineStatus);

		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<Terminal> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public List<Terminal> getTermInfAll(Terminal terminal) {
		//【查询】终端信息
		List<Terminal> list = terminalMapper.selectByCondition(terminal);

		//【处理】终端在线状态控制
		list.forEach(this::getTerminalOnlineStatus);

		//【返回】查询结果
		return list;
	}

	@Override
	public Terminal selectByPrimaryKey(Integer id) {

		return terminalMapper.selectByPrimaryKey(id);
	}

	@Override
	public ResultMsg termExcelAdd(File file, String fileFileName) {

		return ResultMsg.success();
	}

	@Override
	public Terminal selectByTermSeq(String termSeq) {
		return terminalMapper.selectByTermSeq(termSeq);
	}

	@Override
	public EUDataGridResult selectTerminalByNoGroupId(Terminal terminal, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);

		List<Terminal> list = terminalMapper.selectTerminalByNoGroupId(terminal);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<Terminal> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public EUDataGridResult selectTerminalByGroupId(Terminal terminal, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);

		List<Terminal> list = terminalMapper.selectTerminalByGroupId(terminal);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<Terminal> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public EUDataGridResult selectApp(TerminalApp terminalApp, Integer page, Integer rows) {
		EUDataGridResult result = new EUDataGridResult();
		Terminal terminal = terminalMapper.selectByPrimaryKey(terminalApp.getId());
		if(CtUtils.isEmpty(terminal)) {
			return result;
		}
		terminalApp.setTermSeq(terminal.getTermSeq());
		// 分页处理
		PageHelper.startPage(page, rows);
		List<TerminalApp> list = terminalAppMapper.selectApp(terminalApp);
		// 创建一个返回值对象
		
		result.setRows(list);
		// 取记录总条数
		PageInfo<TerminalApp> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	@Override
	public EUDataGridResult basicApp(TerminalApp terminalApp, Integer page, Integer rows) {
		Terminal terminal = terminalMapper.selectByPrimaryKey(terminalApp.getId());
		EUDataGridResult result = new EUDataGridResult();
		if(CtUtils.isEmpty(terminal)) {
			return result;
		}
		terminalApp.setTermSeq(terminal.getTermSeq());
		PageHelper.startPage(page, rows);
		List<TerminalApp> list = terminalAppMapper.basicApp(terminalApp);
		// 创建一个返回值对象
		
		result.setRows(list);
		// 取记录总条数
		PageInfo<TerminalApp> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}


	@Override
	public TermSysDetail sysDetail(Integer id) {
		TermSysDetail termSysDetail = termSysDetailMapper.selectByPrimaryKey(id);

		return termSysDetail;
	}

	@Override
	public void addLog(Map map) {
		terminalMapper.addLog(map);
	}

	@Override
	public Terminal queryTerminalInfoByTermSeq(String termSeq) {
		return terminalMapper.selectByTermSeq(termSeq);
	}

	@Override
	public Terminal selectByImei(String imei) {
		return terminalMapper.selectByImei(imei);
	}

	@Override
	public int insertTerminalInfo(Terminal terminalInfo) {
		return terminalMapper.insert(terminalInfo);
	}

	@Override
	public int updateTerminalInfo(Terminal terminalInfo) {
		return terminalMapper.updateByPrimaryKeySelective(terminalInfo);
	}
	@Override
	public int updateTermGroupId(Terminal terminalInfo) {
		return terminalMapper.updateTermGroupId(terminalInfo);
	}
	@Override
	public ResultMsg update(Terminal terminal) {
		terminalMapper.updateTerminal(terminal);
		return ResultMsg.success();
	}

	public String[] getLast12Months() {
		String[] last12Months = new String[12];
		SimpleDateFormat monthDate = new SimpleDateFormat("yyyyMM");
		Calendar cal = Calendar.getInstance();
		for (int i = 0; i < 12; i++) {
			String month_name1 = monthDate.format(cal.getTime());
			last12Months[11 - i] = month_name1;
			cal.add(Calendar.MONTH, -1);
		}
		return last12Months;
	}

	public String fillZero(String str, int len) {
		int tmp = str.length();
		int t;
		String str1 = str;
		if (tmp >= len) {
			return str1;
		}
		t = len - tmp;
		for (int i = 0; i < t; i++) {
			str1 = "0" + str1;
		}
		return str1;
	}

	@Override
	public List<Terminal> queryTermBelong(Map map) {

		return terminalMapper.queryTermBelong(map);
	}

	@Override
	public int saveImportTerminal(Terminal terminal) {

		Terminal term = terminalMapper.selectByTermSeq(terminal.getTermSeq());
		if (!CtUtils.isEmpty(term)) {
			logger.info("终端序列号为：" + terminal.getTermSeq() + "的数据已经存在，不做处理");
			return 0;
		} else {
			terminalMapper.insert(terminal);
		}
		return terminal.getId();
	}

	@Override
	public ResultMsg testAuto(Terminal terminal, Integer userId) {
		String[] termSeqArr = terminal.getTermSeq().split(",");
		OqcChcekHistory oqcChcekHistory = new OqcChcekHistory();
		if (CtUtils.isEmpty(termSeqArr)) {
			return ResultMsg.build(ResultMsg.ERROR_CODE, "终端序列号不得为空");
		}
		Integer merchantId = terminalMapper.getDefMerchant(terminal.getInsId());
		if (CtUtils.isEmpty(merchantId) || merchantId == 0) {
			return ResultMsg.build(ResultMsg.ERROR_CODE, "当前测试机构没有绑定默认的商户信息");
		}
		Boolean isRight = false;
		for (int i = 0; i < termSeqArr.length; i++) {
			if (termSeqArr[i].length() > 25 || termSeqArr[i].length() < 7) {
				isRight = true;
			}
		}
		if (isRight) {
			return ResultMsg.build(ResultMsg.ERROR_CODE, "存在单个终端序列号过长或过短,请重新输入！！！");
		} else {
			for (int j = 0; j < termSeqArr.length; j++) {
				terminal.setTermSeq(String.valueOf(termSeqArr[j]));
				terminal.setCreateTime(new Date());
				terminal.setId(null);
				Terminal terminal1 = terminalMapper.queryTerminalExist(terminal.getTermSeq());
				if (!CtUtils.isEmpty(terminal1)) {
					ResultMsg.build(ResultMsg.ERROR_CODE, "当前终端信息已经存在!!!");
					continue;
				}
				logger.info("终端商户管理信息新增成功");
				logger.info("开始进行信息同步操作");
				String terminalAsycnUrl = terminalMapper.queryTerminalAsycn("terminalAsycnUrl");
				String termId = "P" + terminal.getTermSeq().substring(terminal.getTermSeq().length() - 7,
						terminal.getTermSeq().length());
				String mchntCd = terminalMapper.queryMchntCd("mchntCd");
				Map map2 = new HashMap();
				map2.put("terminalAsycnUrl", terminalAsycnUrl);
				map2.put("termId", termId);
				map2.put("mchntCd", mchntCd);
				if (CtUtils.isEmpty(terminalAsycnUrl)) {
					logger.info("与云支付管理系统的同步接口地址没有配置");
					return ResultMsg.build(ResultMsg.ERROR_CODE, "与云支付管理系统的同步接口地址没有配置");
				} else if (CtUtils.isEmpty(termId)) {
					logger.info("默认终端号不存在，请配置");
					return ResultMsg.build(ResultMsg.ERROR_CODE, "默认终端号不存在，请配置");
				} else if (CtUtils.isEmpty(mchntCd)) {
					logger.info("默认商户号号不存在，请配置");
					return ResultMsg.build(ResultMsg.ERROR_CODE, "默认商户号号不存在，请配置");
				} else {
					Boolean isSuccessAdd = false;
					try {
						isSuccessAdd = synchroTerminal(map2, terminal.getTermSeq());
					} catch (Exception e) {
						logger.info("数据同步到全渠道支付系统失败");
						return ResultMsg.build(ResultMsg.ERROR_CODE, "数据同步到全渠道支付系统失败");
					}
					if (isSuccessAdd) {
						logger.info("数据同步到云支付管理系统成功");
						terminalMapper.insert(terminal);
						oqcChcekHistory.setAddFlag("0");
						oqcChcekHistory.setTermid(termId);
						oqcChcekHistory.setMchntcd(mchntCd);
						oqcChcekHistory.setTermSeq(terminal.getTermSeq());
						oqcChcekHistory.setTermMfrId(terminal.getTermMfrId());
						oqcChcekHistory.setTermTypeCode(terminal.getTermTypeCode());
						oqcChcekHistory.setUpdateTime(new Date());
						oqcChcekHistory.setUpdateUserId(userId);
						OqcChcekHistoryMapper.insert(oqcChcekHistory);
						Date now = new Date(); // 当前时间
						Calendar calendar = Calendar.getInstance(); // 得到日历
						calendar.setTime(now);// 把当前时间赋给日历
						calendar.add(Calendar.MONTH, +3);
						Date now3 = calendar.getTime();
						Map map = new HashMap();
						map.put("termId", terminal.getId());
						map.put("termSeq", terminal.getTermSeq());
						map.put("merchantId", merchantId);
						map.put("warrantyPeriod", now3);
						terminalMapper.insertTestAutoRel(map);
						logger.info("终端信息新增成功");
					} else {
						logger.info("数据同步到云支付管理系统失败");
						return ResultMsg.build(ResultMsg.ERROR_CODE, "终端信息同步到全支付渠道系统失败");
					}
				}
			}
		}
		return ResultMsg.success();
	}

	public Boolean delTerminal(Map<String, String> map, String termSeq) {
		JSONObject jo = new JSONObject();
		Map<String, String> body = new HashMap<String, String>();
		body.put("transId", "1111");
		body.put("addFlag", "1");
		body.put("termId", String.valueOf(map.get("termId")));
		body.put("mchntCd", String.valueOf(map.get("mchntCd")));
		body.put("termSeq", termSeq);
		body.put("termStatus", "1");
		jo.putAll(body);
		AcpsMgmRequest m = new AcpsMgmRequest();
		AcpsMgmRequest.Header h = new AcpsMgmRequest.Header();
		h.setIcode("10001");
		h.setIno("cpay10001");
		String data = "[" + jo.toString() + "]";
		String key = "BjDa9EG1";
		m.setBody(CipherUtil.encryptDES(data, key));
		m.setHeader(h);
		String result = HttpClientUtil.doPostJson(map.get("terminalAsycnUrl"), JsonUtils.objectToJson(m));
		result = result.substring(1, result.length() - 1);
		Map<String, String> rs = new HashMap<String, String>();
		JsonUtils.jsonToMap(rs, JSONObject.fromObject(result));
		if (!"1".equals(rs.get("resultCode"))) {
			return false;
		}
		return true;
	}

	/***
	 * 
	 * 将终端信息同步到云支付管理系统
	 * 
	 * @param map
	 * @param termSeq
	 * @return
	 */
	public Boolean synchroTerminal(Map<String, String> map, String termSeq) {
		JSONObject jo = new JSONObject();
		Map<String, String> body = new HashMap<String, String>();
		body.put("transId", "1111");
		body.put("addFlag", "0");
		body.put("termId", String.valueOf(map.get("termId")));
		body.put("mchntCd", String.valueOf(map.get("mchntCd")));
		body.put("termSeq", termSeq);
		body.put("termStatus", "1");
		jo.putAll(body);
		AcpsMgmRequest m = new AcpsMgmRequest();
		AcpsMgmRequest.Header h = new AcpsMgmRequest.Header();
		h.setIcode("10001");
		h.setIno("cpay10001");
		String data = "[" + jo.toString() + "]";
		String key = "BjDa9EG1";
		m.setBody(CipherUtil.encryptDES(data, key));
		m.setHeader(h);
		String result = HttpClientUtil.doPostJson(map.get("terminalAsycnUrl"), JsonUtils.objectToJson(m));
		result = result.substring(1, result.length() - 1);
		Map<String, String> rs = new HashMap<String, String>();
		JsonUtils.jsonToMap(rs, JSONObject.fromObject(result));
		if (!"1".equals(rs.get("resultCode"))) {
			return false;
		}
		return true;
	}

	@Override
	public ResultMsg delTestAuto(Integer id, Integer userId) {
		OqcChcekHistory oqcChcekHistory = new OqcChcekHistory();
		Terminal terminal = terminalMapper.selectByPrimaryKey(id);
		Integer factoryId = terminalMapper.getFactoryId(id);
		logger.info("开始删除历史数据的新增数据");
		OqcChcekHistoryMapper.deleteByTermSeq(terminal.getTermSeq());
		logger.info("删除历史数据的新增数据成功");
		String terminalAsycnUrl = terminalMapper.queryTerminalAsycn("terminalAsycnUrl");
		String mchntCd = terminalMapper.queryMchntCd("mchntCd");
		String termId = "P"
				+ terminal.getTermSeq().substring(terminal.getTermSeq().length() - 7, terminal.getTermSeq().length());
		Map map = new HashMap();
		map.put("terminalAsycnUrl", terminalAsycnUrl);
		map.put("termId", termId);
		map.put("mchntCd", mchntCd);
		if (CtUtils.isEmpty(terminalAsycnUrl)) {
			logger.info("与云支付管理系统的同步接口地址没有配置");
			return ResultMsg.build(ResultMsg.ERROR_CODE, "与云支付管理系统的同步接口地址没有配置");
		} else if (CtUtils.isEmpty(termId)) {
			logger.info("默认终端号不存在，请配置");
			return ResultMsg.build(ResultMsg.ERROR_CODE, "默认终端号不存在，请配置");
		} else if (CtUtils.isEmpty(mchntCd)) {
			logger.info("默认商户号号不存在，请配置");
			return ResultMsg.build(ResultMsg.ERROR_CODE, "默认商户号号不存在，请配置");
		} else {
			Boolean isSuccessDel = delTerminal(map, terminal.getTermSeq());
			if (isSuccessDel) {
				logger.info("数据同步删除到云支付管理系统成功");
				logger.info("开始执行数据库终端序列号清除操作");
				oqcChcekHistory.setAddFlag("1");
				oqcChcekHistory.setTermid(termId);
				oqcChcekHistory.setMchntcd(mchntCd);
				oqcChcekHistory.setTermSeq(terminal.getTermSeq());
				oqcChcekHistory.setTermMfrId(factoryId);
				oqcChcekHistory.setTermTypeCode(terminal.getTermTypeName());
				oqcChcekHistory.setUpdateTime(new Date());
				oqcChcekHistory.setUpdateUserId(userId);
				oqcChcekHistory.setType("1");
				OqcChcekHistoryMapper.insert(oqcChcekHistory);
				terminalMapper.cleanTerm(terminal.getTermSeq());
				logger.info("数据库终端序列号清除操作成功");
				return ResultMsg.success();
			} else {
				logger.info("数据同步删除到云支付管理系统失败");
				return ResultMsg.build(ResultMsg.ERROR_CODE, "数据同步删除到云支付管理系统失败");
			}
		}

	}
	@Override
	public Terminal selectByImsi(String imsi) {
		return terminalMapper.selectByImsi(imsi);
	}

	@Override
	public TerminalPosition getTerminalCurrentPosition(Integer id) {

		Terminal terminal = terminalMapper.selectByPrimaryKey(id);
		TerminalPosition terminalPosition = terminalPositionMapper.queryCurrentPosition(terminal.getTermSeq());
		if (CtUtils.isEmpty(terminalPosition)) {
			TerminalPosition terminalPosition2 = new TerminalPosition();
			terminalPosition2.setLatitude(terminal.getLatitude());
			terminalPosition2.setLongitude(terminal.getLongitude());
			return terminalPosition2;
		}
		return terminalPosition;
	}
	@Override
	public TerminalPosition getCheckTerminalPosition(String termSeq) {
		return terminalPositionMapper.queryCurrentPosition(termSeq);
	}

	@Override
	public List<Terminal> selectTermList(Terminal terminal) {
		return terminalMapper.selectByCondition(terminal);
	}
	@Override
	public List<Terminal> selectTermListExcel(Terminal terminal) {
		return terminalMapper.selectByConditionExcel(terminal);
	}

	@Override
	public TerminalRealTimeStatus getTermPosiByTermSeq(String termSeq) {

		return terminalRealTimeStatusMapper.queryByTermSeq(termSeq);
	}

	@Override
	public TerminalCircle getCircleByTermSeq(String termSeq) {
		// TODO
		Terminal terminal  = terminalMapper.selectByTermSeq(termSeq);
		TerminalCircle terminalCircleWhere = terminalCircleMapper.selectByTermSeq(termSeq);
		TerminalCircle terminalCircle = new TerminalCircle();
		if(CtUtils.isEmpty(terminalCircleWhere)){
			terminalCircle.setCenterLatitude(terminal.getLatitude());
			terminalCircle.setCenterLongitude(terminal.getLongitude());
			terminalCircle.setRadius(10000);
		}else{
			if(CtUtils.isEmpty(terminalCircleWhere.getCenterLatitude()) || 
					CtUtils.isEmpty(terminalCircleWhere.getCenterLongitude())){
				terminalCircle.setCenterLatitude(terminal.getLatitude());
				terminalCircle.setCenterLongitude(terminal.getLongitude());
				terminalCircle.setRadius(10000);
			}else{
				terminalCircle.setCenterLatitude(terminalCircleWhere.getCenterLatitude());
				terminalCircle.setCenterLongitude(terminalCircleWhere.getCenterLongitude());
				terminalCircle.setRadius(terminalCircleWhere.getRadius());
			}
		}
		return terminalCircle;
	}

	@Override
	public ResultMsg lockTerm(String termSeq) {
		int count = terminalLockStateMapper.selectByTermSeq(termSeq);
		TerminalLockState terminalLockState = new TerminalLockState();
		terminalLockState.setLockStatus("0");
		terminalLockState.setTermSeq(termSeq);
		if(count>0){
			terminalLockStateMapper.update(terminalLockState);
		}else{
			terminalLockStateMapper.insert(terminalLockState);
		}
		return ResultMsg.success();
	}

	@Override
	public ResultMsg unlockTerm(String termSeq) {
		int count = terminalLockStateMapper.selectByTermSeq(termSeq);
		TerminalLockState terminalLockState = new TerminalLockState();
		terminalLockState.setLockStatus("1");
		terminalLockState.setTermSeq(termSeq);
		if(count>0){
			terminalLockStateMapper.update(terminalLockState);
		}else{
			terminalLockStateMapper.insert(terminalLockState);
		}
		return ResultMsg.success();
	}

	@Override
	public ResultMsg changeMerchant(TerminalChangeMerchant entity) {
		Integer oldMerchantId = terminalChangeMerchantMapper.selectMerchantByTermId(entity.getTermId());
		if(entity.getNewMerchantId().equals(oldMerchantId)){
			return ResultMsg.build(ResultMsg.SUCCESS_CODE, "请勿选择同一商户进行变更");
		}
		entity.setAuditStatus("0");
		entity.setOldMerchantId(oldMerchantId);
		terminalChangeMerchantMapper.insert(entity);
		return ResultMsg.success();
	}

	
	@Override
	public TerminalCountInfo getTerminalCount(Integer insId) {
		TerminalCountInfo terminalCountInfo = new TerminalCountInfo();
        terminalCountInfo.setMonth(getLast12Months());

		List<TerminalCount> dataCount = terminalMapper.getCount(insId);

		int[] terminalCountArr =new int[12];
		int[] actCountArr = new int[12];
        for(TerminalCount terminalCount : dataCount) {
            for (int i = 0; i < terminalCountInfo.getMonth().length; i++) {
                if(terminalCount.getTermMonth().equals(terminalCountInfo.getMonth()[i])){
                    terminalCountArr[i] = terminalCount.getTermAmount();
                    actCountArr[i] = terminalCount.getActAmount();
                    break;
                }
            }
        }
        terminalCountInfo.setTerminalCount(terminalCountArr);
        terminalCountInfo.setActCount(actCountArr);
		return terminalCountInfo;
	}

	public String[] getMonths(TermInfoReportCount reportCount) {
		List<String> list = new ArrayList<>();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
		try {
			Date startTime = format.parse(reportCount.getStartTime());
			Date endTime = format.parse(reportCount.getEndTime());
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startTime);
			while(calendar.getTime().before(endTime)){
				list.add(format.format(calendar.getTime()));
				calendar.add(Calendar.MONTH,1);
			}

		} catch (ParseException e) {
			e.printStackTrace();
		}
		list.add(reportCount.getEndTime());
		return (String[])list.toArray(new String[list.size()]);
	}
	@Override
	public TermInfoReportCount getTermInfoReport(TermInfoReportCount reportCount) {


		//设置月份数组
		reportCount.setMonth(getMonths(reportCount));
		//获取所有厂商
		List<Factory> factoryList = factoryService.getFactory();
		Map<String,int[]> factoryData = new HashMap<>();
		//循环 获取所有的厂商数据
        int[] allData = new int[reportCount.getMonth().length];
		for (Factory factory : factoryList) {

			reportCount.setFactoryId(factory.getId());
			int[] data = new int[reportCount.getMonth().length];

			//获取对应机构 对应厂商下的数据
			List<TermInfoReport> termInfoReportList = terminalMapper.getTermInfoReport(reportCount);

			for (TermInfoReport termInfoReport : termInfoReportList) {
				//查找月份字符串对应的数组id，然后将数据存入int数组的对应位置
				for (int i = 0; i < reportCount.getMonth().length; i++) {
					if (termInfoReport.getMonth().equals(reportCount.getMonth()[i])) {
						data[i] = termInfoReport.getTerminalCount();
                        allData[i] += termInfoReport.getTerminalCount();
						break;
					}
				}
			}
			factoryData.put(factory.getName(),data);
		}
		factoryData.put("allData",allData);
		reportCount.setFactoryData(factoryData);
		return reportCount;
	}

	@Override
	public List<Terminal> selectTermListForUpload(Terminal terminalParam) {
		// TODO
		return terminalMapper.selectTermListForUpload(terminalParam);
	}

	/**
	 *【获取】终端在线状态
	 * @param terminal 终端信息
	 */
	private void getTerminalOnlineStatus(Terminal terminal) {
		//【判断】是否终端状态为在线
		if (Objects.equals(terminal.getNetworkStatus(), TERMINAL_ONLINE)) {
			terminal.setOnlineStatus(OffLineStatus.ON_LINE.getCode());
			return;
		}

		//【判断】终端是否激活（未激活肯定没在线）
		if (Objects.equals(TermActivateStatus.UN_ACTIVATED.getCode(), terminal.getActivateStatus())) {
			terminal.setOnlineStatus(OffLineStatus.OFF_LINE.getCode());
			return;
		}

		//【判断】终端上次登录时间是否为空
		if (CtUtils.isEmpty(terminal.getLatestAccessTime())) {
			terminal.setOnlineStatus(OffLineStatus.OFF_LINE.getCode());
			return;
		}

		//【判断】心跳间隔判断*1.5未通讯：判断为下线（默认心跳一小时）
		Integer max_minute = 60;
		TerminalParam institutionParam = new TerminalParam();
		institutionParam.setInsId(terminal.getInsId());
		institutionParam.setParaVersion(terminal.getTermSeq());
		TerminalParam retInstitutionParam = terminalParamService.getInstitutionParam(institutionParam);
		if (retInstitutionParam == null || retInstitutionParam.getParaContent() == null) {
			retInstitutionParam = terminalParamService.getInstitutionParamByInsList(terminal.getInsId());
		}
		if (!CtUtils.isEmpty(retInstitutionParam)) {
			max_minute = processHtIntvl(retInstitutionParam.getParaContent());
		}
		logger.info("【最大超时间隔】为「{}」", max_minute);

		//【判断】终端登录时间是否超出后台限制最大值
		long diffSec = (new Date()).getTime() - TimeUtil.getDateFromFullStr(terminal.getLatestAccessTime()).getTime();
		Boolean isOffline = diffSec > max_minute * 1.5 * 60 * 1000;
		if (Objects.equals(isOffline, Boolean.TRUE)) {
			terminal.setOnlineStatus(OffLineStatus.OFF_LINE.getCode());
		} else {
			terminal.setOnlineStatus(OffLineStatus.ON_LINE.getCode());
		}
	}

	public static Integer processHtIntvl(String paraContent) {
		final int DEFAULT_HT_INTVL = 60;
		int htIntvl = DEFAULT_HT_INTVL;

		try {
			JSONObject jsonObject = JSONObject.fromObject(paraContent);

			//【使用】Optional 包装可能为空的 htIntvl 值
			Optional<Object> htIntvlObj = Optional.ofNullable(jsonObject.get("htIntvl"));

			//【使用】map 方法尝试将值转换为字符串，如果值存在
			Optional<String> htIntvlStr = htIntvlObj.map(Object::toString);

			//【判断】如果字符串存在且不为空，则尝试解析为整数
			if (htIntvlStr.isPresent() && !htIntvlStr.get().isEmpty()) {
				try {
					htIntvl = Integer.parseInt(htIntvlStr.get());
				} catch (NumberFormatException e) {
					logger.error("Error parsing htIntvl to integer, using default value「{}」『{}』", DEFAULT_HT_INTVL, e.getMessage());
				}
			} else {
				logger.error("htIntvl is null or empty, using default value『{}』", DEFAULT_HT_INTVL);
			}
		} catch (Exception e) {
			logger.error("Error parsing JSON: {}", e.getMessage());
		}

		double result = htIntvl;
		return (int) result;
	}
}