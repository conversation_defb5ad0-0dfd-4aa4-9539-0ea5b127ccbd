package com.centerm.cpay.coms.service.impl;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.SpringContextUtil;
import com.centerm.cpay.coms.dao.mapper.TerminalPayMethodDetailMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalPayMethodMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalPayMethod;
import com.centerm.cpay.coms.dao.pojo.TerminalPayMethodDetail;

public class TerminalPayMethodDetailTask implements Runnable {

	Logger logger = LoggerFactory.getLogger(TerminalPayMethodDetailTask.class);
	private TerminalPayMethod terminalPayMethod;

	private TerminalPayMethodDetailMapper terminalPayMethodDetailMapper;
	private TerminalPayMethodMapper terminalPayMethodMapper;

	public TerminalPayMethodDetailTask(TerminalPayMethod terminalPayMethod) {
		terminalPayMethodDetailMapper = SpringContextUtil.getBean("terminalPayMethodDetailMapper");
		terminalPayMethodMapper = SpringContextUtil.getBean("terminalPayMethodMapper");
		this.terminalPayMethod = terminalPayMethod;
	}

	@Override
	public void run() {

		TerminalPayMethodDetail terminalPayMethodDetail = new TerminalPayMethodDetail();
		String termSeqCollection = terminalPayMethod.getTermCollection();
		String[] termSeqCollectionArr = termSeqCollection.split(",");
		if (CtUtils.isEmpty(termSeqCollection)) {
			logger.info("渠道切换终端序列号为空");
		} else {
			for (int i = 0; i < termSeqCollectionArr.length; i++) {
				terminalPayMethodDetail.setMethodId(terminalPayMethod.getId());
				terminalPayMethodDetail.setBatchNumber(terminalPayMethod.getBatchNumber());
				terminalPayMethodDetail.setTermSeq(termSeqCollectionArr[i]);
				terminalPayMethodDetail.setSendPayCode(terminalPayMethod.getPayCode());
				terminalPayMethodDetail.setCreateTime(new Date());
				terminalPayMethodDetail.setChangeStatus("2");
				terminalPayMethodDetail.setReceivePayCode("0");
				int count  = terminalPayMethodDetailMapper.selectExistByTermSeq(termSeqCollectionArr[i]);//判断当前表中终端序列号是否存在
				if(count>0){
					terminalPayMethodDetailMapper.deleteRecordByTermSeq(termSeqCollectionArr[i]);//如果存在就删除
				}
				terminalPayMethodDetailMapper.insert(terminalPayMethodDetail);
				terminalPayMethod.setReleaseStatus("2");
				terminalPayMethodMapper.updateRelease(terminalPayMethod);
			}
		}
		logger.info("【渠道切换成功】");
	}

	public TerminalPayMethod getTerminalPayMethod() {
		return terminalPayMethod;
	}

	public void setTerminalPayMethod(TerminalPayMethod terminalPayMethod) {
		this.terminalPayMethod = terminalPayMethod;
	}
}
