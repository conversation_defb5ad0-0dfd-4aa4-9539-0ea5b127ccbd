package com.centerm.cpay.coms.dao.pojo;

public class TerminalParam {
    private Integer id;

    private int insId;

    private String paraContent;

    private String paraVersion;

    private int terminalGroupId;
    
    private String insName;
    
    private String insCode;
    
    private String terminalGroupName;
    
    private String htIntvl;//心跳时间(秒)
    
    private String reHtIntvl;//心跳重连次数
    
    private String htIntvlMinute;//心跳时间(分)
    
    private String reDownNum;//文件传输重试次数
    
    private String fileTraTimeout;//文件传输超时时间
    
    private String upInfoIntvl;//信息上送间隔时间
    
    private String upFlowIntvl;//流量上送间隔时间
    
    private String instIntvl;//定位上送间隔时间
    
    private String adQueryIntvl;//广告轮询时间
    
    private String systemStartUp;//系统启动项
    
    private String alarmInfo;//设备故障标识
    
    private String eleFenceRadius;//电子围栏半径
    
    private String fileDownloadHtval;//文件下载时间间隔
    
    private String mapPswd;//运维口令
    
    private String managePwd;//主管口令
    
    private String broadcastTime;//广播间隔时间(秒)
    
    private String isWifi;//仅在WIFI下通讯
    
    private String systemUpdateRequiredPower;//系统更新电量阀值
    
    private String appUpdateRequiredPower;//应用更新电量阀值
    
    private String sysUpRequiresNoOperTime;//系统更新闲置时间
    
    private String appUpRequiresNoOperTime;//应用更新闲置时间
    
    private String tmsDomainName;//域名
    private String tmsDomainNameBak;//域名
    private String tmsDomainNameBakFirst;//域名备机2
    private String tmsDomainNameBakSecond;//域名备机3

    private String amsDomainName;

    private String casDomainName;
    
    private String iotActivateUrl;
    
    private String lockTerminalImgUrl;

    private String alarmInfoCode;

    private String sysUpRequiresDisplay;

    private String sysUpRequiresVolume;

    private Integer sysUpRequiresVolumeValue;
    
    private String idcardSwitch;//是否开通身份证验证

	private String locatingInterval;//终端定位频率

	private String enableLocation;//终端是否开启定位

	private String isWifiDownload;
	public String getTmsDomainNameBakFirst() {
		return tmsDomainNameBakFirst;
	}

	public void setTmsDomainNameBakFirst(String tmsDomainNameBakFirst) {
		this.tmsDomainNameBakFirst = tmsDomainNameBakFirst;
	}

	public String getTmsDomainNameBakSecond() {
		return tmsDomainNameBakSecond;
	}

	public void setTmsDomainNameBakSecond(String tmsDomainNameBakSecond) {
		this.tmsDomainNameBakSecond = tmsDomainNameBakSecond;
	}

	private String installWhiteListQ7;

	public String getInstallWhiteListQ7() {
		return installWhiteListQ7;
	}

	public void setInstallWhiteListQ7(String installWhiteListQ7) {
		this.installWhiteListQ7 = installWhiteListQ7;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public int getInsId() {
		return insId;
	}

	public void setInsId(int insId) {
		this.insId = insId;
	}

	public String getParaContent() {
		return paraContent;
	}

	public void setParaContent(String paraContent) {
		this.paraContent = paraContent;
	}

	public String getParaVersion() {
		return paraVersion;
	}

	public void setParaVersion(String paraVersion) {
		this.paraVersion = paraVersion;
	}

	public int getTerminalGroupId() {
		return terminalGroupId;
	}

	public void setTerminalGroupId(int terminalGroupId) {
		this.terminalGroupId = terminalGroupId;
	}

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public String getInsCode() {
		return insCode;
	}

	public void setInsCode(String insCode) {
		this.insCode = insCode;
	}

	public String getTerminalGroupName() {
		return terminalGroupName;
	}

	public void setTerminalGroupName(String terminalGroupName) {
		this.terminalGroupName = terminalGroupName;
	}

	public String getHtIntvlMinute() {
		return htIntvlMinute;
	}

	public void setHtIntvlMinute(String htIntvlMinute) {
		this.htIntvlMinute = htIntvlMinute;
	}

	public String getReDownNum() {
		return reDownNum;
	}

	public void setReDownNum(String reDownNum) {
		this.reDownNum = reDownNum;
	}
	
	public String getFileTraTimeout() {
		return fileTraTimeout;
	}

	public void setFileTraTimeout(String fileTraTimeout) {
		this.fileTraTimeout = fileTraTimeout;
	}

	public String getUpInfoIntvl() {
		return upInfoIntvl;
	}

	public void setUpInfoIntvl(String upInfoIntvl) {
		this.upInfoIntvl = upInfoIntvl;
	}

	public String getUpFlowIntvl() {
		return upFlowIntvl;
	}

	public void setUpFlowIntvl(String upFlowIntvl) {
		this.upFlowIntvl = upFlowIntvl;
	}

	public String getInstIntvl() {
		return instIntvl;
	}

	public void setInstIntvl(String instIntvl) {
		this.instIntvl = instIntvl;
	}

	public String getAdQueryIntvl() {
		return adQueryIntvl;
	}

	public void setAdQueryIntvl(String adQueryIntvl) {
		this.adQueryIntvl = adQueryIntvl;
	}

	public String getSystemStartUp() {
		return systemStartUp;
	}

	public void setSystemStartUp(String systemStartUp) {
		this.systemStartUp = systemStartUp;
	}

	public String getAlarmInfo() {
		return alarmInfo;
	}

	public void setAlarmInfo(String alarmInfo) {
		this.alarmInfo = alarmInfo;
	}

	public String getEleFenceRadius() {
		return eleFenceRadius;
	}

	public void setEleFenceRadius(String eleFenceRadius) {
		this.eleFenceRadius = eleFenceRadius;
	}

	public String getMapPswd() {
		return mapPswd;
	}

	public void setMapPswd(String mapPswd) {
		this.mapPswd = mapPswd;
	}

	public String getSystemUpdateRequiredPower() {
		return systemUpdateRequiredPower;
	}

	public void setSystemUpdateRequiredPower(String systemUpdateRequiredPower) {
		this.systemUpdateRequiredPower = systemUpdateRequiredPower;
	}

	public String getAppUpdateRequiredPower() {
		return appUpdateRequiredPower;
	}

	public void setAppUpdateRequiredPower(String appUpdateRequiredPower) {
		this.appUpdateRequiredPower = appUpdateRequiredPower;
	}

	public String getSysUpRequiresNoOperTime() {
		return sysUpRequiresNoOperTime;
	}

	public void setSysUpRequiresNoOperTime(String sysUpRequiresNoOperTime) {
		this.sysUpRequiresNoOperTime = sysUpRequiresNoOperTime;
	}

	public String getAppUpRequiresNoOperTime() {
		return appUpRequiresNoOperTime;
	}

	public void setAppUpRequiresNoOperTime(String appUpRequiresNoOperTime) {
		this.appUpRequiresNoOperTime = appUpRequiresNoOperTime;
	}

	public String getTmsDomainName() {
		return tmsDomainName;
	}

	public void setTmsDomainName(String tmsDomainName) {
		this.tmsDomainName = tmsDomainName;
	}

	public String getAmsDomainName() {
		return amsDomainName;
	}

	public void setAmsDomainName(String amsDomainName) {
		this.amsDomainName = amsDomainName;
	}

	public String getCasDomainName() {
		return casDomainName;
	}

	public void setCasDomainName(String casDomainName) {
		this.casDomainName = casDomainName;
	}

	public String getAlarmInfoCode() {
		return alarmInfoCode;
	}

	public void setAlarmInfoCode(String alarmInfoCode) {
		this.alarmInfoCode = alarmInfoCode;
	}

	public String getHtIntvl() {
		return htIntvl;
	}

	public void setHtIntvl(String htIntvl) {
		this.htIntvl = htIntvl;
	}

	public String getReHtIntvl() {
		return reHtIntvl;
	}

	public void setReHtIntvl(String reHtIntvl) {
		this.reHtIntvl = reHtIntvl;
	}

	public String getManagePwd() {
		return managePwd;
	}

	public void setManagePwd(String managePwd) {
		this.managePwd = managePwd;
	}

	public String getBroadcastTime() {
		return broadcastTime;
	}

	public void setBroadcastTime(String broadcastTime) {
		this.broadcastTime = broadcastTime;
	}

	public String getIsWifi() {
		return isWifi;
	}

	public void setIsWifi(String isWifi) {
		this.isWifi = isWifi;
	}

	public String getIdcardSwitch() {
		return idcardSwitch;
	}

	public void setIdcardSwitch(String idcardSwitch) {
		this.idcardSwitch = idcardSwitch;
	}

	public String getFileDownloadHtval() {
		return fileDownloadHtval;
	}

	public void setFileDownloadHtval(String fileDownloadHtval) {
		this.fileDownloadHtval = fileDownloadHtval;
	}

	public String getTmsDomainNameBak() {
		return tmsDomainNameBak;
	}

	public void setTmsDomainNameBak(String tmsDomainNameBak) {
		this.tmsDomainNameBak = tmsDomainNameBak;
	}

    public String getIotActivateUrl() {
        return iotActivateUrl;
    }

    public void setIotActivateUrl(String iotActivateUrl) {
        this.iotActivateUrl = iotActivateUrl;
    }

    public String getLockTerminalImgUrl() {
        return lockTerminalImgUrl;
    }

    public void setLockTerminalImgUrl(String lockTerminalImgUrl) {
        this.lockTerminalImgUrl = lockTerminalImgUrl;
    }

	public String getSysUpRequiresDisplay() {
		return sysUpRequiresDisplay;
	}

	public void setSysUpRequiresDisplay(String sysUpRequiresDisplay) {
		this.sysUpRequiresDisplay = sysUpRequiresDisplay;
	}

	public String getSysUpRequiresVolume() {
		return sysUpRequiresVolume;
	}

	public void setSysUpRequiresVolume(String sysUpRequiresVolume) {
		this.sysUpRequiresVolume = sysUpRequiresVolume;
	}

	public Integer getSysUpRequiresVolumeValue() {
		return sysUpRequiresVolumeValue;
	}

	public void setSysUpRequiresVolumeValue(Integer sysUpRequiresVolumeValue) {
		this.sysUpRequiresVolumeValue = sysUpRequiresVolumeValue;
	}

	public String getLocatingInterval() {
		return locatingInterval;
	}

	public void setLocatingInterval(String locatingInterval) {
		this.locatingInterval = locatingInterval;
	}

	public String getEnableLocation() {
		return enableLocation;
	}

	public void setEnableLocation(String enableLocation) {
		this.enableLocation = enableLocation;
	}

	public String getIsWifiDownload() {
		return isWifiDownload;
	}

	public void setIsWifiDownload(String isWifiDownload) {
		this.isWifiDownload = isWifiDownload;
	}
}