<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalAppMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="para_version" property="paraVersion" jdbcType="CHAR" />
    <result column="app_version" property="appVersion" jdbcType="CHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="app_version_outside" property="appVersionOutSide" jdbcType="VARCHAR" />
    <result column="app_used_num" property="appUsedNum" jdbcType="INTEGER" />
    <result column="app_used_duration" property="appUsedDuration" jdbcType="INTEGER" />
    <result column="cur_flow_used" property="appFlowUsed" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, term_seq, app_code, para_version, app_version, app_name,app_version_outside
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_app_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_terminal_app_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    insert into coms_terminal_app_relation ( term_seq, app_code, 
      para_version, app_version, app_name
      )
    values ( #{termSeq,jdbcType=VARCHAR}, #{appCode,jdbcType=VARCHAR}, 
      #{paraVersion,jdbcType=CHAR}, #{appVersion,jdbcType=CHAR}, #{appName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    insert into coms_terminal_app_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="paraVersion != null" >
        para_version,
      </if>
      <if test="appVersion != null" >
        app_version,
      </if>
      <if test="appName != null" >
        app_name,
      </if>
      <if test="appVersionOutSide != null" >
        app_version_outside,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="paraVersion != null" >
        #{paraVersion,jdbcType=CHAR},
      </if>
      <if test="appVersion != null" >
        #{appVersion,jdbcType=CHAR},
      </if>
      <if test="appName != null" >
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersionOutSide != null" >
        #{appVersionOutSide,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    update coms_terminal_app_relation
    <set >
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="paraVersion != null" >
        para_version = #{paraVersion,jdbcType=CHAR},
      </if>
      <if test="appVersion != null" >
        app_version = #{appVersion,jdbcType=CHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersionOutSide != null" >
        app_version_outside=#{appVersionOutSide,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    update coms_terminal_app_relation
    set term_seq = #{termSeq,jdbcType=VARCHAR},
      app_code = #{appCode,jdbcType=VARCHAR},
      para_version = #{paraVersion,jdbcType=CHAR},
      app_version = #{appVersion,jdbcType=CHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      app_version_outside=#{appVersionOutSide,jdbcType=VARCHAR},
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectApp" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    select 
    a.id, a.term_seq, a.app_code, a.para_version, a.app_version, a.app_name,a.app_version_outside,
    a.app_used_num,a.app_used_duration
    from coms_terminal_app_relation a 
    left join coms_terminal_info b on a.term_seq = b.term_seq
    where a.app_code not in 
       ('com.centerm.cpay.launcher',
		'com.centerm.smartposservice',
		'com.centerm.cpay.applicationshop',
		'com.centerm.cpay.autoactive"',
		'com.centerm.cpay.securitysuite',
		'com.centerm.cpay.cpaytermset',
		'com.centerm.cpay.gtms',
		'com.android.providers.telephony',
'com.android.providers.calendar',
'com.android.providers.media',
'com.centerm.dev.serialdev',
'com.qualcomm.shutdownlistner',
'com.android.wallpapercropper',
'com.quicinc.cne.CNEService',
'com.android.protips',
'com.android.documentsui',
'com.android.externalstorage',
'com.qualcomm.sta',
'com.qualcomm.svi',
'com.android.mms.service',
'com.centerm.dev.socketserver',
'com.android.providers.downloads',
'com.centerm.dev.magcard',
'com.qapp.secprotect',
'com.qualcomm.interfacepermissions',
'com.android.browser',
'com.android.inputmethod.pinyin',
'com.android.defcontainer',
'com.android.providers.downloads.ui',
'com.android.pacprocessor',
'com.qualcomm.cabl',
'org.codeaurora.bluetooth',
'android',
'com.qualcomm.wfd.service',
'com.centerm.dev.psamcard',
'com.android.nfc',
'com.android.backupconfirm',
'com.android.provision',
'org.codeaurora.ims',
'com.qualcomm.qcrilmsgtunnel',
'com.android.providers.settings',
'com.qualcomm.qcom_qmi',
'com.android.sharedstoragebackup',
'com.centerm.dev.iccard',
'com.android.dreams.basic',
'com.android.webview',
'com.android.inputdevices',
'com.centerm.dev.pinpad',
'com.centerm.dev.rfcard',
'com.centerm.dev.auth',
'com.centerm.dev.base',
'com.qualcomm.gsmtuneaway',
'com.qti.xdivert',
'com.centerm.dev.system',
'com.android.cellbroadcastreceiver',
'com.android.onetimeinitializer',
'com.android.qrd.engineeringmode',
'com.android.server.telecom',
'com.centerm.dev.memerycard',
'com.cyanogenmod.trebuchet',
'com.android.packageinstaller',
'org.codeaurora.btmultisim',
'com.dsi.ant.server',
'com.android.proxyhandler',
'com.qualcomm.qti.loadcarrier',
'com.android.inputmethod.latin',
'com.qti.backupagent',
'com.android.managedprovisioning',
'com.centerm.dev.emv',
'com.qualcomm.qti.accesscache',
'com.android.settings',
'com.qualcomm.location',
'com.android.vpndialogs',
'com.qualcomm.location.XT',
'com.centerm.powermenu',
'com.android.phone',
'com.android.shell',
'com.android.providers.userdictionary',
'com.android.location.fused',
'com.android.deskclock',
'com.centerm.frame',
'com.android.systemui',
'com.centerm.dev.barcode',
'com.centerm.custom.soundeffect',
'com.centerm.dev.printer',
'com.qualcomm.qti.networksetting',
'com.qualcomm.fastdormancy',
'com.android.bluetooth',
'com.qualcomm.timeservice',
'com.android.providers.contacts',
'com.android.captiveportallogin'
		) 
    and b.id = #{id,jdbcType=INTEGER}
     <if test="appCode != null and appCode !=''" >
       and a.app_code LIKE '%${appCode}%'
      </if>
      <if test="appName != null and appName != ''" >
       and a.app_name LIKE '%${appName}%'
      </if>
      <if test="appVersion != null and appVersion !=''" >
      and  a.app_version  LIKE '%${appVersion}%'
      </if>
      <if test="appVersionOutSide != null and appVersionOutSide !=''" >
      and  a.app_version_outside  LIKE '%${appVersionOutSide}%'
      </if>
  </select>
  <select id="basicApp" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalApp" >
    select 
    a.id, a.term_seq, a.app_code, a.para_version, a.app_version, a.app_name,a.app_version_outside,
    a.app_used_num,a.app_used_duration
    from coms_terminal_app_relation a 
    left join coms_terminal_info b on a.term_seq = b.term_seq
    where b.id = #{id,jdbcType=INTEGER}
     <if test="appCode != null and appCode !=''" >
       and a.app_code LIKE '%${appCode}%'
      </if>
      <if test="appName != null and appName != ''" >
       and a.app_name LIKE '%${appName}%'
      </if>
      <if test="appVersion != null and appVersion !=''" >
      and  a.app_version  LIKE '%${appVersion}%'
      </if>
      <if test="appVersionOutSide != null and appVersionOutSide !=''" >
      and  a.app_version_outside  LIKE '%${appVersionOutSide}%'
      </if>
  </select>
</mapper>