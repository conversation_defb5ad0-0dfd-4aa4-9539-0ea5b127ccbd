package com.centerm.cpay.coms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.centerm.cpay.common.utils.JsonUtils;
import com.centerm.cpay.coms.dao.pojo.TerminalParamJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;

import com.centerm.cpay.coms.dao.mapper.TerminalAlarmSetMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalParamMapper;
import com.centerm.cpay.coms.dao.pojo.TerminalAlarmSet;
import com.centerm.cpay.coms.dao.pojo.TerminalParam;
import com.centerm.cpay.coms.service.TerminalParamService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class TerminalParamServiceImpl implements TerminalParamService {
	@Autowired
	private TerminalParamMapper terminalParamMapper;
	@Autowired
	private TerminalAlarmSetMapper terminalAlarmSetMapper;

	@Override
	public EUDataGridResult getTerminalParamList(TerminalParam terminalParam, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);

		List<TerminalParam> list = terminalParamMapper.selectByCondition(terminalParam);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<TerminalParam> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg deleteByPrimaryKey(Integer id) {
		terminalParamMapper.deleteByPrimaryKey(id);
		return ResultMsg.success();
	}

	@Override
	public TerminalParam selectByPrimaryKey(Integer id) {

		TerminalParam terminalParam = terminalParamMapper.selectByPrimaryKey(id);
		String pageContent = terminalParam.getParaContent();
		
		if(CtUtils.isEmpty(pageContent)){
			
			return null;
			
		}else{
			TerminalParamJson terminalParamJson = JsonUtils.jsonToPojo(pageContent,TerminalParamJson.class);
			terminalParam.setHtIntvl(terminalParamJson.getHtIntvl());
			terminalParam.setReHtIntvl(terminalParamJson.getReHtIntvl());
			terminalParam.setReDownNum(terminalParamJson.getReDownNum());
			terminalParam.setFileTraTimeout(terminalParamJson.getFileTraTimeout());
			terminalParam.setUpInfoIntvl(terminalParamJson.getUpInfoIntvl());
			terminalParam.setUpFlowIntvl(terminalParamJson.getUpFlowIntvl());
			terminalParam.setMapPswd(terminalParamJson.getMapPswd());
			terminalParam.setManagePwd(terminalParamJson.getManagePwd());
			terminalParam.setSystemUpdateRequiredPower(terminalParamJson.getSystemUpdateRequiredPower());
			terminalParam.setAppUpdateRequiredPower(terminalParamJson.getAppUpdateRequiredPower());
			terminalParam.setSysUpRequiresNoOperTime(terminalParamJson.getSysUpRequiresNoOperTime());
			terminalParam.setAppUpRequiresNoOperTime(terminalParamJson.getAppUpRequiresNoOperTime());
			terminalParam.setTmsDomainName(terminalParamJson.getTmsDomainName());
			terminalParam.setTmsDomainNameBakFirst(terminalParamJson.getTmsDomainNameBakFirst());
			terminalParam.setTmsDomainNameBakSecond(terminalParamJson.getTmsDomainNameBakSecond());
			terminalParam.setAmsDomainName(terminalParamJson.getAmsDomainName());
			terminalParam.setBroadcastTime(terminalParamJson.getBroadcastTime());
			terminalParam.setFileDownloadHtval(terminalParamJson.getFileDownloadHtval());
			terminalParam.setAlarmInfoCode(terminalParamJson.getAlarmInfoCode());
			terminalParam.setIsWifi(terminalParamJson.getIsWifi());
			terminalParam.setIotActivateUrl(terminalParamJson.getIotActivateUrl());
			terminalParam.setLockTerminalImgUrl(terminalParamJson.getLockTerminalImgUrl());
			terminalParam.setEnableLocation(terminalParamJson.getEnableLocation());
			terminalParam.setLocatingInterval(terminalParamJson.getLocatingInterval());
			terminalParam.setIsWifiDownload(terminalParamJson.getIsWifiDownload());
			terminalParam.setSysUpRequiresDisplay(terminalParamJson.getSysUpRequiresDisplay());
			terminalParam.setSysUpRequiresVolume(terminalParamJson.getSysUpRequiresVolume());
			terminalParam.setSysUpRequiresVolumeValue(terminalParamJson.getSysUpRequiresVolumeValue());
			terminalParam.setInstallWhiteListQ7(terminalParamJson.getInstallWhiteListQ7());
		}

		return terminalParam;
	}

	@Override
	public ResultMsg insert(TerminalParam terminalParam) {
		List<TerminalParam> list = terminalParamMapper.queryParamExistAdd(terminalParam);
		if(!list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"The current set of parameters exists");
		}else{
			TerminalParamJson terminalParamJson = new TerminalParamJson();
			terminalParamJson.setEnableLocation(terminalParam.getEnableLocation() == null ? "":terminalParam.getEnableLocation());
			terminalParamJson.setLocatingInterval(terminalParam.getLocatingInterval() == null ? "":terminalParam.getLocatingInterval());
			terminalParamJson.setHtIntvl(terminalParam.getHtIntvl() == null ? "":terminalParam.getHtIntvl());
			terminalParamJson.setReHtIntvl(terminalParam.getReHtIntvl() == null ? "":terminalParam.getReHtIntvl());
			terminalParamJson.setReDownNum(terminalParam.getReDownNum() == null ? "":terminalParam.getReDownNum());
			terminalParamJson.setFileTraTimeout(terminalParam.getFileTraTimeout() == null ? "":terminalParam.getFileTraTimeout());
			terminalParamJson.setUpInfoIntvl(terminalParam.getUpInfoIntvl() == null ? "":terminalParam.getUpInfoIntvl());
			terminalParamJson.setUpFlowIntvl(terminalParam.getUpFlowIntvl() == null ? "":terminalParam.getUpFlowIntvl());
			terminalParamJson.setMapPswd(terminalParam.getMapPswd() == null ? "":terminalParam.getMapPswd());
			terminalParamJson.setManagePwd(terminalParam.getManagePwd() == null ? "":terminalParam.getManagePwd());
			terminalParamJson.setSystemUpdateRequiredPower(terminalParam.getSystemUpdateRequiredPower() == null ? "":terminalParam.getSystemUpdateRequiredPower());
			terminalParamJson.setAppUpdateRequiredPower(terminalParam.getAppUpdateRequiredPower() == null ? "":terminalParam.getAppUpdateRequiredPower());
			terminalParamJson.setSysUpRequiresNoOperTime(terminalParam.getSysUpRequiresNoOperTime() == null ? "":terminalParam.getSysUpRequiresNoOperTime());
			terminalParamJson.setAppUpRequiresNoOperTime(terminalParam.getAppUpRequiresNoOperTime() == null ? "":terminalParam.getAppUpRequiresNoOperTime());
			terminalParamJson.setTmsDomainName(terminalParam.getTmsDomainName() == null ? "":terminalParam.getTmsDomainName());
			terminalParamJson.setTmsDomainNameBak(terminalParam.getTmsDomainNameBakFirst() == null ? "":terminalParam.getTmsDomainNameBakFirst());
			terminalParamJson.setTmsDomainNameBakFirst(terminalParam.getTmsDomainNameBakFirst() == null ? "":terminalParam.getTmsDomainNameBakFirst());
			terminalParamJson.setTmsDomainNameBakSecond(terminalParam.getTmsDomainNameBakSecond() == null ? "":terminalParam.getTmsDomainNameBakSecond());
			terminalParamJson.setAmsDomainName(terminalParam.getAmsDomainName() == null ? "":terminalParam.getAmsDomainName());
			terminalParamJson.setBroadcastTime(terminalParam.getBroadcastTime() == null ? "":terminalParam.getBroadcastTime());
			terminalParamJson.setAlarmInfoCode(terminalParam.getAlarmInfoCode() == null ? "":terminalParam.getAlarmInfoCode());
			terminalParamJson.setFileDownloadHtval(terminalParam.getFileDownloadHtval() == null ? "":terminalParam.getFileDownloadHtval());
			terminalParamJson.setIotActivateUrl(terminalParam.getIotActivateUrl() == null ? "":terminalParam.getIotActivateUrl());
			terminalParamJson.setLockTerminalImgUrl(terminalParam.getLockTerminalImgUrl() == null ? "":terminalParam.getLockTerminalImgUrl());
			terminalParamJson.setIsWifi(terminalParam.getIsWifi() == null ? "":terminalParam.getIsWifi());
			terminalParamJson.setIsWifiDownload(terminalParam.getIsWifiDownload() == null ? "":terminalParam.getIsWifiDownload());
			terminalParamJson.setSysUpRequiresDisplay(terminalParam.getSysUpRequiresDisplay());
			terminalParamJson.setSysUpRequiresVolume(terminalParam.getSysUpRequiresVolume());
			terminalParamJson.setSysUpRequiresVolumeValue(terminalParam.getSysUpRequiresVolumeValue());
			terminalParamJson.setInstallWhiteListQ7(terminalParam.getInstallWhiteListQ7());
			String paraVersion = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(System.currentTimeMillis()));
			terminalParam.setParaVersion(paraVersion);
			String paraContent = JsonUtils.objectToJson(terminalParamJson);
			terminalParam.setParaContent(paraContent);
			terminalParamMapper.insert(terminalParam);
			return ResultMsg.success();
		}

	}

	@Override
	public ResultMsg updateByPrimaryKey(TerminalParam terminalParam) {
		List<TerminalParam> list = terminalParamMapper.queryParamExistEdit(terminalParam);
		if(!list.isEmpty()){
			return ResultMsg.build(ResultMsg.ERROR_CODE,"The current set of parameters exists");
		}else{
			TerminalParamJson terminalParamJson = new TerminalParamJson();
			terminalParamJson.setEnableLocation(terminalParam.getEnableLocation() == null ? "":terminalParam.getEnableLocation());
			terminalParamJson.setLocatingInterval(terminalParam.getLocatingInterval() == null ? "":terminalParam.getLocatingInterval());
			terminalParamJson.setHtIntvl(terminalParam.getHtIntvl() == null ? "":terminalParam.getHtIntvl());
			terminalParamJson.setReHtIntvl(terminalParam.getReHtIntvl() == null ? "":terminalParam.getReHtIntvl());
			terminalParamJson.setReDownNum(terminalParam.getReDownNum() == null ? "":terminalParam.getReDownNum());
			terminalParamJson.setFileTraTimeout(terminalParam.getFileTraTimeout() == null ? "":terminalParam.getFileTraTimeout());
			terminalParamJson.setUpInfoIntvl(terminalParam.getUpInfoIntvl() == null ? "":terminalParam.getUpInfoIntvl());
			terminalParamJson.setUpFlowIntvl(terminalParam.getUpFlowIntvl() == null ? "":terminalParam.getUpFlowIntvl());
			terminalParamJson.setMapPswd(terminalParam.getMapPswd() == null ? "":terminalParam.getMapPswd());
			terminalParamJson.setManagePwd(terminalParam.getManagePwd() == null ? "":terminalParam.getManagePwd());
			terminalParamJson.setSystemUpdateRequiredPower(terminalParam.getSystemUpdateRequiredPower() == null ? "":terminalParam.getSystemUpdateRequiredPower());
			terminalParamJson.setAppUpdateRequiredPower(terminalParam.getAppUpdateRequiredPower() == null ? "":terminalParam.getAppUpdateRequiredPower());
			terminalParamJson.setSysUpRequiresNoOperTime(terminalParam.getSysUpRequiresNoOperTime() == null ? "":terminalParam.getSysUpRequiresNoOperTime());
			terminalParamJson.setAppUpRequiresNoOperTime(terminalParam.getAppUpRequiresNoOperTime() == null ? "":terminalParam.getAppUpRequiresNoOperTime());
			terminalParamJson.setTmsDomainName(terminalParam.getTmsDomainName() == null ? "":terminalParam.getTmsDomainName());
			terminalParamJson.setTmsDomainNameBak(terminalParam.getTmsDomainNameBakFirst() == null ? "":terminalParam.getTmsDomainNameBakFirst());
			terminalParamJson.setTmsDomainNameBakFirst(terminalParam.getTmsDomainNameBakFirst() == null ? "":terminalParam.getTmsDomainNameBakFirst());
			terminalParamJson.setTmsDomainNameBakSecond(terminalParam.getTmsDomainNameBakSecond() == null ? "":terminalParam.getTmsDomainNameBakSecond());
			terminalParamJson.setAmsDomainName(terminalParam.getAmsDomainName() == null ? "":terminalParam.getAmsDomainName());
			terminalParamJson.setBroadcastTime(terminalParam.getBroadcastTime() == null ? "":terminalParam.getBroadcastTime());
			terminalParamJson.setAlarmInfoCode(terminalParam.getAlarmInfoCode() == null ? "":terminalParam.getAlarmInfoCode());
			terminalParamJson.setFileDownloadHtval(terminalParam.getFileDownloadHtval() == null ? "":terminalParam.getFileDownloadHtval());
			terminalParamJson.setIsWifi(terminalParam.getIsWifi() == null ? "":terminalParam.getIsWifi());
			terminalParamJson.setIsWifiDownload(terminalParam.getIsWifiDownload() == null ? "":terminalParam.getIsWifiDownload());
	        terminalParamJson.setIotActivateUrl(terminalParam.getIotActivateUrl() == null ? "":terminalParam.getIotActivateUrl());
	        terminalParamJson.setLockTerminalImgUrl(terminalParam.getLockTerminalImgUrl() == null ? "":terminalParam.getLockTerminalImgUrl());
			terminalParamJson.setSysUpRequiresDisplay(terminalParam.getSysUpRequiresDisplay());
			terminalParamJson.setSysUpRequiresVolume(terminalParam.getSysUpRequiresVolume());
			terminalParamJson.setSysUpRequiresVolumeValue(terminalParam.getSysUpRequiresVolumeValue());
			terminalParamJson.setInstallWhiteListQ7(terminalParam.getInstallWhiteListQ7());
			String paraVersion = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date(System.currentTimeMillis()));
			terminalParam.setParaVersion(paraVersion);
			String paraContent =  JsonUtils.objectToJson(terminalParamJson);
			terminalParam.setParaContent(paraContent);
		terminalParamMapper.updateByPrimaryKey(terminalParam);
		return ResultMsg.success();
		}
	}

	@Override
	public String queryAlarmInfoByCode(List<String> codesList) {
		List<TerminalAlarmSet> list = terminalAlarmSetMapper.queryAlarmInfoByCode(codesList);
		String ids = "";
		if(!CtUtils.isEmpty(list)){
			for(int i =0;i<list.size();i++){
				ids+=String.valueOf(list.get(i)).replace("{id=", "").replace("}", "")+",";
			}
			ids = ids.substring(0, ids.length()-1);
		}
		return ids;
	}

	@Override
	public TerminalParam getInstitutionParam(TerminalParam institutionParam) {
		return terminalParamMapper.selectByInstIdAndGroupId(institutionParam);
	}

	@Override
	public TerminalParam getInstitutionParamByInsList(int insId) {
		List<TerminalParam> paramList = terminalParamMapper.selectByCond(insId);
		if (paramList == null || paramList.size() == 0) {
			return null;
		}
		return paramList.get(0);
	}
}