package com.centerm.cpay.coms.dao.pojo;

public class TerminalParamJson {
    private String htIntvl;//心跳时间(秒)

    private String reHtIntvl;//心跳重连次数

    private String reDownNum;//文件传输重试次数

    private String fileTraTimeout;//文件传输超时时间

    private String upInfoIntvl;//信息上送间隔时间

    private String upFlowIntvl;//流量上送间隔时间

    private String mapPswd;//运维口令

    private String managePwd;//主管口令

    private String broadcastTime;//广播间隔时间(秒)

    private String fileDownloadHtval;//文件下载间隔时间

    private String systemUpdateRequiredPower;//系统更新电量阀值

    private String appUpdateRequiredPower;//应用更新电量阀值

    private String sysUpRequiresNoOperTime;//系统更新闲置时间

    private String appUpRequiresNoOperTime;//应用更新闲置时间

    private String tmsDomainName;//域名
    
    private String tmsDomainNameBak;//域名
    
    private String tmsDomainNameBakFirst;//域名备机2
    
    private String tmsDomainNameBakSecond;//域名备机3

    private String amsDomainName;

    private String alarmInfoCode;
    
    private String isWifi;
    
    private String iotActivateUrl;
    
    private String lockTerminalImgUrl;

    private String locatingInterval;//终端定位频率

    private String enableLocation;//终端是否开启定位

    private String isWifiDownload;

    private String sysUpRequiresDisplay;

    private String sysUpRequiresVolume;

    private Integer sysUpRequiresVolumeValue;

    private String installWhiteListQ7;

    public String getInstallWhiteListQ7() {
        return installWhiteListQ7;
    }

    public void setInstallWhiteListQ7(String installWhiteListQ7) {
        this.installWhiteListQ7 = installWhiteListQ7;
    }

    public String getIsWifiDownload() {
        return isWifiDownload;
    }

    public void setIsWifiDownload(String isWifiDownload) {
        this.isWifiDownload = isWifiDownload;
    }

    public String getLocatingInterval() {
        return locatingInterval;
    }

    public void setLocatingInterval(String locatingInterval) {
        this.locatingInterval = locatingInterval;
    }

    public String getEnableLocation() {
        return enableLocation;
    }

    public void setEnableLocation(String enableLocation) {
        this.enableLocation = enableLocation;
    }

    public String getTmsDomainNameBakFirst() {
        return tmsDomainNameBakFirst;
    }

    public void setTmsDomainNameBakFirst(String tmsDomainNameBakFirst) {
        this.tmsDomainNameBakFirst = tmsDomainNameBakFirst;
    }

    public String getTmsDomainNameBakSecond() {
        return tmsDomainNameBakSecond;
    }

    public void setTmsDomainNameBakSecond(String tmsDomainNameBakSecond) {
        this.tmsDomainNameBakSecond = tmsDomainNameBakSecond;
    }

    public String getReHtIntvl() {
        return reHtIntvl;
    }

    public void setReHtIntvl(String reHtIntvl) {
        this.reHtIntvl = reHtIntvl;
    }

    public String getAmsDomainName() {
        return amsDomainName;
    }

    public void setAmsDomainName(String amsDomainName) {
        this.amsDomainName = amsDomainName;
    }

    public String getAlarmInfoCode() {
        return alarmInfoCode;
    }

    public void setAlarmInfoCode(String alarmInfoCode) {
        this.alarmInfoCode = alarmInfoCode;
    }

    public String getHtIntvl() {
        return htIntvl;
    }

    public void setHtIntvl(String htIntvl) {
        this.htIntvl = htIntvl;
    }

    public String getReDownNum() {
        return reDownNum;
    }

    public void setReDownNum(String reDownNum) {
        this.reDownNum = reDownNum;
    }
    public String getFileTraTimeout() {
		return fileTraTimeout;
	}

	public void setFileTraTimeout(String fileTraTimeout) {
		this.fileTraTimeout = fileTraTimeout;
	}

	public String getUpInfoIntvl() {
        return upInfoIntvl;
    }

    public void setUpInfoIntvl(String upInfoIntvl) {
        this.upInfoIntvl = upInfoIntvl;
    }

    public String getUpFlowIntvl() {
        return upFlowIntvl;
    }

    public void setUpFlowIntvl(String upFlowIntvl) {
        this.upFlowIntvl = upFlowIntvl;
    }

    public String getMapPswd() {
        return mapPswd;
    }

    public void setMapPswd(String mapPswd) {
        this.mapPswd = mapPswd;
    }

    public String getManagePwd() {
        return managePwd;
    }

    public void setManagePwd(String managePwd) {
        this.managePwd = managePwd;
    }

    public String getBroadcastTime() {
        return broadcastTime;
    }

    public void setBroadcastTime(String broadcastTime) {
        this.broadcastTime = broadcastTime;
    }

    public String getSystemUpdateRequiredPower() {
        return systemUpdateRequiredPower;
    }

    public void setSystemUpdateRequiredPower(String systemUpdateRequiredPower) {
        this.systemUpdateRequiredPower = systemUpdateRequiredPower;
    }

    public String getAppUpdateRequiredPower() {
        return appUpdateRequiredPower;
    }

    public void setAppUpdateRequiredPower(String appUpdateRequiredPower) {
        this.appUpdateRequiredPower = appUpdateRequiredPower;
    }

    public String getSysUpRequiresNoOperTime() {
        return sysUpRequiresNoOperTime;
    }

    public void setSysUpRequiresNoOperTime(String sysUpRequiresNoOperTime) {
        this.sysUpRequiresNoOperTime = sysUpRequiresNoOperTime;
    }

    public String getAppUpRequiresNoOperTime() {
        return appUpRequiresNoOperTime;
    }

    public void setAppUpRequiresNoOperTime(String appUpRequiresNoOperTime) {
        this.appUpRequiresNoOperTime = appUpRequiresNoOperTime;
    }

    public String getTmsDomainName() {
        return tmsDomainName;
    }

    public void setTmsDomainName(String tmsDomainName) {
        this.tmsDomainName = tmsDomainName;
    }

	public String getFileDownloadHtval() {
		return fileDownloadHtval;
	}

	public void setFileDownloadHtval(String fileDownloadHtval) {
		this.fileDownloadHtval = fileDownloadHtval;
	}

	public String getIsWifi() {
		return isWifi;
	}

	public void setIsWifi(String isWifi) {
		this.isWifi = isWifi;
	}

	public String getTmsDomainNameBak() {
		return tmsDomainNameBak;
	}

	public void setTmsDomainNameBak(String tmsDomainNameBak) {
		this.tmsDomainNameBak = tmsDomainNameBak;
	}

    public String getIotActivateUrl() {
        return iotActivateUrl;
    }

    public void setIotActivateUrl(String iotActivateUrl) {
        this.iotActivateUrl = iotActivateUrl;
    }

    public String getLockTerminalImgUrl() {
        return lockTerminalImgUrl;
    }

    public void setLockTerminalImgUrl(String lockTerminalImgUrl) {
        this.lockTerminalImgUrl = lockTerminalImgUrl;
    }

    public String getSysUpRequiresDisplay() {
        return sysUpRequiresDisplay;
    }

    public void setSysUpRequiresDisplay(String sysUpRequiresDisplay) {
        this.sysUpRequiresDisplay = sysUpRequiresDisplay;
    }

    public String getSysUpRequiresVolume() {
        return sysUpRequiresVolume;
    }

    public void setSysUpRequiresVolume(String sysUpRequiresVolume) {
        this.sysUpRequiresVolume = sysUpRequiresVolume;
    }

    public Integer getSysUpRequiresVolumeValue() {
        return sysUpRequiresVolumeValue;
    }

    public void setSysUpRequiresVolumeValue(Integer sysUpRequiresVolumeValue) {
        this.sysUpRequiresVolumeValue = sysUpRequiresVolumeValue;
    }
}