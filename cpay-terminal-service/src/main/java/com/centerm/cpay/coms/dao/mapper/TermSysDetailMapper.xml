<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TermSysDetailMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TermSysDetail" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_id" property="termId" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="comm_para_version" property="commParaVersion" jdbcType="VARCHAR" />
    <result column="launcher_para_version" property="launcherParaVersion" jdbcType="VARCHAR" />
    <result column="os_version" property="osVersion" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    <result column="tms_sdk" property="tmsSDK" jdbcType="VARCHAR" />
    <result column="pay_sdk" property="paySDK" jdbcType="VARCHAR" />
    <result column="emv_ver" property="emvVer" jdbcType="VARCHAR" />
    <result column="pay_app_code" property="payAppCode" jdbcType="VARCHAR" />
    <result column="pay_app_name" property="payAppName" jdbcType="VARCHAR" />
    <result column="pay_app_version" property="payAppVersion" jdbcType="VARCHAR" />
    <result column="tms_app_version" property="tmsAppVersion" jdbcType="VARCHAR" />
    <result column="pay_app_version_outside" property="payAppVersionOutSide" jdbcType="VARCHAR" />
    <result column="tms_app_version_outside" property="tmsAppVersionOutSide" jdbcType="VARCHAR" />
    <result column="safe_mod_ver" property="safeModVer" jdbcType="VARCHAR" />
    <result column="os_ver" property="osVer" jdbcType="VARCHAR" />
    <result column="android_ver" property="androidVer" jdbcType="VARCHAR" />
    <result column="network_type" property="networkType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, term_id, term_seq, comm_para_version, launcher_para_version, os_version, update_time,
    tms_sdk, pay_sdk, emv_ver, pay_app_code, pay_app_name, pay_app_version, pay_app_version_outside,
    safe_mod_ver, os_ver, android_ver,tms_app_version, tms_app_version_outside,network_type
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from coms_terminal_sysdetail
    where term_id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_terminal_sysdetail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TermSysDetail" >
    insert into coms_terminal_sysdetail ( term_id, term_seq,
      comm_para_version, launcher_para_version, os_version,
      update_time, tms_sdk, pay_sdk,
      emv_ver, pay_app_code, pay_app_name,
      pay_app_version, pay_app_version_outside, safe_mod_ver,
      os_ver, android_ver)
    values (#{termId,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR},
      #{commParaVersion,jdbcType=VARCHAR}, #{launcherParaVersion,jdbcType=VARCHAR}, #{osVersion,jdbcType=VARCHAR},
      #{updateTime,jdbcType=VARCHAR}, #{tmsSDK,jdbcType=VARCHAR}, #{paySDK,jdbcType=VARCHAR},
      #{emvVer,jdbcType=VARCHAR}, #{payAppCode,jdbcType=VARCHAR}, #{payAppName,jdbcType=VARCHAR},
      #{payAppVersion,jdbcType=VARCHAR}, #{payAppVersionOutSide,jdbcType=VARCHAR}, #{safeModVer,jdbcType=VARCHAR},
      #{osVer,jdbcType=VARCHAR}, #{androidVer,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TermSysDetail" >
    insert into coms_terminal_sysdetail
    <trim prefix="(" suffix=")" suffixOverrides="," >

      <if test="termId != null" >
        term_id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="commParaVersion != null" >
        comm_para_version,
      </if>
      <if test="launcherParaVersion != null" >
        launcher_para_version,
      </if>
      <if test="osVersion != null" >
        os_version,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="tmsSDK != null" >
        tms_sdk,
      </if>
      <if test="paySDK != null" >
        pay_sdk,
      </if>
      <if test="emvVer != null" >
        emv_ver,
      </if>
      <if test="payAppCode != null" >
        pay_app_code,
      </if>
      <if test="payAppName != null" >
        pay_app_name,
      </if>
      <if test="payAppVersion != null" >
        pay_app_version,
      </if>
      <if test="payAppVersionOutSide != null" >
        pay_app_version_outside,
      </if>
      <if test="safeModVer != null" >
        safe_mod_ver,
      </if>
      <if test="osVer != null" >
        os_ver,
      </if>
      <if test="androidVer != null" >
        android_ver,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

      <if test="termId != null" >
        #{termId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="commParaVersion != null" >
        #{commParaVersion,jdbcType=VARCHAR},
      </if>
      <if test="launcherParaVersion != null" >
        #{launcherParaVersion,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null" >
        #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="tmsSDK != null" >
        #{tmsSDK,jdbcType=VARCHAR},
      </if>
      <if test="paySDK != null" >
        #{paySDK,jdbcType=VARCHAR},
      </if>
      <if test="emvVer != null" >
        #{emvVer,jdbcType=VARCHAR},
      </if>
      <if test="payAppCode != null" >
        #{payAppCode,jdbcType=VARCHAR},
      </if>
      <if test="payAppName != null" >
        #{payAppName,jdbcType=VARCHAR},
      </if>
      <if test="payAppVersion != null" >
        #{payAppVersion,jdbcType=VARCHAR},
      </if>
      <if test="payAppVersionOutSide != null" >
        #{payAppVersionOutSide,jdbcType=VARCHAR},
      </if>
      <if test="safeModVer != null" >
        #{safeModVer,jdbcType=VARCHAR},
      </if>
      <if test="osVer != null" >
        #{osVer,jdbcType=VARCHAR},
      </if>
      <if test="androidVer != null" >
        #{androidVer,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TermSysDetail" >
    update coms_terminal_sysdetail
    <set >
      <if test="termId != null" >
        term_id = #{termId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="commParaVersion != null" >
        comm_para_version = #{commParaVersion,jdbcType=VARCHAR},
      </if>
      <if test="launcherParaVersion != null" >
        launcher_para_version = #{launcherParaVersion,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null" >
        os_version = #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="tmsSDK != null" >
        tms_sdk = #{tmsSDK,jdbcType=VARCHAR},
      </if>
      <if test="paySDK != null" >
        pay_sdk = #{paySDK,jdbcType=VARCHAR},
      </if>
      <if test="emvVer != null" >
        emv_ver = #{emvVer,jdbcType=VARCHAR},
      </if>
      <if test="payAppCode != null" >
        pay_app_code = #{payAppCode,jdbcType=VARCHAR},
      </if>
      <if test="payAppName != null" >
        pay_app_name = #{payAppName,jdbcType=VARCHAR},
      </if>
      <if test="payAppVersion != null" >
        pay_app_version = #{payAppVersion,jdbcType=VARCHAR},
      </if>
      <if test="payAppVersionOutSide != null" >
        pay_app_version_outside = #{payAppVersionOutSide,jdbcType=VARCHAR},
      </if>
      <if test="safeModVer != null" >
        safe_mod_ver = #{safeModVer,jdbcType=VARCHAR},
      </if>
      <if test="osVer != null" >
        os_ver = #{osVer,jdbcType=VARCHAR},
      </if>
      <if test="androidVer != null" >
        android_ver = #{androidVer,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TermSysDetail" >
    update coms_terminal_sysdetail
    set term_id = #{termId,jdbcType=INTEGER},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      comm_para_version = #{commParaVersion,jdbcType=VARCHAR},
      launcher_para_version = #{launcherParaVersion,jdbcType=VARCHAR},
      os_version = #{osVersion,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR},
      tms_sdk = #{tmsSDK,jdbcType=VARCHAR},
      pay_sdk = #{paySDK,jdbcType=VARCHAR},
      emv_ver = #{emvVer,jdbcType=VARCHAR},
      pay_app_code = #{payAppCode,jdbcType=VARCHAR},
      pay_app_name = #{payAppName,jdbcType=VARCHAR},
      pay_app_version = #{payAppVersion,jdbcType=VARCHAR},
      pay_app_version_outside = #{payAppVersionOutSide,jdbcType=VARCHAR},
      safe_mod_ver = #{safeModVer,jdbcType=VARCHAR},
      os_ver = #{osVer,jdbcType=VARCHAR},
      android_ver = #{androidVer,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOsVer" resultType="java.lang.String" parameterType="java.lang.String">
    select os_ver from COMS_TERMINAL_SYSDETAIL where TERM_SEQ = #{termSeq,jdbcType=VARCHAR}
  </select>
</mapper>