package com.centerm.cpay.coms.dao.pojo;

public class TermSysDetail {
    private Integer id;

    private Integer termId;

    private String termSeq;

    private String sysDetail;

    private String commParaVersion;

    private String launcherParaVersion;

    private String osVersion;

    private String updateTime;
    
    private String osVer;
    
    private String safeModVer;
    
    private String pbocVer;
    
    private String M3Ver;
    
    private String middlewareVer;
    
    private String platformVer;
    
    private String androidVer;
    
    private String paramVer;
    
    private String blueTooth;
    
    private String paySDK;
    private String tmsSDK;
    private String emvVer;
    private String payAppCode;
    private String payAppName;
    private String payAppVersion;
    private String payAppVersionOutSide;
    private String tmsAppVersion;
    private String tmsAppVersionOutSide;
    private String networkType;

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }

    public String getTmsAppVersion() {
        return tmsAppVersion;
    }

    public void setTmsAppVersion(String tmsAppVersion) {
        this.tmsAppVersion = tmsAppVersion;
    }

    public String getTmsAppVersionOutSide() {
        return tmsAppVersionOutSide;
    }

    public void setTmsAppVersionOutSide(String tmsAppVersionOutSide) {
        this.tmsAppVersionOutSide = tmsAppVersionOutSide;
    }

    public String getPaySDK() {
        return paySDK;
    }

    public void setPaySDK(String paySDK) {
        this.paySDK = paySDK;
    }

    public String getTmsSDK() {
        return tmsSDK;
    }

    public void setTmsSDK(String tmsSDK) {
        this.tmsSDK = tmsSDK;
    }

    public String getPayAppCode() {
        return payAppCode;
    }

    public void setPayAppCode(String payAppCode) {
        this.payAppCode = payAppCode;
    }

    public String getPayAppName() {
        return payAppName;
    }

    public void setPayAppName(String payAppName) {
        this.payAppName = payAppName;
    }

    public String getPayAppVersion() {
        return payAppVersion;
    }

    public void setPayAppVersion(String payAppVersion) {
        this.payAppVersion = payAppVersion;
    }

    public String getPayAppVersionOutSide() {
        return payAppVersionOutSide;
    }

    public void setPayAppVersionOutSide(String payAppVersionOutSide) {
        this.payAppVersionOutSide = payAppVersionOutSide;
    }

    public String getEmvVer() {
		return emvVer;
	}

	public void setEmvVer(String emvVer) {
		this.emvVer = emvVer;
	}

    
    public String getParamVer() {
		return paramVer;
	}

	public void setParamVer(String paramVer) {
		this.paramVer = paramVer;
	}

	public String getOsVer() {
		return osVer;
	}

	public void setOsVer(String osVer) {
		this.osVer = osVer;
	}

	public String getSafeModVer() {
		return safeModVer;
	}

	public void setSafeModVer(String safeModVer) {
		this.safeModVer = safeModVer;
	}

	public String getPbocVer() {
		return pbocVer;
	}

	public void setPbocVer(String pbocVer) {
		this.pbocVer = pbocVer;
	}

	public String getM3Ver() {
		return M3Ver;
	}

	public void setM3Ver(String m3Ver) {
		M3Ver = m3Ver;
	}

	public String getMiddlewareVer() {
		return middlewareVer;
	}

	public void setMiddlewareVer(String middlewareVer) {
		this.middlewareVer = middlewareVer;
	}

	public String getPlatformVer() {
		return platformVer;
	}

	public void setPlatformVer(String platformVer) {
		this.platformVer = platformVer;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTermId() {
        return termId;
    }

    public void setTermId(Integer termId) {
        this.termId = termId;
    }

    public String getTermSeq() {
        return termSeq;
    }

    public void setTermSeq(String termSeq) {
        this.termSeq = termSeq == null ? null : termSeq.trim();
    }

    public String getSysDetail() {
        return sysDetail;
    }

    public void setSysDetail(String sysDetail) {
        this.sysDetail = sysDetail == null ? null : sysDetail.trim();
    }

    public String getCommParaVersion() {
        return commParaVersion;
    }

    public void setCommParaVersion(String commParaVersion) {
        this.commParaVersion = commParaVersion == null ? null : commParaVersion.trim();
    }

    public String getLauncherParaVersion() {
        return launcherParaVersion;
    }

    public void setLauncherParaVersion(String launcherParaVersion) {
        this.launcherParaVersion = launcherParaVersion == null ? null : launcherParaVersion.trim();
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion == null ? null : osVersion.trim();
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime == null ? null : updateTime.trim();
    }

	public String getAndroidVer() {
		return androidVer;
	}

	public void setAndroidVer(String androidVer) {
		this.androidVer = androidVer;
	}

	public String getBlueTooth() {
		return blueTooth;
	}

	public void setBlueTooth(String blueTooth) {
		this.blueTooth = blueTooth;
	}
    
}