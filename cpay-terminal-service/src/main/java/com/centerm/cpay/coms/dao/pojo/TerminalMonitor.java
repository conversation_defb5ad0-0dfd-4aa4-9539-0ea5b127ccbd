package com.centerm.cpay.coms.dao.pojo;

public class TerminalMonitor {
    private Long id;

    private String termSeqId;

    private String longitude;

    private String latitude;

    private String recCrtTm;
    
	private String expInfoList;
	
	private String insId;

	private String city;
	
	private String count;
	
	private String province;
	
	private String county;
	
	private String address;
	
	private String termStatus;
	
	private String removeStatus;
	
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTermSeqId() {
		return termSeqId;
	}

	public void setTermSeqId(String termSeqId) {
		this.termSeqId = termSeqId;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getRecCrtTm() {
		return recCrtTm;
	}

	public void setRecCrtTm(String recCrtTm) {
		this.recCrtTm = recCrtTm;
	}

	public String getInsId() {
		return insId;
	}

	public void setInsId(String insId) {
		this.insId = insId;
	}

	public String getExpInfoList() {
		return expInfoList;
	}

	public void setExpInfoList(String expInfoList) {
		this.expInfoList = expInfoList;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}
	
	public String getTermStatus() {
		return termStatus;
	}

	public void setTermStatus(String termStatus) {
		this.termStatus = termStatus;
	}

	public String getRemoveStatus() {
		return removeStatus;
	}

	public void setRemoveStatus(String removeStatus) {
		this.removeStatus = removeStatus;
	}

}
