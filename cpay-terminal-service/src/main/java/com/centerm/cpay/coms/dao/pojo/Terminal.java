package com.centerm.cpay.coms.dao.pojo;

import java.util.Date;

public class Terminal {
	private Integer id;

	private String termSeq;

	private Integer insId;

	private Integer termMfrId;

	private String termTypeCode;

	private String longitude;

	private String latitude;

	private String imei;

	private String netMark;

	private String imsi;

	private String province;

	private String city;

	private String county;

	private String address;

	private int status;

	private Date createTime;
	private String createTimeStr;

	private String termMfrName;

	private String termTypeName;

	private String insName;

	private String linkMan;

	private String linkPhone;

	private String statusDetail;// 0:停用 ,1:禁用

	private Integer terminalGroupId;// 终端组id

	private String groupName;// 终端组名称

	private String industryType;// 行业类别

	private Date productionTime;// 出厂日期

	private String proTimeStr;

	private String validationCode;

	private String merchantId;
	
	private String terminalTypeId;
	
	private String auto;//自助激活
	

	
	private String power;//终端电量
	
	private String onlineStatus;//在线离线状态 0在线，1。离线
	
	private String removeStatus;//移机状态,0正常，1移机
	

	
	private String lockStatus;//锁机状态，1-锁机，0-解锁
	// 费数据库字段
	private String hyName;// 行业名称

	private String termTypeCodes;

	private String termStatus;// 终端激活状态

	private String merchantStatus;// 商户认证状态

	//终端类型code值 详见字典
	private String activateType;
	private String activateName;
	private String activateTypes;
	//接收终端类型name值
	private String terminalTypeName;

	private String payMerchantName;

	//终端编号
	private String termNo;
	/**
	 * 商户信息表字段
	 */
	//商户编号(终端信息表商户表共同拥有该字段)
	private String payMerchantNo;
	//商户名称
	private String merchantName;
	//商户手机号
	private String applyerPhone;
	//商户姓名
	private String applyerName;
	//是否支持dcc
	private String dccSupFlag;
	//银联间直连标志
	private String cupConnMode;
	//业务类型
	private String bussType;
	//终端状态查询条件
	private String latestAccessStartTime;
	private String activateStatus;//激活状态
	private String latestAccessTime;//最后一次tms请求时间

	private String timeZone;//时区
	private String remark;//备注TID
	
	private String networkStatus;
	
	private String hardwareStatBmp;

    public String getNetworkStatus() {
        return networkStatus;
    }

    public void setNetworkStatus(String networkStatus) {
        this.networkStatus = networkStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

	public String getActivateStatus() {
		return activateStatus;
	}

	public void setActivateStatus(String activateStatus) {
		this.activateStatus = activateStatus;
	}

	public String getLatestAccessTime() {
		return latestAccessTime;
	}

	public void setLatestAccessTime(String latestAccessTime) {
		this.latestAccessTime = latestAccessTime;
	}

	public String getLatestAccessStartTime() {
		return latestAccessStartTime;
	}

	public void setLatestAccessStartTime(String latestAccessStartTime) {
		this.latestAccessStartTime = latestAccessStartTime;
	}

	public String getLatestAccessEndTime() {
		return latestAccessEndTime;
	}

	public void setLatestAccessEndTime(String latestAccessEndTime) {
		this.latestAccessEndTime = latestAccessEndTime;
	}

	private String latestAccessEndTime;

	public String getCupConnMode() {
		return cupConnMode;
	}

	public void setCupConnMode(String cupConnMode) {
		this.cupConnMode = cupConnMode;
	}

	public String getBussType() {
		return bussType;
	}

	public void setBussType(String bussType) {
		this.bussType = bussType;
	}

	public String getActivateName() {
		return activateName;
	}

	public void setActivateName(String activateName) {
		this.activateName = activateName;
	}

	public String getActivateTypes() {
		return activateTypes;
	}

	public void setActivateTypes(String activateTypes) {
		this.activateTypes = activateTypes;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getDccSupFlag() {
		return dccSupFlag;
	}

	public void setDccSupFlag(String dccSupFlag) {
		this.dccSupFlag = dccSupFlag;
	}

	public String getTerminalTypeName() {
		return terminalTypeName;
	}

	public void setTerminalTypeName(String terminalTypeName) {
		this.terminalTypeName = terminalTypeName;
	}

	public final String getTermNo() {
		return termNo;
	}

	public final void setTermNo(String termNo) {
		this.termNo = termNo;
	}

	public String getValidationCode() {
		return validationCode;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public void setValidationCode(String validationCode) {
		this.validationCode = validationCode;
	}

	public String getProTimeStr() {
		return proTimeStr;
	}

	public void setProTimeStr(String proTimeStr) {
		this.proTimeStr = proTimeStr;
	}



	public final String getPayMerchantName() {
		return payMerchantName;
	}

	public final void setPayMerchantName(String payMerchantName) {
		this.payMerchantName = payMerchantName;
	}

	public final String getPayMerchantNo() {
		return payMerchantNo;
	}

	public final void setPayMerchantNo(String payMerchantNo) {
		this.payMerchantNo = payMerchantNo;
	}

	public String getTermStatus() {
		return termStatus;
	}

	public void setTermStatus(String termStatus) {
		this.termStatus = termStatus;
	}

	public String getHyName() {
		return hyName;
	}

	public void setHyName(String hyName) {
		this.hyName = hyName;
	}

	public String getIndustryType() {
		return industryType;
	}

	public void setIndustryType(String industryType) {
		this.industryType = industryType;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getTerminalGroupId() {
		return terminalGroupId;
	}

	public void setTerminalGroupId(Integer terminalGroupId) {
		this.terminalGroupId = terminalGroupId;
	}

	public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}

	public String getStatusDetail() {
		return statusDetail;
	}

	public void setStatusDetail(String statusDetail) {
		this.statusDetail = statusDetail;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTermSeq() {
		return termSeq;
	}

	public void setTermSeq(String termSeq) {
		this.termSeq = termSeq;
	}

	public Integer getInsId() {
		return insId;
	}

	public void setInsId(Integer insId) {
		this.insId = insId;
	}

	public Integer getTermMfrId() {
		return termMfrId;
	}

	public void setTermMfrId(Integer termMfrId) {
		this.termMfrId = termMfrId;
	}

	public String getTermTypeCode() {
		return termTypeCode;
	}

	public void setTermTypeCode(String termTypeCode) {
		this.termTypeCode = termTypeCode;
	}

	public String getApplyerPhone() {
		return applyerPhone;
	}

	public void setApplyerPhone(String applyerPhone) {
		this.applyerPhone = applyerPhone;
	}

	public String getApplyerName() {
		return applyerName;
	}

	public void setApplyerName(String applyerName) {
		this.applyerName = applyerName;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public String getNetMark() {
		return netMark;
	}

	public void setNetMark(String netMark) {
		this.netMark = netMark;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getTermMfrName() {
		return termMfrName;
	}

	public void setTermMfrName(String termMfrName) {
		this.termMfrName = termMfrName;
	}

	public String getTermTypeName() {
		return termTypeName;
	}

	public void setTermTypeName(String termTypeName) {
		this.termTypeName = termTypeName;
	}

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public Date getProductionTime() {
		return productionTime;
	}

	public void setProductionTime(Date productionTime) {
		this.productionTime = productionTime;
	}

	public String getTermTypeCodes() {
		return termTypeCodes;
	}

	public void setTermTypeCodes(String termTypeCodes) {
		this.termTypeCodes = termTypeCodes;
	}

	public String getImsi() {
		return imsi;
	}

	public void setImsi(String imsi) {
		this.imsi = imsi;
	}

	public String getMerchantStatus() {
		return merchantStatus;
	}

	public void setMerchantStatus(String merchantStatus) {
		this.merchantStatus = merchantStatus;
	}

	public String getTerminalTypeId() {
		return terminalTypeId;
	}

	public void setTerminalTypeId(String terminalTypeId) {
		this.terminalTypeId = terminalTypeId;
	}

	public String getAuto() {
		return auto;
	}

	public void setAuto(String auto) {
		this.auto = auto;
	}

	public String getActivateType() {
		return activateType;
	}

	public void setActivateType(String activateType) {
		this.activateType = activateType;
	}

	public String getPower() {
		return power;
	}

	public void setPower(String power) {
		this.power = power;
	}

	public String getOnlineStatus() {
		return onlineStatus;
	}

	public void setOnlineStatus(String onlineStatus) {
		this.onlineStatus = onlineStatus;
	}

	public String getRemoveStatus() {
		return removeStatus;
	}

	public void setRemoveStatus(String removeStatus) {
		this.removeStatus = removeStatus;
	}

	public String getLockStatus() {
		return lockStatus;
	}

	public void setLockStatus(String lockStatus) {
		this.lockStatus = lockStatus;
	}

    public String getHardwareStatBmp() {
        return hardwareStatBmp;
    }

    public void setHardwareStatBmp(String hardwareStatBmp) {
        this.hardwareStatBmp = hardwareStatBmp;
    }



}