package com.centerm.cpay.coms.dao.pojo;

import java.util.Date;

public class SystemMessage {
    private Integer id;

    private String content;

    private Integer releaseInsid;

    private String releaseType;

    private Date createTime;

    private Integer releaseGroupId;

    private Integer insId;

    private String releaseTime;

    private String validDate;

    private String collection;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Integer getReleaseInsid() {
        return releaseInsid;
    }

    public void setReleaseInsid(Integer releaseInsid) {
        this.releaseInsid = releaseInsid;
    }

    public String getReleaseType() {
        return releaseType;
    }

    public void setReleaseType(String releaseType) {
        this.releaseType = releaseType == null ? null : releaseType.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getReleaseGroupId() {
        return releaseGroupId;
    }

    public void setReleaseGroupId(Integer releaseGroupId) {
        this.releaseGroupId = releaseGroupId;
    }

    public Integer getInsId() {
        return insId;
    }

    public void setInsId(Integer insId) {
        this.insId = insId;
    }

    public String getCollection() {
        return collection;
    }

    public void setCollection(String collection) {
        this.collection = collection == null ? null : collection.trim();
    }

	public String getReleaseTime() {
		return releaseTime;
	}

	public void setReleaseTime(String releaseTime) {
		this.releaseTime = releaseTime;
	}

	public String getValidDate() {
		return validDate;
	}

	public void setValidDate(String validDate) {
		this.validDate = validDate;
	}
    
}