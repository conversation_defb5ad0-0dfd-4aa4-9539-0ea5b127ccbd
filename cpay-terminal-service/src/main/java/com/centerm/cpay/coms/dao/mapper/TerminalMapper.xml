<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalMapper">
	<resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.Terminal">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
		<result column="ins_id" property="insId" jdbcType="INTEGER" />
		<result column="term_mfr_id" property="termMfrId" jdbcType="INTEGER" />
		<result column="term_type_code" property="termTypeCode"
				jdbcType="VARCHAR" />
		<result column="link_man" property="linkMan" jdbcType="VARCHAR" />
		<result column="link_phone" property="linkPhone" jdbcType="VARCHAR" />
		<result column="longitude" property="longitude" jdbcType="VARCHAR" />
		<result column="latitude" property="latitude" jdbcType="VARCHAR" />
		<result column="imei" property="imei" jdbcType="CHAR" />
		<result column="imsi" property="imsi" jdbcType="CHAR" />
		<result column="net_mark" property="netMark" jdbcType="CHAR" />
		<result column="province" property="province" jdbcType="VARCHAR" />
		<result column="city" property="city" jdbcType="VARCHAR" />
		<result column="county" property="county" jdbcType="VARCHAR" />
		<result column="address" property="address" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="INTEGER" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_time_str" property="createTimeStr" jdbcType="VARCHAR" />
		<result column="address" property="address" jdbcType="VARCHAR" />
		<result column="applyer_name" property="applyerName" jdbcType="VARCHAR" />
		<result column="applyer_phone" property="applyerPhone"
				jdbcType="VARCHAR" />
		<result column="ins_name" property="insName" jdbcType="VARCHAR" />
		<result column="merchant_name" property="merchantName"
				jdbcType="VARCHAR" />
		<result column="factory_name" property="termMfrName" jdbcType="VARCHAR" />
		<result column="term_type_name" property="termTypeName"
				jdbcType="VARCHAR" />
		<result column="status_detail" property="statusDetail"
				jdbcType="VARCHAR" />
		<result column="term_group_id" property="terminalGroupId"
				jdbcType="VARCHAR" />
		<result column="group_name" property="groupName" jdbcType="VARCHAR" />
		<result column="type" property="industryType" jdbcType="VARCHAR" />
		<result column="production_time" property="productionTime"
				jdbcType="DATE" />
		<result column="hyName" property="hyName" jdbcType="VARCHAR" />
		<result column="term_status" property="termStatus" jdbcType="CHAR" />
		<result column="merchant_status" property="merchantStatus"
				jdbcType="CHAR" />
		<result column="validate_code" property="validationCode"
				jdbcType="VARCHAR" />
		<result column="proTime_Str" property="proTimeStr" jdbcType="VARCHAR" />
		<result column="merchant_id" property="merchantId" jdbcType="INTEGER" />
		<result column="termType_id" property="terminalTypeId"
				jdbcType="INTEGER" />
		<result column="auto" property="auto" jdbcType="VARCHAR" />
		<result column="activate_type" property="activateType" jdbcType="CHAR" />
		<result column="activate_name" property="activateName" jdbcType="VARCHAR" />
		<result column="term_no" property="termNo" jdbcType="VARCHAR" />
		<result column="pay_merchant_no" property="payMerchantNo" jdbcType="VARCHAR" />
		<result column="pay_merchant_name" property="payMerchantName" jdbcType="VARCHAR" />
		<result column="terminal_type_name" property="terminalTypeName" jdbcType="VARCHAR" />
		<result column="dcc_sup_flag" property="dccSupFlag" jdbcType="CHAR" />
		<result column="cup_conn_mode" property="cupConnMode" jdbcType="CHAR" />
		<result column="buss_type" property="bussType" jdbcType="VARCHAR" />
		<result column="time_zone" property="timeZone" jdbcType="VARCHAR" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="network_status" property="networkStatus" jdbcType="VARCHAR" />
		<result column="hardware_stat_bmp" property="hardwareStatBmp" jdbcType="CHAR" />
	</resultMap>

	<resultMap type="com.centerm.cpay.coms.dao.pojo.TerminalCount"
			   id="AmountMap">
		<result column="termAmount" property="termAmount" />
		<result column="termMonth" property="termMonth" />
		<result column="actAmount" property="actAmount" />
		<result column="actMonth" property="actMonth" />
	</resultMap>
	
	<resultMap type="com.centerm.cpay.coms.dao.pojo.TermInfoReport"
			   id="TermInfoReportMap">
		<result column="terminalCount" property="terminalCount" />
		<result column="factoryName" property="factoryName" />
		<result column="month" property="month" />
	</resultMap>
	
	<select id="getCount" resultMap="AmountMap" parameterType="Integer">
		SELECT
		count(id) AS termAmount,
		sum(CASE when STATUS='1' then 1 else 0 end) AS actAmount,
		MONTH AS termMonth
		FROM
		(SELECT
		id,DATE_FORMAT(create_time,'%Y%m') as month,STATUS
		FROM
		coms_terminal_info A where
		A.ins_id in (SELECT
		s1.id from cpay_institution s1 where s1.detail like
		CONCAT('%',(SELECT
		s2.detail from cpay_institution s2 where s2.id=#{insId}),'%'))
		and DATE_FORMAT(create_time,'%Y-%m')>

		DATE_FORMAT(date_sub(curdate(),
		interval 12 month),'%Y-%m'))t
		GROUP BY month
	</select>
	<select id="getActCount" resultMap="AmountMap" parameterType="Integer">
		SELECT
		TO_CHAR(D.create_time,'%Y%m') actMonth,
		COUNT(id) actAmount
		FROM
		(
		SELECT
		B.id,
		C.id as tid,
		B.create_time
		FROM
		(
		SELECT
		id,
		create_time
		FROM
		coms_terminal_info A
		WHERE
		A.ins_id IN
		(
		SELECT
		s1.id
		FROM
		cpay_institution s1
		WHERE
		locate(
		(
		SELECT
		s2.detail
		FROM
		cpay_institution s2
		WHERE
		s2.id=#{insId}),s1.detail)>0 ))B
		LEFT JOIN
		COMS_TERMINAL_INFO C
		ON
		B.id = C.id
		WHERE
		C.status='1' )D
		WHERE
		create_time >=(CURRENT DATE-12 MONTH-DAY(CURRENT TIMESTAMP) days)
		GROUP BY
		TO_CHAR(D.create_time,'%Y%m')
	</select>
	<sql id="Base_Column_List">
		id, term_seq, ins_id, term_mfr_id, term_type_code,
		link_man, link_phone,
		longitude,
		latitude, imei,imsi, net_mark, province,
		city, county, address, status, create_time,activate_type,term_group_id,term_no,pay_merchant_no,dcc_sup_flag
	</sql>
	<select id="selectByCondition" resultMap="BaseResultMap"
			parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,a.time_zone,a.remark,a.network_status,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		d.DCC_SUP_FLAG,
		d.CUP_CONN_MODE,
		d.BUSS_TYPE,
		t.name as group_name,
		a.status AS activateStatus,
		c.latest_access_time AS latestAccessTime
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_dyn_info c on a.term_seq = c.term_seq
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID where 1=1
		<if test="id != null">
			and a.id = ${id}
		</if>
		<if test="termSeq != null and termSeq !=''">
			and a.term_seq LIKE '%${termSeq}%'
		</if>
		<if test="termNo != null and termNo !=''">
			and a.term_no LIKE '%${termNo}%'
		</if>
		<if test="remark != null and remark !=''">
			and a.remark LIKE '%${remark}%'
		</if>
		<if test="payMerchantNo != null and payMerchantNo !=''">
			and a.pay_merchant_no LIKE '%${payMerchantNo}%'
		</if>
		<if test="termMfrName != null and termMfrName != ''">
			and b.name LIKE '%${termMfrName}%'
		</if>
		<if test="termMfrId != null">
			and a.term_mfr_id=#{termMfrId,jdbcType=INTEGER}
		</if>
		<if test="termTypeCode != null and termTypeCode != ''">
			and a.term_type_code = #{termTypeCode,jdbcType=VARCHAR}
		</if>
		<if test="merchantName  != null and merchantName !=''">
			and d.name LIKE '%${merchantName}%'
		</if>
		<if test="applyerPhone  != null and applyerPhone !=''">
			and d.applyer_phone LIKE '%${applyerPhone}%'
		</if>
		<if test="industryType != null">
			and d.type = #{industryType,jdbcType=VARCHAR}
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and t.id = #{terminalGroupId,jdbcType=INTEGER}
		</if>
		<if test="termTypeCodes != null">
			and a.term_type_code in (${termTypeCodes})
		</if>
		<if test="terminalTypeId != null">
			and f.id=#{terminalTypeId,jdbcType=INTEGER}
		</if>
		<if test="activateType != null and activateType != ''">
			and a.activate_type= #{activateType,jdbcType=CHAR}
		</if>
		<if test="activateTypes != null and activateTypes != ''">
			and  a.activate_type in
			<foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="dccSupFlag != null and dccSupFlag != ''">
			and d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
		</if>
		<if test="cupConnMode != null and cupConnMode != ''">
			and d.CUP_CONN_MODE = #{cupConnMode,jdbcType=CHAR}
		</if>
		<if test="bussType != null and bussType != ''">
			and d.BUSS_TYPE = #{bussType,jdbcType=VARCHAR}
		</if>
		order by term_seq asc
	</select>
	<select id="selectByConditionExcel" resultMap="BaseResultMap"
			parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.INS_ID,a.time_zone,a.remark,a.network_status,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		d.applyer_phone,
		d.APPLYER_NAME,
		case d.DCC_SUP_FLAG
		when 'Y' then '是'
		when 'N' then '否'
		end as DCC_SUP_FLAG,
		t.name as group_name,
		cd.name as ACTIVATE_NAME,
		cd2.name as cup_conn_mode,
		cd3.name as buss_type
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join cpay_dict cd on cd.TYPE = a.ACTIVATE_TYPE and
		cd.PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'terminal_type')
		left join cpay_dict cd2 on cd2.TYPE = d.cup_conn_mode and
		cd2.PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'cup_conn_mode')
		left join cpay_dict cd3 on cd3.TYPE = d.buss_type and
		cd3.PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'buss_type')
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID where 1=1
		<if test="id != null">
			and a.id = ${id}
		</if>
		<if test="termSeq != null and termSeq !=''">
			and a.term_seq LIKE '%${termSeq}%'
		</if>
		<if test="termNo != null and termNo !=''">
			and a.term_no LIKE '%${termNo}%'
		</if>
		<if test="remark != null and remark !=''">
			and a.remark LIKE '%${remark}%'
		</if>
		<if test="payMerchantNo != null and payMerchantNo !=''">
			and a.pay_merchant_no LIKE '%${payMerchantNo}%'
		</if>
		<if test="termMfrName != null and termMfrName != ''">
			and b.name LIKE '%${termMfrName}%'
		</if>
		<if test="termMfrId != null">
			and a.term_mfr_id=#{termMfrId,jdbcType=INTEGER}
		</if>
		<if test="termTypeCode != null and termTypeCode != ''">
			and a.term_type_code = #{termTypeCode,jdbcType=VARCHAR}
		</if>
		<if test="merchantName  != null and merchantName !=''">
			and d.name LIKE '%${merchantName}%'
		</if>
		<if test="applyerPhone  != null and applyerPhone !=''">
			and d.applyer_phone LIKE '%${applyerPhone}%'
		</if>
		<if test="industryType != null">
			and d.type = #{industryType,jdbcType=VARCHAR}
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and t.id = #{terminalGroupId,jdbcType=INTEGER}
		</if>
		<if test="termTypeCodes != null">
			and a.term_type_code in (${termTypeCodes})
		</if>
		<if test="terminalTypeId != null">
			and f.id=#{terminalTypeId,jdbcType=INTEGER}
		</if>
		<if test="activateType != null and activateType != ''">
			and a.activate_type= #{activateType,jdbcType=CHAR}
		</if>
		<if test="dccSupFlag != null and dccSupFlag != ''">
			and d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
		</if>
		<if test="cupConnMode != null and cupConnMode != ''">
			and d.cup_conn_mode = #{cupConnMode,jdbcType=CHAR}
		</if>
		<if test="bussType != null and bussType != ''">
			and d.buss_type = #{bussType,jdbcType=VARCHAR}
		</if>
		order by term_seq asc
	</select>

	<select id="selectTerminalByNoGroupId" resultMap="BaseResultMap"
			parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,a.time_zone,a.remark,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		d.DCC_SUP_FLAG
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join COMS_TERMINAL_GROUP g on g.id = #{terminalGroupId}
		where 1=1
		and a.TERM_GROUP_ID = '0'
		<if test="id != null">
			and a.id = ${id}
		</if>
		<if test="termSeq != null and termSeq !=''">
			and a.term_seq LIKE concat(concat('%',#{termSeq}),'%')
		</if>
		<if test="termNo != null and termNo !=''">
			and a.term_no LIKE concat(concat('%',#{termNo}),'%')
		</if>
		<if test="remark != null and remark !=''">
			and a.remark LIKE concat('%',#{remark},'%')
		</if>

		<if test="termTypeCode != null and termTypeCode != ''">
			and f.code = #{termTypeCode,jdbcType=VARCHAR}
		</if>
		<if test="termTypeName != null and termTypeName != ''">
			and f.name LIKE concat(concat('%',#{termTypeName}),'%')
		</if>
		<if test="payMerchantNo  != null and payMerchantNo !=''">
			and a.pay_merchant_no LIKE concat(concat('%',#{payMerchantNo}),'%')
		</if>
		<if test="merchantName  != null and merchantName !=''">
			and d.name LIKE concat(concat('%',#{merchantName}),'%')
		</if>
		<if test="activateType != null and activateType != ''">
			and a.activate_type=#{activateType,jdbcType=CHAR}
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=g.ins_id),s1.detail)>0
			or
			locate(s1.detail,(SELECT s2.detail from cpay_institution
			s2 where
			s2.id=g.ins_id))>0)
		</if>
		order by term_seq asc
	</select>

	<select id="selectTerminalByGroupId" resultMap="BaseResultMap"
			parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,a.time_zone,a.remark,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		d.DCC_SUP_FLAG,
		t.name as group_name
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		where 1=1
		and a.term_group_id !='0'
		<if test="id != null">
			and a.id = ${id}
		</if>
		<if test="termSeq != null and termSeq != ''">
			and a.term_seq LIKE concat(concat('%',#{termSeq}),'%')
		</if>
		<if test="remark != null and remark != ''">
			and a.remark LIKE concat('%',#{remark},'%')
		</if>
		<if test="termNo != null and termNo != ''">
			and a.term_no LIKE concat(concat('%',#{termNo}),'%')
		</if>
		<if test="imei != null and imei != ''">
			and a.imei LIKE concat(concat('%',#{imei}),'%')
		</if>
		<if test="termMfrName != null and termMfrName != ''">
			and b.name like concat(concat('%',#{termMfrName}),'%')
		</if>
		<if test="termTypeName != null and termTypeName != ''">
			and f.name LIKE concat(concat('%',#{termTypeName}),'%')
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and a.term_group_id = #{terminalGroupId,jdbcType=INTEGER}
		</if>
		<if test="payMerchantNo != null and payMerchantNo != ''">
			and a.pay_merchant_no LIKE concat(concat('%',#{payMerchantNo}),'%')
		</if>
		<if test="merchantName  != null and merchantName != ''">
			and d.name LIKE concat(concat('%',#{merchantName}),'%')
		</if>
		<if test="activateType != null and activateType != ''">
			and a.activate_type=#{activateType,jdbcType=CHAR}
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
		</if>
		order by term_seq asc
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from
		coms_terminal_info
		where id = #{id,jdbcType=INTEGER}
	</delete>
	<insert id="insert" useGeneratedKeys="true" keyProperty="id"
			parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		<!--新增production_time的字段的存储，初始化时为创建时间-->
		insert into coms_terminal_info (term_seq, ins_id,
		term_mfr_id, term_type_code, link_man,
		link_phone, longitude, latitude,
		imei, net_mark, province,
		city, county, address,
		status, create_time,PRODUCTION_TIME,activate_type,term_no,PAY_MERCHANT_NO,time_zone,remark)
		values (#{termSeq,jdbcType=VARCHAR},
		#{insId,jdbcType=INTEGER},
		#{termMfrId,jdbcType=INTEGER},
		#{termTypeCode,jdbcType=VARCHAR}, #{linkMan,jdbcType=VARCHAR},
		#{linkPhone,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR},
		#{latitude,jdbcType=VARCHAR},
		#{imei,jdbcType=CHAR},
		#{netMark,jdbcType=CHAR}, #{province,jdbcType=VARCHAR},
		#{city,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR},
		#{address,jdbcType=VARCHAR},
		#{status,jdbcType=TINYINT},
		#{createTime,jdbcType=TIMESTAMP},#{createTime,jdbcType=TIMESTAMP},#{activateType,jdbcType=CHAR},
		#{termNo,jdbcType=VARCHAR},#{payMerchantNo,jdbcType=VARCHAR},#{timeZone,jdbcType=VARCHAR},#{remark,jdbcType=VARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		insert into coms_terminal_info
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="termSeq != null">
				term_seq,
			</if>
			<if test="insId != null">
				ins_id,
			</if>
			<if test="termMfrId != null">
				term_mfr_id,
			</if>
			<if test="termTypeCode != null">
				term_type_code,
			</if>
			<if test="linkMan != null">
				link_man,
			</if>
			<if test="linkPhone != null">
				link_phone,
			</if>
			<if test="longitude != null">
				longitude,
			</if>
			<if test="latitude != null">
				latitude,
			</if>
			<if test="imei != null">
				imei,
			</if>
			<if test="netMark != null">
				net_mark,
			</if>
			<if test="province != null">
				province,
			</if>
			<if test="city != null">
				city,
			</if>
			<if test="county != null">
				county,
			</if>
			<if test="address != null">
				address,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="terminalGroupId != null">
				term_group_id,
			</if>
			<if test="termNo != null">
				term_no,
			</if>
			<if test="payMerchantNo != null">
				pay_merchant_no,
			</if>
			<if test="activateType != null">
				activate_type,
			</if>
			<if test="timeZone != null">
				time_zone,
			</if>
			<if test="remark != null">
				remark,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="termSeq != null">
				#{termSeq,jdbcType=VARCHAR},
			</if>
			<if test="insId != null">
				#{insId,jdbcType=INTEGER},
			</if>
			<if test="termMfrId != null">
				#{termMfrId,jdbcType=INTEGER},
			</if>
			<if test="termTypeCode != null">
				#{termTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="linkMan != null">
				#{linkMan,jdbcType=VARCHAR},
			</if>
			<if test="linkPhone != null">
				#{linkPhone,jdbcType=VARCHAR},
			</if>
			<if test="longitude != null">
				#{longitude,jdbcType=VARCHAR},
			</if>
			<if test="latitude != null">
				#{latitude,jdbcType=VARCHAR},
			</if>
			<if test="imei != null">
				#{imei,jdbcType=CHAR},
			</if>
			<if test="netMark != null">
				#{netMark,jdbcType=CHAR},
			</if>
			<if test="province != null">
				#{province,jdbcType=VARCHAR},
			</if>
			<if test="city != null">
				#{city,jdbcType=VARCHAR},
			</if>
			<if test="county != null">
				#{county,jdbcType=VARCHAR},
			</if>
			<if test="address != null">
				#{address,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=TINYINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="terminalGroupId != null">
				#{terminalGroupId,jdbcType=INTEGER},
			</if>
			<if test="termNo != null">
				#{termNo,jdbcType=VARCHAR},
			</if>
			<if test="payMerchantNo != null">
				#{payMerchantNo,jdbcType=VARCHAR},
			</if>
			<if test="activateType != null">
				#{activateType,jdbcType=VARCHAR},
			</if>
			<if test="timeZone != null">
				#{timeZone,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<select id="selectByPrimaryKey" resultMap="BaseResultMap"
			parameterType="java.lang.Integer">
		select
		a.*,
		DATE_FORMAT(CREATE_TIME,'%Y-%m-%d %H:%m:%s') as create_time_str,a.time_zone,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		d.applyer_phone,
		d.APPLYER_NAME,
		d.DCC_SUP_FLAG,
		d.CUP_CONN_MODE,
		d.BUSS_TYPE,
		t.name as group_name,
		a.imsi,
		a.imei,
		case a.status
		when 0 then 'Non-activated'
		when 1 then
		'Activated'
		end
		as term_status
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		where 1=1
		and a.id = #{id,jdbcType=INTEGER}
	</select>
	<update id="updateTermGroupId" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		update COMS_TERMINAL_INFO
		set
		 term_group_id = #{terminalGroupId,jdbcType=INTEGER},PRODUCTION_TIME = #{productionTime,jdbcType=TIMESTAMP}
		where 1=1 
		<if test="termSeq != null and termSeq != ''">
			and term_seq = #{termSeq,jdbcType=VARCHAR}
		</if>
		<if test="termNo != null">
			and term_no = #{termNo,jdbcType=VARCHAR}
		</if>
		<if test="payMerchantNo != null">
			and pay_merchant_no = #{payMerchantNo,jdbcType=VARCHAR}
		</if>
		and term_group_id =0
	</update>

	<update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		update coms_terminal_info
		<set>
			<if test="termSeq != null">
				term_seq = #{termSeq,jdbcType=VARCHAR},
			</if>
			<if test="insId != null">
				ins_id = #{insId,jdbcType=INTEGER},
			</if>
			<if test="termMfrId != null">
				term_mfr_id = #{termMfrId,jdbcType=INTEGER},
			</if>
			<if test="termTypeCode != null">
				term_type_code = #{termTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="imei != null">
				imei = #{imei,jdbcType=CHAR},
			</if>
			<if test="imsi != null">
				imsi = #{imsi,jdbcType=CHAR},
			</if>
			<if test="netMark != null">
				net_mark = #{netMark,jdbcType=CHAR},
			</if>
			<if test="province != null">
				province = #{province,jdbcType=VARCHAR},
			</if>
			<if test="city != null">
				city = #{city,jdbcType=VARCHAR},
			</if>
			<if test="county != null">
				county = #{county,jdbcType=VARCHAR},
			</if>
			<if test="address != null">
				address = #{address,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="terminalGroupId != null">
				term_group_id = #{terminalGroupId,jdbcType=INTEGER},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="networkStatus != null">
				network_status = #{networkStatus,jdbcType=VARCHAR},
			</if>
		</set>
		where term_seq = #{termSeq,jdbcType=VARCHAR}
	</update>
	<update id="updateTerminal" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		update coms_terminal_info
		<set>
			<if test="linkMan != null">
				link_man = #{linkMan,jdbcType=VARCHAR},
			</if>
			<if test="linkPhone != null">
				link_phone = #{linkPhone,jdbcType=VARCHAR},
			</if>
			<if test="productionTime != null">
				production_time = #{productionTime,jdbcType=DATE},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=DATE}
			</if>
		</set>
		where term_seq = #{termSeq,jdbcType=VARCHAR}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		update
		coms_terminal_info
		set term_seq = #{termSeq,jdbcType=VARCHAR},
		ins_id =
		#{insId,jdbcType=INTEGER},
		term_mfr_id = #{termMfrId,jdbcType=INTEGER},
		term_type_code = #{termTypeCode,jdbcType=VARCHAR},
		link_man =
		#{linkMan,jdbcType=VARCHAR},
		link_phone =
		#{linkPhone,jdbcType=VARCHAR},
		longitude =
		#{longitude,jdbcType=VARCHAR},
		latitude = #{latitude,jdbcType=VARCHAR},
		imei = #{imei,jdbcType=CHAR},
		imsi = #{imsi,jdbcType=CHAR},
		net_mark =
		#{netMark,jdbcType=CHAR},
		province = #{province,jdbcType=VARCHAR},
		city
		= #{city,jdbcType=VARCHAR},
		county = #{county,jdbcType=VARCHAR},
		address = #{address,jdbcType=VARCHAR},
		status =
		#{status,jdbcType=TINYINT},
		remark = #{remark,jdbcType=VARCHAR},
		create_time =
		#{createTime,jdbcType=TIMESTAMP}
		where id = #{id,jdbcType=INTEGER}
	</update>
	<select id="selectByTermSeq" resultMap="BaseResultMap"
			parameterType="java.lang.String">
		select
		t.*
		from coms_terminal_info t
		where
		t.term_seq = #{termSeq,jdbcType=VARCHAR}
	</select>
	<select id="selectByImei" resultMap="BaseResultMap"
			parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from coms_terminal_info t
		where t.imei like concat(concat('%',#{imei}),'%')
		limit 1
	</select>

	<insert id="addLog" parameterType="java.util.Map">
		insert into cpay_log
		(user_name,module,action,op_desc,op_time)
		values(#{userName},#{module},#{action},#{opDesc},#{opTime})
	</insert>
	<select id="queryTermBelong" resultMap="BaseResultMap"
		parameterType="java.util.Map">
		SELECT
		a.id,
		a.term_seq,
		e. NAME AS ins_name,
		a.imei,
		a.imsi,
		a.net_mark,
		a.production_time,
		d.NAME AS merchant_name,
		d.applyer_phone,
		a.province,
		a.city,
		a.county,
		a.ADDRESS
		from
		coms_terminal_info a
		left join coms_merchant_info d ON a.PAY_MERCHANT_NO = d.MERCHANT_NO
		left join cpay_institution e on a.ins_id = e.id
		where 1 = 1
		and a.ins_id IN (
		select
		s1.id
		from
		cpay_institution s1
		where
		s1.detail LIKE
		CONCAT(
		'%',
		(SELECT
		s2.detail
		FROM
		cpay_institution s2
		where s2.id=#{insId}
		),
		'%'
		)
		) and
		a.id = #{id}
	</select>
	<select id="queryTermOrgAuto" resultType="java.lang.String"
			parameterType="java.lang.Integer">
		select auto from cpay_institution where id=(select
		ins_id from coms_terminal_info where id=#{id,jdbcType=INTEGER})
	</select>
	<select id="queryTermOrgMerExist" resultType="java.lang.Integer"
			parameterType="java.lang.Integer">
		select
		if(default_merchant_id is
		null,0,default_merchant_id)
		as default_merchant_id
		from cpay_institution
		where id=(select ins_id from coms_terminal_info
		where
		id=#{id,jdbcType=INTEGER})
	</select>

	<select id="selectByMerchantId" resultType="java.lang.Integer"
			parameterType="java.lang.Integer">
		select
		t.id
		from coms_merchant_info
		t,cpay_institution b
		where t.id = #{id,jdbcType=INTEGER}
		and t.access_id
		= b.id
	</select>
	<select id="getDefMerchant" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select default_merchant_id from cpay_institution t where id=#{insId,jdbcType=VARCHAR}
	</select>
	<insert id="insertTestAutoRel" parameterType="java.util.Map">
		insert into coms_terminal_merchant(
		term_id,term_seq,merchant_id,term_status,warranty_period,merchant_status,term_group_id) 
		values(
		#{termId,jdbcType=INTEGER},
		#{termSeq,jdbcType=VARCHAR},
		#{merchantId,jdbcType=INTEGER},
		'1',
		#{warrantyPeriod,jdbcType=DATE},
		'1','0')
	</insert>
	<select id="queryTerminalExist" parameterType="java.lang.String" resultMap="BaseResultMap">
		select * from coms_terminal_info where term_seq=#{termSeq,jdbcType=VARCHAR}
	</select>
	<select id="queryTerminalAsycn" parameterType="java.lang.String" resultType="java.lang.String">
		select conf_value from cpay_sys_conf where `key`='terminalAsycnUrl'
	</select>
	<select id="queryMchntCd" parameterType="java.lang.String" resultType="java.lang.String">
		select `value` from cpay_sys_conf where `key`='mchntCd'
	</select>
	<select id="queryTermId" parameterType="java.lang.String" resultType="java.lang.String">
		select `value` from cpay_sys_conf where `key`='termId'
	</select>
	<delete id="cleanTerm" parameterType="java.lang.String" statementType="CALLABLE">
			<![CDATA[  
			    	{call cleanSystemTerminal (#{termSeq,mode=IN,jdbcType=VARCHAR})}  
			]]> 
	</delete>
	<select id="selectByImsi" resultMap="BaseResultMap"	parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from coms_terminal_info t
		where t.imsi = #{imsi}
	</select>
	<select id="getFactoryId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select a.id from coms_terminal_manufacturer a left join coms_terminal_info b on a.id = b.term_mfr_id where b.id=#{id,jdbcType=INTEGER}
	</select>
	<select id="queryTerminalCollection" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal" resultMap="BaseResultMap">
		SELECT a.term_seq FROM coms_terminal_info a
		left join coms_terminal_merchant b on a.id = b.term_id
		left join coms_terminal_group c on c.id = b.term_group_id where 1=1
		<if test="insId != null and insId !=0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=${insId}),'%'))
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and c.id = #{terminalGroupId,jdbcType=INTEGER}
		</if>
		and b.merchant_status='1' and b.term_status='1'
	</select>
	<select id="selectByTerminalAdvQueryOne" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalAdvQuery">
		select distinct t.* from (
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		case d.DCC_SUP_FLAG
		when 'Y' then '是'
		when 'N' then '否'
		end as DCC_SUP_FLAG,
		t.name as group_name,
		cd.NAME as activate_name,
		cd1.name as cup_conn_mode,
		cd2.name as buss_type
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		left join (SELECT type,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'terminal_type')) cd
		on cd.TYPE = a.ACTIVATE_TYPE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'cup_conn_mode')) cd1
		ON cd1.TYPE =d.CUP_CONN_MODE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'buss_type')) cd2
		ON cd2.TYPE =d.BUSS_TYPE
		where 1=1
		and a.ins_id in(SELECT s1.id from cpay_institution s1
		where s1.detail
		like
		CONCAT('%',(SELECT s2.detail from cpay_institution
		s2 where s2.id=${insId}),'%'))
		order by term_seq asc
		) t where 1=1
		<if test="queryCriteria != null and queryCriteria != ''" >
			and ${queryCriteria}
		</if>
	</select>
	<select id="selectByTerminalAdvQueryTwo" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalAdvQuery">
		SELECT DISTINCT
		t.*
		FROM
			(
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		case d.DCC_SUP_FLAG
		when 'Y' then '是'
		when 'N' then '否'
		end as DCC_SUP_FLAG,
		t.name as group_name,
		cd.NAME as activate_name,
		cd1.name as cup_conn_mode,
		cd2.name as buss_type
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		left join (SELECT type,name FROM cpay_dict
			WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'terminal_type')) cd
		on cd.TYPE = a.ACTIVATE_TYPE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
			WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'cup_conn_mode')) cd1
		ON cd1.TYPE =d.CUP_CONN_MODE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
			WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'buss_type')) cd2
		ON cd2.TYPE =d.BUSS_TYPE
		where 1=1
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=${insId}),'%'))

		order by term_seq asc
		) t
		LEFT JOIN coms_terminal_app_relation xt ON t.term_seq = xt.term_seq
		LEFT JOIN coms_terminal_sysdetail st ON t.term_seq = st.term_seq
		WHERE
		1 = 1
		<if test="queryCriteria != null and queryCriteria != ''" >
			and ${queryCriteria}
		</if>
	</select>
	<select id="selectByTerminalAdvQueryThree" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalAdvQuery">
		SELECT DISTINCT
		t.*
		FROM
		(
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		case d.DCC_SUP_FLAG
		when 'Y' then '是'
		when 'N' then '否'
		end as DCC_SUP_FLAG,
		t.name as group_name,
		cd.NAME as activate_name,
		cd1.name as cup_conn_mode,
		cd2.name as buss_type
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		left join (SELECT type,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'terminal_type')) cd
		on cd.TYPE = a.ACTIVATE_TYPE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'cup_conn_mode')) cd1
		ON cd1.TYPE =d.CUP_CONN_MODE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'buss_type')) cd2
		ON cd2.TYPE =d.BUSS_TYPE
		where 1=1
		and a.ins_id in(SELECT s1.id from cpay_institution s1
		where s1.detail
		like
		CONCAT('%',(SELECT s2.detail from cpay_institution
		s2 where s2.id=${insId}),'%'))
		order by term_seq asc
		) t
		LEFT JOIN coms_terminal_app_relation xt ON t.term_seq = xt.term_seq
		WHERE
		1 = 1
		<if test="queryCriteria != null and queryCriteria != ''" >
			and ${queryCriteria}
		</if>
	</select>
	<select id="selectByTerminalAdvQueryFour" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalAdvQuery">
		SELECT DISTINCT
		t.*
		FROM
		(
		select
		a.id,a.TERM_SEQ,a.TERM_NO,a.PAY_MERCHANT_NO,a.TERM_MFR_ID,a.TERM_TYPE_CODE,a.ACTIVATE_TYPE,a.INS_ID,
		e.name as ins_name,
		b.name as factory_name,
		f.name as term_type_name,
		d.name as merchant_name,
		case d.DCC_SUP_FLAG
		when 'Y' then '是'
		when 'N' then '否'
		end as DCC_SUP_FLAG,
		t.name as group_name,
		cd.NAME as activate_name,
		cd1.name as cup_conn_mode,
		cd2.name as buss_type
		from
		coms_terminal_info a
		LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
		LEFT JOIN coms_terminal_type f ON f. CODE = a.term_type_code
		LEFT JOIN coms_merchant_info d ON d.MERCHANT_NO = a.PAY_MERCHANT_NO
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		left join coms_terminal_group t on t.id = a.TERM_GROUP_ID
		left join (SELECT type,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'terminal_type')) cd
		on cd.TYPE = a.ACTIVATE_TYPE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'cup_conn_mode')) cd1
		ON cd1.TYPE =d.CUP_CONN_MODE
		LEFT JOIN (SELECT TYPE,name FROM cpay_dict
		WHERE PARENT_ID = (SELECT id FROM cpay_dict WHERE TYPE = 'buss_type')) cd2
		ON cd2.TYPE =d.BUSS_TYPE
		where 1=1
		and a.ins_id in(SELECT s1.id from cpay_institution s1
		where s1.detail
		like
		CONCAT('%',(SELECT s2.detail from cpay_institution
		s2 where s2.id=${insId}),'%'))
		order by term_seq asc
		) t
		LEFT JOIN coms_terminal_sysdetail st ON t.term_seq = st.term_seq
		WHERE
		1 = 1
		<if test="queryCriteria != null and queryCriteria != ''" >
			and ${queryCriteria}
		</if>
	</select>
	<select id="queryTermSeqByImsi" parameterType="java.lang.String" resultType="java.lang.String">
		select term_seq from coms_terminal_info where imsi=#{imsi,jdbcType=VARCHAR}
	</select>
	<select id="selectByInsId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		select term_seq from coms_terminal_info where 1=1 
			and ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail like
			CONCAT(concat('%',(SELECT s2.detail from cpay_institution
		s2 where s2.id=#{insId,jdbcType=INTEGER})),'%'))
	</select>
	<select id="queryIsStart" resultType="java.lang.String">
		select `value` from cpay_sys_conf where `key`='isStartEle'
	</select>
	<select id="getTermInfoReport" parameterType="com.centerm.cpay.coms.dao.pojo.TermInfoReport" resultMap="TermInfoReportMap">
	SELECT
	count(*) AS terminalCount,DATE_FORMAT(a.create_time,'%Y%m') AS month,
	b.name as factoryName
	FROM
	coms_terminal_info a
	LEFT JOIN coms_terminal_manufacturer b ON a.term_mfr_id = b.id
	where a.term_mfr_id=#{factoryId,jdbcType=INTEGER}
	<if test="isShowAll == 1">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=INTEGER}),'%'))
	</if>
	<if test="isShowAll == 0">
		and a.ins_id = #{insId,jdbcType=INTEGER}
	</if>
	<if test="startTime != null">
		  and  <![CDATA[ DATE_FORMAT(a.create_time,'%Y%m') >= #{startTime,jdbcType=VARCHAR} ]]>
	  </if>
	 <if test="endTime != null">
		  and  <![CDATA[ DATE_FORMAT(a.create_time,'%Y%m') <= #{endTime,jdbcType=VARCHAR} ]]>
	 </if>
	GROUP BY
	b.name,DATE_FORMAT(a.create_time,'%Y%m')
	</select>
	<select id="selectTermListForUpload" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal" resultMap="BaseResultMap">
		select a.term_group_id
		from coms_terminal_info a
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		where 1=1
		<if test="termSeq != null and termSeq !=''">
			and a.term_seq =#{termSeq,jdbcType=VARCHAR}
		</if>
		<if test="termNo != null and termNo !=''">
			and a.term_no =#{termNo,jdbcType=VARCHAR}
		</if>
		<if test="payMerchantNo != null and payMerchantNo !=''">
			and a.pay_merchant_no =#{payMerchantNo,jdbcType=VARCHAR}
		</if>
		<if test="insId != null and insId !=0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=${insId}),'%'))
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			left join (SELECT s2.detail from cpay_institution
			s2 where s2.id=#{terminalGroupId}) s3
			on 1=1
			where locate(s3.detail,s1.detail)>0 or
			locate(s1.detail,s3.detail)>0)
		</if>
	</select>
	<select id="getAllTerminalDyfInfo" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal">
		select a.term_seq,a.network_status,a.status AS activateStatus,c.hardware_stat_bmp ,c.latest_access_time latestAccessTime ,t.name groupName,e.name insName
		from coms_terminal_info a
		left join coms_terminal_dyn_info c on a.term_seq = c.term_seq
		left join  coms_terminal_group t on t.id = a.term_group_id
		LEFT JOIN cpay_institution e ON a.ins_id = e.id
		where 1=1
		<if test="termSeq != null and termSeq != ''">
			and a.term_seq LIKE '%${termSeq}%'
		</if>
		<choose>
			<when test="activateStatus == 1">
				and a.status  = #{activateStatus,jdbcType=VARCHAR}
			</when>
			<when test="activateStatus == 0">
				and (a.status  = #{activateStatus,jdbcType=VARCHAR} or a.status  is null)
			</when>
		</choose>
		<if test="latestAccessStartTime != null and latestAccessStartTime != ''">
			and c.latest_access_time >= #{latestAccessStartTime,jdbcType=VARCHAR}
		</if>
		<if test="latestAccessEndTime != null and latestAccessEndTime != ''">
			and (#{latestAccessEndTime,jdbcType=VARCHAR} > c.latest_access_time or c.latest_access_time is null)
		</if>
		<if test="terminalGroupId != null and terminalGroupId != 0">
			and t.id = #{terminalGroupId,jdbcType=INTEGER}
		</if>
		<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s11.id from cpay_institution s11
			where s11.detail
			like
			CONCAT('%',(SELECT s21.detail from cpay_institution
			s21 where s21.id=${insId}),'%'))
		</if>
	</select>
</mapper>