<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.TerminalPositionMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="term_seq" property="termSeqId" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="rec_crt_tm" property="recCrtTm" jdbcType="VARCHAR" />
    <result column="rec_upd_tm" property="recUpdTm" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, term_seq, longitude, latitude, rec_crt_tm, rec_upd_tm
  </sql>
  <select id="getSysConfByKey" resultType="java.lang.String"
		parameterType="java.lang.String">
		select conf_value
		from cpay_sys_conf  WHERE conf_key	= #{key,jdbcType=VARCHAR}
	</select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_position
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectBySample" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    <!-- select 
    <include refid="Base_Column_List" />
    from coms_terminal_position a
    where a.term_seq = #{termSeqId} and
    <![CDATA[
    rec_upd_tm >= date_format(#{startTime},'%Y%m%d%H%i%s') and 
    rec_upd_tm <= date_format(#{endTime},'%Y%m%d%H%i%s')
    ]]>
    order by a.rec_upd_tm asc -->

    select * from (
SELECT
	distinct 
	term_seq,
	longitude,
	latitude,
    rec_crt_tm,
    rec_upd_tm
FROM
	coms_terminal_position a
WHERE 1=1 
	  <if test="termSeqId != null" >
       AND a.term_seq = #{termSeqId}
      </if>
      <![CDATA[
        and 
        date_format(rec_upd_tm,'%Y%m%d%H%i%s') >= date_format(#{startTime},'%Y%m%d%H%i%s') and 
    	date_format(rec_upd_tm,'%Y%m%d%H%i%s') <= date_format(#{endTime},'%Y%m%d%H%i%s')
      ]]>
) c
order by c.rec_upd_tm asc
  </select>
 
  <select id="selectPoByTermId" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_position a
    where  a.term_seq = #{termSeqId} 
    order by a.rec_upd_tm asc limit 0,5
  </select>
  
  <select id="queryTerminalPosition" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
	FROM coms_terminal_position
	WHERE rec_crt_tm IN (
		SELECT MAX(rec_crt_tm) AS rec_crt_tm
		FROM coms_terminal_position
		GROUP BY term_seq ) and term_seq in (
	SELECT term_seq FROM coms_terminal_position
	WHERE rec_crt_tm IN (
		SELECT MAX(rec_crt_tm) AS rec_crt_tm
		FROM `coms_terminal_position`
		GROUP BY term_seq))
  </select>
  <select id="selectByTermId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_position
    where term_seq = #{termSeqId}
    order by rec_crt_tm desc limit 0,1
  </select>
  
  <select id="selectByTermList" resultMap="BaseResultMap" parameterType="java.util.List" >
    SELECT * from coms_terminal_position where term_seq in(select term_seq
    from coms_terminal_position GROUP BY term_seq HAVING term_seq in
     <foreach collection="list" index="index" open="("  separator=","
		close=")" item="item">
		#{item.termSeqId}
	</foreach>
	and rec_crt_tm = max(rec_crt_tm))  order by term_seq
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from coms_terminal_position
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    insert into coms_terminal_position ( term_seq_id, longitude, 
      latitude, rec_crt_tm)
    values (#{id,jdbcType=BIGINT}, #{termSeqId,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, 
      #{latitude,jdbcType=VARCHAR}, #{recCrtTm,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    insert into coms_terminal_position
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="termSeqId != null" >
        term_seq_id,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="recCrtTm != null" >
        rec_crt_tm,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="termSeqId != null" >
        #{termSeqId,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="recCrtTm != null" >
        #{recCrtTm,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    update coms_terminal_position
    <set >
      <if test="termSeqId != null" >
        term_seq_id = #{termSeqId,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="recCrtTm != null" >
        rec_crt_tm = #{recCrtTm,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.TerminalPosition" >
    update coms_terminal_position
    set term_seq_id = #{termSeqId,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      rec_crt_tm = #{recCrtTm,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="queryCurrentPosition" parameterType="java.lang.String" resultMap="BaseResultMap">
  	select * from coms_terminal_position where term_seq=#{termSeq} order by id desc limit 0,1
  </select>
</mapper>