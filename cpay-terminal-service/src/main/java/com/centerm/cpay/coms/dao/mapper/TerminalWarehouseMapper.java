package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.coms.dao.pojo.TerminalWarehouse;

public interface TerminalWarehouseMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalWarehouse record);

    int insertSelective(TerminalWarehouse record);

    TerminalWarehouse selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalWarehouse record);

    int updateByPrimaryKey(TerminalWarehouse record);

	List<TerminalWarehouse> selectByCondition(TerminalWarehouse terminalWarehouse);

	TerminalWarehouse selectByTermSeq(String termSeq);

	void deleteTerminalMerchantRel(String termSeq);

	List<TerminalWarehouse> selectInsInfo(Integer insId);

	List<TerminalWarehouse> showTermStatus(Integer insId);

	List<TerminalWarehouse> showTermKc(Integer insId);

	List<TerminalWarehouse> showTermKcByIns(Integer insId);

	List<TerminalWarehouse> showTermKcList(TerminalWarehouse terminalWarehouse);

}