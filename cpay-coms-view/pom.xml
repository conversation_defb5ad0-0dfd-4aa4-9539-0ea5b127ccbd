<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.centerm</groupId>
		<artifactId>cpay-coms</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>cpay-coms-view</artifactId>
	<packaging>war</packaging>
	<!-- 依赖管理 -->
	<dependencies>
	    <dependency>
		  <groupId>com.centerm</groupId>
		  <artifactId>cpay-coms-controller</artifactId>
		  <version>0.0.1-SNAPSHOT</version>
		</dependency>
	</dependencies>	
	<build>
		<finalName>coms-view</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<configuration>
					<port>8081</port>
					<path>/coms-view</path>
					<uriEncoding>UTF-8</uriEncoding>
				</configuration>
			</plugin>
		</plugins>
	  </build>
<!-- <build>
		<plugins>
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<configuration>
					<url>http://114.55.227.72:8089/manager/text</url>
					<username>admin</username>
					<password>adminadmin</password>
					<path>/cpay-coms</path>
					<uriEncoding>UTF-8</uriEncoding>
				</configuration>
			</plugin>
		</plugins>
  </build> -->
</project>