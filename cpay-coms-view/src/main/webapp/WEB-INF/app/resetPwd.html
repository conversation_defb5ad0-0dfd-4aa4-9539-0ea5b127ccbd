<!DOCTYPE html>
<html lang="en" ng-app="resetPwdModule">
<head>
<meta charset="utf8">
<title>智能终端管理平台</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv=Cache-Control content=no-cache />
<meta http-equiv="expires" content="0">
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<LINK REL="SHORTCUT ICON" HREF="images/favicon.ico">
<!--<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css"/>-->
<link href="css/login.css" rel="stylesheet" type="text/css" />
<!-- 用于默认主题的css-->
<link id="skincss" href="css/default.css" rel="stylesheet"
	type="text/css" />
<!-- 用于cpay主题的css-->
<!--<link id="skincss" href="css/jpay.css" rel="stylesheet" type="text/css"/>-->
<!--[if lt IE 9]>
    <script src="lib/bootstrap/js/html5shiv.js"></script>
    <script src="lib/bootstrap/js/respond.min.js"></script>
    <![endif]-->
<script src="lib/jquery-1.10.1.min.js" type="text/javascript"></script>
<script src="lib/jquery/jQuery.md5.js" type="text/javascript"></script>
<script src="lib/angular/angular.js"></script>
<script>
	if (window.location.hash != "") {
		window.location.href = "login";
	}
</script>
</head>
<body style="margin: 0">

	<div class="div"></div>
	<div class="login" ng-app="" ng-controller="resetPwdController">
		<div class="login-container">
			<div class="logo"></div>
			<div class="login-input" ng-cloak>
				<form name="loginForm" ng-show="loginView" id="loginForm">
					<div class="user-info" ng-if="returnMsg">
						<label class="return-msg" ng-bind="errorMsg"></label>
					</div>
					<div class="user-info">
						<input class="user-password" ng-class="{'error-input':nameError}"
							type="password" placeholder="新密码（至少六位）" maxlength="16"
							name="newPwd" ng-model="user.newPwd" autocomplete="off"
							method="POST" /> <span class="error-msg" ng-show="newPwdError"
							ng-bind="newPwdError"></span>
					</div>
					<div class="password-info">
						<input class="user-password" ng-class="{'error-input':pwdError}"
							type="password" placeholder="再次确认新密码" maxlength="16"
							name="againNewPwd" ng-model="user.againNewPwd" autocomplete="off"
							method="POST" /> <span class="error-msg"
							ng-show="againNewPwdError" ng-bind="againNewPwdError"></span>
					</div>
					<div class="user-info">
						<input class="user-input"
							ng-class="{'error-input':validateCodeError}" type="text"
							placeholder="验证码" name="validateCode"
							ng-model="user.validateCode" maxlength="4"
							validator="required,positiveInteger" />
					</div>
					<div class="code-validate">
						<div style="overflow: hidden">
							<input class="validate-code"
								ng-class="{'error-input':vailCodeError}" type="text"
								placeholder="请输入验证码" name="telephone"
								ng-model="user.telephone"/> <img
								ng-click="changeValidateCode();" id="validate-img"
								class="validate-img" title="刷新" src="" />
						</div>
						<span class="error-msg" ng-show="validateCodeError"
							ng-bind="validateCodeError"></span>
					</div>
					<!--<div class="login-div">-->
					<button type="submit" class="reset-btn" ng-click="save()">保存</button>
					<!--</div>-->
				</form>
			</div>
		</div>
		<div class="copyright" align="center">Copyright © 2002-2019
			Centerm Co., Ltd.All Rights Reserved. 闽ICP备12007612号</div>
	</div>
	<script src="app/login/resetPwd.js"></script>
</body>
</html>