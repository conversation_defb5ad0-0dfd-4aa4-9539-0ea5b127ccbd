<!DOCTYPE html>
<html lang="en" ng-app="loginModule">
<head>
<meta charset="utf8">
<title>Remote Management Platform</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv=Cache-Control content=no-cache />
<meta http-equiv="expires" content="0">
<meta content="width=device-width, initial-scale=1.0, user-scalable=no"
	name="viewport" />
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta content="email=no" name="format-detection">
<LINK REL="SHORTCUT ICON" HREF="images/favicon.ico">
<!--<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css"/>-->
<link href="css/login.css" rel="stylesheet" type="text/css" />
<!-- 用于默认主题的css-->
<link id="skincss" href="css/default.css" rel="stylesheet"
	type="text/css" />
<!-- 用于cpay主题的css-->
<!--<link id="skincss" href="css/jpay.css" rel="stylesheet" type="text/css"/>-->
<!--[if lt IE 9]>
    <script src="lib/bootstrap/js/html5shiv.js"></script>
    <script src="lib/bootstrap/js/respond.min.js"></script>
    <![endif]-->
<script src="lib/jquery-1.10.1.min.js" type="text/javascript"></script>
<script src="lib/jquery/jQuery.md5.js" type="text/javascript"></script>
<script src="lib/angular/angular.js"></script>
<script>
	if (window.location.hash != "") {
		window.location.href = "login";
	}
</script>
</head>
<body style="margin: 0">

	<div class="div"></div>
	<div class="login" ng-app="" ng-controller="loginController">
		<div class="login-container">
			<div class="logo">
				REMOTE MANAGEMENT PLATFORM
			</div>
			<div class="login-input" ng-cloak>
				<div class="login-input-logo"></div>
				<form name="loginForm" ng-show="loginView" id="loginForm">
					<div class="error-info">
						<label class="return-msg" ng-if="returnMsg" ng-bind="errorMsg"></label>
					</div>
					<div class="user-info inp-cont">
						<input class="user-input" ng-class="{'error-input':nameError}"
							type="text" placeholder="username/telephone/email" name="user"
							ng-model="user.username" ng-focus="userNameOnblur()" /> <span
							class="error-msg" ng-show="nameError" ng-bind="nameError"></span>
					</div>
					<div class="password-info inp-cont">
						<input class="user-password" ng-class="{'error-input':pwdError}"
							type="password" placeholder="password" name="password"
							ng-model="user.password" autocomplete="off" method="POST" /> <span
							class="error-msg" ng-show="pwdError" ng-bind="pwdError"></span>
					</div>
					<div ng-show="false">
						<span id="loginIp" ng-model="loginIp"></span>
					</div>
					<div class="code-validate" ng-show="judgeCode">
						<div style="overflow: hidden">
							<input class="validate-code"
								ng-class="{'error-input':vailCodeError}" type="text"
								placeholder="vailCode" name="vailCode"
								ng-model="user.vailCode" ng-focus="userNameOnblur()" /> <img
								ng-click="changeValidateCode();" id="validate-img"
								class="validate-img" title="reflash" src="" />
						</div>
						<span class="error-msg" ng-show="vailCodeError"
							ng-bind="vailCodeError"></span>
					</div>
					<div class="password-remember">
						<a href="javascript:void(0)"
							ng-class="{'checked':remember,'unchecked':!remember}"
							ng-click="remember =!remember;">Remember Me</a>
							<!--<a class="forget-password" href="javascript:void(0)"
							ng-click="resetPwd()">忘记密码了？</a>-->
					</div>
					<!--<div class="login-div">-->
					<button type="submit" class="login-btn" ng-click="login()">login</button>
					<!--</div>-->
				</form>
			</div>
		</div>
		<div class="copyright" id="expirationDate" align="center">Copyright © 2002-<span id="currentYear"></span> Centerm Co., Ltd. All Rights Reserved.</div>
	</div>

	<script src="app/login/login.js"></script>
	<script>
		var loginIp = "";
		function getIPs(callback) {
			var ip_dups = {};
			var RTCPeerConnection = window.RTCPeerConnection
					|| window.mozRTCPeerConnection
					|| window.webkitRTCPeerConnection;
			var useWebKit = !!window.webkitRTCPeerConnection;
			if (!RTCPeerConnection) {
				var win = iframe.contentWindow;
				RTCPeerConnection = win.RTCPeerConnection
						|| win.mozRTCPeerConnection
						|| win.webkitRTCPeerConnection;
				useWebKit = !!win.webkitRTCPeerConnection;
			}
			var mediaConstraints = {
				optional : [ {
					RtpDataChannels : true
				} ]
			};
			var servers = {
				iceServers : [ {
					urls : "stun:stun.services.mozilla.com"
				} ]
			};
			var pc = new RTCPeerConnection(servers, mediaConstraints);
			function handleCandidate(candidate) {
				var ip_regex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/
				var ip_addrs = ip_regex.exec(candidate);
				var ip_addr = "";
				if (ip_addrs != null) {
					ip_addr = ip_addrs[0];
				}
				if (ip_dups[ip_addr] === undefined)
					callback(ip_addr);

				ip_dups[ip_addr] = true;
			}
			pc.onicecandidate = function(ice) {
				if (ice.candidate)
					handleCandidate(ice.candidate.candidate);
			};
			pc.createDataChannel("");
			pc.createOffer(function(result) {
				pc.setLocalDescription(result, function() {
				}, function() {
				});

			}, function() {
			});
			setTimeout(function() {
				var lines = pc.localDescription.sdp.split('\n');

				lines.forEach(function(line) {
					if (line.indexOf('a=candidate:') === 0)
						handleCandidate(line);
				});
			}, 1000);
		}
		getIPs(function(ip) {
			if(ip !=null && ip !="" && ip.length<20){
				loginIp = ip;
				$("#loginIp").html(loginIp);
			}else{
				
			}
		});

		$(document).ready(function() {
			updateExpirationDate();
		});

		function updateExpirationDate() {
			// 获取当前年份
			const currentYear = new Date().getFullYear();
			// 使用 jQuery 更新到期年份
			$('#currentYear').text(currentYear);
		}
	</script>
</body>
</html>