"use strict";
var v =new Date().getTime();
require.config({
    'baseUrl': 'app',
    urlArgs: "v=" + v,
    paths: {
        'jquery': '../lib/jquery-1.10.1.min',
        'uiRouter': '../lib/angular-ui-router/angular-ui-router.min',
        'ngResource': '../lib/angular/angular-resource.min',
        'angularAMD': '../lib/angularAMD/angularAMD.min',
        'ngload': '../lib/angularAMD/ngload.min',
        'ui-bootstrap': '../lib/ui-bootstrap/ui-bootstrap-0.8.0',
        "ngDialog": "../lib/ngDialog/ngDialog",
        'dialogService': '../modules/common/services/dialogService',
        'formService': '../modules/common/services/formService',
        'animate': '../lib/angular/angular-animate.min',
        'sanitize': '../lib/angular/angular-sanitize.min',
        "angular-validation-rule": "../lib/angular-validation/angular-validation-rule",
        "angular-validation": "../lib/angular-validation/angular-validation",
        "angular-loader": "../lib/loader/angular-loader",
        "organizationService": "../modules/common/services/institutionService",
        'BaseApi': '../modules/common/services/BaseApi',
        'formDirective': '../modules/common/directives/formDirective',
        'commonService': '../modules/common/services/commonService',
        'treeService': '../modules/common/services/treeService',
        'myFilter': '../modules/common/filters/myFilter',
        'gridService': '../modules/common/services/gridService',
        'toolsService': '../modules/common/services/toolsService',
        'securityService': '../modules/common/services/securityService',
        'ngFileUpload': '../lib/angular/ng-file-upload.min',
        'ngFileUploadShim': '../lib/angular/ng-file-upload-shim.min',
        'ztree': '../lib/Ztree/jquery.ztree.all.min',
        'bmap':'../lib/echarts3.0/bmap',
        'echarts':'../lib/echarts3.0/echarts'

    },
    waitSeconds: 0,
    shim: {
        'BaseApi': {
            exports: "BaseApi",
            deps: ['securityService']
        }
    },
    deps: ['js/app']
});
