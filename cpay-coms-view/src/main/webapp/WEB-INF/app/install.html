<!DOCTYPE html>
<html lang="en" ng-app="installModule">
<head>
<meta charset="utf8">
<title>云POS运维管理平台</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv=Cache-Control content=no-cache />
<meta http-equiv="expires" content="0">
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<LINK REL="SHORTCUT ICON" HREF="images/favicon.ico">
<link href="css/install.css" rel="stylesheet" type="text/css" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/style-metro.css" rel="stylesheet" type="text/css" />
<script src="lib/jquery-1.10.1.min.js" type="text/javascript"></script>
<script src="lib/jquery/jQuery.md5.js" type="text/javascript"></script>
<script src="lib/angular/angular.js"></script>
<script src="lib/angular/ng-file-upload.min.js"></script>
<script src="lib/angular/ng-file-upload-shim.min.js">
	<script>
	if (window.location.hash != "") {
		window.location.href = "login";
	}
</script>
</head>
<body style="margin: 0">
	<div class="container-fluid">
		<div class="container-fluid-in">
			<div class="row-fluid">
				<div class="install" ng-app="" ng-controller="installController">
					<div class="install-check" ng-cloak>
						<h1>Checking Installation</h1>
						<p>Checking mysql version</p>
					</div>
					<form name="installForm" id="installForm">
						<h1>Installation Options</h1>
						<div class="fileHead-info">
							<label>Linux系统文件保存头</label> <input class="fileHead" type="text"
								placeholder="/data/CPAY2.0/files/" name="fileHead"
								ng-model="user.fileHead" disabled /> <span class="error-msg"
								ng-show="fileHeadError" ng-bind="fileHeadError"></span>
						</div>
						<div class="uploadLoadPath-info">
							<label>UploadLoadPath</label> <input class="uploadLoadPath"
								type="text" placeholder="/data/CPAY2.0/files/upload/"
								name="uploadLoadPath" ng-model="user.uploadLoadPath" disabled />
							<span class="error-msg" ng-show="uploadLoadPathError"
								ng-bind="uploadLoadPathError"></span>
						</div>
						<div class="databaseType-info">
							<label>Type of Database</label> <select>
								<option value="sky">Mysql（default）</option>
							</select>
						</div>
						<div class="hostname-info">
							<label>Hostname (for Database Server)</label> <input
								class="hostname-input" ng-class="{'error-input':hostnameError}"
								type="text" placeholder="主机" name="hostname"
								ng-model="user.hostname" /> <span class="error-msg"
								ng-show="hostnameError" ng-bind="hostnameError"></span>
						</div>
						<div class="user-info">
							<label>Username (for Database)</label> <input class="user-input"
								ng-class="{'error-input':nameError}" type="text"
								placeholder="用户名" name="user" ng-model="user.username" /> <span
								class="error-msg" ng-show="nameError" ng-bind="nameError"></span>
						</div>
						<div class="password-info">
							<label>Password (for Database)</label> <input
								class="user-password" ng-class="{'error-input':pwdError}"
								type="password" placeholder="密码" name="password"
								ng-model="user.password" /> <span class="error-msg"
								ng-show="pwdError" ng-bind="pwdError"></span>
						</div>
						<div class="databaseName-info">
							<label>Database name (for Database)</label> <input
								class="user-databaseName"
								ng-class="{'error-input':databaseNameError}" type="text"
								placeholder="数据库名" name="databaseName"
								ng-model="user.databaseName" /> <span class="error-msg"
								ng-show="databaseNameError" ng-bind="databaseNameError"></span>
						</div>
						<!--<div class="adminUser-info">
								<label>Admin Username (to create Database if require)</label> <input
									class="admin-user" ng-class="{'error-input':adminUserError}"
									type="text" placeholder="root权限用户名" name="user"
									ng-model="user.adminUser" /> <span class="error-msg"
									ng-show="adminUserError" ng-bind="adminUserError"></span>
							</div>
							<div class="adminPwd-info">
								<label>Admin Password (to create Database if require)</label> <input
									class="admin-password"
									ng-class="{'error-input':adminPwd-Error}" type="password"
									placeholder="root密码" name="adminPwd" ng-model="user.adminPwd" />
								<span class="error-msg" ng-show="adminPwdError"
									ng-bind="adminPwdError"></span>
							</div>  -->

						<div class="control-group">
							<label class="control-label">Mysql (for Database)</label>
							<div class="controls">
								<div class="uneditable-input">
									<i class="icon-file fileupload-exists"></i> <span
										class="fileupload-preview" ng-bind="file.name"
										onchange="getFullPath()"></span>
								</div>
								<div class="btn btn-file" ng-click="selectFile()" ngf-select
									ng-model="file" name="file">选择</div>
								<div class="btn " ng-click="submitFile()">上传</div>
								<span id="appPath" class="help-inline"></span> <span
									class="error-msg" ng-show="fileNameError"
									ng-bind="fileNameError"></span>
							</div>
						</div>

						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls">
								<div style="display: none"
									class="progress progress-success progress-striped span6"
									id="sqlFile" role="progressbar" aria-valuemin="0"
									aria-valuemax="50">
									<div class="bar" style="width: 0%;" id="sqlBar"></div>
								</div>
							</div>
						</div>

						<div style="clear: both"></div>
						<!--附件结束  -->
						<div class="control-group">
							<label class="control-label">Aappt (for app)</label>
							<div class="controls">
								<div class="uneditable-input">
									<i class="icon-file fileupload-exists"></i> <span
										class="fileupload-preview" ng-bind="aaptFile.name"
										onchange="getFullPath()"></span>
								</div>
								<div class="btn btn-file" ng-click="selectAaptFile()" ngf-select
									ng-model="aaptFile" name="aaptFile">选择</div>
								<div class="btn " ng-click="submitAaptFile()">上传</div>
								<span id="appPath" class="help-inline"></span> <span
									class="error-msg" ng-show="aaptFileError"
									ng-bind="aaptFileError"></span>
							</div>
						</div>


						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls">
								<div style="display: none"
									class="progress progress-success progress-striped span6"
									id="aaptFile" role="progressbar" aria-valuemin="0"
									aria-valuemax="50">
									<div class="bar" style="width: 0%;" id="apptBar""></div>
								</div>
							</div>
						</div>
						<div style="clear: both"></div>
						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls">
								<label>Attempt Installation</label>
								<button type="submit" class="btn btn-default btn-xs blue"
									ng-click="install()">Install/upgrade Database</button>
							</div>
						</div>


						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls" style="display: none" id="databaseInstall">
								<h3>正在初始化数据库，请勿关闭页面...</h3>
								<span></span>

							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
	<script src="app/install/install.js"></script>
</body>
</html>