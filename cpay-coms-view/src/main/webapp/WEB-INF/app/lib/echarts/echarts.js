!function(t,e){"function"==typeof define&&define.amd?define([],e):"object"==typeof module&&module.exports?module.exports=e():t.echarts=e()}(this,function(){var t,e;!function(){function i(t,e){if(!e)return t;if(0===t.indexOf(".")){var i=e.split("/"),n=t.split("/"),r=i.length-1,a=n.length,o=0,s=0;t:for(var l=0;a>l;l++)switch(n[l]){case"..":if(!(r>o))break t;o++,s++;break;case".":s++;break;default:break t}return i.length=r-o,n=n.slice(s),i.concat(n).join("/")}return t}function n(t){function e(e,o){if("string"==typeof e){var s=n[e];return s||(s=a(i(e,t)),n[e]=s),s}e instanceof Array&&(o=o||function(){},o.apply(this,r(e,o,t)))}var n={};return e}function r(e,n,r){for(var s=[],l=o[r],u=0,c=Math.min(e.length,n.length);c>u;u++){var h,f=i(e[u],r);switch(f){case"require":h=l&&l.require||t;break;case"exports":h=l.exports;break;case"module":h=l;break;default:h=a(f)}s.push(h)}return s}function a(t){var e=o[t];if(!e)throw new Error("No "+t);if(!e.defined){var i=e.factory,n=i.apply(this,r(e.deps||[],i,t));"undefined"!=typeof n&&(e.exports=n),e.defined=1}return e.exports}var o={};e=function(t,e,i){o[t]={id:t,deps:e,factory:i,defined:0,exports:{},require:n(t)}},t=n("")}();var i="undefined",n="moveTo",r="transform",a="__dirty",o="stroke",s="lineWidth",l="applyTransform",u="retrieve",c="category",h="ecModel",f="getShallow",d="getItemModel",p="ordinal",v="dataToCoord",m="dimensions",g="dataToPoint",y="createElement",_="getExtent",x="contain",b="function",w="isArray",M="replace",T="zlevel",S="mouseout",C="splice",A="series",P="extend",L="remove",z="isObject",I="colorStops",k="update",D="create",O="getItemVisual",R="dataIndex",E="indexOf",B="length",N="ignore",G="canvasSupported",F="animation",V="string",H="prototype",q="toLowerCase",W="defaults",j="coordinateSystem",Z="getData",U="opacity",X="setStyle",Y="position",$="bottom",Q="center",K="middle",J="getHeight",te="getWidth",ee="target",ie="silent",ne="height",re="getBoundingRect",ae="getFont",oe="textStyle",se="getModel",le="zrender/core/util",ue="require";e("echarts/chart/line",[ue,le,"../echarts","./line/LineSeries","./line/LineView","../visual/symbol","../layout/points","../processor/dataSample","../component/grid"],function(t){var e=t(le),i=t("../echarts"),n=i.PRIORITY;t("./line/LineSeries"),t("./line/LineView"),i.registerVisual(e.curry(t("../visual/symbol"),"line","circle","line")),i.registerLayout(e.curry(t("../layout/points"),"line")),i.registerProcessor(n.PROCESSOR.STATISTIC,e.curry(t("../processor/dataSample"),"line")),t("../component/grid")}),e("echarts/component/legend",[ue,"./legend/LegendModel","./legend/legendAction","./legend/LegendView","../echarts","./legend/legendFilter"],function(t){t("./legend/LegendModel"),t("./legend/legendAction"),t("./legend/LegendView");var e=t("../echarts");e.registerProcessor(t("./legend/legendFilter"))}),e("echarts/component/title",[ue,"../echarts","../util/graphic","../util/layout"],function(t){var e=t("../echarts"),i=t("../util/graphic"),n=t("../util/layout");e.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),e.extendComponentView({type:"title",render:function(t,e,r){if(this.group.removeAll(),t.get("show")){var a=this.group,o=t[se](oe),s=t[se]("subtextStyle"),l=t.get("textAlign"),u=t.get("textBaseline"),c=new i.Text({style:{text:t.get("text"),textFont:o[ae](),fill:o.getTextColor()},z2:10}),h=c[re](),f=t.get("subtext"),d=new i.Text({style:{text:f,textFont:s[ae](),fill:s.getTextColor(),y:h[ne]+t.get("itemGap"),textBaseline:"top"},z2:10}),p=t.get("link"),v=t.get("sublink");c[ie]=!p,d[ie]=!v,p&&c.on("click",function(){window.open(p,"_"+t.get(ee))}),v&&d.on("click",function(){window.open(v,"_"+t.get("subtarget"))}),a.add(c),f&&a.add(d);var m=a[re](),g=t.getBoxLayoutParams();g.width=m.width,g[ne]=m[ne];var y=n.getLayoutRect(g,{width:r[te](),height:r[J]()},t.get("padding"));l||(l=t.get("left")||t.get("right"),l===K&&(l=Q),"right"===l?y.x+=y.width:l===Q&&(y.x+=y.width/2)),u||(u=t.get("top")||t.get($),u===Q&&(u=K),u===$?y.y+=y[ne]:u===K&&(y.y+=y[ne]/2),u=u||"top"),a.attr(Y,[y.x,y.y]);var _={textAlign:l,textVerticalAlign:u};c[X](_),d[X](_),m=a[re]();var x=y.margin,b=t.getItemStyle(["color",U]);b.fill=t.get("backgroundColor");var w=new i.Rect({shape:{x:m.x-x[3],y:m.y-x[0],width:m.width+x[1]+x[3],height:m[ne]+x[0]+x[2]},style:b,silent:!0});i.subPixelOptimizeRect(w),a.add(w)}}})}),e("echarts/chart/bar",[ue,le,"../coord/cartesian/Grid","./bar/BarSeries","./bar/BarView","../layout/barGrid","../echarts","../component/grid"],function(t){var e=t(le);t("../coord/cartesian/Grid"),t("./bar/BarSeries"),t("./bar/BarView");var i=t("../layout/barGrid"),n=t("../echarts");n.registerLayout(e.curry(i,"bar")),n.registerVisual(function(t){t.eachSeriesByType("bar",function(t){var e=t[Z]();e.setVisual("legendSymbol","roundRect")})}),t("../component/grid")}),e("echarts/component/grid",[ue,"../util/graphic",le,"../echarts","../coord/cartesian/Grid","./axis"],function(t){var e=t("../util/graphic"),i=t(le),n=t("../echarts");t("../coord/cartesian/Grid"),t("./axis"),n.extendComponentView({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new e.Rect({shape:t[j].getRect(),style:i[W]({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),n.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}),e("zrender/vml/vml",[ue,"./graphic","../zrender","./Painter"],function(t){t("./graphic"),t("../zrender").registerPainter("vml",t("./Painter"))}),e("echarts/component/tooltip",[ue,"./tooltip/TooltipModel","./tooltip/TooltipView","../echarts"],function(t){t("./tooltip/TooltipModel"),t("./tooltip/TooltipView"),t("../echarts").registerAction({type:"showTip",event:"showTip",update:"none"},function(){}),t("../echarts").registerAction({type:"hideTip",event:"hideTip",update:"none"},function(){})}),e("echarts/echarts",[ue,"zrender/core/env","./model/Global","./ExtensionAPI","./CoordinateSystem","./model/OptionManager","./model/Component","./model/Series","./view/Component","./view/Chart","./util/graphic","./util/model","./util/throttle","zrender",le,"zrender/tool/color","zrender/mixin/Eventful","zrender/core/timsort","./visual/seriesColor","./preprocessor/backwardCompat","./loading/default","./data/List","./model/Model","./util/number","./util/format","zrender/core/matrix","zrender/core/vector"],function(t){function e(t){return function(e,i,n){e=e&&e[q](),ve[H][t].call(this,e,i,n)}}function i(){ve.call(this)}function n(t,e,n){function r(t,e){return t.prio-e.prio}n=n||{},typeof e===V&&(e=Ee[e]),this.id,this.group,this._dom=t;var a=this._zr=fe.init(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n[ne]});this._throttledZrFlush=he.throttle(de.bind(a.flush,a),17),this._theme=de.clone(e),this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._api=new U(this),this._coordSysMgr=new Y,ve.call(this),this._messageCenter=new i,this._initEvents(),this.resize=de.bind(this.resize,this),this._pendingActions=[],me(Re,r),me(De,r),a[F].on("frame",this._onframe,this)}function r(t,e,i){var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=ce.parseFinder(r,e);for(var o=0;o<a[B];o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}function a(t,e){var i=this._model;i&&i.eachComponent({mainType:"series",query:e},function(n){var r=this._chartsMap[n.__viewId];r&&r.__alive&&r[t](n,i,this._api,e)},this)}function o(t,e){var i=Ie[t.type],n=i.actionInfo,r=n[k]||k;this[Se]=!0;var a=[t],o=!1;t.batch&&(o=!0,a=de.map(t.batch,function(e){return e=de[W](de[P]({},e),t),e.batch=null,e}));for(var s,l=[],u="highlight"===t.type||"downplay"===t.type,c=0;c<a[B];c++){var h=a[c];s=i.action(h,this._model),s=s||de[P]({},h),s.type=n.event||s.type,l.push(s),u&&Le[r].call(this,h)}"none"===r||u||(this[Ae]?(Le.prepareAndUpdate.call(this,t),this[Ae]=!1):Le[r].call(this,t)),s=o?{type:n.event||t.type,batch:l}:l[0],this[Se]=!1,!e&&this._messageCenter.trigger(s.type,s)}function s(t){for(var e=this._pendingActions;e[B];){var i=e.shift();o.call(this,i,t)}}function l(t,e,i){var n=this._api;ge(this._componentsViews,function(r){var a=r.__model;r[t](a,e,n,i),g(a,r)},this),e.eachSeries(function(r){var a=this._chartsMap[r.__viewId];a[t](r,e,n,i),g(r,a),m(r,a)},this),v(this._zr,e)}function u(t,e){for(var i="component"===t,n=i?this._componentsViews:this._chartsViews,r=i?this._componentsMap:this._chartsMap,a=this._zr,o=0;o<n[B];o++)n[o].__alive=!1;e[i?"eachComponent":"eachSeries"](function(t,o){if(i){if(t===A)return}else o=t;var s=o.id+"_"+o.type,l=r[s];if(!l){var u=K.parseClassType(o.type),c=i?ae.getClass(u.main,u.sub):oe.getClass(u.sub);if(!c)return;l=new c,l.init(e,this._api),r[s]=l,n.push(l),a.add(l.group)}o.__viewId=s,l.__alive=!0,l.__id=s,l.__model=o},this);for(var o=0;o<n[B];){var s=n[o];s.__alive?o++:(a[L](s.group),s.dispose(e,this._api),n[C](o,1),delete r[s.__id])}}function c(t,e){ge(De,function(i){i.func(t,e)})}function h(t){var e={};t.eachSeries(function(t){var i=t.get("stack"),n=t[Z]();if(i&&"list"===n.type){var r=e[i];r&&(n.stackedOn=r),e[i]=n}})}function f(t,e){var i=this._api;ge(Re,function(n){n.isLayout&&n.func(t,i,e)})}function d(t,e){var i=this._api;t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()}),ge(Re,function(n){n.func(t,i,e)})}function p(t,e){var i=this._api;ge(this._componentsViews,function(n){var r=n.__model;n.render(r,t,i,e),g(r,n)},this),ge(this._chartsViews,function(t){t.__alive=!1},this),t.eachSeries(function(n){var r=this._chartsMap[n.__viewId];r.__alive=!0,r.render(n,t,i,e),r.group[ie]=!!n.get(ie),g(n,r),m(n,r)},this),v(this._zr,t),ge(this._chartsViews,function(e){e.__alive||e[L](t,i)},this)}function v(t,e){var i=t.storage,n=0;i.traverse(function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!_.node&&i.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function m(t,e){var i=0;e.group.traverse(function(t){"group"===t.type||t[N]||i++});var n=+t.get("progressive"),r=i>t.get("progressiveThreshold")&&n&&!_.node;r&&e.group.traverse(function(t){t.isGroup||(t.progressive=r?Math.floor(i++/n):-1,r&&t.stopAnimation(!0))});var a=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t[X]("blend",a)})}function g(t,e){var i=t.get("z"),n=t.get(T);e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t[T]=n))})}function y(t){function e(t,e){for(var i=0;i<t[B];i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";de.each(ke,function(o,s){t._messageCenter.on(s,function(o){if(Ge[t.group]&&t[a]!==i){var s=t.makeActionFromEvent(o),l=[];de.each(Ne,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),ge(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}var _=t("zrender/core/env"),x=t("./model/Global"),U=t("./ExtensionAPI"),Y=t("./CoordinateSystem"),Q=t("./model/OptionManager"),K=t("./model/Component"),re=t("./model/Series"),ae=t("./view/Component"),oe=t("./view/Chart"),ue=t("./util/graphic"),ce=t("./util/model"),he=t("./util/throttle"),fe=t("zrender"),de=t(le),pe=t("zrender/tool/color"),ve=t("zrender/mixin/Eventful"),me=t("zrender/core/timsort"),ge=de.each,ye=1e3,_e=5e3,xe=1e3,be=2e3,we=3e3,Me=4e3,Te=5e3,Se="__flagInMainProcess",Ce="__hasGradientOrPatternBg",Ae="__optionUpdated";i[H].on=e("on"),i[H].off=e("off"),i[H].one=e("one"),de.mixin(i,ve);var Pe=n[H];Pe._onframe=function(){this[Ae]&&(this[Se]=!0,Le.prepareAndUpdate.call(this),this[Se]=!1,this[Ae]=!1)},Pe.getDom=function(){return this._dom},Pe.getZr=function(){return this._zr},Pe.setOption=function(t,e,i){if(this[Se]=!0,!this._model||e){var n=new Q(this._api),r=this._theme,a=this._model=new x(null,null,r,n);a.init(null,null,r,n)}this.__lastOnlyGraphic=!(!t||!t.graphic),de.each(t,function(t,e){"graphic"!==e&&(this.__lastOnlyGraphic=!1)},this),this._model.setOption(t,Oe),i?this[Ae]=!0:(Le.prepareAndUpdate.call(this),this._zr.flush(),this[Ae]=!1),this[Se]=!1,s.call(this,!1)},Pe.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},Pe[se]=function(){return this._model},Pe.getOption=function(){return this._model&&this._model.getOption()},Pe[te]=function(){return this._zr[te]()},Pe[J]=function(){return this._zr[J]()},Pe.getRenderedCanvas=function(t){if(_[G]){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,i=e.storage.getDisplayList();return de.each(i,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},Pe.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;ge(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group[N]||(n.push(e),e.group[N]=!0)})});var a=this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return ge(n,function(t){t.group[N]=!1}),a},Pe.getConnectedDataURL=function(t){if(_[G]){var e=this.group,i=Math.min,n=Math.max,r=1/0;if(Ge[e]){var a=r,o=r,s=-r,l=-r,u=[],c=t&&t.pixelRatio||1;de.each(Ne,function(r){if(r.group===e){var c=r.getRenderedCanvas(de.clone(t)),h=r.getDom().getBoundingClientRect();a=i(h.left,a),o=i(h.top,o),s=n(h.right,s),l=n(h[$],l),u.push({dom:c,left:h.left,top:h.top})}}),a*=c,o*=c,s*=c,l*=c;var h=s-a,f=l-o,d=de.createCanvas();d.width=h,d[ne]=f;var p=fe.init(d);return ge(u,function(t){var e=new ue.Image({style:{x:t.left*c-a,y:t.top*c-o,image:t.dom}});p.add(e)}),p.refreshImmediately(),d.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},Pe.convertToPixel=de.curry(r,"convertToPixel"),Pe.convertFromPixel=de.curry(r,"convertFromPixel"),Pe.containPixel=function(t,e){var i,n=this._model;return t=ce.parseFinder(n,t),de.each(t,function(t,n){n[E]("Models")>=0&&de.each(t,function(t){var r=t[j];if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(i|=a.containPoint(e,t))}},this)},this),!!i},Pe.getVisual=function(t,e){var i=this._model;t=ce.parseFinder(i,t,{defaultMainType:"series"});var n=t.seriesModel,r=n[Z](),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty(R)?r.indexOfRawIndex(t[R]):null;return null!=a?r[O](a,e):r.getVisual(e)};var Le={update:function(t){var e=this._model,i=this._api,n=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),n[D](this._model,this._api),c.call(this,e,i),h.call(this,e),n[k](e,i),d.call(this,e,t),p.call(this,e,t);var a=e.get("backgroundColor")||"transparent",o=r.painter;if(o.isSingleCanvas&&o.isSingleCanvas())r.configLayer(0,{clearColor:a});else{if(!_[G]){var s=pe.parse(a);a=pe.stringify(s,"rgb"),0===s[3]&&(a="transparent")}a[I]||a.image?(r.configLayer(0,{clearColor:a}),this[Ce]=!0,this._dom.style.background="transparent"):(this[Ce]&&r.configLayer(0,{clearColor:null}),this[Ce]=!1,this._dom.style.background=a)}}},updateView:function(t){var e=this._model;e&&(e.eachSeries(function(t){t[Z]().clearAllVisual()}),d.call(this,e,t),l.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e.eachSeries(function(t){t[Z]().clearAllVisual()}),d.call(this,e,t),l.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(f.call(this,e,t),l.call(this,"updateLayout",e,t))},highlight:function(t){a.call(this,"highlight",t)},downplay:function(t){a.call(this,"downplay",t)},prepareAndUpdate:function(t){var e=this._model;u.call(this,"component",e),u.call(this,"chart",e),this.__lastOnlyGraphic?(ge(this._componentsViews,function(i){var n=i.__model;n&&"graphic"===n.mainType&&(i.render(n,e,this._api,t),g(n,i))},this),this.__lastOnlyGraphic=!1):Le[k].call(this,t)}};Pe.resize=function(t){this[Se]=!0,this._zr.resize(t);var e=this._model&&this._model.resetOption("media");Le[e?"prepareAndUpdate":k].call(this),this._loadingFX&&this._loadingFX.resize(),this[Se]=!1,s.call(this)},Pe.showLoading=function(t,e){if(de[z](t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Be[t]){var i=Be[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},Pe.hideLoading=function(){this._loadingFX&&this._zr[L](this._loadingFX),this._loadingFX=null},Pe.makeActionFromEvent=function(t){var e=de[P]({},t);return e.type=ke[t.type],e},Pe.dispatchAction=function(t,e){if(de[z](e)||(e={silent:!!e}),Ie[t.type]){if(this[Se])return void this._pendingActions.push(t);o.call(this,t,e[ie]),e.flush?this._zr.flush(!0):e.flush!==!1&&_.browser.weChat&&this._throttledZrFlush(),s.call(this,e[ie])}},Pe.on=e("on"),Pe.off=e("off"),Pe.one=e("one");var ze=["click","dblclick","mouseover",S,"mousemove","mousedown","mouseup","globalout","contextmenu"];Pe._initEvents=function(){ge(ze,function(t){this._zr.on(t,function(e){var i,n=this[se](),r=e[ee];if("globalout"===t)i={};else if(r&&null!=r[R]){var a=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=a&&a.getDataParams(r[R],r.dataType)||{}}else r&&r.eventData&&(i=de[P]({},r.eventData));i&&(i.event=e,i.type=t,this.trigger(t,i))},this)},this),ge(ke,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},Pe.isDisposed=function(){return this._disposed},Pe.clear=function(){this.setOption({series:[]},!0)},Pe.dispose=function(){if(!this._disposed){this._disposed=!0;var t=this._api,e=this._model;ge(this._componentsViews,function(i){i.dispose(e,t)}),ge(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete Ne[this.id]}},de.mixin(n,ve);var Ie=[],ke={},De=[],Oe=[],Re=[],Ee={},Be={},Ne={},Ge={},Fe=new Date-0,Ve=new Date-0,He="_echarts_instance_",qe={version:"3.3.2",dependencies:{zrender:"3.2.2"}};qe.init=function(t,e,i){var r=new n(t,e,i);return r.id="ec_"+Fe++,Ne[r.id]=r,t.setAttribute&&t.setAttribute(He,r.id),y(r),r},qe.connect=function(t){if(de[w](t)){var e=t;t=null,de.each(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+Ve++,de.each(e,function(e){e.group=t})}return Ge[t]=!0,t},qe.disConnect=function(t){Ge[t]=!1},qe.dispose=function(t){de.isDom(t)?t=qe.getInstanceByDom(t):typeof t===V&&(t=Ne[t]),t instanceof n&&!t.isDisposed()&&t.dispose()},qe.getInstanceByDom=function(t){var e=t.getAttribute(He);return Ne[e]},qe.getInstanceById=function(t){return Ne[t]},qe.registerTheme=function(t,e){Ee[t]=e},qe.registerPreprocessor=function(t){Oe.push(t)},qe.registerProcessor=function(t,e){typeof t===b&&(e=t,t=ye),De.push({prio:t,func:e})},qe.registerAction=function(t,e,i){typeof e===b&&(i=e,e="");var n=de[z](t)?t.type:[t,t={event:e}][0];t.event=(t.event||n)[q](),e=t.event,Ie[n]||(Ie[n]={action:i,actionInfo:t}),ke[e]=n},qe.registerCoordinateSystem=function(t,e){Y.register(t,e)},qe.registerLayout=function(t,e){typeof t===b&&(e=t,t=xe),Re.push({prio:t,func:e,isLayout:!0})},qe.registerVisual=function(t,e){typeof t===b&&(e=t,t=we),Re.push({prio:t,func:e})},qe.registerLoading=function(t,e){Be[t]=e};var We=K.parseClassType;return qe.extendComponentModel=function(t,e){var i=K;if(e){var n=We(e);i=K.getClass(n.main,n.sub,!0)}return i[P](t)},qe.extendComponentView=function(t,e){var i=ae;if(e){var n=We(e);i=ae.getClass(n.main,n.sub,!0)}return i[P](t)},qe.extendSeriesModel=function(t,e){var i=re;if(e){e="series."+e[M]("series.","");var n=We(e);i=K.getClass(n.main,n.sub,!0)}return i[P](t)},qe.extendChartView=function(t,e){var i=oe;if(e){e[M]("series.","");var n=We(e);i=oe.getClass(n.main,!0)}return i[P](t)},qe.setCanvasCreator=function(t){de.createCanvas=t},qe.registerVisual(be,t("./visual/seriesColor")),qe.registerPreprocessor(t("./preprocessor/backwardCompat")),qe.registerLoading("default",t("./loading/default")),qe.registerAction({type:"highlight",event:"highlight",update:"highlight"},de.noop),qe.registerAction({type:"downplay",event:"downplay",update:"downplay"},de.noop),qe.List=t("./data/List"),qe.Model=t("./model/Model"),qe.graphic=t("./util/graphic"),qe.number=t("./util/number"),qe.format=t("./util/format"),qe.matrix=t("zrender/core/matrix"),qe.vector=t("zrender/core/vector"),qe.color=t("zrender/tool/color"),qe.util={},ge(["map","each","filter",E,"inherits","reduce","filter","bind","curry",w,"isString",z,"isFunction",P,W,"clone"],function(t){qe.util[t]=de[t]}),qe.PRIORITY={PROCESSOR:{FILTER:ye,STATISTIC:_e},VISUAL:{LAYOUT:xe,GLOBAL:be,CHART:we,COMPONENT:Me,BRUSH:Te}},qe}),e("echarts/scale/Time",[ue,le,"../util/number","../util/format","./Interval"],function(t){var e=t(le),i=t("../util/number"),n=t("../util/format"),r=t("./Interval"),a=r[H],o=Math.ceil,s=Math.floor,l=1e3,u=60*l,c=60*u,h=24*c,f=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][2]<e?i=r+1:n=r}return i},d=r[P]({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return n.formatTime(e[0],i)},niceExtent:function(t,e,n){var r=this._extent;if(r[0]===r[1]&&(r[0]-=h,r[1]+=h),r[1]===-1/0&&1/0===r[0]){var a=new Date;r[1]=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r[0]=r[1]-h}this.niceTicks(t);var l=this._interval;e||(r[0]=i.round(s(r[0]/l)*l)),n||(r[1]=i.round(o(r[1]/l)*l))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0],r=n/t,a=p[B],l=f(p,r,0,a),u=p[Math.min(l,a-1)],c=u[2];if("year"===u[0]){var h=n/c,d=i.nice(h/t,!0);c*=d}var v=[o(e[0]/c)*c,s(e[1]/c)*c];this._stepLvl=u,this._interval=c,this._niceExtent=v},parse:function(t){return+i.parseDate(t)}});e.each([x,"normalize"],function(t){d[H][t]=function(e){return a[t].call(this,this.parse(e))}});var p=[["hh:mm:ss",1,l],["hh:mm:ss",5,5*l],["hh:mm:ss",10,10*l],["hh:mm:ss",15,15*l],["hh:mm:ss",30,30*l],["hh:mm\nMM-dd",1,u],["hh:mm\nMM-dd",5,5*u],["hh:mm\nMM-dd",10,10*u],["hh:mm\nMM-dd",15,15*u],["hh:mm\nMM-dd",30,30*u],["hh:mm\nMM-dd",1,c],["hh:mm\nMM-dd",2,2*c],["hh:mm\nMM-dd",6,6*c],["hh:mm\nMM-dd",12,12*c],["MM-dd\nyyyy",1,h],["week",7,7*h],["month",1,31*h],["quarter",3,380*h/4],["half-year",6,380*h/2],["year",1,380*h]];return d[D]=function(){return new d},d}),e("echarts/scale/Log",[ue,le,"./Scale","../util/number","./Interval"],function(t){function e(t,e){return u(t,l(e))}var i=t(le),n=t("./Scale"),r=t("../util/number"),a=t("./Interval"),o=n[H],s=a[H],l=r.getPrecisionSafe,u=r.round,c=Math.floor,h=Math.ceil,f=Math.pow,d=Math.log,p=n[P]({type:"log",base:10,$constructor:function(){n.apply(this,arguments),this._originalScale=new a},getTicks:function(){var t=this._originalScale,n=this._extent,a=t[_]();return i.map(s.getTicks.call(this),function(i){var o=r.round(f(this.base,i));return o=i===n[0]&&t.__fixMin?e(o,a[0]):o,o=i===n[1]&&t.__fixMax?e(o,a[1]):o},this)},getLabel:s.getLabel,scale:function(t){return t=o.scale.call(this,t),f(this.base,t)},setExtent:function(t,e){var i=this.base;t=d(t)/d(i),e=d(e)/d(i),s.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,i=o[_].call(this);i[0]=f(t,i[0]),i[1]=f(t,i[1]);var n=this._originalScale,r=n[_]();return n.__fixMin&&(i[0]=e(i[0],r[0])),n.__fixMax&&(i[1]=e(i[1],r[1])),i},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=d(t[0])/d(e),t[1]=d(t[1])/d(e),o.unionExtent.call(this,t)},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=r.quantity(i),a=t/i*n;for(.5>=a&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var o=[r.round(h(e[0]/n)*n),r.round(c(e[1]/n)*n)];this._interval=n,this._niceExtent=o}},niceExtent:function(t,e,i){s.niceExtent.call(this,t,e,i);var n=this._originalScale;n.__fixMin=e,n.__fixMax=i}});return i.each([x,"normalize"],function(t){p[H][t]=function(e){return e=d(e)/d(this.base),o[t].call(this,e)}}),p[D]=function(){return new p},p}),e(le,[ue],function(){function t(e){if(null==e||"object"!=typeof e)return e;var i=e,n=k.call(e);if("[object Array]"===n){i=[];for(var r=0,a=e[B];a>r;r++)i[r]=t(e[r])}else if(I[n])i=e.constructor.from(e);else if(!z[n]&&!S(e)){i={};for(var o in e)e.hasOwnProperty(o)&&(i[o]=t(e[o]))}return i}function e(i,n,r){if(!M(n)||!M(i))return r?t(n):i;for(var a in n)if(n.hasOwnProperty(a)){var o=i[a],s=n[a];!M(s)||!M(o)||_(s)||_(o)||S(s)||S(o)||T(s)||T(o)?!r&&a in i||(i[a]=t(n[a],!0)):e(o,s,r)}return i}function i(t,i){for(var n=t[0],r=1,a=t[B];a>r;r++)n=e(n,t[r],i);return n}function n(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function r(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function a(){return document[y]("canvas")}function o(){return L||(L=q.createCanvas().getContext("2d")),L}function s(t,e){if(t){if(t[E])return t[E](e);for(var i=0,n=t[B];n>i;i++)if(t[i]===e)return i}return-1}function l(t,e){function i(){}var n=t[H];i[H]=e[H],t[H]=new i;for(var r in n)t[H][r]=n[r];t[H].constructor=t,t.superClass=e}function u(t,e,i){t=H in t?t[H]:t,e=H in e?e[H]:e,r(t,e,i)}function c(t){return t?typeof t==V?!1:"number"==typeof t[B]:void 0}function h(t,e,i){if(t&&e)if(t.forEach&&t.forEach===O)t.forEach(e,i);else if(t[B]===+t[B])for(var n=0,r=t[B];r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function f(t,e,i){if(t&&e){if(t.map&&t.map===G)return t.map(e,i);for(var n=[],r=0,a=t[B];a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function d(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===F)return t.reduce(e,i,n);for(var r=0,a=t[B];a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function p(t,e,i){if(t&&e){if(t.filter&&t.filter===R)return t.filter(e,i);for(var n=[],r=0,a=t[B];a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function v(t,e,i){if(t&&e)for(var n=0,r=t[B];r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function m(t,e){var i=N.call(arguments,2);return function(){return t.apply(e,i.concat(N.call(arguments)))}}function g(t){var e=N.call(arguments,1);return function(){return t.apply(this,e.concat(N.call(arguments)))}}function _(t){return"[object Array]"===k.call(t)}function x(t){return typeof t===b}function w(t){return"[object String]"===k.call(t)}function M(t){var e=typeof t;return e===b||!!t&&"object"==e}function T(t){return!!z[k.call(t)]}function S(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function C(){for(var t=0,e=arguments[B];e>t;t++)if(null!=arguments[t])return arguments[t]}function A(){return Function.call.apply(N,arguments)}function P(t,e){if(!t)throw new Error(e)}var L,z={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},I={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},k=Object[H].toString,D=Array[H],O=D.forEach,R=D.filter,N=D.slice,G=D.map,F=D.reduce,q={inherits:l,mixin:u,clone:t,merge:e,mergeAll:i,extend:n,defaults:r,getContext:o,createCanvas:a,indexOf:s,slice:A,find:v,isArrayLike:c,each:h,map:f,reduce:d,filter:p,bind:m,curry:g,isArray:_,isString:w,isObject:M,isFunction:x,isBuildInObject:T,isDom:S,retrieve:C,assert:P,noop:function(){}};return q}),e("echarts/chart/line/LineSeries",[ue,"../helper/createListFromArray","../../model/Series"],function(t){var e=t("../helper/createListFromArray"),i=t("../../model/Series");return i[P]({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,i){return e(t.data,this,i)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{normal:{position:"top"}},lineStyle:{normal:{width:2,type:"solid"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}})}),e("echarts/chart/line/LineView",[ue,le,"../helper/SymbolDraw","../helper/Symbol","./lineAnimationDiff","../../util/graphic","../../util/model","./poly","../../view/Chart"],function(t){function e(t,e){if(t[B]===e[B]){for(var i=0;i<t[B];i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function i(t){return"number"==typeof t?t:t?.3:0}function n(t){var e=t.getGlobalExtent();if(t.onBand){var i=t.getBandWidth()/2-1,n=e[1]>e[0]?1:-1;e[0]+=n*i,e[1]-=n*i}return e}function r(t){return t>=0?1:-1}function a(t,e){var i=t.getBaseAxis(),n=t.getOtherAxis(i),a=i.onZero?0:n.scale[_]()[0],o=n.dim,s="x"===o||"radius"===o?1:0;return e.mapArray([o],function(n,l){for(var u,c=e.stackedOn;c&&r(c.get(o,l))===r(n);){u=c;break}var h=[];return h[s]=e.get(i.dim,l),h[1-s]=u?u.get(o,l,!0):a,t[g](h)},!0)}function o(t,e,i){var r=n(t.getAxis("x")),a=n(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),s=Math.min(r[0],r[1]),l=Math.min(a[0],a[1]),u=Math.max(r[0],r[1])-s,c=Math.max(a[0],a[1])-l,h=i.get("lineStyle.normal.width")||2,f=i.get("clipOverflow")?h/2:Math.max(u,c);o?(l-=f,c+=2*f):(s-=f,u+=2*f);var d=new x.Rect({shape:{x:s,y:l,width:u,height:c}});return e&&(d.shape[o?"width":ne]=0,x.initProps(d,{shape:{width:u,height:c}},i)),d}function s(t,e,i){var n=t.getAngleAxis(),r=t.getRadiusAxis(),a=r[_](),o=n[_](),s=Math.PI/180,l=new x.Sector({shape:{cx:t.cx,cy:t.cy,r0:a[0],r:a[1],startAngle:-o[0]*s,endAngle:-o[1]*s,clockwise:n.inverse}});return e&&(l.shape.endAngle=-o[0]*s,x.initProps(l,{shape:{endAngle:-o[1]*s}},i)),l}function l(t,e,i){return"polar"===t.type?s(t,e,i):o(t,e,i)}function u(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t[B]-1;o++){var s=t[o+1],l=t[o];a.push(l);var u=[];switch(i){case"end":u[r]=s[r],u[1-r]=l[1-r],a.push(u);break;case K:var c=(l[r]+s[r])/2,h=[];u[r]=h[r]=c,u[1-r]=l[1-r],h[1-r]=s[1-r],a.push(u),a.push(h);break;default:u[r]=l[r],u[1-r]=s[1-r],a.push(u)}}return t[o]&&a.push(t[o]),a}function c(t,e){var i=t.getVisual("visualMeta");if(i&&i[B]&&t.count()){for(var n,r=i[B]-1;r>=0;r--)if(i[r].dimension<2){n=i[r];break}if(n&&"cartesian2d"===e.type){var a=n.dimension,o=t[m][a],s=e.getAxis(o),l=h.map(n.stops,function(t){return{coord:s.toGlobalCoord(s[v](t.value)),color:t.color}}),u=l[B],c=n.outerColors.slice();u&&l[0].coord>l[u-1].coord&&(l.reverse(),c.reverse());var f=10,d=l[0].coord-f,p=l[u-1].coord+f,g=p-d;if(.001>g)return"transparent";h.each(l,function(t){t.offset=(t.coord-d)/g}),l.push({offset:u?l[u-1].offset:.5,color:c[1]||"transparent"}),l.unshift({offset:u?l[0].offset:.5,color:c[0]||"transparent"});var y=new x.LinearGradient(0,0,0,0,l,!0);return y[o]=d,y[o+"2"]=p,y}}}var h=t(le),f=t("../helper/SymbolDraw"),d=t("../helper/Symbol"),y=t("./lineAnimationDiff"),x=t("../../util/graphic"),b=t("../../util/model"),w=t("./poly"),M=t("../../view/Chart");return M[P]({type:"line",init:function(){var t=new x.Group,e=new f;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,n,r){var o=t[j],s=this.group,f=t[Z](),d=t[se]("lineStyle.normal"),p=t[se]("areaStyle.normal"),v=f.mapArray(f.getItemLayout,!0),m="polar"===o.type,g=this._coordSys,y=this._symbolDraw,_=this._polyline,x=this._polygon,b=this._lineGroup,w=t.get(F),M=!p.isEmpty(),T=a(o,f),S=t.get("showSymbol"),C=S&&!m&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(f,o),A=this._data;A&&A.eachItemGraphicEl(function(t,e){t.__temp&&(s[L](t),A.setItemGraphicEl(e,null))}),S||y[L](),s.add(b);var P=!m&&t.get("step");_&&g.type===o.type&&P===this._step?(M&&!x?x=this._newPolygon(v,T,o,w):x&&!M&&(b[L](x),x=this._polygon=null),b.setClipPath(l(o,!1,t)),S&&y.updateData(f,C),f.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),e(this._stackedOnPoints,T)&&e(this._points,v)||(w?this._updateAnimation(f,T,o,r,P):(P&&(v=u(v,o,P),T=u(T,o,P)),_.setShape({points:v}),x&&x.setShape({points:v,stackedOnPoints:T})))):(S&&y.updateData(f,C),P&&(v=u(v,o,P),T=u(T,o,P)),_=this._newPolyline(v,o,w),M&&(x=this._newPolygon(v,T,o,w)),b.setClipPath(l(o,!0,t)));var z=c(f,o)||f.getVisual("color");_.useStyle(h[W](d.getLineStyle(),{fill:"none",stroke:z,lineJoin:"bevel"}));var I=t.get("smooth");if(I=i(t.get("smooth")),_.setShape({smooth:I,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),x){var k=f.stackedOn,D=0;if(x.useStyle(h[W](p.getAreaStyle(),{fill:z,opacity:.7,lineJoin:"bevel"})),k){var O=k.hostModel;D=i(O.get("smooth"))}x.setShape({smooth:I,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})
}this._data=f,this._coordSys=o,this._stackedOnPoints=T,this._points=v,this._step=P},dispose:function(){},highlight:function(t,e,i,n){var r=t[Z](),a=b.queryDataIndex(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;o=new d(r,a),o[Y]=s,o.setZ(t.get(T),t.get("z")),o[N]=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else M[H].highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t[Z](),a=b.queryDataIndex(r,n);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group[L](o)):o.downplay())}else M[H].downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup[L](e),e=new w.Polyline({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup[L](i),i=new w.Polygon({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_getSymbolIgnoreFunc:function(t,e){var i=e.getAxesByScale(p)[0];return i&&i.isLabelIgnored?h.bind(i.isLabelIgnored,i):void 0},_updateAnimation:function(t,e,i,n,r){var a=this._polyline,o=this._polygon,s=t.hostModel,l=y(this._data,t,this._stackedOnPoints,e,this._coordSys,i),c=l.current,h=l.stackedOnCurrent,f=l.next,d=l.stackedOnNext;r&&(c=u(l.current,i,r),h=u(l.stackedOnCurrent,i,r),f=u(l.next,i,r),d=u(l.stackedOnNext,i,r)),a.shape.__points=l.current,a.shape.points=c,x.updateProps(a,{shape:{points:f}},s),o&&(o.setShape({points:c,stackedOnPoints:h}),x.updateProps(o,{shape:{points:f,stackedOnPoints:d}},s));for(var p=[],v=l.status,m=0;m<v[B];m++){var g=v[m].cmd;if("="===g){var _=t.getItemGraphicEl(v[m].idx1);_&&p.push({el:_,ptIdx:m})}}a.animators&&a.animators[B]&&a.animators[0].during(function(){for(var t=0;t<p[B];t++){var e=p[t].el;e.attr(Y,a.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup.removeAll(),this._symbolDraw[L](!0),e&&e.eachItemGraphicEl(function(i,n){i.__temp&&(t[L](i),e.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}})}),e("echarts/visual/symbol",[ue],function(){return function(t,e,i,n){n.eachRawSeriesByType(t,function(t){var r=t[Z](),a=t.get("symbol")||e,o=t.get("symbolSize");r.setVisual({legendSymbol:i||a,symbol:a,symbolSize:o}),n.isSeriesFiltered(t)||(typeof o===b&&r.each(function(e){var i=t.getRawValue(e),n=t.getDataParams(e);r.setItemVisual(e,"symbolSize",o(i,n))}),r.each(function(t){var e=r[d](t),i=e[f]("symbol",!0),n=e[f]("symbolSize",!0);null!=i&&r.setItemVisual(t,"symbol",i),null!=n&&r.setItemVisual(t,"symbolSize",n)}))})}}),e("echarts/processor/dataSample",[],function(){var t={average:function(t){for(var e=0,i=0,n=0;n<t[B];n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t[B];i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t[B];i++)t[i]>e&&(e=t[i]);return e},min:function(t){for(var e=1/0,i=0;i<t[B];i++)t[i]<e&&(e=t[i]);return e},nearest:function(t){return t[0]}},e=function(t){return Math.round(t[B]/2)};return function(i,n){n.eachSeriesByType(i,function(i){var n=i[Z](),r=i.get("sampling"),a=i[j];if("cartesian2d"===a.type&&r){var o=a.getBaseAxis(),s=a.getOtherAxis(o),l=o[_](),u=l[1]-l[0],c=Math.round(n.count()/u);if(c>1){var h;typeof r===V?h=t[r]:typeof r===b&&(h=r),h&&(n=n.downSample(s.dim,1/c,h,e),i.setData(n))}}},this)}}),e("echarts/component/legend/LegendModel",[ue,le,"../../model/Model","../../echarts"],function(t){var e=t(le),i=t("../../model/Model"),n=t("../../echarts").extendComponentModel({type:"legend",dependencies:[A],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{}},mergeOption:function(t){n.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this[h]);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t[B];i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var n=e.map(this.get("data")||[],function(t){return(typeof t===V||"number"==typeof t)&&(t={name:t}),new i(t,this,this[h])},this);this._data=n;var r=e.map(t.getSeries(),function(t){return t.name});t.eachSeries(function(t){if(t.legendDataProvider){var e=t.legendDataProvider();r=r.concat(e.mapArray(e.getName))}}),this._availableNames=r},getData:function(){return this._data},select:function(t){var i=this.option.selected,n=this.get("selectedMode");if("single"===n){var r=this._data;e.each(r,function(t){i[t.get("name")]=!1})}i[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var i=this.option.selected;return!(i.hasOwnProperty(t)&&!i[t])&&e[E](this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:"top",align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});return n}),e("echarts/layout/points",[ue],function(){return function(t,e){e.eachSeriesByType(t,function(t){var e=t[Z](),i=t[j];if(i){var n=i[m];"singleAxis"===i.type?e.each(n[0],function(t,n){e.setItemLayout(n,isNaN(t)?[0/0,0/0]:i[g](t))}):e.each(n,function(t,n,r){e.setItemLayout(r,isNaN(t)||isNaN(n)?[0/0,0/0]:i[g]([t,n]))},!0)}})}}),e("echarts/component/legend/legendAction",[ue,"../../echarts",le],function(t){function e(t,e,i){var r,a={},o="toggleSelected"===t;return i.eachComponent("legend",function(i){o&&null!=r?i[r?"select":"unSelect"](e.name):(i[t](e.name),r=i.isSelected(e.name));var s=i[Z]();n.each(s,function(t){var e=t.get("name");if("\n"!==e&&""!==e){var n=i.isSelected(e);a[e]=e in a?a[e]&&n:n}})}),{name:e.name,selected:a}}var i=t("../../echarts"),n=t(le);i.registerAction("legendToggleSelect","legendselectchanged",n.curry(e,"toggleSelected")),i.registerAction("legendSelect","legendselected",n.curry(e,"select")),i.registerAction("legendUnSelect","legendunselected",n.curry(e,"unSelect"))}),e("echarts/component/legend/LegendView",[ue,le,"../../util/symbol","../../util/graphic","../helper/listComponent","../../echarts"],function(t){function e(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function i(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i.dispatchAction({type:"highlight",seriesName:t.name,name:e})}function n(t,e,i){var n=i.getZr().storage.getDisplayList()[0];n&&n.useHoverLayer||t.get("legendHoverLink")&&i.dispatchAction({type:"downplay",seriesName:t.name,name:e})}var r=t(le),a=t("../../util/symbol"),o=t("../../util/graphic"),s=t("../helper/listComponent"),l=r.curry;return t("../../echarts").extendComponentView({type:"legend",init:function(){this._symbolTypeStore={}},render:function(t,a,u){var c=this.group;if(c.removeAll(),t.get("show")){var h=t.get("selectedMode"),f=t.get("align");"auto"===f&&(f="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left");var d={};r.each(t[Z](),function(r){var s=r.get("name");if(""===s||"\n"===s)return void c.add(new o.Group({newline:!0}));var p=a.getSeriesByName(s)[0];if(!d[s])if(p){var v=p[Z](),m=v.getVisual("color");typeof m===b&&(m=m(p.getDataParams(0)));var g=v.getVisual("legendSymbol")||"roundRect",y=v.getVisual("symbol"),_=this._createItem(s,r,t,g,y,f,m,h);_.on("click",l(e,s,u)).on("mouseover",l(i,p,null,u)).on(S,l(n,p,null,u)),d[s]=!0}else a.eachRawSeries(function(a){if(!d[s]&&a.legendDataProvider){var o=a.legendDataProvider(),c=o.indexOfName(s);if(0>c)return;var p=o[O](c,"color"),v="roundRect",m=this._createItem(s,r,t,v,null,f,p,h);m.on("click",l(e,s,u)).on("mouseover",l(i,a,s,u)).on(S,l(n,a,s,u)),d[s]=!0}},this)},this),s.layout(c,t,u),s.addBackground(c,t)}},_createItem:function(t,e,i,n,s,l,u,c){var h=i.get("itemWidth"),f=i.get("itemHeight"),d=i.get("inactiveColor"),p=i.isSelected(t),v=new o.Group,m=e[se](oe),g=e.get("icon"),y=e[se]("tooltip"),_=y.parentModel;if(n=g||n,v.add(a.createSymbol(n,0,0,h,f,p?u:d)),!g&&s&&(s!==n||"none"==s)){var x=.8*f;"none"===s&&(s="circle"),v.add(a.createSymbol(s,(h-x)/2,(f-x)/2,x,x,p?u:d))}var w="left"===l?h+5:-5,T=l,S=i.get("formatter"),C=t;typeof S===V&&S?C=S[M]("{name}",null!=t?t:""):typeof S===b&&(C=S(t));var A=new o.Text({style:{text:C,x:w,y:f/2,fill:p?m.getTextColor():d,textFont:m[ae](),textAlign:T,textVerticalAlign:"middle"}});v.add(A);var L=new o.Rect({shape:v[re](),invisible:!0,tooltip:y.get("show")?r[P]({content:t,formatter:_.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},y.option):null});return v.add(L),v.eachChild(function(t){t[ie]=!0}),L[ie]=!c,this.group.add(v),o.setHoverStyle(v),v}})}),e("echarts/coord/cartesian/Grid",[ue,"exports","../../util/layout","../../coord/axisHelper",le,"./Cartesian2D","./Axis2D","./GridModel","../../CoordinateSystem"],function(t){function e(t,e){return t.findGridModel()===e}function i(t){var e,i=t.model,n=i.getFormattedLabels(),r=i[se]("axisLabel.textStyle"),a=1,o=n[B];o>40&&(a=Math.ceil(o/40));for(var s=0;o>s;s+=a)if(!t.isLabelIgnored(s)){var l=r.getTextRect(n[s]);e?e.union(l):e=l}return e}function n(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this._model=t}function r(t,e){var i=t[_](),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function a(t){return u.map(w,function(e){var i=t.getReferringComponents(e)[0];return i})}function o(t){return"cartesian2d"===t.get(j)}var s=t("../../util/layout"),l=t("../../coord/axisHelper"),u=t(le),h=t("./Cartesian2D"),f=t("./Axis2D"),d=u.each,y=l.ifAxisCrossZero,x=l.niceScaleExtent;t("./GridModel");var b=n[H];b.type="grid",b.getRect=function(){return this._rect},b[k]=function(t,e){function i(t){var e=n[t];for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r&&(r.type===c||!y(r)))return!0}return!1}var n=this._axesMap;this._updateScale(t,this._model),d(n.x,function(t){x(t,t.model)}),d(n.y,function(t){x(t,t.model)}),d(n.x,function(t){i("y")&&(t.onZero=!1)}),d(n.y,function(t){i("x")&&(t.onZero=!1)}),this.resize(this._model,e)},b.resize=function(t,e){function n(){d(o,function(t){var e=t.isHorizontal(),i=e?[0,a.width]:[0,a[ne]],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),r(t,e?a.x:a.y)})}var a=s.getLayoutRect(t.getBoxLayoutParams(),{width:e[te](),height:e[J]()});this._rect=a;var o=this._axesList;n(),t.get("containLabel")&&(d(o,function(t){if(!t.model.get("axisLabel.inside")){var e=i(t);if(e){var n=t.isHorizontal()?ne:"width",r=t.model.get("axisLabel.margin");a[n]-=e[n]+r,"top"===t[Y]?a.y+=e[ne]+r:"left"===t[Y]&&(a.x+=e.width+r)}}}),n())},b.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},b.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}for(var n=0,r=this._coordsList;n<r[B];n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},b.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian[g](i):n.axis?n.axis.toGlobalCoord(n.axis[v](i)):null},b.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},b._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r[j],u[E](l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var c=s[j];c===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},b.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},b._initCartesian=function(t,i){function n(n){return function(s,u){if(e(s,t,i)){var h=s.get(Y);"x"===n?"top"!==h&&h!==$&&(h=$,r[h]&&(h="top"===h?$:"top")):"left"!==h&&"right"!==h&&(h="left",r[h]&&(h="left"===h?"right":"left")),r[h]=!0;var d=new f(n,l.createScaleByModel(s),[0,0],s.get("type"),h),p=d.type===c;d.onBand=p&&s.get("boundaryGap"),d.inverse=s.get("inverse"),d.onZero=s.get("axisLine.onZero"),s.axis=d,d.model=s,d.grid=this,d.index=u,this._axesList.push(d),a[n][u]=d,o[n]++}}}var r={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},o={x:0,y:0};return i.eachComponent("xAxis",n("x"),this),i.eachComponent("yAxis",n("y"),this),o.x&&o.y?(this._axesMap=a,void d(a.x,function(t,e){d(a.y,function(i,n){var r="x"+e+"y"+n,a=new h(r);a.grid=this,this._coordsMap[r]=a,this._coordsList.push(a),a.addAxis(t),a.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},b._updateScale=function(t,i){function n(t,e,i){d(i.coordDimToDataDim(e.dim),function(i){e.scale.unionExtent(t.getDataExtent(i,e.scale.type!==p))})}u.each(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(r){if(o(r)){var s=a(r,t),l=s[0],u=s[1];if(!e(l,i,t)||!e(u,i,t))return;var c=this.getCartesian(l.componentIndex,u.componentIndex),h=r[Z](),f=c.getAxis("x"),d=c.getAxis("y");"list"===h.type&&(n(h,f,r),n(h,d,r))}},this)};var w=["xAxis","yAxis"];return n[D]=function(t,e){var i=[];return t.eachComponent("grid",function(r,a){var o=new n(r,t,e);o.name="grid_"+a,o.resize(r,e),r[j]=o,i.push(o)}),t.eachSeries(function(e){if(o(e)){var i=a(e,t),n=i[0],r=i[1],s=n.findGridModel(),l=s[j];e[j]=l.getCartesian(n.componentIndex,r.componentIndex)}}),i},n[m]=h[H][m],t("../../CoordinateSystem").register("cartesian2d",n),n}),e("echarts/component/legend/legendFilter",[],function(){return function(t){var e=t.findComponents({mainType:"legend"});e&&e[B]&&t.filterSeries(function(t){for(var i=0;i<e[B];i++)if(!e[i].isSelected(t.name))return!1;return!0})}}),e("echarts/util/layout",[ue,le,"zrender/core/BoundingRect","./number","./format"],function(t){function e(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var c,h,f=l[Y],d=l[re](),p=e.childAt(u+1),v=p&&p[re]();if("horizontal"===t){var m=d.width+(v?-v.x+d.x:0);c=a+m,c>n||l.newline?(a=0,c=m,o+=s+i,s=d[ne]):s=Math.max(s,d[ne])}else{var g=d[ne]+(v?-v.y+d.y:0);h=o+g,h>r||l.newline?(a+=s+i,o=0,h=g,s=d.width):s=Math.max(s,d.width)}l.newline||(f[0]=a,f[1]=o,"horizontal"===t?a=c+i:o=h+i)})}var i=t(le),n=t("zrender/core/BoundingRect"),r=t("./number"),a=t("./format"),o=r.parsePercent,s=i.each,u={},c=u.LOCATION_PARAMS=["left","right","top",$,"width",ne];return u.box=e,u.vbox=i.curry(e,"vertical"),u.hbox=i.curry(e,"horizontal"),u.getAvailableSize=function(t,e,i){var n=e.width,r=e[ne],s=o(t.x,n),l=o(t.y,r),u=o(t.x2,n),c=o(t.y2,r);return(isNaN(s)||isNaN(parseFloat(t.x)))&&(s=0),(isNaN(u)||isNaN(parseFloat(t.x2)))&&(u=n),(isNaN(l)||isNaN(parseFloat(t.y)))&&(l=0),(isNaN(c)||isNaN(parseFloat(t.y2)))&&(c=r),i=a.normalizeCssArray(i||0),{width:Math.max(u-s-i[1]-i[3],0),height:Math.max(c-l-i[0]-i[2],0)}},u.getLayoutRect=function(t,e,i){i=a.normalizeCssArray(i||0);var r=e.width,s=e[ne],l=o(t.left,r),u=o(t.top,s),c=o(t.right,r),h=o(t[$],s),f=o(t.width,r),d=o(t[ne],s),p=i[2]+i[0],v=i[1]+i[3],m=t.aspect;switch(isNaN(f)&&(f=r-c-v-l),isNaN(d)&&(d=s-h-p-u),isNaN(f)&&isNaN(d)&&(m>r/s?f=.8*r:d=.8*s),null!=m&&(isNaN(f)&&(f=m*d),isNaN(d)&&(d=f/m)),isNaN(l)&&(l=r-c-f-v),isNaN(u)&&(u=s-h-d-p),t.left||t.right){case Q:l=r/2-f/2-i[3];break;case"right":l=r-f-v}switch(t.top||t[$]){case K:case Q:u=s/2-d/2-i[0];break;case $:u=s-d-p}l=l||0,u=u||0,isNaN(f)&&(f=r-l-(c||0)),isNaN(d)&&(d=s-u-(h||0));var g=new n(l+i[3],u+i[0],f,d);return g.margin=i,g},u.positionElement=function(t,e,r,a,o){var s=!o||!o.hv||o.hv[0],c=!o||!o.hv||o.hv[1],h=o&&o.boundingMode||"all";if(s||c){var f;if("raw"===h)f="group"===t.type?new n(0,0,+e.width||0,+e[ne]||0):t[re]();else if(f=t[re](),t.needLocalTransform()){var d=t.getLocalTransform();f=f.clone(),f[l](d)}e=u.getLayoutRect(i[W]({width:f.width,height:f[ne]},e),r,a);var p=t[Y],v=s?e.x-f.x:0,m=c?e.y-f.y:0;t.attr(Y,"raw"===h?[v,m]:[p[0]+v,p[1]+m])}},u.mergeLayoutParam=function(t,e,n){function r(i){var r={},l=0,u={},c=0,h=n.ignoreSize?1:2;if(s(i,function(e){u[e]=t[e]}),s(i,function(t){a(e,t)&&(r[t]=u[t]=e[t]),o(r,t)&&l++,o(u,t)&&c++}),c!==h&&l){if(l>=h)return r;for(var f=0;f<i[B];f++){var d=i[f];if(!a(r,d)&&a(t,d)){r[d]=t[d];break}}return r}return u}function a(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function l(t,e,i){s(t,function(t){e[t]=i[t]})}!i[z](n)&&(n={});var u=["width","left","right"],c=[ne,"top",$],h=r(u),f=r(c);l(u,t,h),l(c,t,f)},u.getLayoutParams=function(t){return u.copyLayoutParams({},t)},u.copyLayoutParams=function(t,e){return e&&t&&s(c,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t},u}),e("echarts/util/graphic",[ue,le,"zrender/tool/path","zrender/graphic/Path","zrender/tool/color","zrender/core/matrix","zrender/core/vector","zrender/container/Group","zrender/graphic/Image","zrender/graphic/Text","zrender/graphic/shape/Circle","zrender/graphic/shape/Sector","zrender/graphic/shape/Ring","zrender/graphic/shape/Polygon","zrender/graphic/shape/Polyline","zrender/graphic/shape/Rect","zrender/graphic/shape/Line","zrender/graphic/shape/BezierCurve","zrender/graphic/shape/Arc","zrender/graphic/CompoundPath","zrender/graphic/LinearGradient","zrender/graphic/RadialGradient","zrender/core/BoundingRect"],function(t){function e(t){return null!=t&&"none"!=t}function i(t){return typeof t===V?M.lift(t,-.1):t}function n(t){if(t.__hoverStlDirty){var n=t.style[o],r=t.style.fill,a=t.__hoverStl;a.fill=a.fill||(e(r)?i(r):null),a[o]=a[o]||(e(n)?i(n):null);var s={};for(var l in a)a.hasOwnProperty(l)&&(s[l]=t.style[l]);t.__normalStl=s,t.__hoverStlDirty=!1}}function r(t){t.__isHover||(n(t),t.useHoverLayer?t.__zr&&t.__zr.addHover(t,t.__hoverStl):(t[X](t.__hoverStl),t.z2+=1),t.__isHover=!0)}function a(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t[X](e),t.z2-=1),t.__isHover=!1}}function u(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&r(t)}):r(t)}function c(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&a(t)}):a(t)}function h(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&n(t)}function d(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&u(this)}function p(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&c(this)}function v(){this.__isEmphasis=!0,u(this)}function m(){this.__isEmphasis=!1,c(this)}function g(t,e,i,n,r,a){typeof r===b&&(a=r,r=null);var o=n&&(n.ifEnableAnimation?n.ifEnableAnimation():n[f](F));if(o){var s=t?"Update":"",l=n&&n[f]("animationDuration"+s),u=n&&n[f]("animationEasing"+s),c=n&&n[f]("animationDelay"+s);typeof c===b&&(c=c(r)),l>0?e.animateTo(i,l,c||0,u,a):(e.attr(i),a&&a())}else e.attr(i),a&&a()}var y=t(le),_=t("zrender/tool/path"),x=Math.round,w=t("zrender/graphic/Path"),M=t("zrender/tool/color"),T=t("zrender/core/matrix"),C=t("zrender/core/vector"),A={};return A.Group=t("zrender/container/Group"),A.Image=t("zrender/graphic/Image"),A.Text=t("zrender/graphic/Text"),A.Circle=t("zrender/graphic/shape/Circle"),A.Sector=t("zrender/graphic/shape/Sector"),A.Ring=t("zrender/graphic/shape/Ring"),A.Polygon=t("zrender/graphic/shape/Polygon"),A.Polyline=t("zrender/graphic/shape/Polyline"),A.Rect=t("zrender/graphic/shape/Rect"),A.Line=t("zrender/graphic/shape/Line"),A.BezierCurve=t("zrender/graphic/shape/BezierCurve"),A.Arc=t("zrender/graphic/shape/Arc"),A.CompoundPath=t("zrender/graphic/CompoundPath"),A.LinearGradient=t("zrender/graphic/LinearGradient"),A.RadialGradient=t("zrender/graphic/RadialGradient"),A.BoundingRect=t("zrender/core/BoundingRect"),A.extendShape=function(t){return w[P](t)},A.extendPath=function(t,e){return _.extendFromString(t,e)},A.makePath=function(t,e,i,n){var r=_.createFromString(t,e),a=r[re]();if(i){var o=a.width/a[ne];if(n===Q){var s,l=i[ne]*o;l<=i.width?s=i[ne]:(l=i.width,s=l/o);var u=i.x+i.width/2,c=i.y+i[ne]/2;i.x=u-l/2,i.y=c-s/2,i.width=l,i[ne]=s}this.resizePath(r,i)}return r},A.mergePath=_.mergePath,A.resizePath=function(t,e){if(t[l]){var i=t[re](),n=i.calculateTransform(e);t[l](n)}},A.subPixelOptimizeLine=function(t){var e=A.subPixelOptimize,i=t.shape,n=t.style[s];return x(2*i.x1)===x(2*i.x2)&&(i.x1=i.x2=e(i.x1,n,!0)),x(2*i.y1)===x(2*i.y2)&&(i.y1=i.y2=e(i.y1,n,!0)),t},A.subPixelOptimizeRect=function(t){var e=A.subPixelOptimize,i=t.shape,n=t.style[s],r=i.x,a=i.y,o=i.width,l=i[ne];return i.x=e(i.x,n,!0),i.y=e(i.y,n,!0),i.width=Math.max(e(r+o,n,!1)-i.x,0===o?0:1),i[ne]=Math.max(e(a+l,n,!1)-i.y,0===l?0:1),t},A.subPixelOptimize=function(t,e,i){var n=x(2*t);return(n+x(e))%2===0?n/2:(n+(i?1:-1))/2},A.setHoverStyle=function(t,e,i){t.__hoverSilentOnTouch=i&&i.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&h(t,e)}):h(t,e),t.on("mouseover",d).on(S,p),t.on("emphasis",v).on("normal",m)},A.setText=function(t,e,i){var n=e[f](Y)||"inside",r=n[E]("inside")>=0?"white":i,a=e[se](oe);y[P](t,{textDistance:e[f]("distance")||5,textFont:a[ae](),textPosition:n,textFill:a.getTextColor()||r})},A.updateProps=function(t,e,i,n,r){g(!0,t,e,i,n,r)},A.initProps=function(t,e,i,n,r){g(!1,t,e,i,n,r)},A.getTransform=function(t,e){for(var i=T.identity([]);t&&t!==e;)T.mul(i,t.getLocalTransform(),i),t=t.parent;return i},A[l]=function(t,e,i){return i&&(e=T.invert([],e)),C[l]([],t,e)},A.transformDirection=function(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:t===$?r:0];return a=A[l](a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?$:"top"},A.groupTransition=function(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:C.clone(t[Y]),rotation:t.rotation};return t.shape&&(e.shape=y[P]({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),A.updateProps(t,n,i,t[R])}}})}},A}),e("echarts/chart/bar/BarSeries",[ue,"../../model/Series","../helper/createListFromArray"],function(t){var e=t("../../model/Series"),i=t("../helper/createListFromArray");return e[P]({type:"series.bar",dependencies:["grid","polar"],getInitialData:function(t,e){return i(t.data,this,e)},getMarkerPosition:function(t){var e=this[j];if(e){var i=e[g](t,!0),n=this[Z](),r=n.getLayout("offset"),a=n.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},brushSelector:"rect",defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,itemStyle:{normal:{},emphasis:{}}}})}),e("echarts/chart/bar/BarView",[ue,le,"../../util/graphic","../../model/Model","./barItemStyle","../../echarts"],function(t){function e(t,e){var i=t.width>0?1:-1,n=t[ne]>0?1:-1;e=Math.min(e,Math.abs(t.width),Math.abs(t[ne])),t.x+=i*e/2,t.y+=n*e/2,t.width-=i*e,t[ne]-=n*e}var i=t(le),n=t("../../util/graphic");return i[P](t("../../model/Model")[H],t("./barItemStyle")),t("../../echarts").extendChartView({type:"bar",render:function(t,e,i){var n=t.get(j);return"cartesian2d"===n&&this._renderOnCartesian(t,e,i),this.group},dispose:i.noop,_renderOnCartesian:function(t){function r(r,a){var s=o.getItemLayout(r),l=o[d](r).get(f)||0;e(s,l);var u=new n.Rect({shape:i[P]({},s)});if(h){var p=u.shape,v=c?ne:"width",m={};p[v]=0,m[v]=s[v],n[a?"updateProps":"initProps"](u,{shape:m},t,r)}return u}var a=this.group,o=t[Z](),s=this._data,l=t[j],u=l.getBaseAxis(),c=u.isHorizontal(),h=t.get(F),f=["itemStyle","normal","barBorderWidth"];o.diff(s).add(function(t){if(o.hasValue(t)){var e=r(t);o.setItemGraphicEl(t,e),a.add(e)}})[k](function(i,l){var u=s.getItemGraphicEl(l);if(!o.hasValue(i))return void a[L](u);u||(u=r(i,!0));var c=o.getItemLayout(i),h=o[d](i).get(f)||0;e(c,h),n.updateProps(u,{shape:c},t,i),o.setItemGraphicEl(i,u),a.add(u)})[L](function(e){var i=s.getItemGraphicEl(e);i&&(i.style.text="",n.updateProps(i,{shape:{width:0}},t,e,function(){a[L](i)}))}).execute(),this._updateStyle(t,o,c),this._data=o},_updateStyle:function(t,e,r){function a(t,e,i,r,a){n.setText(t,e,i),t.text=r,"outside"===t.textPosition&&(t.textPosition=a)}e.eachItemGraphicEl(function(o,s){var l=e[d](s),c=e[O](s,"color"),h=e[O](s,U),f=e.getItemLayout(s),p=l[se]("itemStyle.normal"),v=l[se]("itemStyle.emphasis").getBarItemStyle();o.setShape("r",p.get("barBorderRadius")||0),o.useStyle(i[W]({fill:c,opacity:h},p.getBarItemStyle()));var m=r?f[ne]>0?$:"top":f.width>0?"left":"right",g=l[se]("label.normal"),y=l[se]("label.emphasis"),_=o.style;g.get("show")?a(_,g,c,i[u](t.getFormattedLabel(s,"normal"),t.getRawValue(s)),m):_.text="",y.get("show")?a(v,y,c,i[u](t.getFormattedLabel(s,"emphasis"),t.getRawValue(s)),m):v.text="",n.setHoverStyle(o,v)})},remove:function(t){var e=this.group;t.get(F)?this._data&&this._data.eachItemGraphicEl(function(i){i.style.text="",n.updateProps(i,{shape:{width:0}},t,i[R],function(){e[L](i)})}):e.removeAll()}})}),e("echarts/layout/barGrid",[ue,le,"../util/number"],function(t){function e(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function i(t){return t.dim+t.index}function n(t){var n={};a.each(t,function(t){var r=t[Z](),a=t[j],o=a.getBaseAxis(),l=o[_](),u=o.type===c?o.getBandWidth():Math.abs(l[1]-l[0])/r.count(),h=n[i(o)]||{bandWidth:u,remainedWidth:u,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},f=h.stacks;n[i(o)]=h;var d=e(t);f[d]||h.autoWidthCount++,f[d]=f[d]||{width:0,maxWidth:0};var p=s(t.get("barWidth"),u),v=s(t.get("barMaxWidth"),u),m=t.get("barGap"),g=t.get("barCategoryGap");p&&!f[d].width&&(p=Math.min(h.remainedWidth,p),f[d].width=p,h.remainedWidth-=p),v&&(f[d].maxWidth=v),null!=m&&(h.gap=m),null!=g&&(h.categoryGap=g)});var r={};return a.each(n,function(t,e){r[e]={};var i=t.stacks,n=t.bandWidth,o=s(t.categoryGap,n),l=s(t.gap,1),u=t.remainedWidth,c=t.autoWidthCount,h=(u-o)/(c+(c-1)*l);h=Math.max(h,0),a.each(i,function(t){var e=t.maxWidth;!t.width&&e&&h>e&&(e=Math.min(e,u),u-=e,t.width=e,c--)}),h=(u-o)/(c+(c-1)*l),h=Math.max(h,0);var f,d=0;a.each(i,function(t){t.width||(t.width=h),f=t,d+=t.width*(1+l)}),f&&(d-=f.width*l);var p=-d/2;a.each(i,function(t,i){r[e][i]=r[e][i]||{offset:p,width:t.width},p+=t.width*(1+l)})}),r}function r(t,r){var o=n(a.filter(r.getSeriesByType(t),function(t){return!r.isSeriesFiltered(t)&&t[j]&&"cartesian2d"===t[j].type})),s={},l={};r.eachSeriesByType(t,function(t){var n=t[Z](),r=t[j],a=r.getBaseAxis(),u=e(t),c=o[i(a)][u],h=c.offset,f=c.width,d=r.getOtherAxis(a),p=t.get("barMinHeight")||0,m=a.onZero?d.toGlobalCoord(d[v](0)):d.getGlobalExtent()[0],g=r.dataToPoints(n,!0);s[u]=s[u]||[],l[u]=l[u]||[],n.setLayout({offset:h,size:f}),n.each(d.dim,function(t,e){if(!isNaN(t)){s[u][e]||(s[u][e]={p:m,n:m},l[u][e]={p:m,n:m});var i,r,a,o,c=t>=0?"p":"n",v=g[e],y=s[u][e][c],_=l[u][e][c];d.isHorizontal()?(i=y,r=v[1]+h,a=v[0]-_,o=f,l[u][e][c]+=a,Math.abs(a)<p&&(a=(0>a?-1:1)*p),s[u][e][c]+=a):(i=v[0]+h,r=y,a=f,o=v[1]-_,l[u][e][c]+=o,Math.abs(o)<p&&(o=(0>=o?-1:1)*p),s[u][e][c]+=o),n.setItemLayout(e,{x:i,y:r,width:a,height:o})}},!0)},this)}var a=t(le),o=t("../util/number"),s=o.parsePercent;return r}),e("echarts/component/axis",[ue,"../coord/cartesian/AxisModel","./axis/AxisView"],function(t){t("../coord/cartesian/AxisModel"),t("./axis/AxisView")}),e("zrender/zrender",[ue,"./core/guid","./core/env","./core/util","./Handler","./Storage","./animation/Animation","./dom/HandlerProxy","./Painter"],function(t){function e(t){delete h[t]}var i=t("./core/guid"),n=t("./core/env"),r=t("./core/util"),a=t("./Handler"),o=t("./Storage"),s=t("./animation/Animation"),l=t("./dom/HandlerProxy"),u=!n[G],c={canvas:t("./Painter")},h={},f={};f.version="3.2.2",f.init=function(t,e){var n=new d(i(),t,e);return h[n.id]=n,n},f.dispose=function(t){if(t)t.dispose();else{for(var e in h)h.hasOwnProperty(e)&&h[e].dispose();h={}}return f},f.getInstance=function(t){return h[t]},f.registerPainter=function(t,e){c[t]=e};var d=function(t,e,i){i=i||{},this.dom=e,this.id=t;var h=this,f=new o,d=i.renderer;if(u){if(!c.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");d="vml"}else d&&c[d]||(d="canvas");var p=new c[d](e,f,i);this.storage=f,this.painter=p;var v=n.node?null:new l(p.getViewportRoot());this.handler=new a(f,p,v,p.root),this[F]=new s({stage:{update:r.bind(this.flush,this)}}),this[F].start(),this._needsRefresh;var m=f.delFromMap,g=f.addToMap;f.delFromMap=function(t){var e=f.get(t);m.call(f,t),e&&e.removeSelfFromZr(h)},f.addToMap=function(t){g.call(f,t),t.addSelfToZr(h)}};return d[H]={constructor:d,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){this._needsRefresh&&this.refreshImmediately(),this._needsRefreshHover&&this.refreshHoverImmediately()},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t[ne]),this.handler.resize()},clearAnimation:function(){this[F].clear()},getWidth:function(){return this.painter[te]()},getHeight:function(){return this.painter[J]()},pathToImage:function(t,e,n){var r=i();return this.painter.pathToImage(r,t,e,n)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this[F].stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this[F]=this.storage=this.painter=this.handler=null,e(this.id)}},f}),e("zrender/vml/Painter",[ue,"../core/log","./core"],function(t){function e(t){return parseInt(t,10)}function i(t,e){o.initVML(),this.root=t,this.storage=e;var i=document[y]("div"),n=document[y]("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var r=e.delFromMap,a=e.addToMap;e.delFromMap=function(t){var i=e.get(t);r.call(e,t),i&&i.onRemove&&i.onRemove(n)},e.addToMap=function(t){t.onAdd&&t.onAdd(n),a.call(e,t)},this._firstPaint=!0}function n(t){return function(){r('In IE8.0 VML mode painter not support method "'+t+'"')}}var r=t("../core/log"),o=t("./core");i[H]={constructor:i,getViewportRoot:function(){return this._vmlViewport},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t[B];i++){var n=t[i];n.invisible||n[N]?(n.__alreadyNotVisible||n.onRemove(e),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(e),n.__alreadyNotVisible=!1,n[a]&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,e),n.afterBrush&&n.afterBrush())),n[a]=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;
if(this._width!=t||this._height!=e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i[ne]=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,i=t.currentStyle;return(t.clientWidth||e(i.width))-e(i.paddingLeft)-e(i.paddingRight)|0},_getHeight:function(){var t=this.root,i=t.currentStyle;return(t.clientHeight||e(i[ne]))-e(i.paddingTop)-e(i.paddingBottom)|0}};for(var s=["getLayer","insertLayer","eachLayer","eachBuildinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],l=0;l<s[B];l++){var u=s[l];i[H][u]=n(u)}return i}),e("echarts/component/tooltip/TooltipModel",[ue,"../../echarts"],function(t){t("../../echarts").extendComponentModel({type:"tooltip",defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove",alwaysShowContent:!1,confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:!0,animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",lineStyle:{color:"#555",width:1,type:"solid"},crossStyle:{color:"#555",width:1,type:"dashed",textStyle:{}},shadowStyle:{color:"rgba(150,150,150,0.3)"}},textStyle:{color:"#fff",fontSize:14}}})}),e("echarts/component/tooltip/TooltipView",[ue,"./TooltipContent","../../util/graphic",le,"../../util/format","../../util/number","../../util/model","zrender/core/env","../../model/Model","../../echarts"],function(t){function e(t,e){if(!t||!e)return!1;var i=L.round;return i(t[0])===i(e[0])&&i(t[1])===i(e[1])}function i(t,e,i,n){return{x1:t,y1:e,x2:i,y2:n}}function a(t,e,i,n){return{x:t,y:e,width:i,height:n}}function s(t,e,i,n,r,a){return{cx:t,cy:e,r0:i,r:n,startAngle:r,endAngle:a,clockwise:!0}}function u(t,e,i,n,r){var a=i.clientWidth,o=i.clientHeight,s=20;return t+a+s>n?t-=a+s:t+=s,e+o+s>r?e-=o+s:e+=s,[t,e]}function f(t,e,i,n,r){var a=i.clientWidth,o=i.clientHeight;return t=Math.min(t+a,n)-a,e=Math.min(e+o,r)-o,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function p(t,e,i){var n=i.clientWidth,r=i.clientHeight,a=5,o=0,s=0,l=e.width,u=e[ne];switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+u/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case $:o=e.x+l/2-n/2,s=e.y+u+a;break;case"left":o=e.x-n-a,s=e.y+u/2-r/2;break;case"right":o=e.x+l+a,s=e.y+u/2-r/2}return[o,s]}function v(t,e,i,a,o,s,c,h){var d=h[te](),v=h[J](),m=c&&c[re]().clone();if(c&&m[l](c[r]),typeof t===b&&(t=t([e,i],s,o.el,m)),C[w](t))e=I(t[0],d),i=I(t[1],v);else if(typeof t===V&&c){var g=p(t,m,o.el);e=g[0],i=g[1]}else{var g=u(e,i,o.el,d,v);e=g[0],i=g[1]}if(a){var g=f(e,i,o.el,d,v);e=g[0],i=g[1]}o[n](e,i)}function y(t){var e=t[j],i=t.get("tooltip.trigger",!0);return!(!e||"cartesian2d"!==e.type&&"polar"!==e.type&&"singleAxis"!==e.type||"item"===i)}var x=t("./TooltipContent"),M=t("../../util/graphic"),C=t(le),P=t("../../util/format"),L=t("../../util/number"),z=t("../../util/model"),I=L.parsePercent,D=t("zrender/core/env"),O=t("../../model/Model");t("../../echarts").extendComponentView({type:"tooltip",_axisPointers:{},init:function(t,e){if(!D.node){var i=new x(e.getDom(),e);this._tooltipContent=i,e.on("showTip",this._manuallyShowTip,this),e.on("hideTip",this._manuallyHideTip,this)}},render:function(t,e,i){if(!D.node){this.group.removeAll(),this._axisPointers={},this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastHover={};var n=this._tooltipContent;n[k](),n.enterable=t.get("enterable"),this._alwaysShowContent=t.get("alwaysShowContent"),this._seriesGroupByAxis=this._prepareAxisTriggerData(t,e);var r=this._crossText;r&&this.group.add(r);var a=t.get("triggerOn");if(null!=this._lastX&&null!=this._lastY&&"none"!==a){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){o._manuallyShowTip({x:o._lastX,y:o._lastY})})}var s=this._api.getZr();s.off("click",this._tryShow),s.off("mousemove",this._mousemove),s.off(S,this._hide),s.off("globalout",this._hide),"click"===a?s.on("click",this._tryShow,this):"mousemove"===a&&(s.on("mousemove",this._mousemove,this),s.on(S,this._hide,this),s.on("globalout",this._hide,this))}},_mousemove:function(t){var e=this._tooltipModel.get("showDelay"),i=this;clearTimeout(this._showTimeout),e>0?this._showTimeout=setTimeout(function(){i._tryShow(t)},e):this._tryShow(t)},_manuallyShowTip:function(t){function e(e){var i=e[Z](),n=z.queryDataIndex(i,t);return null!=n&&!C[w](n)&&i.hasValue(n)?!0:void 0}if(t.from!==this.uid){var i=this._ecModel,n=t.seriesIndex,a=i.getSeriesByIndex(n),o=this._api,s="axis"===this._tooltipModel.get("trigger");if(null==t.x||null==t.y){if(s?(a&&!e(a)&&(a=null),a||i.eachSeries(function(t){y(t)&&!a&&e(t)&&(a=t)})):a=a||i.getSeriesByIndex(0),a){var u=a[Z](),c=z.queryDataIndex(u,t);if(null==c||C[w](c))return;var h,f,d=u.getItemGraphicEl(c),p=a[j];if(a.getTooltipPosition){var v=a.getTooltipPosition(c)||[];h=v[0],f=v[1]}else if(p&&p[g]){var v=p[g](u.getValues(C.map(p[m],function(t){return a.coordDimToDataDim(t)[0]}),c,!0));h=v&&v[0],f=v&&v[1]}else if(d){var _=d[re]().clone();_[l](d[r]),h=_.x+_.width/2,f=_.y+_[ne]/2}null!=h&&null!=f&&this._tryShow({offsetX:h,offsetY:f,position:t[Y],target:d,event:{}})}}else{var d=o.getZr().handler.findHover(t.x,t.y);this._tryShow({offsetX:t.x,offsetY:t.y,position:t[Y],target:d,event:{}})}}},_manuallyHideTip:function(t){t.from!==this.uid&&this._hide()},_prepareAxisTriggerData:function(t,e){var i={};return e.eachSeries(function(t){if(y(t)){var e,n,r=t[j];"cartesian2d"===r.type?(e=r.getBaseAxis(),n=e.dim+e.index):"singleAxis"===r.type?(e=r.getAxis(),n=e.dim+e.type):(e=r.getBaseAxis(),n=e.dim+r.name),i[n]=i[n]||{coordSys:[],series:[]},i[n].coordSys.push(r),i[n][A].push(t)}},this),i},_tryShow:function(t){var e=t[ee],i=this._tooltipModel,n=i.get("trigger"),r=this._ecModel,a=this._api;if(i)if(this._lastX=t.offsetX,this._lastY=t.offsetY,e&&null!=e[R]){var o=e.dataModel||r.getSeriesByIndex(e.seriesIndex),s=e[R],l=o[Z]()[d](s);"axis"===(l.get("tooltip.trigger")||n)?this._showAxisTooltip(i,r,t):(this._ticket="",this._hideAxisPointer(),this._resetLastHover(),this._showItemTooltipContent(o,s,e.dataType,t)),a.dispatchAction({type:"showTip",from:this.uid,dataIndexInside:e[R],seriesIndex:e.seriesIndex})}else if(e&&e.tooltip){var u=e.tooltip;if(typeof u===V){var c=u;u={content:c,formatter:c}}var h=new O(u,i),f=h.get("content"),p=Math.random();this._showTooltipContent(h,f,h.get("formatterParams")||{},p,t.offsetX,t.offsetY,t[Y],e,a)}else"item"===n?this._hide():this._showAxisTooltip(i,r,t),"cross"===i.get("axisPointer.type")&&a.dispatchAction({type:"showTip",from:this.uid,x:t.offsetX,y:t.offsetY})},_showAxisTooltip:function(t,i,n){var r=t[se]("axisPointer"),a=r.get("type");if("cross"===a){var o=n[ee];if(o&&null!=o[R]){var s=i.getSeriesByIndex(o.seriesIndex),l=o[R];this._showItemTooltipContent(s,l,o.dataType,n)}}this._showAxisPointer();var u=!0;C.each(this._seriesGroupByAxis,function(i){var o=i.coordSys,s=o[0],l=[n.offsetX,n.offsetY];if(!s.containPoint(l))return void this._hideAxisPointer(s.name);u=!1;var c=s[m],h=s.pointToData(l,!0);l=s[g](h);var f=s.getBaseAxis(),d=r.get("axis");"auto"===d&&(d=f.dim);var p=!1,v=this._lastHover;if("cross"===a)e(v.data,h)&&(p=!0),v.data=h;else{var y=C[E](c,d);v.data===h[y]&&(p=!0),v.data=h[y]}var _=t.get(F);"cartesian2d"!==s.type||p?"polar"!==s.type||p?"singleAxis"!==s.type||p||this._showSinglePointer(r,s,d,l,_):this._showPolarPointer(r,s,d,l,_):this._showCartesianPointer(r,s,d,l,_),"cross"!==a&&this._dispatchAndShowSeriesTooltipContent(s,i[A],l,h,p,n[Y])},this),this._tooltipModel.get("show")||this._hideAxisPointer(),u&&this._hide()},_showCartesianPointer:function(t,e,n,r,o){function s(n,r,a){var o="x"===n?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),s=u._getPointerElement(e,t,n,o);M.subPixelOptimizeLine({shape:o,style:s.style}),d?M.updateProps(s,{shape:o},t):s.attr({shape:o})}function l(i,n,r){var o=e.getAxis(i),s=o.getBandWidth(),l=r[1]-r[0],c="x"===i?a(n[0]-s/2,r[0],s,l):a(r[0],n[1]-s/2,l,s),h=u._getPointerElement(e,t,i,c);d?M.updateProps(h,{shape:c},t):h.attr({shape:c})}var u=this,h=t.get("type"),f=e.getBaseAxis(),d=o&&"cross"!==h&&f.type===c&&f.getBandWidth()>20;if("cross"===h)s("x",r,e.getAxis("y").getGlobalExtent()),s("y",r,e.getAxis("x").getGlobalExtent()),this._updateCrossText(e,r,t);else{var p=e.getAxis("x"===n?"y":"x"),v=p.getGlobalExtent();"cartesian2d"===e.type&&("line"===h?s:l)(n,r,v)}},_showSinglePointer:function(t,e,n,r,a){function o(n,r,a){var o=e.getAxis(),l=o.orient,c="horizontal"===l?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),h=s._getPointerElement(e,t,n,c);u?M.updateProps(h,{shape:c},t):h.attr({shape:c})}var s=this,l=t.get("type"),u=a&&"cross"!==l&&e.getBaseAxis().type===c,h=e.getRect(),f=[h.y,h.y+h[ne]];o(n,r,f)},_showPolarPointer:function(t,e,n,r,a){function o(n,r,a){var o,s=e.pointToCoord(r);if("angle"===n){var l=e.coordToPoint([a[0],s[1]]),c=e.coordToPoint([a[1],s[1]]);o=i(l[0],l[1],c[0],c[1])}else o={cx:e.cx,cy:e.cy,r:s[0]};var h=u._getPointerElement(e,t,n,o);p?M.updateProps(h,{shape:o},t):h.attr({shape:o})}function l(i,n,r){var a,o=e.getAxis(i),l=o.getBandWidth(),c=e.pointToCoord(n),h=Math.PI/180;a="angle"===i?s(e.cx,e.cy,r[0],r[1],(-c[1]-l/2)*h,(-c[1]+l/2)*h):s(e.cx,e.cy,c[0]-l/2,c[0]+l/2,0,2*Math.PI);var f=u._getPointerElement(e,t,i,a);p?M.updateProps(f,{shape:a},t):f.attr({shape:a})}var u=this,h=t.get("type"),f=e.getAngleAxis(),d=e.getRadiusAxis(),p=a&&"cross"!==h&&e.getBaseAxis().type===c;if("cross"===h)o("angle",r,d[_]()),o("radius",r,f[_]()),this._updateCrossText(e,r,t);else{var v=e.getAxis("radius"===n?"angle":"radius"),m=v[_]();("line"===h?o:l)(n,r,m)}},_updateCrossText:function(t,e,i){var n=i[se]("crossStyle"),r=n[se](oe),a=this._tooltipModel,o=this._crossText;o||(o=this._crossText=new M.Text({style:{textAlign:"left",textVerticalAlign:"bottom"}}),this.group.add(o));var s=t.pointToData(e),l=t[m];s=C.map(s,function(e,i){var n=t.getAxis(l[i]);return e=n.type===c||"time"===n.type?n.scale.getLabel(e):P.addCommas(e.toFixed(n.getPixelPrecision()))}),o[X]({fill:r.getTextColor()||n.get("color"),textFont:r[ae](),text:s.join(", "),x:e[0]+5,y:e[1]-5}),o.z=a.get("z"),o[T]=a.get(T)},_getPointerElement:function(t,e,i,n){var r=this._tooltipModel,a=r.get("z"),s=r.get(T),l=this._axisPointers,u=t.name;if(l[u]=l[u]||{},l[u][i])return l[u][i];var c=e.get("type"),h=e[se](c+"Style"),f="shadow"===c,d=h[f?"getAreaStyle":"getLineStyle"](),p="polar"===t.type?f?"Sector":"radius"===i?"Circle":"Line":f?"Rect":"Line";f?d[o]=null:d.fill=null;var v=l[u][i]=new M[p]({style:d,z:a,zlevel:s,silent:!0,shape:n});return this.group.add(v),v},_dispatchAndShowSeriesTooltipContent:function(t,e,i,n,r,a){var o,s=this._tooltipModel,l=t.getBaseAxis(),u="x"===l.dim||"radius"===l.dim?0:1,h=C.map(e,function(t){return{seriesIndex:t.seriesIndex,dataIndexInside:t.getAxisTooltipDataIndex?t.getAxisTooltipDataIndex(t.coordDimToDataDim(l.dim),n,l):t[Z]().indexOfNearest(t.coordDimToDataDim(l.dim)[0],n[u],!1,l.type===c?.5:null)}});C.each(h,function(t,i){e[i][Z]().hasValue(t.dataIndexInside)&&(o=i)}),o=o||0;var f=this._lastHover,d=this._api;if(f.payloadBatch&&!r&&d.dispatchAction({type:"downplay",batch:f.payloadBatch}),r||(d.dispatchAction({type:"highlight",batch:h}),f.payloadBatch=h),d.dispatchAction({type:"showTip",dataIndexInside:h[o].dataIndexInside,seriesIndex:h[o].seriesIndex,from:this.uid}),l&&s.get("showContent")&&s.get("show")){var p=C.map(e,function(t,e){return t.getDataParams(h[e].dataIndexInside)});if(r)v(a||s.get(Y),i[0],i[1],s.get("confine"),this._tooltipContent,p,null,d);else{var m=h[o].dataIndexInside,g="time"===l.type?l.scale.getLabel(n[u]):e[o][Z]().getName(m),y=(g?g+"<br />":"")+C.map(e,function(t,e){return t.formatTooltip(h[e].dataIndexInside,!0)}).join("<br />"),_="axis_"+t.name+"_"+m;this._showTooltipContent(s,y,p,_,i[0],i[1],a,null,d)}}},_showItemTooltipContent:function(t,e,i,n){var r=this._api,a=t[Z](i),o=a[d](e),s=o.get("tooltip",!0);if(typeof s===V){var l=s;s={formatter:l}}var u=this._tooltipModel,c=t[se]("tooltip",u),f=new O(s,c,c[h]),p=t.getDataParams(e,i),v=t.formatTooltip(e,!1,i),m="item_"+t.name+"_"+e;this._showTooltipContent(f,v,p,m,n.offsetX,n.offsetY,n[Y],n[ee],r)},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var u=this._tooltipContent,c=t.get("confine"),h=t.get("formatter");o=o||t.get(Y);var f=e;if(h)if(typeof h===V)f=P.formatTpl(h,i);else if(typeof h===b){var d=this,p=n,m=function(t,e){t===d._ticket&&(u.setContent(e),v(o,r,a,c,u,i,s,l))};d._ticket=p,f=h(i,p,m)}u.show(t),u.setContent(f),v(o,r,a,c,u,i,s,l)}},_showAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&C.each(e,function(t){t.show()})}else this.group.eachChild(function(t){t.show()}),this.group.show()},_resetLastHover:function(){var t=this._lastHover;t.payloadBatch&&this._api.dispatchAction({type:"downplay",batch:t.payloadBatch}),this._lastHover={}},_hideAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&C.each(e,function(t){t.hide()})}else this.group.children()[B]&&this.group.hide()},_hide:function(){clearTimeout(this._showTimeout),this._hideAxisPointer(),this._resetLastHover(),this._alwaysShowContent||this._tooltipContent.hideLater(this._tooltipModel.get("hideDelay")),this._api.dispatchAction({type:"hideTip",from:this.uid}),this._lastX=this._lastY=null},dispose:function(t,e){if(!D.node){var i=e.getZr();this._tooltipContent.hide(),i.off("click",this._tryShow),i.off("mousemove",this._mousemove),i.off(S,this._hide),i.off("globalout",this._hide),e.off("showTip",this._manuallyShowTip),e.off("hideTip",this._manuallyHideTip)}}})}),e("zrender/vml/graphic",[ue,"../core/env","../core/vector","../core/BoundingRect","../core/PathProxy","../tool/color","../contain/text","../graphic/mixin/RectText","../graphic/Displayable","../graphic/Image","../graphic/Text","../graphic/Path","../graphic/Gradient","./core"],function(t){if(!t("../core/env")[G]){var e=t("../core/vector"),i=t("../core/BoundingRect"),n=t("../core/PathProxy").CMD,a=t("../tool/color"),u=t("../contain/text"),c=t("../graphic/mixin/RectText"),h=t("../graphic/Displayable"),f=t("../graphic/Image"),d=t("../graphic/Text"),p=t("../graphic/Path"),v=t("../graphic/Gradient"),m=t("./core"),g=Math.round,_=Math.sqrt,x=Math.abs,b=Math.cos,w=Math.sin,S=Math.max,C=e[l],A=",",P="progid:DXImageTransform.Microsoft",L=21600,z=L/2,k=1e5,D=1e3,O=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=L+","+L,t.coordorigin="0,0"},R=function(t){return String(t)[M](/&/g,"&amp;")[M](/"/g,"&quot;")},E=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},N=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},F=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},q=function(t,e,i){return(parseFloat(t)||0)*k+(parseFloat(e)||0)*D+i},W=function(t,e){return typeof t===V?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},j=function(t,e,i){var n=a.parse(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=E(n[0],n[1],n[2]),t[U]=i*n[3])},Z=function(t){var e=a.parse(t);return[E(e[0],e[1],e[2]),e[3]]},X=function(t,e,i){var n=e.fill;if(null!=n)if(n instanceof v){var a,o=0,s=[0,0],l=0,u=1,c=i[re](),h=c.width,f=c[ne];if("linear"===n.type){a="gradient";var d=i[r],p=[n.x*h,n.y*f],m=[n.x2*h,n.y2*f];d&&(C(p,p,d),C(m,m,d));var g=m[0]-p[0],y=m[1]-p[1];o=180*Math.atan2(g,y)/Math.PI,0>o&&(o+=360),1e-6>o&&(o=0)}else{a="gradientradial";var p=[n.x*h,n.y*f],d=i[r],_=i.scale,x=h,b=f;s=[(p[0]-c.x)/x,(p[1]-c.y)/b],d&&C(p,p,d),x/=_[0]*L,b/=_[1]*L;var w=S(x,b);l=0/w,u=2*n.r/w-l}var M=n[I].slice();M.sort(function(t,e){return t.offset-e.offset});for(var T=M[B],A=[],P=[],z=0;T>z;z++){var k=M[z],D=Z(k.color);P.push(k.offset*u+l+" "+D[0]),(0===z||z===T-1)&&A.push(D)}if(T>=2){var O=A[0][0],R=A[1][0],E=A[0][1]*e[U],N=A[1][1]*e[U];t.type=a,t.method="none",t.focus="100%",t.angle=o,t.color=O,t.color2=R,t.colors=P.join(","),t[U]=N,t.opacity2=E}"radial"===a&&(t.focusposition=s.join(","))}else j(t,n,e[U])},Y=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e[o]||e[o]instanceof v||j(t,e[o],e[U])},J=function(t,e,i,n){var r="fill"==e,a=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i[s])?(t[r?"filled":"stroked"]="true",i[e]instanceof v&&F(t,a),a||(a=m.createNode(e)),r?X(a,i,n):Y(a,i),N(t,a)):(t[r?"filled":"stroked"]="false",F(t,a))},te=[[],[],[]],ee=function(t,e){var i,r,a,o,s,l,u=n.M,c=n.C,h=n.L,f=n.A,d=n.Q,p=[];for(o=0;o<t[B];){switch(a=t[o++],r="",i=0,a){case u:r=" m ",i=1,s=t[o++],l=t[o++],te[0][0]=s,te[0][1]=l;break;case h:r=" l ",i=1,s=t[o++],l=t[o++],te[0][0]=s,te[0][1]=l;break;case d:case c:r=" c ",i=3;var v,m,y=t[o++],x=t[o++],M=t[o++],T=t[o++];a===d?(v=M,m=T,M=(M+2*y)/3,T=(T+2*x)/3,y=(s+2*y)/3,x=(l+2*x)/3):(v=t[o++],m=t[o++]),te[0][0]=y,te[0][1]=x,te[1][0]=M,te[1][1]=T,te[2][0]=v,te[2][1]=m,s=v,l=m;break;case f:var S=0,P=0,I=1,k=1,D=0;e&&(S=e[4],P=e[5],I=_(e[0]*e[0]+e[1]*e[1]),k=_(e[2]*e[2]+e[3]*e[3]),D=Math.atan2(-e[1]/k,e[0]/I));var O=t[o++],R=t[o++],E=t[o++],N=t[o++],G=t[o++]+D,F=t[o++]+G+D;o++;var V=t[o++],H=O+b(G)*E,q=R+w(G)*N,y=O+b(F)*E,x=R+w(F)*N,W=V?" wa ":" at ";Math.abs(H-y)<1e-10&&(Math.abs(F-G)>.01?V&&(H+=270/L):Math.abs(q-R)<1e-10?V&&O>H||!V&&H>O?x-=270/L:x+=270/L:V&&R>q||!V&&q>R?y+=270/L:y-=270/L),p.push(W,g(((O-E)*I+S)*L-z),A,g(((R-N)*k+P)*L-z),A,g(((O+E)*I+S)*L-z),A,g(((R+N)*k+P)*L-z),A,g((H*I+S)*L-z),A,g((q*k+P)*L-z),A,g((y*I+S)*L-z),A,g((x*k+P)*L-z)),s=y,l=x;break;case n.R:var j=te[0],Z=te[1];j[0]=t[o++],j[1]=t[o++],Z[0]=j[0]+t[o++],Z[1]=j[1]+t[o++],e&&(C(j,j,e),C(Z,Z,e)),j[0]=g(j[0]*L-z),Z[0]=g(Z[0]*L-z),j[1]=g(j[1]*L-z),Z[1]=g(Z[1]*L-z),p.push(" m ",j[0],A,j[1]," l ",Z[0],A,j[1]," l ",Z[0],A,Z[1]," l ",j[0],A,Z[1]);break;case n.Z:p.push(" x ")}if(i>0){p.push(r);for(var U=0;i>U;U++){var X=te[U];e&&C(X,X,e),p.push(g(X[0]*L-z),A,g(X[1]*L-z),i-1>U?A:"")}}}return p.join("")};p[H].brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=m.createNode("shape"),O(i),this._vmlEl=i),J(i,"fill",e,this),J(i,o,e,this);var n=this[r],a=null!=n,l=i.getElementsByTagName(o)[0];if(l){var u=e[s];if(a&&!e.strokeNoScale){var c=n[0]*n[3]-n[1]*n[2];u*=_(x(c))}l.weight=u+"px"}var h=this.path;this.__dirtyPath&&(h.beginPath(),this.buildPath(h,this.shape),h.toStatic(),this.__dirtyPath=!1),i.path=ee(h.data,this[r]),i.style.zIndex=q(this[T],this.z,this.z2),N(t,i),null!=e.text?this.drawRectText(t,this[re]()):this.removeRectText(t)},p[H].onRemove=function(t){F(t,this._vmlEl),this.removeRectText(t)},p[H].onAdd=function(t){N(t,this._vmlEl),this.appendRectText(t)};var ie=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};f[H].brushVML=function(t){var e,i,n=this.style,a=n.image;if(ie(a)){var o=a.src;if(o===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var s=a.runtimeStyle,l=s.width,u=s[ne];s.width="auto",s[ne]="auto",e=a.width,i=a[ne],s.width=l,s[ne]=u,this._imageSrc=o,this._imageWidth=e,this._imageHeight=i}a=o}else a===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(a){var c=n.x||0,h=n.y||0,f=n.width,d=n[ne],p=n.sWidth,v=n.sHeight,x=n.sx||0,b=n.sy||0,w=p&&v,M=this._vmlEl;M||(M=m.doc[y]("div"),O(M),this._vmlEl=M);var L,z=M.style,I=!1,k=1,D=1;if(this[r]&&(L=this[r],k=_(L[0]*L[0]+L[1]*L[1]),D=_(L[2]*L[2]+L[3]*L[3]),I=L[1]||L[2]),I){var R=[c,h],E=[c+f,h],B=[c,h+d],G=[c+f,h+d];C(R,R,L),C(E,E,L),C(B,B,L),C(G,G,L);var F=S(R[0],E[0],B[0],G[0]),V=S(R[1],E[1],B[1],G[1]),H=[];H.push("M11=",L[0]/k,A,"M12=",L[2]/D,A,"M21=",L[1]/k,A,"M22=",L[3]/D,A,"Dx=",g(c*k+L[4]),A,"Dy=",g(h*D+L[5])),z.padding="0 "+g(F)+"px "+g(V)+"px 0",z.filter=P+".Matrix("+H.join("")+", SizingMethod=clip)"}else L&&(c=c*k+L[4],h=h*D+L[5]),z.filter="",z.left=g(c)+"px",z.top=g(h)+"px";var W=this._imageEl,j=this._cropEl;W||(W=m.doc[y]("div"),this._imageEl=W);var Z=W.style;if(w){if(e&&i)Z.width=g(k*e*f/p)+"px",Z[ne]=g(D*i*d/v)+"px";else{var X=new Image,Y=this;X.onload=function(){X.onload=null,e=X.width,i=X[ne],Z.width=g(k*e*f/p)+"px",Z[ne]=g(D*i*d/v)+"px",Y._imageWidth=e,Y._imageHeight=i,Y._imageSrc=a},X.src=a}j||(j=m.doc[y]("div"),j.style.overflow="hidden",this._cropEl=j);var $=j.style;$.width=g((f+x*f/p)*k),$[ne]=g((d+b*d/v)*D),$.filter=P+".Matrix(Dx="+-x*f/p*k+",Dy="+-b*d/v*D+")",j.parentNode||M.appendChild(j),W.parentNode!=j&&j.appendChild(W)}else Z.width=g(k*f)+"px",Z[ne]=g(D*d)+"px",M.appendChild(W),j&&j.parentNode&&(M.removeChild(j),this._cropEl=null);var Q="",K=n[U];1>K&&(Q+=".Alpha(opacity="+g(100*K)+") "),Q+=P+".AlphaImageLoader(src="+a+", SizingMethod=scale)",Z.filter=Q,M.style.zIndex=q(this[T],this.z,this.z2),N(t,M),null!=n.text&&this.drawRectText(t,this[re]())}},f[H].onRemove=function(t){F(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},f[H].onAdd=function(t){N(t,this._vmlEl),this.appendRectText(t)};var ae,oe="normal",se={},le=0,ue=100,ce=document[y]("div"),he=function(t){var e=se[t];if(!e){le>ue&&(le=0,se={});var i,n=ce.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(r){}e={style:n.fontStyle||oe,variant:n.fontVariant||oe,weight:n.fontWeight||oe,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},se[t]=e,le++}return e};u.measureText=function(t,e){var i=m.doc;ae||(ae=i[y]("div"),ae.style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",m.doc.body.appendChild(ae));try{ae.style.font=e}catch(n){}return ae.innerHTML="",ae.appendChild(i.createTextNode(t)),{width:ae.offsetWidth}};for(var fe=new i,de=function(t,e,i,n){var a=this.style,s=a.text;if(null!=s&&(s+=""),s){var c,h,f=a.textAlign,d=he(a.textFont),p=d.style+" "+d.variant+" "+d.weight+" "+d.size+'px "'+d.family+'"',v=a.textBaseline,y=a.textVerticalAlign;i=i||u[re](s,p,f,v);var _=this[r];if(_&&!n&&(fe.copy(e),fe[l](_),e=fe),n)c=e.x,h=e.y;else{var x=a.textPosition,b=a.textDistance;if(x instanceof Array)c=e.x+W(x[0],e.width),h=e.y+W(x[1],e[ne]),f=f||"left",v=v||"top";else{var w=u.adjustTextPositionOnRect(x,e,i,b);c=w.x,h=w.y,f=f||w.textAlign,v=v||w.textBaseline}}if(y){switch(y){case K:h-=i[ne]/2;break;case $:h-=i[ne]}v="top"}var M=d.size;switch(v){case"hanging":case"top":h+=M/1.75;break;case K:break;default:h-=M/2.25}switch(f){case"left":break;case Q:c-=i.width/2;break;case"right":c-=i.width}var S,P,L,z=m.createNode,I=this._textVmlEl;I?(L=I.firstChild,S=L.nextSibling,P=S.nextSibling):(I=z("line"),S=z("path"),P=z("textpath"),L=z("skew"),P.style["v-text-align"]="left",O(I),S.textpathok=!0,P.on=!0,I.from="0 0",I.to="1000 0.05",N(I,L),N(I,S),N(I,P),this._textVmlEl=I);var k=[c,h],D=I.style;_&&n?(C(k,k,_),L.on=!0,L.matrix=_[0].toFixed(3)+A+_[2].toFixed(3)+A+_[1].toFixed(3)+A+_[3].toFixed(3)+",0,0",L.offset=(g(k[0])||0)+","+(g(k[1])||0),L.origin="0 0",D.left="0px",D.top="0px"):(L.on=!1,D.left=g(c)+"px",D.top=g(h)+"px"),P[V]=R(s);try{P.style.font=p}catch(E){}J(I,"fill",{fill:n?a.fill:a.textFill,opacity:a[U]},this),J(I,o,{stroke:n?a[o]:a.textStroke,opacity:a[U],lineDash:a.lineDash},this),I.style.zIndex=q(this[T],this.z,this.z2),N(t,I)}},pe=function(t){F(t,this._textVmlEl),this._textVmlEl=null},ve=function(t){N(t,this._textVmlEl)},me=[c,h,f,p,d],ge=0;ge<me[B];ge++){var ye=me[ge][H];ye.drawRectText=de,ye.removeRectText=pe,ye.appendRectText=ve}d[H].brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this[re](),!0):this.removeRectText(t)},d[H].onRemove=function(t){this.removeRectText(t)},d[H].onAdd=function(t){this.appendRectText(t)}}}),e("zrender/core/env",[],function(){function t(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:document[y]("canvas").getContext?!0:!1,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=10)}}var e={};return e=typeof navigator===i?{browser:{},os:{},node:!0,canvasSupported:!0}:t(navigator.userAgent)}),e("echarts/model/Global",[ue,le,"../util/model","./Model","./Component","./globalDefault","./mixin/colorPalette"],function(t){function e(t,e){l.each(e,function(e,i){g.hasClass(i)||("object"==typeof e?t[i]=t[i]?l.merge(t[i],e,!1):l.clone(e):null==t[i]&&(t[i]=e))})}function i(t){t=t,this.option={},this.option[_]=1,this._componentsMap={},this._seriesIndices=null,e(t,this._theme.option),l.merge(t,y,!1),this.mergeOption(t)}function n(t,e){l[w](e)||(e=e?[e]:[]);var i={};return h(e,function(e){i[e]=(t[e]||[]).slice()}),i}function r(t,e,i){var n=e.type?e.type:i?i.subType:g.determineSubType(t,e);return n}function a(t){return d(t,function(t){return t.componentIndex})||[]}function o(t,e){return e.hasOwnProperty("subType")?f(t,function(t){return t.subType===e.subType}):t}function s(t){}var l=t(le),u=t("../util/model"),c=t("./Model"),h=l.each,f=l.filter,d=l.map,p=l[w],v=l[E],m=l[z],g=t("./Component"),y=t("./globalDefault"),_="\x00_ec_inner",x=c[P]({constructor:x,init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new c(i),this._optionManager=n},setOption:function(t,e){l.assert(!(_ in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption()},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var r=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(r)):i.call(this,r),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=n.getTimelineOption(this);a&&(this.mergeOption(a),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o[B]&&h(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,s){var c=u.normalizeToArray(t[e]),f=u.mappingToExists(o[e],c);u.makeIdAndName(f),h(f,function(t){var i=t.option;m(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=r(e,i,t.exist))});var d=n(o,s);i[e]=[],o[e]=[],h(f,function(t,n){var r=t.exist,a=t.option;if(l.assert(m(a)||r,"Empty component definition"),a){var s=g.getClass(e,t.keyInfo.subType,!0);if(r&&r instanceof s)r.name=t.keyInfo.name,r.mergeOption(a,this),r.optionUpdated(a,!1);else{var u=l[P]({dependentModels:d,componentIndex:n},t.keyInfo);r=new s(a,this,this,u),l[P](r,u),r.init(a,this,this,u),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);o[e][n]=r,i[e][n]=r.option},this),e===A&&(this._seriesIndices=a(o[A]))}var i=this.option,o=this._componentsMap,s=[];h(t,function(t,e){null!=t&&(g.hasClass(e)?s.push(e):i[e]=null==i[e]?l.clone(t):l.merge(i[e],t,!0))}),g.topologicalTravel(s,g.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=l.clone(this.option);return h(t,function(e,i){if(g.hasClass(i)){for(var e=u.normalizeToArray(e),n=e[B]-1;n>=0;n--)u.isIdInner(e[n])&&e[C](n,1);t[i]=e}}),delete t[_],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap[t];return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap[e];if(!a||!a[B])return[];var s;if(null!=i)p(i)||(i=[i]),s=f(d(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var l=p(n);s=f(a,function(t){return l&&v(n,t.id)>=0||!l&&t.id===n})}else if(null!=r){var u=p(r);s=f(a,function(t){return u&&v(r,t.name)>=0||!u&&t.name===r})}else s=a;return o(s,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return t&&(t.hasOwnProperty(e)||t.hasOwnProperty(i)||t.hasOwnProperty(n))?{mainType:r,index:t[e],id:t[i],name:t[n]}:null}function i(e){return t.filter?f(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),s=a?this.queryComponents(a):this._componentsMap[r];return i(o(s,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if(typeof t===b)i=e,e=t,h(n,function(t,n){h(t,function(t,r){e.call(i,n,t,r)})});else if(l.isString(t))h(n[t],e,i);else if(m(t)){var r=this.findComponents(t);h(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap[A];return f(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap[A][t]},getSeriesByType:function(t){var e=this._componentsMap[A];return f(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap[A].slice()},eachSeries:function(t,e){s(this),h(this._seriesIndices,function(i){var n=this._componentsMap[A][i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){h(this._componentsMap[A],t,e)},eachSeriesByType:function(t,e,i){s(this),h(this._seriesIndices,function(n){var r=this._componentsMap[A][n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return h(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return s(this),l[E](this._seriesIndices,t.componentIndex)<0},filterSeries:function(t,e){s(this);var i=f(this._componentsMap[A],t,e);this._seriesIndices=a(i)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=a(t[A]);var e=[];h(t,function(t,i){e.push(i)}),g.topologicalTravel(e,g.getAllClassMainTypes(),function(e){h(t[e],function(t){t.restoreData()})})}});return l.mixin(x,t("./mixin/colorPalette")),x}),e("echarts/ExtensionAPI",[ue,le],function(t){function e(t){i.each(n,function(e){this[e]=i.bind(t[e],t)},this)}var i=t(le),n=["getDom","getZr",te,J,"dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL",se,"getOption"];return e}),e("echarts/CoordinateSystem",[ue,le],function(t){function e(){this._coordinateSystems=[]}var i=t(le),n={};return e[H]={constructor:e,create:function(t,e){var r=[];i.each(n,function(i){var n=i[D](t,e);r=r.concat(n||[])}),this._coordinateSystems=r},update:function(t,e){i.each(this._coordinateSystems,function(i){i[k]&&i[k](t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},e.register=function(t,e){n[t]=e},e.get=function(t){return n[t]},e}),e("echarts/model/OptionManager",[ue,le,"../util/model","./Component"],function(t){function e(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function i(t,e,i){var n,r,a=[],o=[],l=t.timeline;if(t.baseOption&&(r=t.baseOption),(l||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var u=t.media;c(u,function(t){t&&t.option&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=l),c([r].concat(a).concat(s.map(o,function(t){return t.option})),function(t){c(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function n(t,e,i){var n={width:e,height:i,aspectratio:e/i},a=!0;return s.each(t,function(t,e){var i=e.match(p);if(i&&i[1]&&i[2]){var o=i[1],s=i[2][q]();r(n[s],t,o)||(a=!1)}}),a}function r(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function a(t,e){return t.join(",")===e.join(",")}function o(t,e){e=e||{},c(e,function(e,i){if(null!=e){var n=t[i];if(u.hasClass(i)){e=l.normalizeToArray(e),n=l.normalizeToArray(n);var r=l.mappingToExists(n,e);t[i]=f(r,function(t){return t.option&&t.exist?d(t.exist,t.option,!0):t.exist||t.option})}else t[i]=d(n,e,!0)}})}var s=t(le),l=t("../util/model"),u=t("./Component"),c=s.each,h=s.clone,f=s.map,d=s.merge,p=/^(min|max)?(.+)$/;return e[H]={constructor:e,setOption:function(t,e){t=h(t,!0);var n=this._optionBackup,r=i.call(this,t,e,!n);this._newBaseOption=r.baseOption,n?(o(n.baseOption,r.baseOption),r.timelineOptions[B]&&(n.timelineOptions=r.timelineOptions),r.mediaList[B]&&(n.mediaList=r.mediaList),r.mediaDefault&&(n.mediaDefault=r.mediaDefault)):this._optionBackup=r},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=f(e.timelineOptions,h),this._mediaList=f(e.mediaList,h),this._mediaDefault=h(e.mediaDefault),this._currentMediaIndices=[],h(t?e.baseOption:this._newBaseOption)
},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i[B]){var n=t.getComponent("timeline");n&&(e=h(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api[te](),e=this._api[J](),i=this._mediaList,r=this._mediaDefault,o=[],s=[];if(!i[B]&&!r)return s;for(var l=0,u=i[B];u>l;l++)n(i[l].query,t,e)&&o.push(l);return!o[B]&&r&&(o=[-1]),o[B]&&!a(o,this._currentMediaIndices)&&(s=f(o,function(t){return h(-1===t?r.option:i[t].option)})),this._currentMediaIndices=o,s}},e}),e("echarts/model/Component",[ue,"./Model",le,"../util/component","../util/clazz","../util/layout","./mixin/boxLayout"],function(t){function e(t){var e=[];return n.each(l.getClassesByMainType(t),function(t){r.apply(e,t[H].dependencies||[])}),n.map(e,function(t){return o.parseClassType(t).main})}var i=t("./Model"),n=t(le),r=Array[H].push,a=t("../util/component"),o=t("../util/clazz"),s=t("../util/layout"),l=i[P]({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,r){i.call(this,t,e,n,r),this.uid=a.getUID("componentModel")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,r=i?s.getLayoutParams(t):{},a=e.getTheme();n.merge(t,a.get(this.mainType)),n.merge(t,this.getDefaultOption()),i&&s.mergeLayoutParam(t,r,i)},mergeOption:function(t){n.merge(this.option,t,!0);var e=this.layoutMode;e&&s.mergeLayoutParam(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){if(!this.hasOwnProperty("__defaultOption")){for(var t=[],e=this.constructor;e;){var i=e[H].defaultOption;i&&t.push(i),e=e.superClass}for(var r={},a=t[B]-1;a>=0;a--)r=n.merge(r,t[a],!0);this.__defaultOption=r}return this.__defaultOption},getReferringComponents:function(t){return this[h].queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});return o.enableClassManagement(l,{registerWhenExtend:!0}),a.enableSubTypeDefaulter(l),a.enableTopologicalTravel(l,e),n.mixin(l,t("./mixin/boxLayout")),l}),e("echarts/model/Series",[ue,le,"../util/format","../util/model","./Component","./mixin/colorPalette","zrender/core/env","../util/layout"],function(t){var e=t(le),i=t("../util/format"),n=t("../util/model"),r=t("./Component"),a=t("./mixin/colorPalette"),o=t("zrender/core/env"),s=t("../util/layout"),l=i.encodeHTML,u=i.addCommas,c=r[P]({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.mergeDefaultAndTheme(t,i),this._dataBeforeProcessed=this.getInitialData(t,i),this._data=this._dataBeforeProcessed.cloneShallow()},mergeDefaultAndTheme:function(t,i){var r=this.layoutMode,a=r?s.getLayoutParams(t):{};e.merge(t,i.getTheme().get(this.subType)),e.merge(t,this.getDefaultOption()),n.defaultEmphasis(t.label,n.LABEL_OPTIONS),this.fillDataTextStyle(t.data),r&&s.mergeLayoutParam(t,a,r)},mergeOption:function(t,i){t=e.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&s.mergeLayoutParam(this.option,t,n);var r=this.getInitialData(t,i);r&&(this._data=r,this._dataBeforeProcessed=r.cloneShallow())},fillDataTextStyle:function(t){if(t)for(var e=0;e<t[B];e++)t[e]&&t[e].label&&n.defaultEmphasis(t[e].label,n.LABEL_OPTIONS)},getInitialData:function(){},getData:function(t){return null==t?this._data:this._data.getLinkedData(t)},setData:function(t){this._data=t},getRawData:function(){return this._dataBeforeProcessed},coordDimToDataDim:function(t){return[t]},dataDimToCoordDim:function(t){return t},getBaseAxis:function(){var t=this[j];return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,n){function r(t){var r=[];return e.each(t,function(t,e){var o,s=a.getDimensionInfo(e),l=s&&s.type;o=l===p?t+"":"time"===l?n?"":i.formatTime("yyyy/MM/dd hh:mm:ss",t):u(t),o&&r.push(o)}),r.join(", ")}var a=this._data,o=this.getRawValue(t),s=e[w](o)?r(o):u(o),c=a.getName(t),h=a[O](t,"color");e[z](h)&&h[I]&&(h=(h[I][0]||{}).color),h=h||"transparent";var f='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+h+'"></span>',d=this.name;return"\x00-"===d&&(d=""),n?f+l(this.name)+" : "+s:(d&&l(d)+"<br />")+f+(c?l(c)+" : "+s:s)},ifEnableAnimation:function(){if(o.node)return!1;var t=this[f](F);return t&&this[Z]().count()>this[f]("animationThreshold")&&(t=!1),t},restoreData:function(){this._data=this._dataBeforeProcessed.cloneShallow()},getColorFromPalette:function(t,e){var i=this[h],n=a.getColorFromPalette.call(this,t,e);return n||(n=i.getColorFromPalette(t,e)),n},getAxisTooltipDataIndex:null,getTooltipPosition:null});return e.mixin(c,n.dataFormatMixin),e.mixin(c,a),c}),e("echarts/view/Component",[ue,"zrender/container/Group","../util/component","../util/clazz"],function(t){var e=t("zrender/container/Group"),i=t("../util/component"),n=t("../util/clazz"),r=function(){this.group=new e,this.uid=i.getUID("viewComponent")};r[H]={constructor:r,init:function(){},render:function(){},dispose:function(){}};var a=r[H];return a.updateView=a.updateLayout=a.updateVisual=function(){},n.enableClassExtend(r),n.enableClassManagement(r,{registerWhenExtend:!0}),r}),e("echarts/util/model",[ue,"./format","./number","../model/Model",le],function(t){function e(t,e){return t&&t.hasOwnProperty(e)}var i=t("./format"),n=t("./number"),r=t("../model/Model"),a=t(le),o=a.each,s=a[z],l={};return l.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},l.defaultEmphasis=function(t,e){if(t){var i=t.emphasis=t.emphasis||{},n=t.normal=t.normal||{};o(e,function(t){var e=a[u](i[t],n[t]);null!=e&&(i[t]=e)})}},l.LABEL_OPTIONS=[Y,"show",oe,"distance","formatter"],l.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},l.isDataItemOption=function(t){return s(t)&&!(t instanceof Array)},l.converDataValue=function(t,e){var i=e&&e.type;return i===p?t:("time"!==i||isFinite(t)||null==t||"-"===t||(t=+n.parseDate(t)),null==t||""===t?0/0:+t)},l.createDataFormatModel=function(t,e){var i=new r;return a.mixin(i,l.dataFormatMixin),i.seriesIndex=e.seriesIndex,i.name=e.name||"",i.mainType=e.mainType,i.subType=e.subType,i[Z]=function(){return t},i},l.dataFormatMixin={getDataParams:function(t,e){var i=this[Z](e),n=this.seriesIndex,r=this.name,a=this.getRawValue(t,e),o=i.getRawIndex(t),s=i.getName(t,!0),l=i.getRawDataItem(t);return{componentType:this.mainType,componentSubType:this.subType,seriesType:this.mainType===A?this.subType:null,seriesIndex:n,seriesName:r,name:s,dataIndex:o,data:l,dataType:e,value:a,color:i[O](t,"color"),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,r){e=e||"normal";var a=this[Z](n),o=a[d](t),s=this.getDataParams(t,n);null!=r&&s.value instanceof Array&&(s.value=s.value[r]);var l=o.get(["label",e,"formatter"]);return typeof l===b?(s.status=e,l(s)):typeof l===V?i.formatTpl(l,s):void 0},getRawValue:function(t,e){var i=this[Z](e),n=i.getRawDataItem(t);return null!=n?!s(n)||n instanceof Array?n:n.value:void 0},formatTooltip:a.noop},l.mappingToExists=function(t,e){e=(e||[]).slice();var i=a.map(t||[],function(t){return{exist:t}});return o(e,function(t,n){if(s(t)){for(var r=0;r<i[B];r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i[B];r++){var a=i[r].exist;if(!(i[r].option||null!=a.id&&null!=t.id||null==t.name||l.isIdInner(t)||l.isIdInner(a)||a.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),o(e,function(t){if(s(t)){for(var e=0;e<i[B];e++){var n=i[e].exist;if(!i[e].option&&!l.isIdInner(n)&&null==t.id){i[e].option=t;break}}e>=i[B]&&i.push({option:t})}}),i},l.makeIdAndName=function(t){var e={};o(t,function(t){var i=t.exist;i&&(e[i.id]=t)}),o(t,function(t){var i=t.option;a.assert(!i||null==i.id||!e[i.id]||e[i.id]===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&(e[i.id]=t),!t.keyInfo&&(t.keyInfo={})}),o(t,function(t){var i=t.exist,n=t.option,r=t.keyInfo;if(s(n)){if(r.name=null!=n.name?n.name+"":i?i.name:"\x00-",i)r.id=i.id;else if(null!=n.id)r.id=n.id+"";else{var a=0;do r.id="\x00"+r.name+"\x00"+a++;while(e[r.id])}e[r.id]=t}})},l.isIdInner=function(t){return s(t)&&t.id&&0===(t.id+"")[E]("\x00_ec_\x00")},l.compressBatches=function(t,e){function i(t,e,i){for(var n=0,r=t[B];r>n;n++)for(var a=t[n].seriesId,o=l.normalizeToArray(t[n][R]),s=i&&i[a],u=0,c=o[B];c>u;u++){var h=o[u];s&&s[h]?s[h]=null:(e[a]||(e[a]={}))[h]=1}}function n(t,e){var i=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r])if(e)i.push(+r);else{var a=n(t[r],!0);a[B]&&i.push({seriesId:r,dataIndex:a})}return i}var r={},a={};return i(t||[],r),i(e||[],a,r),[n(r),n(a)]},l.queryDataIndex=function(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e[R]?a[w](e[R])?a.map(e[R],function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e[R]):null!=e.name?a[w](e.name)?a.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0},l.parseFinder=function(t,i,n){if(a.isString(i)){var r={};r[i+"Index"]=0,i=r}var s=n&&n.defaultMainType;!s||e(i,s+"Index")||e(i,s+"Id")||e(i,s+"Name")||(i[s+"Index"]=0);var l={};return o(i,function(e,n){var e=i[n];if(n===R||"dataIndexInside"===n)return void(l[n]=e);var r=n.match(/^(\w+)(Index|Id|Name)$/)||[],a=r[1],o=r[2];if(a&&o){var s={mainType:a};s[o[q]()]=e;var u=t.queryComponents(s);l[a+"Models"]=u,l[a+"Model"]=u[0]}}),l},l}),e("echarts/util/throttle",[],function(){var t={},e="\x00__throttleOriginMethod",i="\x00__throttleRate",n="\x00__throttleType";return t.throttle=function(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l=0,u=0,c=null;e=e||0;var h=function(){r=(new Date).getTime(),o=this,s=arguments,a=r-(i?l:u)-e,clearTimeout(c),i?c=setTimeout(n,e):a>=0?n():c=setTimeout(n,-a),l=r};return h.clear=function(){c&&(clearTimeout(c),c=null)},h},t.createOrUpdate=function(r,a,o,s){var l=r[a];if(l){var u=l[e]||l,c=l[n],h=l[i];if(h!==o||c!==s){if(null==o||!s)return r[a]=u;l=r[a]=t.throttle(u,o,"debounce"===s),l[e]=u,l[n]=s,l[i]=o}return l}},t.clear=function(t,i){var n=t[i];n&&n[e]&&(t[i]=n[e])},t}),e("zrender/mixin/Eventful",[ue],function(){var t=Array[H].slice,e=function(){this._$handlers={}};return e[H]={constructor:e,one:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][B];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!0,ctx:i||this}),this},on:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][B];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!1,ctx:i||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t][B]},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t][B];a>r;r++)i[t][r].h!=e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t][B]&&delete i[t]}else delete i[t];return this},trigger:function(e){if(this._$handlers[e]){var i=arguments,n=i[B];n>3&&(i=t.call(i,1));for(var r=this._$handlers[e],a=r[B],o=0;a>o;){switch(n){case 1:r[o].h.call(r[o].ctx);break;case 2:r[o].h.call(r[o].ctx,i[1]);break;case 3:r[o].h.call(r[o].ctx,i[1],i[2]);break;default:r[o].h.apply(r[o].ctx,i)}r[o].one?(r[C](o,1),a--):o++}}return this},triggerWithContext:function(e){if(this._$handlers[e]){var i=arguments,n=i[B];n>4&&(i=t.call(i,1,i[B]-1));for(var r=i[i[B]-1],a=this._$handlers[e],o=a[B],s=0;o>s;){switch(n){case 1:a[s].h.call(r);break;case 2:a[s].h.call(r,i[1]);break;case 3:a[s].h.call(r,i[1],i[2]);break;default:a[s].h.apply(r,i)}a[s].one?(a[C](s,1),o--):s++}}return this}},e}),e("zrender/tool/color",[ue],function(){function t(t){return t=Math.round(t),0>t?0:t>255?255:t}function e(t){return t=Math.round(t),0>t?0:t>360?360:t}function i(t){return 0>t?0:t>1?1:t}function n(e){return t(e[B]&&"%"===e.charAt(e[B]-1)?parseFloat(e)/100*255:parseInt(e,10))}function r(t){return i(t[B]&&"%"===t.charAt(t[B]-1)?parseFloat(t)/100:parseFloat(t))}function a(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function o(t,e,i){return t+(e-t)*i}function s(t){if(t){t+="";var e=t[M](/ /g,"")[q]();if(e in g)return g[e].slice();if("#"!==e.charAt(0)){var i=e[E]("("),a=e[E](")");if(-1!==i&&a+1===e[B]){var o=e.substr(0,i),s=e.substr(i+1,a-(i+1)).split(","),u=1;switch(o){case"rgba":if(4!==s[B])return;u=r(s.pop());case"rgb":if(3!==s[B])return;return[n(s[0]),n(s[1]),n(s[2]),u];case"hsla":if(4!==s[B])return;return s[3]=r(s[3]),l(s);case"hsl":if(3!==s[B])return;return l(s);default:return}}}else{if(4===e[B]){var c=parseInt(e.substr(1),16);if(!(c>=0&&4095>=c))return;return[(3840&c)>>4|(3840&c)>>8,240&c|(240&c)>>4,15&c|(15&c)<<4,1]}if(7===e[B]){var c=parseInt(e.substr(1),16);if(!(c>=0&&16777215>=c))return;return[(16711680&c)>>16,(65280&c)>>8,255&c,1]}}}}function l(e){var i=(parseFloat(e[0])%360+360)%360/360,n=r(e[1]),o=r(e[2]),s=.5>=o?o*(n+1):o+n-o*n,l=2*o-s,u=[t(255*a(l,s,i+1/3)),t(255*a(l,s,i)),t(255*a(l,s,i-1/3))];return 4===e[B]&&(u[3]=e[3]),u}function u(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,u=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>u?l/(s+o):l/(2-s-o);var c=((s-n)/6+l/2)/l,h=((s-r)/6+l/2)/l,f=((s-a)/6+l/2)/l;n===s?e=f-h:r===s?e=1/3+c-f:a===s&&(e=2/3+h-c),0>e&&(e+=1),e>1&&(e-=1)}var d=[360*e,i,u];return null!=t[3]&&d.push(t[3]),d}}function c(t,e){var i=s(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0;return m(i,4===i[B]?"rgba":"rgb")}}function h(t){var e=s(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function f(e,i,n){if(i&&i[B]&&e>=0&&1>=e){n=n||[0,0,0,0];var r=e*(i[B]-1),a=Math.floor(r),s=Math.ceil(r),l=i[a],u=i[s],c=r-a;return n[0]=t(o(l[0],u[0],c)),n[1]=t(o(l[1],u[1],c)),n[2]=t(o(l[2],u[2],c)),n[3]=t(o(l[3],u[3],c)),n}}function d(e,n,r){if(n&&n[B]&&e>=0&&1>=e){var a=e*(n[B]-1),l=Math.floor(a),u=Math.ceil(a),c=s(n[l]),h=s(n[u]),f=a-l,d=m([t(o(c[0],h[0],f)),t(o(c[1],h[1],f)),t(o(c[2],h[2],f)),i(o(c[3],h[3],f))],"rgba");return r?{color:d,leftIndex:l,rightIndex:u,value:a}:d}}function p(t,i,n,a){return t=s(t),t?(t=u(t),null!=i&&(t[0]=e(i)),null!=n&&(t[1]=r(n)),null!=a&&(t[2]=r(a)),m(l(t),"rgba")):void 0}function v(t,e){return t=s(t),t&&null!=e?(t[3]=i(e),m(t,"rgba")):void 0}function m(t,e){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}var g={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};return{parse:s,lift:c,toHex:h,fastMapToColor:f,mapToColor:d,modifyHSL:p,modifyAlpha:v,stringify:m}}),e("zrender/core/timsort",[],function(){function t(t){for(var e=0;t>=l;)e|=1&t,t>>=1;return t+e}function e(t,e,n,r){var a=e+1;if(a===n)return 1;if(r(t[a++],t[e])<0){for(;n>a&&r(t[a],t[a-1])<0;)a++;i(t,e,a)}else for(;n>a&&r(t[a],t[a-1])>=0;)a++;return a-e}function i(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function n(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var u=n-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=o}}function r(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}for(o++;l>o;){var c=o+(l-o>>>1);a(t,e[i+c])>0?o=c+1:l=c}return l}function a(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var c=o+(l-o>>>1);a(t,e[i+c])<0?l=c:o=c+1}return l}function o(t,e){function i(t,e){f[y]=t,d[y]=e,y+=1}function n(){for(;y>1;){var t=y-2;if(t>=1&&d[t-1]<=d[t]+d[t+1]||t>=2&&d[t-2]<=d[t]+d[t-1])d[t-1]<d[t+1]&&t--;else if(d[t]>d[t+1])break;s(t)}}function o(){for(;y>1;){var t=y-2;t>0&&d[t-1]<d[t+1]&&t--,s(t)}}function s(i){var n=f[i],o=d[i],s=f[i+1],u=d[i+1];d[i]=o+u,i===y-3&&(f[i+1]=f[i+2],d[i+1]=d[i+2]),y--;var c=a(t[s],t,n,o,0,e);n+=c,o-=c,0!==o&&(u=r(t[n+o-1],t,s,u,u-1,e),0!==u&&(u>=o?l(n,o,s,u):h(n,o,s,u)))}function l(i,n,o,s){var l=0;for(l=0;n>l;l++)_[l]=t[i+l];var c=0,h=o,f=i;if(t[f++]=t[h++],0!==--s){if(1===n){for(l=0;s>l;l++)t[f+l]=t[h+l];return void(t[f+s]=_[c])}for(var d,v,m,g=p;;){d=0,v=0,m=!1;do if(e(t[h],_[c])<0){if(t[f++]=t[h++],v++,d=0,0===--s){m=!0;break}}else if(t[f++]=_[c++],d++,v=0,1===--n){m=!0;break}while(g>(d|v));if(m)break;do{if(d=a(t[h],_,c,n,0,e),0!==d){for(l=0;d>l;l++)t[f+l]=_[c+l];if(f+=d,c+=d,n-=d,1>=n){m=!0;break}}if(t[f++]=t[h++],0===--s){m=!0;break}if(v=r(_[c],t,h,s,0,e),0!==v){for(l=0;v>l;l++)t[f+l]=t[h+l];if(f+=v,h+=v,s-=v,0===s){m=!0;break}}if(t[f++]=_[c++],1===--n){m=!0;break}g--}while(d>=u||v>=u);if(m)break;0>g&&(g=0),g+=2}if(p=g,1>p&&(p=1),1===n){for(l=0;s>l;l++)t[f+l]=t[h+l];t[f+s]=_[c]}else{if(0===n)throw new Error;for(l=0;n>l;l++)t[f+l]=_[c+l]}}else for(l=0;n>l;l++)t[f+l]=_[c+l]}function h(i,n,o,s){var l=0;for(l=0;s>l;l++)_[l]=t[o+l];var c=i+n-1,h=s-1,f=o+s-1,d=0,v=0;if(t[f--]=t[c--],0!==--n){if(1===s){for(f-=n,c-=n,v=f+1,d=c+1,l=n-1;l>=0;l--)t[v+l]=t[d+l];return void(t[f]=_[h])}for(var m=p;;){var g=0,y=0,x=!1;do if(e(_[h],t[c])<0){if(t[f--]=t[c--],g++,y=0,0===--n){x=!0;break}}else if(t[f--]=_[h--],y++,g=0,1===--s){x=!0;break}while(m>(g|y));if(x)break;do{if(g=n-a(_[h],t,i,n,n-1,e),0!==g){for(f-=g,c-=g,n-=g,v=f+1,d=c+1,l=g-1;l>=0;l--)t[v+l]=t[d+l];if(0===n){x=!0;break}}if(t[f--]=_[h--],1===--s){x=!0;break}if(y=s-r(t[c],_,0,s,s-1,e),0!==y){for(f-=y,h-=y,s-=y,v=f+1,d=h+1,l=0;y>l;l++)t[v+l]=_[d+l];if(1>=s){x=!0;break}}if(t[f--]=t[c--],0===--n){x=!0;break}m--}while(g>=u||y>=u);if(x)break;0>m&&(m=0),m+=2}if(p=m,1>p&&(p=1),1===s){for(f-=n,c-=n,v=f+1,d=c+1,l=n-1;l>=0;l--)t[v+l]=t[d+l];t[f]=_[h]}else{if(0===s)throw new Error;for(d=f-(s-1),l=0;s>l;l++)t[d+l]=_[l]}}else for(d=f-(s-1),l=0;s>l;l++)t[d+l]=_[l]}var f,d,p=u,v=0,m=c,g=0,y=0;v=t[B],2*c>v&&(m=v>>>1);var _=[];g=120>v?5:1542>v?10:119151>v?19:40,f=[],d=[],this.mergeRuns=n,this.forceMergeRuns=o,this.pushRun=i}function s(i,r,a,s){a||(a=0),s||(s=i[B]);var u=s-a;if(!(2>u)){var c=0;if(l>u)return c=e(i,a,s,r),void n(i,a,s,a+c,r);var h=new o(i,r),f=t(u);do{if(c=e(i,a,s,r),f>c){var d=u;d>f&&(d=f),n(i,a,a+d,a+c,r),c=d}h.pushRun(a,c),h.mergeRuns(),u-=c,a+=c}while(0!==u);h.forceMergeRuns()}}var l=32,u=7,c=256;return s}),e("echarts/preprocessor/backwardCompat",[ue,le,"./helper/compatStyle"],function(t){function e(t,e){e=e.split(",");for(var i=t,n=0;n<e[B]&&(i=i&&i[e[n]],null!=i);n++);return i}function i(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e[B]-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function n(t){u(o,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var r=t(le),a=t("./helper/compatStyle"),o=[["x","left"],["y","top"],["x2","right"],["y2",$]],s=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],l=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],u=r.each;return function(t){u(t[A],function(t){if(r[z](t)){var o=t.type;if(a(t),("pie"===o||"gauge"===o)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===o){var s=e(t,"pointer.color");null!=s&&i(t,"itemStyle.normal.color",s)}for(var u=0;u<l[B];u++)if(l[u]===t.type){n(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),u(s,function(e){var i=t[e];i&&(r[w](i)||(i=[i]),u(i,function(t){n(t)}))})}}),e("echarts/loading/default",[ue,"../util/graphic",le],function(t){var e=t("../util/graphic"),i=t(le),n=Math.PI;return function(t,r){r=r||{},i[W](r,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var a=new e.Rect({style:{fill:r.maskColor},zlevel:r[T],z:1e4}),o=new e.Arc({shape:{startAngle:-n/2,endAngle:-n/2+.1,r:10},style:{stroke:r.color,lineCap:"round",lineWidth:5},zlevel:r[T],z:10001}),s=new e.Rect({style:{fill:"none",text:r.text,textPosition:"right",textDistance:10,textFill:r.textColor},zlevel:r[T],z:10001});o.animateShape(!0).when(1e3,{endAngle:3*n/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*n/2}).delay(300).start("circularInOut");var l=new e.Group;return l.add(o),l.add(s),l.add(a),l.resize=function(){var e=t[te]()/2,i=t[J]()/2;o.setShape({cx:e,cy:i});var n=o.shape.r;s.setShape({x:e-n,y:i-n,width:2*n,height:2*n}),a.setShape({x:0,y:0,width:t[te](),height:t[J]()})},l.resize(),l}}),e("echarts/model/Model",[ue,le,"../util/clazz","./mixin/lineStyle","./mixin/areaStyle","./mixin/textStyle","./mixin/itemStyle"],function(t){function e(t,e,i){this.parentModel=e,this[h]=i,this.option=t}var i=t(le),n=t("../util/clazz");e[H]={constructor:e,init:null,mergeOption:function(t){i.merge(this.option,t,!0)},get:function(t,e){if(!t)return this.option;typeof t===V&&(t=t.split("."));for(var i=this.option,n=this.parentModel,r=0;r<t[B]&&(!t[r]||(i=i&&"object"==typeof i?i[t[r]]:null,null!=i));r++);return null==i&&n&&!e&&(i=n.get(t)),i},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=this.parentModel;return null==n&&r&&!e&&(n=r[f](t)),n},getModel:function(t,i){var n=this.get(t,!0),r=this.parentModel,a=new e(n,i||r&&r[se](t),this[h]);return a},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i.clone(this.option))},setReadOnly:function(t){n.setReadOnly(this,t)}},n.enableClassExtend(e);var r=i.mixin;return r(e,t("./mixin/lineStyle")),r(e,t("./mixin/areaStyle")),r(e,t("./mixin/textStyle")),r(e,t("./mixin/itemStyle")),e}),e("echarts/data/List",[ue,"../model/Model","./DataDiffer",le,"../util/model"],function(t){function e(t){return f[w](t)||(t=[t]),t}function n(t,e){var i=t[m],n=new x(f.map(i,t.getDimensionInfo,t),t.hostModel);_(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i[B];o++){var s=i[o],l=a[s];r[s]=f[E](e,s)>=0?new l.constructor(a[s][B]):a[s]}return n}var r=i,a=typeof window===i?global:window,o=typeof a.Float64Array===r?Array:a.Float64Array,s=typeof a.Int32Array===r?Array:a.Int32Array,l={"float":o,"int":s,ordinal:Array,number:Array,time:Array},u=t("../model/Model"),c=t("./DataDiffer"),f=t(le),v=t("../util/model"),g=f[z],y=["stackedOn","hasItemOption","_nameList","_idList","_rawData"],_=function(t,e){f.each(y.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods},x=function(t,e){t=t||["x","y"];for(var i={},n=[],r=0;r<t[B];r++){var a,o={};typeof t[r]===V?(a=t[r],o={name:a,stackable:!1,type:"number"}):(o=t[r],a=o.name,o.type=o.type||"number"),n.push(a),i[a]=o}this[m]=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},M=x[H];M.type="list",M.hasItemOption=!0,M.getDimension=function(t){return isNaN(t)||(t=this[m][t]||t),t},M.getDimensionInfo=function(t){return f.clone(this._dimensionInfos[this.getDimension(t)])},M.initData=function(t,e,i){t=t||[],this._rawData=t;var n=this._storage={},r=this.indices=[],a=this[m],o=t[B],s=this._dimensionInfos,u=[],c={};e=e||[];for(var h=0;h<a[B];h++){var f=s[a[h]],d=l[f.type];n[a[h]]=new d(o)}var p=this;i||(p.hasItemOption=!1),i=i||function(t,e,i,n){var r=v.getDataItemValue(t);return v.isDataItemOption(t)&&(p.hasItemOption=!0),v.converDataValue(r instanceof Array?r[n]:r,s[e])};for(var g=0;g<t[B];g++){for(var y=t[g],_=0;_<a[B];_++){var x=a[_],b=n[x];b[g]=i(y,x,g,_)}r.push(g)}for(var h=0;h<t[B];h++){e[h]||t[h]&&null!=t[h].name&&(e[h]=t[h].name);var w=e[h]||"",M=t[h]&&t[h].id;!M&&w&&(c[w]=c[w]||0,M=w,c[w]>0&&(M+="__ec__"+c[w]),c[w]++),M&&(u[h]=M)}this._nameList=e,this._idList=u},M.count=function(){return this.indices[B]},M.get=function(t,e,i){var n=this._storage,r=this.indices[e];if(null==r)return 0/0;var a=n[t]&&n[t][r];if(i){var o=this._dimensionInfos[t];if(o&&o.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(a>=0&&l>0||0>=a&&0>l)&&(a+=l),s=s.stackedOn}}return a},M.getValues=function(t,e,i){var n=[];f[w](t)||(i=e,e=t,t=this[m]);for(var r=0,a=t[B];a>r;r++)n.push(this.get(t[r],e,i));return n},M.hasValue=function(t){for(var e=this[m],i=this._dimensionInfos,n=0,r=e[B];r>n;n++)if(i[e[n]].type!==p&&isNaN(this.get(e[n],t)))return!1;return!0},M.getDataExtent=function(t,e){t=this.getDimension(t);var i=this._storage[t],n=this.getDimensionInfo(t);e=n&&n.stackable&&e;var r,a=(this._extent||(this._extent={}))[t+!!e];if(a)return a;if(i){for(var o=1/0,s=-1/0,l=0,u=this.count();u>l;l++)r=this.get(t,l,e),o>r&&(o=r),r>s&&(s=r);return this._extent[t+!!e]=[o,s]}return[1/0,-1/0]},M.getSum=function(t,e){var i=this._storage[t],n=0;if(i)for(var r=0,a=this.count();a>r;r++){var o=this.get(t,r,e);isNaN(o)||(n+=o)}return n},M[E]=function(t,e){var i=this._storage,n=i[t],r=this.indices;if(n)for(var a=0,o=r[B];o>a;a++){var s=r[a];if(n[s]===e)return a}return-1},M.indexOfName=function(t){for(var e=this.indices,i=this._nameList,n=0,r=e[B];r>n;n++){var a=e[n];if(i[a]===t)return n}return-1},M.indexOfRawIndex=function(t){var e=this.indices,i=e[t];if(null!=i&&i===t)return t;for(var n=0,r=e[B]-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},M.indexOfNearest=function(t,e,i,n){var r=this._storage,a=r[t];null==n&&(n=1/0);var o=-1;if(a)for(var s=Number.MAX_VALUE,l=0,u=this.count();u>l;l++){var c=e-this.get(t,l,i),h=Math.abs(c);n>=c&&(s>h||h===s&&c>0)&&(s=h,o=l)}return o},M.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},M.getRawDataItem=function(t){return this._rawData[this.getRawIndex(t)]},M.getName=function(t){return this._nameList[this.indices[t]]||""},M.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},M.each=function(t,i,n,r){typeof t===b&&(r=n,n=i,i=t,t=[]),t=f.map(e(t),this.getDimension,this);var a=[],o=t[B],s=this.indices;r=r||this;for(var l=0;l<s[B];l++)switch(o){case 0:i.call(r,l);break;case 1:i.call(r,this.get(t[0],l,n),l);break;case 2:i.call(r,this.get(t[0],l,n),this.get(t[1],l,n),l);break;default:for(var u=0;o>u;u++)a[u]=this.get(t[u],l,n);a[u]=l,i.apply(r,a)}},M.filterSelf=function(t,i,n,r){typeof t===b&&(r=n,n=i,i=t,t=[]),t=f.map(e(t),this.getDimension,this);var a=[],o=[],s=t[B],l=this.indices;r=r||this;for(var u=0;u<l[B];u++){var c;if(1===s)c=i.call(r,this.get(t[0],u,n),u);else{for(var h=0;s>h;h++)o[h]=this.get(t[h],u,n);o[h]=u,c=i.apply(r,o)}c&&a.push(l[u])}return this.indices=a,this._extent={},this},M.mapArray=function(t,e,i,n){typeof t===b&&(n=i,i=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i,n),r},M.map=function(t,i,r,a){t=f.map(e(t),this.getDimension,this);var o=n(this,t),s=o.indices=this.indices,l=o._storage,u=[];return this.each(t,function(){var e=arguments[arguments[B]-1],n=i&&i.apply(this,arguments);if(null!=n){"number"==typeof n&&(u[0]=n,n=u);for(var r=0;r<n[B];r++){var a=t[r],o=l[a],c=s[e];o&&(o[c]=n[r])}}},r,a),o},M.downSample=function(t,e,i,r){for(var a=n(this,[t]),o=this._storage,s=a._storage,l=this.indices,u=a.indices=[],c=[],h=[],f=Math.floor(1/e),d=s[t],p=this.count(),v=0;v<o[t][B];v++)s[t][v]=o[t][v];for(var v=0;p>v;v+=f){f>p-v&&(f=p-v,c[B]=f);for(var m=0;f>m;m++){var g=l[v+m];c[m]=d[g],h[m]=g}var y=i(c),g=h[r(c,y)||0];d[g]=y,u.push(g)}return a},M[d]=function(t){var e=this.hostModel;return t=this.indices[t],new u(this._rawData[t],e,e&&e[h])},M.diff=function(t){var e,i=this._idList,n=t&&t._idList,r="e\x00\x00";return new c(t?t.indices:[],this.indices,function(t){return null!=(e=n[t])?e:r+t},function(t){return null!=(e=i[t])?e:r+t})},M.getVisual=function(t){var e=this._visual;return e&&e[t]},M.setVisual=function(t,e){if(g(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},M.setLayout=function(t,e){if(g(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},M.getLayout=function(t){return this._layout[t]},M.getItemLayout=function(t){return this._itemLayouts[t]},M.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?f[P](this._itemLayouts[t]||{},e):e},M.clearItemLayouts=function(){this._itemLayouts[B]=0},M[O]=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)
},M.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};if(this._itemVisuals[t]=n,g(e))for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);else n[e]=i},M.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var T=function(t){t.seriesIndex=this.seriesIndex,t[R]=this[R],t.dataType=this.dataType};return M.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e[R]=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(T,e)),this._graphicEls[t]=e},M.getItemGraphicEl=function(t){return this._graphicEls[t]},M.eachItemGraphicEl=function(t,e){f.each(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},M.cloneShallow=function(){var t=f.map(this[m],this.getDimensionInfo,this),e=new x(t,this.hostModel);return e._storage=this._storage,_(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=f[P]({},this._extent)),e},M.wrapMethod=function(t,e){var i=this[t];typeof i===b&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(f.slice(arguments)))})},M.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],M.CHANGABLE_METHODS=["filterSelf"],x}),e("echarts/util/format",[ue,le,"./number","zrender/contain/text"],function(t){var e=t(le),i=t("./number"),n=t("zrender/contain/text"),r={};r.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0][M](/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t[B]>1?"."+t[1]:""))},r.toCamelCase=function(t,e){return t=(t||"")[q]()[M](/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},r.normalizeCssArray=function(t){var e=t[B];return"number"==typeof t?[t,t,t,t]:2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t},r.encodeHTML=function(t){return String(t)[M](/&/g,"&amp;")[M](/</g,"&lt;")[M](/>/g,"&gt;")[M](/"/g,"&quot;")[M](/'/g,"&#39;")};var a=["a","b","c","d","e","f","g"],o=function(t,e){return"{"+t+(null==e?"":e)+"}"};r.formatTpl=function(t,i){e[w](i)||(i=[i]);var n=i[B];if(!n)return"";for(var r=i[0].$vars||[],s=0;s<r[B];s++){var l=a[s];t=t[M](o(l),o(l,0))}for(var u=0;n>u;u++)for(var c=0;c<r[B];c++)t=t[M](o(a[c],u),i[u][r[c]]);return t};var s=function(t){return 10>t?"0"+t:t};return r.formatTime=function(t,e){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=i.parseDate(e),r=n.getFullYear(),a=n.getMonth()+1,o=n.getDate(),l=n.getHours(),u=n.getMinutes(),c=n.getSeconds();return t=t[M]("MM",s(a))[q]()[M]("yyyy",r)[M]("yy",r%100)[M]("dd",s(o))[M]("d",o)[M]("hh",s(l))[M]("h",l)[M]("mm",s(u))[M]("m",u)[M]("ss",s(c))[M]("s",c)},r.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},r.truncateText=n.truncateText,r}),e("zrender/core/matrix",[],function(){var t=typeof Float32Array===i?Array:Float32Array,e={create:function(){var i=new t(6);return e.identity(i),i},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t},translate:function(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t},rotate:function(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],u=Math.sin(i),c=Math.cos(i);return t[0]=n*c+o*u,t[1]=-n*u+o*c,t[2]=r*c+s*u,t[3]=-r*u+c*s,t[4]=c*a+u*l,t[5]=c*l-u*a,t},scale:function(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t},invert:function(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}};return e}),e("zrender/core/vector",[],function(){var t=typeof Float32Array===i?Array:Float32Array,e={create:function(e,i){var n=new t(2);return null==e&&(e=0),null==i&&(i=0),n[0]=e,n[1]=i,n},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(e){var i=new t(2);return i[0]=e[0],i[1]=e[1],i},set:function(t,e,i){return t[0]=e,t[1]=i,t},add:function(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t},scaleAndAdd:function(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t},sub:function(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t},div:function(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t},normalize:function(t,i){var n=e.len(i);return 0===n?(t[0]=0,t[1]=0):(t[0]=i[0]/n,t[1]=i[1]/n),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t},applyTransform:function(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t},min:function(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t},max:function(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}};return e[B]=e.len,e.lengthSquare=e.lenSquare,e.dist=e.distance,e.distSquare=e.distanceSquare,e}),e("echarts/scale/Interval",[ue,"../util/number","../util/format","./Scale"],function(t){var e=t("../util/number"),i=t("../util/format"),n=t("./Scale"),r=Math.floor,a=Math.ceil,o=e.getPrecisionSafe,s=e.round,l=n[P]({type:"interval",_interval:0,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l[H].setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval||this.niceTicks(),this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice()},getTicks:function(){this._interval||this.niceTicks();var t=this._interval,e=this._extent,i=[],n=1e4;if(t){var r=this._niceExtent,a=o(t)+2;e[0]<r[0]&&i.push(e[0]);for(var l=r[0];l<=r[1];)if(i.push(l),l=s(l+t,a),i[B]>n)return[];e[1]>(i[B]?i[i[B]-1]:r[1])&&i.push(e[1])}return i},getTicksLabels:function(){for(var t=[],e=this.getTicks(),i=0;i<e[B];i++)t.push(this.getLabel(e[i]));return t},getLabel:function(t){return i.addCommas(t)},niceTicks:function(t){t=t||5;var i=this._extent,n=i[1]-i[0];if(isFinite(n)){0>n&&(n=-n,i.reverse());var l=s(e.nice(n/t,!0),Math.max(o(i[0]),o(i[1]))+2),u=o(l)+2,c=[s(a(i[0]/l)*l,u),s(r(i[1]/l)*l,u)];this._interval=l,this._niceExtent=c}},niceExtent:function(t,e,i){var n=this._extent;if(n[0]===n[1])if(0!==n[0]){var o=n[0];i?n[0]-=o/2:(n[1]+=o/2,n[0]-=o/2)}else n[1]=1;var l=n[1]-n[0];isFinite(l)||(n[0]=0,n[1]=1),this.niceTicks(t);var u=this._interval;e||(n[0]=s(r(n[0]/u)*u)),i||(n[1]=s(a(n[1]/u)*u))}});return l[D]=function(){return new l},l}),e("echarts/scale/Scale",[ue,"../util/clazz"],function(t){function e(){this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}var i=t("../util/clazz"),n=e[H];return n.parse=function(t){return t},n[x]=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},n.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},n.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},n.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},n[_]=function(){return this._extent.slice()},n.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},n.getTicksLabels=function(){for(var t=[],e=this.getTicks(),i=0;i<e[B];i++)t.push(this.getLabel(e[i]));return t},i.enableClassExtend(e),i.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("echarts/coord/axisHelper",[ue,"../scale/Ordinal","../scale/Interval","../scale/Time","../scale/Log","../scale/Scale","../util/number",le,"zrender/contain/text"],function(t){var e=t("../scale/Ordinal"),i=t("../scale/Interval");t("../scale/Time"),t("../scale/Log");var n=t("../scale/Scale"),r=t("../util/number"),a=t(le),o=t("zrender/contain/text"),s={};return s.getScaleExtent=function(t,e){var i=t.scale,n=i[_](),o=n[1]-n[0];if(i.type===p)return isFinite(o)?n:[0,0];var s=e.getMin?e.getMin():e.get("min"),l=e.getMax?e.getMax():e.get("max"),u=e.getNeedCrossZero?e.getNeedCrossZero():!e.get("scale"),c=e.get("boundaryGap");a[w](c)||(c=[c||0,c||0]),c[0]=r.parsePercent(c[0],1),c[1]=r.parsePercent(c[1],1);var h=!0,f=!0;return null==s&&(s=n[0]-c[0]*o,h=!1),null==l&&(l=n[1]+c[1]*o,f=!1),"dataMin"===s&&(s=n[0]),"dataMax"===l&&(l=n[1]),u&&(s>0&&l>0&&!h&&(s=0),0>s&&0>l&&!f&&(l=0)),[s,l]},s.niceScaleExtent=function(t,e){var i=t.scale,n=s.getScaleExtent(t,e),r=null!=(e.getMin?e.getMin():e.get("min")),a=null!=(e.getMax?e.getMax():e.get("max")),o=e.get("splitNumber");"log"===i.type&&(i.base=e.get("logBase")),i.setExtent(n[0],n[1]),i.niceExtent(o,r,a);var l=e.get("minInterval");if(isFinite(l)&&!r&&!a&&"interval"===i.type){var u=i.getInterval(),c=Math.max(Math.abs(u),l)/u;n=i[_]();var h=(n[1]+n[0])/2;i.setExtent(c*(n[0]-h)+h,c*(n[1]-h)+h),i.niceExtent(o)}var u=e.get("interval");null!=u&&i.setInterval&&i.setInterval(u)},s.createScaleByModel=function(t,r){if(r=r||t.get("type"))switch(r){case c:return new e(t.getCategories(),[1/0,-1/0]);case"value":return new i;default:return(n.getClass(r)||i)[D](t)}},s.ifAxisCrossZero=function(t){var e=t.scale[_](),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)},s.getAxisLabelInterval=function(t,e,i,n){var r,a=0,s=0,l=1;e[B]>40&&(l=Math.floor(e[B]/40));for(var u=0;u<t[B];u+=l){var c=t[u],h=o[re](e[u],i,Q,"top");h[n?"x":"y"]+=c,h[n?"width":ne]*=1.3,r?r.intersect(h)?(s++,a=Math.max(a,s)):(r.union(h),s=0):r=h.clone()}return 0===a&&l>1?l:(a+1)*l-1},s.getFormattedLabels=function(t,e){var i=t.scale,n=i.getTicksLabels(),r=i.getTicks();return typeof e===V?(e=function(t){return function(e){return t[M]("{value}",null!=e?e:"")}}(e),a.map(n,e)):typeof e===b?a.map(r,function(n,r){return e(t.type===c?i.getLabel(n):n,r)},this):n},s}),e("echarts/view/Chart",[ue,"zrender/container/Group","../util/component","../util/clazz","../util/model",le],function(t){function e(){this.group=new r,this.uid=a.getUID("viewChart")}function i(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)i(t.childAt(n),e)}function n(t,e,n){var r=s.queryDataIndex(t,e);null!=r?l.each(s.normalizeToArray(r),function(e){i(t.getItemGraphicEl(e),n)}):t.eachItemGraphicEl(function(t){i(t,n)})}var r=t("zrender/container/Group"),a=t("../util/component"),o=t("../util/clazz"),s=t("../util/model"),l=t(le);e[H]={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,r){n(t[Z](),r,"emphasis")},downplay:function(t,e,i,r){n(t[Z](),r,"normal")},remove:function(){this.group.removeAll()},dispose:function(){}};var u=e[H];return u.updateView=u.updateLayout=u.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},o.enableClassExtend(e,["dispose"]),o.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("echarts/coord/cartesian/Cartesian2D",[ue,le,"./Cartesian"],function(t){function e(t){n.call(this,t)}var i=t(le),n=t("./Cartesian");return e[H]={constructor:e,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale(p)[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e[x](e.toLocalCoord(t[0]))&&i[x](i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoints:function(t,e){return t.mapArray(["x","y"],function(t,e){return this[g]([t,e])},e,this)},dataToPoint:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.toGlobalCoord(i[v](t[0],e)),n.toGlobalCoord(n[v](t[1],e))]},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.coordToData(i.toLocalCoord(t[0]),e),n.coordToData(n.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},i.inherits(e,n),e}),e("zrender/core/BoundingRect",[ue,"./vector","./matrix"],function(t){function e(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this[ne]=n}var i=t("./vector"),n=t("./matrix"),r=i[l],a=Math.min,o=Math.max;return e[H]={constructor:e,union:function(t){var e=a(t.x,this.x),i=a(t.y,this.y);this.width=o(t.x+t.width,this.x+this.width)-e,this[ne]=o(t.y+t[ne],this.y+this[ne])-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(s){if(s){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this[ne],r(t,t,s),r(e,e,s),r(i,i,s),r(n,n,s),this.x=a(t[0],e[0],i[0],n[0]),this.y=a(t[1],e[1],i[1],n[1]);var l=o(t[0],e[0],i[0],n[0]),u=o(t[1],e[1],i[1],n[1]);this.width=l-this.x,this[ne]=u-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,r=t[ne]/e[ne],a=n[D]();return n.translate(a,a,[-e.x,-e.y]),n.scale(a,a,[i,r]),n.translate(a,a,[t.x,t.y]),a},intersect:function(t){if(!t)return!1;t instanceof e||(t=e[D](t));var i=this,n=i.x,r=i.x+i.width,a=i.y,o=i.y+i[ne],s=t.x,l=t.x+t.width,u=t.y,c=t.y+t[ne];return!(s>r||n>l||u>o||a>c)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i[ne]},clone:function(){return new e(this.x,this.y,this.width,this[ne])},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this[ne]=t[ne]},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this[ne]}}},e[D]=function(t){return new e(t.x,t.y,t.width,t[ne])},e}),e("echarts/coord/cartesian/GridModel",[ue,"./AxisModel","../../model/Component"],function(t){t("./AxisModel");var e=t("../../model/Component");return e[P]({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}})}),e("echarts/coord/cartesian/Axis2D",[ue,le,"../Axis","./axisLabelInterval"],function(t){var e=t(le),i=t("../Axis"),n=t("./axisLabelInterval"),r=function(t,e,n,r,a){i.call(this,t,e,n),this.type=r||"value",this[Y]=a||$};return r[H]={constructor:r,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this[Y];return"top"===t||t===$},getGlobalExtent:function(){var t=this[_]();return t[0]=this.toGlobalCoord(t[0]),t[1]=this.toGlobalCoord(t[1]),t},getLabelInterval:function(){var t=this._labelInterval;return t||(t=this._labelInterval=n(this)),t},isLabelIgnored:function(t){if(this.type===c){var e=this.getLabelInterval();return typeof e===b&&!e(t,this.scale.getLabel(t))||t%(e+1)}},toLocalCoord:null,toGlobalCoord:null},e.inherits(r,i),r}),e("zrender/tool/path",[ue,"../graphic/Path","../core/PathProxy","./transformPath","../core/matrix"],function(t){function e(t,e,i,n,r,a,o,s,l,u,c){var v=l*(p/180),y=d(v)*(t-i)/2+f(v)*(e-n)/2,_=-1*f(v)*(t-i)/2+d(v)*(e-n)/2,x=y*y/(o*o)+_*_/(s*s);x>1&&(o*=h(x),s*=h(x));var b=(r===a?-1:1)*h((o*o*s*s-o*o*_*_-s*s*y*y)/(o*o*_*_+s*s*y*y))||0,w=b*o*_/s,M=b*-s*y/o,T=(t+i)/2+d(v)*w-f(v)*M,S=(e+n)/2+f(v)*w+d(v)*M,C=g([1,0],[(y-w)/o,(_-M)/s]),A=[(y-w)/o,(_-M)/s],P=[(-1*y-w)/o,(-1*_-M)/s],L=g(A,P);m(A,P)<=-1&&(L=p),m(A,P)>=1&&(L=0),0===a&&L>0&&(L-=2*p),1===a&&0>L&&(L+=2*p),c.addData(u,T,S,o,s,C,L,v,a)}function i(t){if(!t)return[];var i,n=t[M](/-/g," -")[M](/  /g," ")[M](/ /g,",")[M](/,,/g,",");for(i=0;i<c[B];i++)n=n[M](new RegExp(c[i],"g"),"|"+c[i]);var r,a=n.split("|"),s=0,l=0,u=new o,h=o.CMD;for(i=1;i<a[B];i++){var f,d=a[i],p=d.charAt(0),v=0,m=d.slice(1)[M](/e,-/g,"e-").split(",");m[B]>0&&""===m[0]&&m.shift();for(var g=0;g<m[B];g++)m[g]=parseFloat(m[g]);for(;v<m[B]&&!isNaN(m[v])&&!isNaN(m[0]);){var y,_,x,b,w,T,S,C=s,A=l;switch(p){case"l":s+=m[v++],l+=m[v++],f=h.L,u.addData(f,s,l);break;case"L":s=m[v++],l=m[v++],f=h.L,u.addData(f,s,l);break;case"m":s+=m[v++],l+=m[v++],f=h.M,u.addData(f,s,l),p="l";break;case"M":s=m[v++],l=m[v++],f=h.M,u.addData(f,s,l),p="L";break;case"h":s+=m[v++],f=h.L,u.addData(f,s,l);break;case"H":s=m[v++],f=h.L,u.addData(f,s,l);break;case"v":l+=m[v++],f=h.L,u.addData(f,s,l);break;case"V":l=m[v++],f=h.L,u.addData(f,s,l);break;case"C":f=h.C,u.addData(f,m[v++],m[v++],m[v++],m[v++],m[v++],m[v++]),s=m[v-2],l=m[v-1];break;case"c":f=h.C,u.addData(f,m[v++]+s,m[v++]+l,m[v++]+s,m[v++]+l,m[v++]+s,m[v++]+l),s+=m[v-2],l+=m[v-1];break;case"S":y=s,_=l;var P=u.len(),L=u.data;r===h.C&&(y+=s-L[P-4],_+=l-L[P-3]),f=h.C,C=m[v++],A=m[v++],s=m[v++],l=m[v++],u.addData(f,y,_,C,A,s,l);break;case"s":y=s,_=l;var P=u.len(),L=u.data;r===h.C&&(y+=s-L[P-4],_+=l-L[P-3]),f=h.C,C=s+m[v++],A=l+m[v++],s+=m[v++],l+=m[v++],u.addData(f,y,_,C,A,s,l);break;case"Q":C=m[v++],A=m[v++],s=m[v++],l=m[v++],f=h.Q,u.addData(f,C,A,s,l);break;case"q":C=m[v++]+s,A=m[v++]+l,s+=m[v++],l+=m[v++],f=h.Q,u.addData(f,C,A,s,l);break;case"T":y=s,_=l;var P=u.len(),L=u.data;r===h.Q&&(y+=s-L[P-4],_+=l-L[P-3]),s=m[v++],l=m[v++],f=h.Q,u.addData(f,y,_,s,l);break;case"t":y=s,_=l;var P=u.len(),L=u.data;r===h.Q&&(y+=s-L[P-4],_+=l-L[P-3]),s+=m[v++],l+=m[v++],f=h.Q,u.addData(f,y,_,s,l);break;case"A":x=m[v++],b=m[v++],w=m[v++],T=m[v++],S=m[v++],C=s,A=l,s=m[v++],l=m[v++],f=h.A,e(C,A,s,l,T,S,x,b,w,f,u);break;case"a":x=m[v++],b=m[v++],w=m[v++],T=m[v++],S=m[v++],C=s,A=l,s+=m[v++],l+=m[v++],f=h.A,e(C,A,s,l,T,S,x,b,w,f,u)}}("z"===p||"Z"===p)&&(f=h.Z,u.addData(f)),r=f}return u.toStatic(),u}function n(t,e){var n,r=i(t);return e=e||{},e.buildPath=function(t){t.setData(r.data),n&&s(t,n);var e=t.getContext();e&&t.rebuildPath(e)},e[l]=function(t){n||(n=u[D]()),u.mul(n,t,n),this.dirty(!0)},e}var r=t("../graphic/Path"),o=t("../core/PathProxy"),s=t("./transformPath"),u=t("../core/matrix"),c=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],h=Math.sqrt,f=Math.sin,d=Math.cos,p=Math.PI,v=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},m=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(v(t)*v(e))},g=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(m(t,e))};return{createFromString:function(t,e){return new r(n(t,e))},extendFromString:function(t,e){return r[P](n(t,e))},mergePath:function(t,e){for(var i=[],n=t[B],o=0;n>o;o++){var s=t[o];s[a]&&s.buildPath(s.path,s.shape,!0),i.push(s.path)}var l=new r(e);return l.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},l}}}),e("zrender/container/Group",[ue,"../core/util","../Element","../core/BoundingRect"],function(t){var e=t("../core/util"),i=t("../Element"),n=t("../core/BoundingRect"),r=function(t){t=t||{},i.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this[a]=!0};return r[H]={constructor:r,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e[B];i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children[B]},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i[E](e);n>=0&&(i[C](n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent[L](t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToMap(t),t instanceof r&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var i=this.__zr,n=this.__storage,a=this._children,o=e[E](a,t);return 0>o?this:(a[C](o,1),t.parent=null,n&&(n.delFromMap(t.id),t instanceof r&&t.delChildrenFromStorage(n)),i&&i.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i[B];e++)t=i[e],n&&(n.delFromMap(t.id),t instanceof r&&t.delChildrenFromStorage(n)),t.parent=null;return i[B]=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i[B];n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children[B];i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children[B];e++){var i=this._children[e];t.addToMap(i),i instanceof r&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children[B];e++){var i=this._children[e];t.delFromMap(i.id),i instanceof r&&i.delChildrenFromStorage(t)}},dirty:function(){return this[a]=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new n(0,0,0,0),r=t||this._children,a=[],o=0;o<r[B];o++){var s=r[o];if(!s[N]&&!s.invisible){var u=s[re](),c=s.getLocalTransform(a);c?(i.copy(u),i[l](c),e=e||i.clone(),e.union(i)):(e=e||u.clone(),e.union(u))}}return e||i}},e.inherits(r,i),r}),e("echarts/visual/seriesColor",[ue,"zrender/graphic/Gradient"],function(t){var e=t("zrender/graphic/Gradient");return function(t){function i(i){var n=(i.visualColorAccessPath||"itemStyle.normal.color").split("."),r=i[Z](),a=i.get(n)||i.getColorFromPalette(i.get("name"));r.setVisual("color",a),t.isSeriesFiltered(i)||(typeof a!==b||a instanceof e||r.each(function(t){r.setItemVisual(t,"color",a(i.getDataParams(t)))}),r.each(function(t){var e=r[d](t),i=e.get(n,!0);null!=i&&r.setItemVisual(t,"color",i)}))}t.eachRawSeries(i)}}),e("zrender/graphic/Path",[ue,"./Displayable","../core/util","../core/PathProxy","../contain/path","./Pattern"],function(t){function e(t){i.call(this,t),this.path=new l}var i=t("./Displayable"),n=t("../core/util"),l=t("../core/PathProxy"),u=t("../contain/path"),c=t("./Pattern"),h=c[H].getCanvasPattern,f=Math.abs;return e[H]={constructor:e,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var i=this.style,n=this.path,r=i.hasStroke(),s=i.hasFill(),l=i.fill,u=i[o],c=s&&!!l[I],f=r&&!!u[I],d=s&&!!l.image,p=r&&!!u.image;if(i.bind(t,this,e),this.setTransform(t),this[a]){var v=this[re]();c&&(this._fillGradient=i.getGradient(t,l,v)),f&&(this._strokeGradient=i.getGradient(t,u,v))}c?t.fillStyle=this._fillGradient:d&&(t.fillStyle=h.call(l,t)),f?t.strokeStyle=this._strokeGradient:p&&(t.strokeStyle=h.call(u,t));var m=i.lineDash,g=i.lineDashOffset,y=!!t.setLineDash,_=this.getGlobalScale();n.setScale(_[0],_[1]),this.__dirtyPath||m&&!y&&r?(n=this.path.beginPath(t),m&&!y&&(n.setLineDash(m),n.setLineDashOffset(g)),this.buildPath(n,this.shape,!1),this.__dirtyPath=!1):(t.beginPath(),this.path.rebuildPath(t)),s&&n.fill(t),m&&y&&(t.setLineDash(m),t.lineDashOffset=g),r&&n[o](t),m&&y&&t.setLineDash([]),this.restoreTransform(t),null!=i.text&&this.drawRectText(t,this[re]())},buildPath:function(){},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n[re]()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this[a]||i){r.copy(t);var o=e[s],l=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),l>1e-10&&(r.width+=o/l,r[ne]+=o/l,r.x-=o/l/2,r.y-=o/l/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[re](),r=this.style;if(t=i[0],e=i[1],n[x](t,e)){var a=this.path.data;if(r.hasStroke()){var o=r[s],l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),u.containStroke(a,o/l,t,e)))return!0}if(r.hasFill())return u[x](a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this[a]=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i[H].attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(n[z](t))for(var r in t)t.hasOwnProperty(r)&&(i[r]=t[r]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this[r];return t&&f(t[0]-1)>1e-10&&f(t[3]-1)>1e-10?Math.sqrt(f(t[0]*t[3]-t[2]*t[1])):1}},e[P]=function(t){var i=function(i){e.call(this,i),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var r=this.shape;for(var a in n)!r.hasOwnProperty(a)&&n.hasOwnProperty(a)&&(r[a]=n[a])}t.init&&t.init.call(this,i)};n.inherits(i,e);for(var r in t)"style"!==r&&"shape"!==r&&(i[H][r]=t[r]);return i},n.inherits(e,i),e}),e("zrender/graphic/Image",[ue,"./Displayable","../core/BoundingRect","../core/util","../core/LRU"],function(t){function e(t){i.call(this,t)}var i=t("./Displayable"),n=t("../core/BoundingRect"),r=t("../core/util"),a=t("../core/LRU"),o=new a(50);return e[H]={constructor:e,type:"image",brush:function(t,e){var i,n=this.style,r=n.image;if(n.bind(t,this,e),i=typeof r===V?this._image:r,!i&&r){var a=o.get(r);if(!a)return i=new Image,i.onload=function(){i.onload=null;for(var t=0;t<a.pending[B];t++)a.pending[t].dirty()},a={image:i,pending:[this]},i.src=r,o.put(r,a),void(this._image=i);if(i=a.image,this._image=i,!i.width||!i[ne])return void a.pending.push(this)}if(i){var s=n.width||i.width,l=n[ne]||i[ne],u=n.x||0,c=n.y||0;if(!i.width||!i[ne])return;if(this.setTransform(t),n.sWidth&&n.sHeight){var h=n.sx||0,f=n.sy||0;t.drawImage(i,h,f,n.sWidth,n.sHeight,u,c,s,l)}else if(n.sx&&n.sy){var h=n.sx,f=n.sy,d=s-h,p=l-f;t.drawImage(i,h,f,d,p,u,c,s,l)}else t.drawImage(i,u,c,s,l);null==n.width&&(n.width=s),null==n[ne]&&(n[ne]=l),this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this[re]())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new n(t.x||0,t.y||0,t.width||0,t[ne]||0)),this._rect}},r.inherits(e,i),e}),e("zrender/graphic/Text",[ue,"./Displayable","../core/util","../contain/text"],function(t){var e=t("./Displayable"),i=t("../core/util"),n=t("../contain/text"),r=function(t){e.call(this,t)};return r[H]={constructor:r,type:"text",brush:function(t,e){var i=this.style,r=i.x||0,a=i.y||0,o=i.text;if(null!=o&&(o+=""),i.bind(t,this,e),o){this.setTransform(t);var s,l=i.textAlign,u=i.textFont||i.font;if(i.textVerticalAlign){var c=n[re](o,u,i.textAlign,"top");switch(s=K,i.textVerticalAlign){case K:a-=c[ne]/2-c.lineHeight/2;break;case $:a-=c[ne]-c.lineHeight/2;break;default:a+=c.lineHeight/2}}else s=i.textBaseline;t.font=u||"12px sans-serif",t.textAlign=l||"left",t.textAlign!==l&&(t.textAlign="left"),t.textBaseline=s||"alphabetic",t.textBaseline!==s&&(t.textBaseline="alphabetic");for(var h=n.measureText("国",t.font).width,f=o.split("\n"),d=0;d<f[B];d++)i.hasFill()&&t.fillText(f[d],r,a),i.hasStroke()&&t.strokeText(f[d],r,a),a+=h;this.restoreTransform(t)}},getBoundingRect:function(){if(!this._rect){var t=this.style,e=t.textVerticalAlign,i=n[re](t.text+"",t.textFont||t.font,t.textAlign,e?"top":t.textBaseline);switch(e){case K:i.y-=i[ne]/2;break;case $:i.y-=i[ne]}i.x+=t.x||0,i.y+=t.y||0,this._rect=i}return this._rect}},i.inherits(r,e),r}),e("zrender/graphic/shape/Circle",[ue,"../Path"],function(t){return t("../Path")[P]({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t[n](e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})}),e("zrender/graphic/shape/Sector",[ue,"../Path"],function(t){return t("../Path")[P]({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,r=e.cy,a=Math.max(e.r0||0,0),o=Math.max(e.r,0),s=e.startAngle,l=e.endAngle,u=e.clockwise,c=Math.cos(s),h=Math.sin(s);t[n](c*a+i,h*a+r),t.lineTo(c*o+i,h*o+r),t.arc(i,r,o,s,l,!u),t.lineTo(Math.cos(l)*a+i,Math.sin(l)*a+r),0!==a&&t.arc(i,r,a,l,s,u),t.closePath()}})}),e("zrender/graphic/shape/Ring",[ue,"../Path"],function(t){return t("../Path")[P]({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,r=e.cy,a=2*Math.PI;t[n](i+e.r,r),t.arc(i,r,e.r,0,a,!1),t[n](i+e.r0,r),t.arc(i,r,e.r0,0,a,!0)}})}),e("zrender/graphic/shape/Polygon",[ue,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[P]({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,i){e.buildPath(t,i,!0)}})}),e("zrender/graphic/shape/Polyline",[ue,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[P]({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,i){e.buildPath(t,i,!1)}})}),e("zrender/graphic/shape/Line",[ue,"../Path"],function(t){return t("../Path")[P]({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,r=e.y1,a=e.x2,o=e.y2,s=e.percent;0!==s&&(t[n](i,r),1>s&&(a=i*(1-s)+a*s,o=r*(1-s)+o*s),t.lineTo(a,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})}),e("zrender/graphic/shape/BezierCurve",[ue,"../../core/curve","../../core/vector","../Path"],function(t){function e(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?c:l)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?c:l)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?u:s)(t.x1,t.cpx1,t.x2,e),(i?u:s)(t.y1,t.cpy1,t.y2,e)]}var i=t("../../core/curve"),r=t("../../core/vector"),a=i.quadraticSubdivide,o=i.cubicSubdivide,s=i.quadraticAt,l=i.cubicAt,u=i.quadraticDerivativeAt,c=i.cubicDerivativeAt,h=[];return t("../Path")[P]({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,r=e.y1,s=e.x2,l=e.y2,u=e.cpx1,c=e.cpy1,f=e.cpx2,d=e.cpy2,p=e.percent;0!==p&&(t[n](i,r),null==f||null==d?(1>p&&(a(i,u,s,p,h),u=h[1],s=h[2],a(r,c,l,p,h),c=h[1],l=h[2]),t.quadraticCurveTo(u,c,s,l)):(1>p&&(o(i,u,f,s,p,h),u=h[1],f=h[2],s=h[3],o(r,c,d,l,p,h),c=h[1],d=h[2],l=h[3]),t.bezierCurveTo(u,c,f,d,s,l)))},pointAt:function(t){return e(this.shape,t,!1)},tangentAt:function(t){var i=e(this.shape,t,!0);return r.normalize(i,i)}})}),e("zrender/graphic/shape/Rect",[ue,"../helper/roundRect","../Path"],function(t){var e=t("../helper/roundRect");return t("../Path")[P]({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,i){var n=i.x,r=i.y,a=i.width,o=i[ne];i.r?e.buildPath(t,i):t.rect(n,r,a,o),t.closePath()}})}),e("zrender/graphic/shape/Arc",[ue,"../Path"],function(t){return t("../Path")[P]({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,r=e.cy,a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(o),c=Math.sin(o);t[n](u*a+i,c*a+r),t.arc(i,r,a,o,s,!l)}})}),e("zrender/graphic/CompoundPath",[ue,"./Path"],function(t){var e=t("./Path");return e[P]({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e[B];i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this[a]=this[a]||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t[B];i++)t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i[B];n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t[B];e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),e[H][re].call(this)}})}),e("zrender/graphic/mixin/RectText",[ue,"../../contain/text","../../core/BoundingRect"],function(t){function e(t,e){return typeof t===V?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}var i=t("../../contain/text"),n=t("../../core/BoundingRect"),a=new n,o=function(){};return o[H]={constructor:o,drawRectText:function(t,n,o){var s=this.style,u=s.text;if(null!=u&&(u+=""),u){t.save();var c,h,f=s.textPosition,d=s.textDistance,p=s.textAlign,v=s.textFont||s.font,m=s.textBaseline,g=s.textVerticalAlign;o=o||i[re](u,v,p,m);var y=this[r];if(s.textTransform?this.setTransform(t):y&&(a.copy(n),a[l](y),n=a),f instanceof Array){if(c=n.x+e(f[0],n.width),h=n.y+e(f[1],n[ne]),p=p||"left",m=m||"top",g){switch(g){case K:h-=o[ne]/2-o.lineHeight/2;break;case $:h-=o[ne]-o.lineHeight/2;break;default:h+=o.lineHeight/2
}m=K}}else{var _=i.adjustTextPositionOnRect(f,n,o,d);c=_.x,h=_.y,p=p||_.textAlign,m=m||_.textBaseline}t.textAlign=p||"left",t.textBaseline=m||"alphabetic";var x=s.textFill,b=s.textStroke;x&&(t.fillStyle=x),b&&(t.strokeStyle=b),t.font=v||"12px sans-serif",t.shadowBlur=s.textShadowBlur,t.shadowColor=s.textShadowColor||"transparent",t.shadowOffsetX=s.textShadowOffsetX,t.shadowOffsetY=s.textShadowOffsetY;var w=u.split("\n");s.textRotation&&(y&&t.translate(y[4],y[5]),t.rotate(s.textRotation),y&&t.translate(-y[4],-y[5]));for(var M=0;M<w[B];M++)x&&t.fillText(w[M],c,h),b&&t.strokeText(w[M],c,h),h+=o.lineHeight;t.restore()}}},o}),e("zrender/graphic/RadialGradient",[ue,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,i.call(this,r)};return n[H]={constructor:n},e.inherits(n,i),n}),e("zrender/contain/text",[ue,"../core/util","../core/BoundingRect"],function(t){function e(t,e){var i=t+":"+e;if(o[i])return o[i];for(var n=(t+"").split("\n"),r=0,a=0,u=n[B];u>a;a++)r=Math.max(d.measureText(n[a],e).width,r);return s>l&&(s=0,o={}),s++,o[i]=r,r}function i(t,i,n,r){var a=((t||"")+"").split("\n")[B],o=e(t,i),s=e("国",i),l=a*s,u=new h(0,0,o,l);switch(u.lineHeight=s,r){case $:case"alphabetic":u.y-=s;break;case K:u.y-=s/2}switch(n){case"end":case"right":u.x-=u.width;break;case Q:u.x-=u.width/2}return u}function n(t,e,i,n){var r=e.x,a=e.y,o=e[ne],s=e.width,l=i[ne],u=o/2-l/2,c="left";switch(t){case"left":r-=n,a+=u,c="right";break;case"right":r+=n+s,a+=u,c="left";break;case"top":r+=s/2,a-=n+l,c=Q;break;case $:r+=s/2,a+=o+n,c=Q;break;case"inside":r+=s/2,a+=u,c=Q;break;case"insideLeft":r+=n,a+=u,c="left";break;case"insideRight":r+=s-n,a+=u,c="right";break;case"insideTop":r+=s/2,a+=n,c=Q;break;case"insideBottom":r+=s/2,a+=o-l-n,c=Q;break;case"insideTopLeft":r+=n,a+=n,c="left";break;case"insideTopRight":r+=s-n,a+=n,c="right";break;case"insideBottomLeft":r+=n,a+=o-l-n;break;case"insideBottomRight":r+=s-n,a+=o-l-n,c="right"}return{x:r,y:a,textAlign:c,textBaseline:"top"}}function r(t,i,n,r,o){if(!i)return"";o=o||{},r=f(r,"...");for(var s=f(o.maxIterations,2),l=f(o.minChar,0),u=e("国",n),c=e("a",n),h=f(o.placeholder,""),d=i=Math.max(0,i-1),p=0;l>p&&d>=c;p++)d-=c;var v=e(r);v>d&&(r="",v=0),d=i-v;for(var m=(t+"").split("\n"),p=0,g=m[B];g>p;p++){var y=m[p],_=e(y,n);if(!(i>=_)){for(var x=0;;x++){if(d>=_||x>=s){y+=r;break}var b=0===x?a(y,d,c,u):_>0?Math.floor(y[B]*d/_):0;y=y.substr(0,b),_=e(y,n)}""===y&&(y=h),m[p]=y}}return m.join("\n")}function a(t,e,i,n){for(var r=0,a=0,o=t[B];o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}var o={},s=0,l=5e3,c=t("../core/util"),h=t("../core/BoundingRect"),f=c[u],d={getWidth:e,getBoundingRect:i,adjustTextPositionOnRect:n,truncateText:r,measureText:function(t,e){var i=c.getContext();return i.font=e||"12px sans-serif",i.measureText(t)}};return d}),e("zrender/graphic/LinearGradient",[ue,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==r?0:r,this.type="linear",this.global=o||!1,i.call(this,a)};return n[H]={constructor:n},e.inherits(n,i),n}),e("zrender/core/PathProxy",[ue,"./curve","./vector","./bbox","./BoundingRect","../config"],function(t){var e=t("./curve"),r=t("./vector"),a=t("./bbox"),s=t("./BoundingRect"),l=t("../config").devicePixelRatio,u={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},c=[],h=[],f=[],d=[],p=Math.min,v=Math.max,m=Math.cos,g=Math.sin,y=Math.sqrt,_=Math.abs,x=typeof Float32Array!=i,b=function(){this.data=[],this._len=0,this._ctx=null,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._ux=0,this._uy=0};return b[H]={constructor:b,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=_(1/l/t)||0,this._uy=_(1/l/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._len=0,this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(u.M,t,e),this._ctx&&this._ctx[n](t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=_(t-this._xi)>this._ux||_(e-this._yi)>this._uy||this._len<5;return this.addData(u.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(u.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(u.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(u.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=m(r)*i+t,this._xi=g(r)*i+t,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(u.R,t,e,i,n),this},closePath:function(){this.addData(u.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t[o](),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t[B];i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t[B];this.data&&this.data[B]==e||!x||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t[B],i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();x&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a[B];o++)this.data[n++]=a[o];this._len=n},addData:function(t){var e=this.data;this._len+arguments[B]>e[B]&&(this._expandData(),e=this.data);for(var i=0;i<arguments[B];i++)e[this._len++]=arguments[i];this._prevCmd=t},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,r,a=this._dashSum,o=this._dashOffset,s=this._lineDash,l=this._ctx,u=this._xi,c=this._yi,h=t-u,f=e-c,d=y(h*h+f*f),m=u,g=c,_=s[B];for(h/=d,f/=d,0>o&&(o=a+o),o%=a,m-=o*h,g-=o*f;h>0&&t>=m||0>h&&m>=t||0==h&&(f>0&&e>=g||0>f&&g>=e);)r=this._dashIdx,i=s[r],m+=h*i,g+=f*i,this._dashIdx=(r+1)%_,h>0&&u>m||0>h&&m>u||f>0&&c>g||0>f&&g>c||l[r%2?n:"lineTo"](h>=0?p(m,t):v(m,t),f>=0?p(g,e):v(g,e));h=m-t,f=g-e,this._dashOffset=-y(h*h+f*f)},_dashedBezierTo:function(t,i,r,a,o,s){var l,u,c,h,f,d=this._dashSum,p=this._dashOffset,v=this._lineDash,m=this._ctx,g=this._xi,_=this._yi,x=e.cubicAt,b=0,w=this._dashIdx,M=v[B],T=0;for(0>p&&(p=d+p),p%=d,l=0;1>l;l+=.1)u=x(g,t,r,o,l+.1)-x(g,t,r,o,l),c=x(_,i,a,s,l+.1)-x(_,i,a,s,l),b+=y(u*u+c*c);for(;M>w&&(T+=v[w],!(T>p));w++);for(l=(T-p)/b;1>=l;)h=x(g,t,r,o,l),f=x(_,i,a,s,l),w%2?m[n](h,f):m.lineTo(h,f),l+=v[w]/b,w=(w+1)%M;w%2!==0&&m.lineTo(o,s),u=o-h,c=s-f,this._dashOffset=-y(u*u+c*c)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t[B]=this._len,x&&(this.data=new Float32Array(t)))},getBoundingRect:function(){c[0]=c[1]=f[0]=f[1]=Number.MAX_VALUE,h[0]=h[1]=d[0]=d[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,o=0,l=0;l<t[B];){var p=t[l++];switch(1==l&&(e=t[l],i=t[l+1],n=e,o=i),p){case u.M:n=t[l++],o=t[l++],e=n,i=o,f[0]=n,f[1]=o,d[0]=n,d[1]=o;break;case u.L:a.fromLine(e,i,t[l],t[l+1],f,d),e=t[l++],i=t[l++];break;case u.C:a.fromCubic(e,i,t[l++],t[l++],t[l++],t[l++],t[l],t[l+1],f,d),e=t[l++],i=t[l++];break;case u.Q:a.fromQuadratic(e,i,t[l++],t[l++],t[l],t[l+1],f,d),e=t[l++],i=t[l++];break;case u.A:var v=t[l++],y=t[l++],_=t[l++],x=t[l++],b=t[l++],w=t[l++]+b,M=(t[l++],1-t[l++]);1==l&&(n=m(b)*_+v,o=g(b)*x+y),a.fromArc(v,y,_,x,b,w,M,f,d),e=m(w)*_+v,i=g(w)*x+y;break;case u.R:n=e=t[l++],o=i=t[l++];var T=t[l++],S=t[l++];a.fromLine(n,o,n+T,o+S,f,d);break;case u.Z:e=n,i=o}r.min(c,c,f),r.max(h,h,d)}return 0===l&&(c[0]=c[1]=h[0]=h[1]=0),new s(c[0],c[1],h[0]-c[0],h[1]-c[1])},rebuildPath:function(t){for(var e,i,r,a,o,s,l=this.data,c=this._ux,h=this._uy,f=this._len,d=0;f>d;){var p=l[d++];switch(1==d&&(r=l[d],a=l[d+1],e=r,i=a),p){case u.M:e=r=l[d++],i=a=l[d++],t[n](r,a);break;case u.L:o=l[d++],s=l[d++],(_(o-r)>c||_(s-a)>h||d===f-1)&&(t.lineTo(o,s),r=o,a=s);break;case u.C:t.bezierCurveTo(l[d++],l[d++],l[d++],l[d++],l[d++],l[d++]),r=l[d-2],a=l[d-1];break;case u.Q:t.quadraticCurveTo(l[d++],l[d++],l[d++],l[d++]),r=l[d-2],a=l[d-1];break;case u.A:var v=l[d++],y=l[d++],x=l[d++],b=l[d++],w=l[d++],M=l[d++],T=l[d++],S=l[d++],C=x>b?x:b,A=x>b?1:x/b,P=x>b?b/x:1,L=Math.abs(x-b)>.001,z=w+M;L?(t.translate(v,y),t.rotate(T),t.scale(A,P),t.arc(0,0,C,w,z,1-S),t.scale(1/A,1/P),t.rotate(-T),t.translate(-v,-y)):t.arc(v,y,C,w,z,1-S),1==d&&(e=m(w)*x+v,i=g(w)*b+y),r=m(z)*x+v,a=g(z)*b+y;break;case u.R:e=r=l[d],i=a=l[d+1],t.rect(l[d++],l[d++],l[d++],l[d++]);break;case u.Z:t.closePath(),r=e,a=i}}}},b.CMD=u,b}),e("zrender/graphic/Displayable",[ue,"../core/util","./Style","../Element","./mixin/RectText"],function(t){function e(t){t=t||{},r.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new n(t.style),this._rect=null,this.__clipPaths=[]}var i=t("../core/util"),n=t("./Style"),r=t("../Element"),o=t("./mixin/RectText");return e[H]={constructor:e,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[re]();return n[x](i[0],i[1])},dirty:function(){this[a]=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?r[H].attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new n(t),this.dirty(!1),this}},i.inherits(e,r),i.mixin(e,o),e}),e("zrender/vml/core",[ue,"exports","module","../core/env"],function(t,e,i){if(!t("../core/env")[G]){var n,r="urn:schemas-microsoft-com:vml",a=window,o=a.document,s=!1;try{!o.namespaces.zrvml&&o.namespaces.add("zrvml",r),n=function(t){return o[y]("<zrvml:"+t+' class="zrvml">')}}catch(l){n=function(t){return o[y]("<"+t+' xmlns="'+r+'" class="zrvml">')}}var u=function(){if(!s){s=!0;var t=o.styleSheets;t[B]<31?o.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}};i.exports={doc:o,initVML:u,createNode:n}}}),e("echarts/model/globalDefault",[],function(){var t="";return typeof navigator!==i&&(t=navigator.platform||""),{color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:t.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:!0,animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3}}),e("zrender/graphic/Gradient",[ue],function(){var t=function(t){this[I]=t||[]};return t[H]={constructor:t,addColorStop:function(t,e){this[I].push({offset:t,color:e})}},t}),e("echarts/model/mixin/colorPalette",[],function(){return{clearColorPalette:function(){this._colorIdx=0,this._colorNameMap={}},getColorFromPalette:function(t,e){e=e||this;var i=e._colorIdx||0,n=e._colorNameMap||(e._colorNameMap={});if(n[t])return n[t];var r=this.get("color",!0)||[];if(r[B]){var a=r[i];return t&&(n[t]=a),e._colorIdx=(i+1)%r[B],a}}}}),e("echarts/util/clazz",[ue,le],function(t){function e(t,e){var i=n.slice(arguments,2);return this.superClass[H][e].apply(t,i)}function i(t,e,i){return this.superClass[H][e].apply(t,i)}var n=t(le),r={},a=".",o="___EC__COMPONENT__CONTAINER___",s=r.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split(a),e.main=t[0]||"",e.sub=t[1]||""),e};return r.enableClassExtend=function(t,r){t.$constructor=t,t[P]=function(t){var r=this,a=function(){t.$constructor?t.$constructor.apply(this,arguments):r.apply(this,arguments)};return n[P](a[H],t),a[P]=this[P],a.superCall=e,a.superApply=i,n.inherits(a,this),a.superClass=r,a}},r.enableClassManagement=function(t,e){function i(t){var e=r[t.main];return e&&e[o]||(e=r[t.main]={},e[o]=!0),e}e=e||{};var r={};if(t.registerClass=function(t,e){if(e)if(e=s(e),e.sub){if(e.sub!==o){var n=i(e);n[e.sub]=t}}else r[e.main]=t;return t},t.getClass=function(t,e,i){var n=r[t];if(n&&n[o]&&(n=e?n[e]:null),i&&!n)throw new Error("Component "+t+"."+(e||"")+" not exists. Load it first.");return n},t.getClassesByMainType=function(t){t=s(t);var e=[],i=r[t.main];return i&&i[o]?n.each(i,function(t,i){i!==o&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=s(t),!!r[t.main]},t.getAllClassMainTypes=function(){var t=[];return n.each(r,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=s(t);var e=r[t.main];return e&&e[o]},t.parseClassType=s,e.registerWhenExtend){var a=t[P];a&&(t[P]=function(e){var i=a.call(this,e);return t.registerClass(i,e.type)})}return t},r.setReadOnly=function(){},r}),e("echarts/model/mixin/lineStyle",[ue,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([[s,"width"],[o,"color"],[U],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getLineStyle:function(t){var i=e.call(this,t),n=this.getLineDash(i[s]);return n&&(i.lineDash=n),i},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[i,i]}}}),e("echarts/model/mixin/areaStyle",[ue,"./makeStyleMapper"],function(t){return{getAreaStyle:t("./makeStyleMapper")([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],[U],["shadowColor"]])}}),e("echarts/model/mixin/textStyle",[ue,"zrender/contain/text"],function(t){function e(t,e){return t&&t[f](e)}var i=t("zrender/contain/text");return{getTextColor:function(){var t=this[h];return this[f]("color")||t&&t.get("textStyle.color")},getFont:function(){var t=this[h],i=t&&t[se](oe);return[this[f]("fontStyle")||e(i,"fontStyle"),this[f]("fontWeight")||e(i,"fontWeight"),(this[f]("fontSize")||e(i,"fontSize")||12)+"px",this[f]("fontFamily")||e(i,"fontFamily")||"sans-serif"].join(" ")},getTextRect:function(t){return i[re](t,this[ae](),this[f]("align"),this[f]("baseline"))},truncateText:function(t,e,n,r){return i.truncateText(t,e,this[ae](),n,r)}}}),e("echarts/model/mixin/itemStyle",[ue,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([["fill","color"],[o,"borderColor"],[s,"borderWidth"],[U],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]);return{getItemStyle:function(t){var i=e.call(this,t),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}}),e("zrender/tool/transformPath",[ue,"../core/PathProxy","../core/vector"],function(t){function e(t,e){var n,l,u,c,h,f,d=t.data,p=i.M,v=i.C,m=i.L,g=i.R,y=i.A,_=i.Q;for(u=0,c=0;u<d[B];){switch(n=d[u++],c=u,l=0,n){case p:l=1;break;case m:l=1;break;case v:l=3;break;case _:l=2;break;case y:var x=e[4],b=e[5],w=o(e[0]*e[0]+e[1]*e[1]),M=o(e[2]*e[2]+e[3]*e[3]),T=s(-e[1]/M,e[0]/w);d[u++]+=x,d[u++]+=b,d[u++]*=w,d[u++]*=M,d[u++]+=T,d[u++]+=T,u+=2,c=u;break;case g:f[0]=d[u++],f[1]=d[u++],r(f,f,e),d[c++]=f[0],d[c++]=f[1],f[0]+=d[u++],f[1]+=d[u++],r(f,f,e),d[c++]=f[0],d[c++]=f[1]}for(h=0;l>h;h++){var f=a[h];f[0]=d[u++],f[1]=d[u++],r(f,f,e),d[c++]=f[0],d[c++]=f[1]}}}var i=t("../core/PathProxy").CMD,n=t("../core/vector"),r=n[l],a=[[],[],[]],o=Math.sqrt,s=Math.atan2;return e}),e("zrender/contain/path",[ue,"../core/PathProxy","./line","./cubic","./quadratic","./arc","./util","../core/curve","./windingLine"],function(t){function e(t,e){return Math.abs(t-e)<g}function i(){var t=_[0];_[0]=_[1],_[1]=t}function n(t,e,n,r,a,o,s,l,u,c){if(c>e&&c>r&&c>o&&c>l||e>c&&r>c&&o>c&&l>c)return 0;var h=d.cubicRootAt(e,r,o,l,c,y);if(0===h)return 0;for(var f,p,v=0,m=-1,g=0;h>g;g++){var x=y[g],b=0===x||1===x?.5:1,w=d.cubicAt(t,n,a,s,x);u>w||(0>m&&(m=d.cubicExtrema(e,r,o,l,_),_[1]<_[0]&&m>1&&i(),f=d.cubicAt(e,r,o,l,_[0]),m>1&&(p=d.cubicAt(e,r,o,l,_[1]))),v+=2==m?x<_[0]?e>f?b:-b:x<_[1]?f>p?b:-b:p>l?b:-b:x<_[0]?e>f?b:-b:f>l?b:-b)}return v}function r(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=d.quadraticRootAt(e,n,a,s,y);if(0===l)return 0;var u=d.quadraticExtremum(e,n,a);if(u>=0&&1>=u){for(var c=0,h=d.quadraticAt(e,n,a,u),f=0;l>f;f++){var p=0===y[f]||1===y[f]?.5:1,v=d.quadraticAt(t,i,r,y[f]);o>v||(c+=y[f]<u?e>h?p:-p:h>a?p:-p)}return c}var p=0===y[0]||1===y[0]?.5:1,v=d.quadraticAt(t,i,r,y[0]);return o>v?0:e>a?p:-p}function a(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);y[0]=-l,y[1]=l;var u=Math.abs(n-r);if(1e-4>u)return 0;if(1e-4>u%m){n=0,r=m;var c=a?1:-1;return o>=y[0]+t&&o<=y[1]+t?c:0}if(a){var l=n;n=f(r),r=f(l)}else n=f(n),r=f(r);n>r&&(r+=m);for(var h=0,d=0;2>d;d++){var p=y[d];if(p+t>o){var v=Math.atan2(s,p),c=a?1:-1;0>v&&(v=m+v),(v>=n&&r>=v||v+m>=n&&r>=v+m)&&(v>Math.PI/2&&v<1.5*Math.PI&&(c=-c),h+=c)}}return h}function o(t,i,o,l,f){for(var d=0,m=0,g=0,y=0,_=0,x=0;x<t[B];){var b=t[x++];switch(b===s.M&&x>1&&(o||(d+=p(m,g,y,_,l,f))),1==x&&(m=t[x],g=t[x+1],y=m,_=g),b){case s.M:y=t[x++],_=t[x++],m=y,g=_;break;case s.L:if(o){if(v(m,g,t[x],t[x+1],i,l,f))return!0}else d+=p(m,g,t[x],t[x+1],l,f)||0;m=t[x++],g=t[x++];break;case s.C:if(o){if(u.containStroke(m,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],i,l,f))return!0}else d+=n(m,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],l,f)||0;m=t[x++],g=t[x++];break;case s.Q:if(o){if(c.containStroke(m,g,t[x++],t[x++],t[x],t[x+1],i,l,f))return!0}else d+=r(m,g,t[x++],t[x++],t[x],t[x+1],l,f)||0;m=t[x++],g=t[x++];break;case s.A:var w=t[x++],M=t[x++],T=t[x++],S=t[x++],C=t[x++],A=t[x++],P=(t[x++],1-t[x++]),L=Math.cos(C)*T+w,z=Math.sin(C)*S+M;x>1?d+=p(m,g,L,z,l,f):(y=L,_=z);var I=(l-w)*S/T+w;if(o){if(h.containStroke(w,M,S,C,C+A,P,i,I,f))return!0}else d+=a(w,M,S,C,C+A,P,I,f);m=Math.cos(C+A)*T+w,g=Math.sin(C+A)*S+M;break;case s.R:y=m=t[x++],_=g=t[x++];var k=t[x++],D=t[x++],L=y+k,z=_+D;if(o){if(v(y,_,L,_,i,l,f)||v(L,_,L,z,i,l,f)||v(L,z,y,z,i,l,f)||v(y,z,y,_,i,l,f))return!0}else d+=p(L,_,L,z,l,f),d+=p(y,z,y,_,l,f);break;case s.Z:if(o){if(v(m,g,y,_,i,l,f))return!0}else d+=p(m,g,y,_,l,f);m=y,g=_}}return o||e(g,_)||(d+=p(m,g,y,_,l,f)||0),0!==d}var s=t("../core/PathProxy").CMD,l=t("./line"),u=t("./cubic"),c=t("./quadratic"),h=t("./arc"),f=t("./util").normalizeRadian,d=t("../core/curve"),p=t("./windingLine"),v=l.containStroke,m=2*Math.PI,g=1e-4,y=[-1,-1,-1],_=[-1,-1];return{contain:function(t,e,i){return o(t,0,!1,e,i)},containStroke:function(t,e,i,n){return o(t,e,!0,i,n)}}}),e("zrender/graphic/Pattern",[ue],function(){var t=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};return t[H].getCanvasPattern=function(t){return this._canvasPattern||(this._canvasPattern=t.createPattern(this.image,this.repeat))},t}),e("zrender/core/curve",[ue,"./vector"],function(t){function e(t){return t>-x&&x>t}function i(t){return t>x||-x>t}function n(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function r(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function a(t,i,n,r,a,o){var s=r+3*(i-n)-t,l=3*(n-2*i+t),u=3*(i-t),c=t-a,h=l*l-3*s*u,f=l*u-9*s*c,d=u*u-3*l*c,p=0;if(e(h)&&e(f))if(e(l))o[0]=0;else{var v=-u/l;v>=0&&1>=v&&(o[p++]=v)}else{var m=f*f-4*h*d;if(e(m)){var g=f/h,v=-l/s+g,x=-g/2;v>=0&&1>=v&&(o[p++]=v),x>=0&&1>=x&&(o[p++]=x)}else if(m>0){var b=_(m),T=h*l+1.5*s*(-f+b),S=h*l+1.5*s*(-f-b);T=0>T?-y(-T,M):y(T,M),S=0>S?-y(-S,M):y(S,M);var v=(-l-(T+S))/(3*s);v>=0&&1>=v&&(o[p++]=v)}else{var C=(2*h*l-3*s*f)/(2*_(h*h*h)),A=Math.acos(C)/3,P=_(h),L=Math.cos(A),v=(-l-2*P*L)/(3*s),x=(-l+P*(L+w*Math.sin(A)))/(3*s),z=(-l+P*(L-w*Math.sin(A)))/(3*s);v>=0&&1>=v&&(o[p++]=v),x>=0&&1>=x&&(o[p++]=x),z>=0&&1>=z&&(o[p++]=z)}}return p}function o(t,n,r,a,o){var s=6*r-12*n+6*t,l=9*n+3*a-3*t-9*r,u=3*n-3*t,c=0;if(e(l)){if(i(s)){var h=-u/s;h>=0&&1>=h&&(o[c++]=h)}}else{var f=s*s-4*l*u;if(e(f))o[0]=-s/(2*l);else if(f>0){var d=_(f),h=(-s+d)/(2*l),p=(-s-d)/(2*l);h>=0&&1>=h&&(o[c++]=h),p>=0&&1>=p&&(o[c++]=p)}}return c}function s(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,u=(s-o)*r+o,c=(l-s)*r+s,h=(c-u)*r+u;a[0]=t,a[1]=o,a[2]=u,a[3]=h,a[4]=h,a[5]=c,a[6]=l,a[7]=n}function l(t,e,i,r,a,o,s,l,u,c,h){var f,d,p,v,m,y=.005,x=1/0;T[0]=u,T[1]=c;for(var w=0;1>w;w+=.05)S[0]=n(t,i,a,s,w),S[1]=n(e,r,o,l,w),v=g(T,S),x>v&&(f=w,x=v);x=1/0;for(var M=0;32>M&&!(b>y);M++)d=f-y,p=f+y,S[0]=n(t,i,a,s,d),S[1]=n(e,r,o,l,d),v=g(S,T),d>=0&&x>v?(f=d,x=v):(C[0]=n(t,i,a,s,p),C[1]=n(e,r,o,l,p),m=g(C,T),1>=p&&x>m?(f=p,x=m):y*=.5);return h&&(h[0]=n(t,i,a,s,f),h[1]=n(e,r,o,l,f)),_(x)}function u(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function c(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function h(t,n,r,a,o){var s=t-2*n+r,l=2*(n-t),u=t-a,c=0;if(e(s)){if(i(l)){var h=-u/l;h>=0&&1>=h&&(o[c++]=h)}}else{var f=l*l-4*s*u;if(e(f)){var h=-l/(2*s);h>=0&&1>=h&&(o[c++]=h)}else if(f>0){var d=_(f),h=(-l+d)/(2*s),p=(-l-d)/(2*s);h>=0&&1>=h&&(o[c++]=h),p>=0&&1>=p&&(o[c++]=p)}}return c}function f(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function d(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function p(t,e,i,n,r,a,o,s,l){var c,h=.005,f=1/0;T[0]=o,T[1]=s;for(var d=0;1>d;d+=.05){S[0]=u(t,i,r,d),S[1]=u(e,n,a,d);var p=g(T,S);f>p&&(c=d,f=p)}f=1/0;for(var v=0;32>v&&!(b>h);v++){var m=c-h,y=c+h;S[0]=u(t,i,r,m),S[1]=u(e,n,a,m);var p=g(S,T);if(m>=0&&f>p)c=m,f=p;else{C[0]=u(t,i,r,y),C[1]=u(e,n,a,y);var x=g(C,T);1>=y&&f>x?(c=y,f=x):h*=.5}}return l&&(l[0]=u(t,i,r,c),l[1]=u(e,n,a,c)),_(f)}var v=t("./vector"),m=v[D],g=v.distSquare,y=Math.pow,_=Math.sqrt,x=1e-8,b=1e-4,w=_(3),M=1/3,T=m(),S=m(),C=m();return{cubicAt:n,cubicDerivativeAt:r,cubicRootAt:a,cubicExtrema:o,cubicSubdivide:s,cubicProjectPoint:l,quadraticAt:u,quadraticDerivativeAt:c,quadraticRootAt:h,quadraticExtremum:f,quadraticSubdivide:d,quadraticProjectPoint:p}}),e("echarts/util/number",[ue],function(){function t(t){return t[M](/^\s+/,"")[M](/\s+$/,"")}var e={},i=1e-4;return e.linearMap=function(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]},e.parsePercent=function(e,i){switch(e){case Q:case K:e="50%";break;case"left":case"top":e="0%";break;case"right":case $:e="100%"}return typeof e===V?t(e).match(/%$/)?parseFloat(e)/100*i:parseFloat(e):null==e?0/0:+e},e.round=function(t,e){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),+(+t).toFixed(e)},e.asc=function(t){return t.sort(function(t,e){return t-e}),t},e.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i},e.getPrecisionSafe=function(t){var e=t.toString(),i=e[E](".");return 0>i?0:e[B]-1-i},e.getPixelPrecision=function(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n);return Math.max(-r+a,0)},e.MAX_SAFE_INTEGER=9007199254740991,e.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},e.isRadianAroundZero=function(t){return t>-i&&i>t},e.parseDate=function(t){if(t instanceof Date)return t;if(typeof t===V){var e=new Date(t);return isNaN(+e)&&(e=new Date(new Date(t[M](/-/g,"/"))-new Date("1970/01/01"))),e}return new Date(Math.round(t))},e.quantity=function(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))},e.nice=function(t,i){var n,r=e.quantity(t),a=t/r;return n=i?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,n*r},e.reformIntervals=function(t){function e(t,i,n){return t.interval[n]<i.interval[n]||t.interval[n]===i.interval[n]&&(t.close[n]-i.close[n]===(n?-1:1)||!n&&e(t,i,1))}t.sort(function(t,i){return e(t,i,0)?-1:1});for(var i=-1/0,n=1,r=0;r<t[B];){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=i&&(a[s]=i,o[s]=s?1:1-n),i=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t[C](r,1):r++}return t},e}),e("zrender/core/bbox",[ue,"./vector","./curve"],function(t){var e=t("./vector"),i=t("./curve"),n={},r=Math.min,a=Math.max,o=Math.sin,s=Math.cos,l=e[D](),u=e[D](),c=e[D](),h=2*Math.PI;n.fromPoints=function(t,e,i){if(0!==t[B]){var n,o=t[0],s=o[0],l=o[0],u=o[1],c=o[1];for(n=1;n<t[B];n++)o=t[n],s=r(s,o[0]),l=a(l,o[0]),u=r(u,o[1]),c=a(c,o[1]);e[0]=s,e[1]=u,i[0]=l,i[1]=c}},n.fromLine=function(t,e,i,n,o,s){o[0]=r(t,i),o[1]=r(e,n),s[0]=a(t,i),s[1]=a(e,n)};var f=[],d=[];return n.fromCubic=function(t,e,n,o,s,l,u,c,h,p){var v,m=i.cubicExtrema,g=i.cubicAt,y=m(t,n,s,u,f);for(h[0]=1/0,h[1]=1/0,p[0]=-1/0,p[1]=-1/0,v=0;y>v;v++){var _=g(t,n,s,u,f[v]);h[0]=r(_,h[0]),p[0]=a(_,p[0])}for(y=m(e,o,l,c,d),v=0;y>v;v++){var x=g(e,o,l,c,d[v]);h[1]=r(x,h[1]),p[1]=a(x,p[1])}h[0]=r(t,h[0]),p[0]=a(t,p[0]),h[0]=r(u,h[0]),p[0]=a(u,p[0]),h[1]=r(e,h[1]),p[1]=a(e,p[1]),h[1]=r(c,h[1]),p[1]=a(c,p[1])},n.fromQuadratic=function(t,e,n,o,s,l,u,c){var h=i.quadraticExtremum,f=i.quadraticAt,d=a(r(h(t,n,s),1),0),p=a(r(h(e,o,l),1),0),v=f(t,n,s,d),m=f(e,o,l,p);u[0]=r(t,s,v),u[1]=r(e,l,m),c[0]=a(t,s,v),c[1]=a(e,l,m)},n.fromArc=function(t,i,n,r,a,f,d,p,v){var m=e.min,g=e.max,y=Math.abs(a-f);if(1e-4>y%h&&y>1e-4)return p[0]=t-n,p[1]=i-r,v[0]=t+n,void(v[1]=i+r);if(l[0]=s(a)*n+t,l[1]=o(a)*r+i,u[0]=s(f)*n+t,u[1]=o(f)*r+i,m(p,l,u),g(v,l,u),a%=h,0>a&&(a+=h),f%=h,0>f&&(f+=h),a>f&&!d?f+=h:f>a&&d&&(a+=h),d){var _=f;f=a,a=_}for(var x=0;f>x;x+=Math.PI/2)x>a&&(c[0]=s(x)*n+t,c[1]=o(x)*r+i,m(p,c,p),g(v,c,v))},n}),e("zrender/config",[],function(){var t=1;typeof window!==i&&(t=Math.max(window.devicePixelRatio||1,1));var e={debugMode:0,devicePixelRatio:t};return e}),e("zrender/graphic/Style",[ue],function(){function t(t,e,i){var n=e.x,r=e.x2,a=e.y,o=e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i[ne]+i.y,o=o*i[ne]+i.y);var s=t.createLinearGradient(n,a,r,o);return s}function e(t,e,i){var n=i.width,r=i[ne],a=Math.min(n,r),o=e.x,s=e.y,l=e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var u=t.createRadialGradient(o,s,0,o,s,l);return u}var i=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],n=function(t){this.extendFrom(t)};n[H]={constructor:n,fill:"#000000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,textFill:"#000",textStroke:null,textPosition:"inside",textBaseline:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textTransform:!1,textRotation:0,blend:null,bind:function(t,e,n){for(var r=this,a=n&&n.style,l=!a,u=0;u<i[B];u++){var c=i[u],h=c[0];(l||r[h]!==a[h])&&(t[h]=r[h]||c[1])}if((l||r.fill!==a.fill)&&(t.fillStyle=r.fill),(l||r[o]!==a[o])&&(t.strokeStyle=r[o]),(l||r[U]!==a[U])&&(t.globalAlpha=null==r[U]?1:r[U]),(l||r.blend!==a.blend)&&(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var f=r[s];t[s]=f/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this[o];return null!=t&&"none"!==t&&this[s]>0},extendFrom:function(t,e){if(t){var i=this;for(var n in t)!t.hasOwnProperty(n)||!e&&i.hasOwnProperty(n)||(i[n]=t[n])}},set:function(t,e){typeof t===V?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(i,n,r){for(var a="radial"===n.type?e:t,o=a(i,n,r),s=n[I],l=0;l<s[B];l++)o.addColorStop(s[l].offset,s[l].color);return o}};for(var r=n[H],a=0;a<i[B];a++){var l=i[a];l[0]in r||(r[l[0]]=l[1])}return n.getGradient=r.getGradient,n}),e("zrender/Element",[ue,"./core/guid","./mixin/Eventful","./mixin/Transformable","./mixin/Animatable","./core/util"],function(t){var e=t("./core/guid"),i=t("./mixin/Eventful"),n=t("./mixin/Transformable"),a=t("./mixin/Animatable"),o=t("./core/util"),s=function(t){n.call(this,t),i.call(this,t),a.call(this,t),this.id=t.id||e()};return s[H]={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this[r];i||(i=this[r]=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if(t===Y||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this[N]=!0,this.__zr&&this.__zr.refresh()},show:function(){this[N]=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if(typeof t===V)this.attrKV(t,e);else if(o[z](t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e[B];i++)t[F].addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e[B];i++)t[F].removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},o.mixin(s,a),o.mixin(s,n),o.mixin(s,i),s}),e("echarts/model/mixin/makeStyleMapper",[ue,le],function(t){var e=t(le);return function(t){for(var i=0;i<t[B];i++)t[i][1]||(t[i][1]=t[i][0]);return function(i){for(var n={},r=0;r<t[B];r++){var a=t[r][1];if(!(i&&e[E](i,a)>=0)){var o=this[f](a);null!=o&&(n[t[r][0]]=o)}}return n}}}),e("echarts/scale/Ordinal",[ue,le,"./Scale"],function(t){var e=t(le),i=t("./Scale"),n=i[H],r=i[P]({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t[B]-1]},parse:function(t){return typeof t===V?e[E](this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),n[x].call(this,t)&&null!=this._data[t]},normalize:function(t){return n.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(n.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},niceTicks:e.noop,niceExtent:e.noop});return r[D]=function(){return new r},r}),e("zrender/mixin/Transformable",[ue,"../core/matrix","../core/vector"],function(t){function e(t){return t>o||-o>t}var i=t("../core/matrix"),n=t("../core/vector"),a=i.identity,o=5e-5,s=function(t){t=t||{},t[Y]||(this[Y]=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=s[H];u[r]=null,u.needLocalTransform=function(){return e(this.rotation)||e(this[Y][0])||e(this[Y][1])||e(this.scale[0]-1)||e(this.scale[1]-1)},u.updateTransform=function(){var t=this.parent,e=t&&t[r],n=this.needLocalTransform(),o=this[r];return n||e?(o=o||i[D](),n?this.getLocalTransform(o):a(o),e&&(n?i.mul(o,t[r],o):i.copy(o,t[r])),this[r]=o,this.invTransform=this.invTransform||i[D](),void i.invert(this.invTransform,o)):void(o&&a(o))
},u.getLocalTransform=function(t){t=t||[],a(t);var e=this.origin,n=this.scale,r=this.rotation,o=this[Y];return e&&(t[4]-=e[0],t[5]-=e[1]),i.scale(t,t,n),r&&i.rotate(t,t,r),e&&(t[4]+=e[0],t[5]+=e[1]),t[4]+=o[0],t[5]+=o[1],t},u.setTransform=function(t){var e=this[r],i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},u.restoreTransform=function(t){var e=(this[r],t.dpr||1);t.setTransform(e,0,0,e,0,0)};var c=[];return u.decomposeTransform=function(){if(this[r]){var t=this.parent,n=this[r];t&&t[r]&&(i.mul(c,t.invTransform,n),n=c);var a=n[0]*n[0]+n[1]*n[1],o=n[2]*n[2]+n[3]*n[3],s=this[Y],l=this.scale;e(a-1)&&(a=Math.sqrt(a)),e(o-1)&&(o=Math.sqrt(o)),n[0]<0&&(a=-a),n[3]<0&&(o=-o),s[0]=n[4],s[1]=n[5],l[0]=a,l[1]=o,this.rotation=Math.atan2(-n[1]/o,n[0]/a)}},u.getGlobalScale=function(){var t=this[r];if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),i=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(i=-i),[e,i]},u.transformCoordToLocal=function(t,e){var i=[t,e],r=this.invTransform;return r&&n[l](i,i,r),i},u.transformCoordToGlobal=function(t,e){var i=[t,e],a=this[r];return a&&n[l](i,i,a),i},s}),e("zrender/core/guid",[],function(){var t=2311;return function(){return t++}}),e("zrender/mixin/Animatable",[ue,"../animation/Animator","../core/util","../core/log"],function(t){var e=t("../animation/Animator"),i=t("../core/util"),n=i.isString,r=i.isFunction,a=i[z],o=t("../core/log"),s=function(){this.animators=[]};return s[H]={constructor:s,animate:function(t,n){var r,a=!1,s=this,l=this.__zr;if(t){var u=t.split("."),c=s;a="shape"===u[0];for(var h=0,f=u[B];f>h;h++)c&&(c=c[u[h]]);c&&(r=c)}else r=s;if(!r)return void o('Property "'+t+'" is not existed in element '+s.id);var d=s.animators,p=new e(r,n);return p.during(function(){s.dirty(a)}).done(function(){d[C](i[E](d,p),1)}),d.push(p),l&&l[F].addAnimator(p),p},stopAnimation:function(t){for(var e=this.animators,i=e[B],n=0;i>n;n++)e[n].stop(t);return e[B]=0,this},animateTo:function(t,e,i,a,o){function s(){u--,u||o&&o()}n(i)?(o=a,a=i,i=0):r(a)?(o=a,a="linear",i=0):r(i)?(o=i,i=0):r(e)?(o=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,i,a,o);var l=this.animators.slice(),u=l[B];u||o&&o();for(var c=0;c<l[B];c++)l[c].done(s).start(a)},_animateToShallow:function(t,e,n,r,o){var s={},l=0;for(var u in n)if(n.hasOwnProperty(u))if(null!=e[u])a(n[u])&&!i.isArrayLike(n[u])?this._animateToShallow(t?t+"."+u:u,e[u],n[u],r,o):(s[u]=n[u],l++);else if(null!=n[u])if(t){var c={};c[t]={},c[t][u]=n[u],this.attr(c)}else this.attr(u,n[u]);return l>0&&this.animate(t,!1).when(null==r?500:r,s).delay(o||0),this}},s}),e("echarts/util/component",[ue,le,"./clazz"],function(t){var e=t(le),i=t("./clazz"),n=i.parseClassType,r=0,a={},o="_";return a.getUID=function(t){return[t||"",r++,Math.random()].join(o)},a.enableSubTypeDefaulter=function(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=n(t),e[t.main]=i},t.determineSubType=function(i,r){var a=r.type;if(!a){var o=n(i).main;t.hasSubTypes(i)&&e[o]&&(a=e[o](r))}return a},t},a.enableTopologicalTravel=function(t,i){function n(t){var n={},o=[];return e.each(t,function(s){var l=r(n,s),u=l.originalDeps=i(s),c=a(u,t);l.entryCount=c[B],0===l.entryCount&&o.push(s),e.each(c,function(t){e[E](l.predecessor,t)<0&&l.predecessor.push(t);var i=r(n,t);e[E](i.successor,t)<0&&i.successor.push(s)})}),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,i){var n=[];return e.each(t,function(t){e[E](i,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,i,r,a){function o(t){u[t].entryCount--,0===u[t].entryCount&&c.push(t)}function s(t){h[t]=!0,o(t)}if(t[B]){var l=n(i),u=l.graph,c=l.noEntryList,h={};for(e.each(t,function(t){h[t]=!0});c[B];){var f=c.pop(),d=u[f],p=!!h[f];p&&(r.call(a,f,d.originalDeps.slice()),delete h[f]),e.each(d.successor,p?s:o)}e.each(h,function(){throw new Error("Circle dependency may exists")})}}},a}),e("echarts/model/mixin/boxLayout",[ue],function(){return{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get($),width:this.get("width"),height:this.get(ne)}}}}),e("echarts/coord/cartesian/Cartesian",[ue,le],function(t){function e(t){return this._axes[t]}var i=t(le),n=function(t){this._axes={},this._dimList=[],this.name=t||""};return n[H]={constructor:n,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return i.map(this._dimList,e,this)},getAxesByScale:function(t){return t=t[q](),i.filter(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,v)},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i[B];r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},n}),e("zrender/core/log",[ue,"../config"],function(t){var e=t("../config");return function(){if(0!==e.debugMode)if(1==e.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(e.debugMode>1)for(var t in arguments)console.log(arguments[t])}}),e("zrender/animation/Animator",[ue,"./Clip","../tool/color","../core/util"],function(t){function e(t,e){return t[e]}function i(t,e,i){t[e]=i}function n(t,e,i){return(e-t)*i+t}function r(t,e,i){return i>.5?e:t}function a(t,e,i,r,a){var o=t[B];if(1==a)for(var s=0;o>s;s++)r[s]=n(t[s],e[s],i);else for(var l=t[0][B],s=0;o>s;s++)for(var u=0;l>u;u++)r[s][u]=n(t[s][u],e[s][u],i)}function o(t,e,i){var n=t[B],r=e[B];if(n!==r){var a=n>r;if(a)t[B]=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:g.call(e[o]))}for(var s=t[0]&&t[0][B],o=0;o<t[B];o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function s(t,e,i){if(t===e)return!0;var n=t[B];if(n!==e[B])return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0][B],r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function l(t,e,i,n,r,a,o,s,l){var c=t[B];if(1==l)for(var h=0;c>h;h++)s[h]=u(t[h],e[h],i[h],n[h],r,a,o);else for(var f=t[0][B],h=0;c>h;h++)for(var d=0;f>d;d++)s[h][d]=u(t[h][d],e[h][d],i[h][d],n[h][d],r,a,o)}function u(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function c(t){if(m(t)){var e=t[B];if(m(t[0])){for(var i=[],n=0;e>n;n++)i.push(g.call(t[n]));return i}return g.call(t)}return t}function h(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function f(t,e,i,c,f){var v=t._getter,g=t._setter,y="spline"===e,_=c[B];if(_){var x,b=c[0].value,w=m(b),M=!1,T=!1,S=w&&m(b[0])?2:1;c.sort(function(t,e){return t.time-e.time}),x=c[_-1].time;for(var C=[],A=[],P=c[0].value,L=!0,z=0;_>z;z++){C.push(c[z].time/x);var I=c[z].value;if(w&&s(I,P,S)||!w&&I===P||(L=!1),P=I,typeof I==V){var k=p.parse(I);k?(I=k,M=!0):T=!0}A.push(I)}if(!L){for(var D=A[_-1],z=0;_-1>z;z++)w?o(A[z],D,S):!isNaN(A[z])||isNaN(D)||T||M||(A[z]=D);w&&o(v(t._target,f),D,S);var O,R,E,N,G,F,H=0,q=0;if(M)var W=[0,0,0,0];var j=function(t,e){var i;if(0>e)i=0;else if(q>e){for(O=Math.min(H+1,_-1),i=O;i>=0&&!(C[i]<=e);i--);i=Math.min(i,_-2)}else{for(i=H;_>i&&!(C[i]>e);i++);i=Math.min(i-1,_-2)}H=i,q=e;var o=C[i+1]-C[i];if(0!==o)if(R=(e-C[i])/o,y)if(N=A[i],E=A[0===i?i:i-1],G=A[i>_-2?_-1:i+1],F=A[i>_-3?_-1:i+2],w)l(E,N,G,F,R,R*R,R*R*R,v(t,f),S);else{var s;if(M)s=l(E,N,G,F,R,R*R,R*R*R,W,1),s=h(W);else{if(T)return r(N,G,R);s=u(E,N,G,F,R,R*R,R*R*R)}g(t,f,s)}else if(w)a(A[i],A[i+1],R,v(t,f),S);else{var s;if(M)a(A[i],A[i+1],R,W,1),s=h(W);else{if(T)return r(A[i],A[i+1],R);s=n(A[i],A[i+1],R)}g(t,f,s)}},Z=new d({target:t._target,life:x,loop:t._loop,delay:t._delay,onframe:j,ondestroy:i});return e&&"spline"!==e&&(Z.easing=e),Z}}}var d=t("./Clip"),p=t("../tool/color"),v=t("../core/util"),m=v.isArrayLike,g=Array[H].slice,y=function(t,n,r,a){this._tracks={},this._target=t,this._loop=n||!1,this._getter=r||e,this._setter=a||i,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};return y[H]={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:c(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},_doneCallback:function(){this._tracks={},this._clipList[B]=0;for(var t=this._doneList,e=t[B],i=0;e>i;i++)t[i].call(this)},start:function(t){var e,i=this,n=0,r=function(){n--,n||i._doneCallback()};for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var o=f(this,t,r,this._tracks[a],a);o&&(this._clipList.push(o),n++,this[F]&&this[F].addClip(o),e=o)}if(e){var s=e.onframe;e.onframe=function(t,e){s(t,e);for(var n=0;n<i._onframeList[B];n++)i._onframeList[n](t,e)}}return n||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this[F],n=0;n<e[B];n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e[B]=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},y}),e("echarts/coord/cartesian/axisLabelInterval",[ue,le,"../axisHelper"],function(t){var e=t(le),i=t("../axisHelper");return function(t){var n=t.model,r=n[se]("axisLabel"),a=r.get("interval");return t.type!==c||"auto"!==a?"auto"===a?0:a:i.getAxisLabelInterval(e.map(t.scale.getTicks(),t[v],t),n.getFormattedLabels(),r[se](oe)[ae](),t.isHorizontal())}}),e("echarts/coord/Axis",[ue,"../util/number",le],function(t){function e(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}var i=t("../util/number"),n=i.linearMap,r=t(le),a=[0,1],o=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};return o[H]={constructor:o,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this[x](this[v](t))},getExtent:function(){var t=this._extent.slice();return t},getPixelPrecision:function(t){return i.getPixelPrecision(t||this.scale[_](),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,i){var r=this._extent,o=this.scale;return t=o.normalize(t),this.onBand&&o.type===p&&(r=r.slice(),e(r,o.count())),n(t,a,r,i)},coordToData:function(t,i){var r=this._extent,o=this.scale;this.onBand&&o.type===p&&(r=r.slice(),e(r,o.count()));var s=n(t,r,a,i);return this.scale.scale(s)},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),i=[],n=0;n<e[B];n++)i.push(e[n][0]);return e[n-1]&&i.push(e[n-1][1]),i}return r.map(this.scale.getTicks(),this[v],this)},getLabelsCoords:function(){return r.map(this.scale.getTicks(),this[v],this)},getBands:function(){for(var t=this[_](),e=[],i=this.scale.count(),n=t[0],r=t[1],a=r-n,o=0;i>o;o++)e.push([a*o/i+n,a*(o+1)/i+n]);return e},getBandWidth:function(){var t=this._extent,e=this.scale[_](),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i}},o}),e("zrender/animation/Clip",[ue,"./easing"],function(t){function e(t){this._target=t[ee],this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart}var i=t("./easing");return e[H]={constructor:e,step:function(t){this._initialized||(this._startTime=t+this._delay,this._initialized=!0);var e=(t-this._startTime)/this._life;if(!(0>e)){e=Math.min(e,1);var n=this.easing,r=typeof n==V?i[n]:n,a=typeof r===b?r(e):e;return this.fire("frame",a),1==e?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime)%this._life;this._startTime=t-e+this.gap,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)}},e}),e("echarts/coord/cartesian/AxisModel",[ue,"../../model/Component",le,"../axisModelCreator","../axisModelCommonMixin","../axisModelZoomMixin"],function(t){function e(t,e){return e.type||(e.data?c:"value")}var i=t("../../model/Component"),n=t(le),r=t("../axisModelCreator"),a=i[P]({type:"cartesian2dAxis",axis:null,init:function(){a.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){a.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){a.superApply(this,"restoreData",arguments),this.resetRange()},findGridModel:function(){return this[h].queryComponents({mainType:"grid",index:this.get("gridIndex"),id:this.get("gridId")})[0]}});n.merge(a[H],t("../axisModelCommonMixin")),n.merge(a[H],t("../axisModelZoomMixin"));var o={offset:0};return r("x",a,e,o),r("y",a,e,o),a}),e("zrender/animation/easing",[],function(){var t={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(e){return 1-t.bounceOut(1-e)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(e){return.5>e?.5*t.bounceIn(2*e):.5*t.bounceOut(2*e-1)+.5}};return t}),e("echarts/coord/axisModelCreator",[ue,"./axisDefault",le,"../model/Component","../util/layout"],function(t){var e=t("./axisDefault"),i=t(le),n=t("../model/Component"),r=t("../util/layout"),a=["value",c,"time","log"];return function(t,o,s,l){i.each(a,function(n){o[P]({type:t+"Axis."+n,mergeDefaultAndTheme:function(e,a){var o=this.layoutMode,l=o?r.getLayoutParams(e):{},u=a.getTheme();i.merge(e,u.get(n+"Axis")),i.merge(e,this.getDefaultOption()),e.type=s(t,e),o&&r.mergeLayoutParam(e,l,o)},defaultOption:i.mergeAll([{},e[n+"Axis"],l],!0)})}),n.registerSubTypeDefaulter(t+"Axis",i.curry(s,t))}}),e("echarts/coord/axisModelZoomMixin",[ue],function(){return{getMin:function(){var t=this.option,e=null!=t.rangeStart?t.rangeStart:t.min;return e instanceof Date&&(e=+e),e},getMax:function(){var t=this.option,e=null!=t.rangeEnd?t.rangeEnd:t.max;return e instanceof Date&&(e=+e),e},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}}}),e("echarts/coord/axisModelCommonMixin",[ue,le,"./axisHelper"],function(t){function e(t){return r[z](t)&&null!=t.value?t.value:t}function i(){return this.get("type")===c&&r.map(this.get("data"),e)}function n(){return a.getFormattedLabels(this.axis,this.get("axisLabel.formatter"))}var r=t(le),a=t("./axisHelper");return{getFormattedLabels:n,getCategories:i}}),e("zrender/contain/line",[],function(){return{containStroke:function(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,u=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),u=(t*n-i*e)/(t-i);var c=l*a-o+u,h=c*c/(l*l+1);return s/2*s/2>=h}}}),e("zrender/contain/cubic",[ue,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,u,c,h){if(0===u)return!1;var f=u;if(h>i+f&&h>r+f&&h>o+f&&h>l+f||i-f>h&&r-f>h&&o-f>h&&l-f>h||c>t+f&&c>n+f&&c>a+f&&c>s+f||t-f>c&&n-f>c&&a-f>c&&s-f>c)return!1;var d=e.cubicProjectPoint(t,i,n,r,a,o,s,l,c,h,null);return f/2>=d}}}),e("zrender/contain/quadratic",[ue,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,u){if(0===s)return!1;var c=s;if(u>i+c&&u>r+c&&u>o+c||i-c>u&&r-c>u&&o-c>u||l>t+c&&l>n+c&&l>a+c||t-c>l&&n-c>l&&a-c>l)return!1;var h=e.quadraticProjectPoint(t,i,n,r,a,o,l,u,null);return c/2>=h}}}),e("zrender/contain/util",[ue],function(){var t=2*Math.PI;return{normalizeRadian:function(e){return e%=t,0>e&&(e+=t),e}}}),e("zrender/contain/windingLine",[],function(){return function(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l>r?o:0}}),e("zrender/contain/arc",[ue,"./util"],function(t){var e=t("./util").normalizeRadian,i=2*Math.PI;return{containStroke:function(t,n,r,a,o,s,l,u,c){if(0===l)return!1;var h=l;u-=t,c-=n;var f=Math.sqrt(u*u+c*c);if(f-h>r||r>f+h)return!1;if(Math.abs(a-o)%i<1e-4)return!0;if(s){var d=a;a=e(o),o=e(d)}else a=e(a),o=e(o);a>o&&(o+=i);var p=Math.atan2(c,u);return 0>p&&(p+=i),p>=a&&o>=p||p+i>=a&&o>=p+i}}}),e("zrender/core/LRU",[ue],function(){var t=function(){this.head=null,this.tail=null,this._len=0},e=t[H];e.insert=function(t){var e=new i(t);return this.insertEntry(e),e},e.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,this.tail=t):this.head=this.tail=t,this._len++},e[L]=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},e.len=function(){return this._len};var i=function(t){this.value=t,this.next,this.prev},n=function(e){this._list=new t,this._map={},this._maxSize=e||10},r=n[H];return r.put=function(t,e){var i=this._list,n=this._map;if(null==n[t]){var r=i.len();if(r>=this._maxSize&&r>0){var a=i.head;i[L](a),delete n[a.key]}var o=i.insert(e);o.key=t,n[t]=o}},r.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i[L](e),i.insertEntry(e)),e.value):void 0},r.clear=function(){this._list.clear(),this._map={}},n}),e("echarts/coord/axisDefault",[ue,le],function(t){var e=t(le),i={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisLine:{show:!0,onZero:!0,lineStyle:{color:"#333",width:1,type:"solid"}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,margin:8,textStyle:{fontSize:12}},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},n=e.merge({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},i),r=e.merge({boundaryGap:[0,0],splitNumber:5},i),a=e[W]({scale:!0,min:"dataMin",max:"dataMax"},r),o=e[W]({logBase:10},r);return o.scale=!0,{categoryAxis:n,valueAxis:r,timeAxis:a,logAxis:o}}),e("zrender/graphic/helper/poly",[ue,"./smoothSpline","./smoothBezier"],function(t){var e=t("./smoothSpline"),i=t("./smoothBezier");return{buildPath:function(t,r,a){var o=r.points,s=r.smooth;if(o&&o[B]>=2){if(s&&"spline"!==s){var l=i(o,s,a,r.smoothConstraint);t[n](o[0][0],o[0][1]);for(var u=o[B],c=0;(a?u:u-1)>c;c++){var h=l[2*c],f=l[2*c+1],d=o[(c+1)%u];t.bezierCurveTo(h[0],h[1],f[0],f[1],d[0],d[1])}}else{"spline"===s&&(o=e(o,a)),t[n](o[0][0],o[0][1]);for(var c=1,p=o[B];p>c;c++)t.lineTo(o[c][0],o[c][1])}a&&t.closePath()}}}}),e("zrender/Storage",[ue,"./core/util","./core/env","./container/Group","./core/timsort"],function(t){function e(t,e){return t[T]===e[T]?t.z===e.z?t.z2-e.z2:t.z-e.z:t[T]-e[T]}var i=t("./core/util"),n=t("./core/env"),r=t("./container/Group"),o=t("./core/timsort"),s=function(){this._elements={},this._roots=[],this._displayList=[],this._displayListLen=0};return s[H]={constructor:s,traverse:function(t,e){for(var i=0;i<this._roots[B];i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var i=this._roots,r=this._displayList,a=0,s=i[B];s>a;a++)this._updateAndAddDisplayable(i[a],null,t);r[B]=this._displayListLen,n[G]&&o(r,e)},_updateAndAddDisplayable:function(t,e,i){if(!t[N]||i){t.beforeUpdate(),t[a]&&t[k](),t.afterUpdate();var n=t.clipPath;if(n&&(n.parent=t,n.updateTransform(),e?(e=e.slice(),e.push(n)):e=[n]),t.isGroup){for(var r=t._children,o=0;o<r[B];o++){var s=r[o];t[a]&&(s[a]=!0),this._updateAndAddDisplayable(s,e,i)}t[a]=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){this._elements[t.id]||(t instanceof r&&t.addChildrenToStorage(this),this.addToMap(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots[B];e++){var n=this._roots[e];n instanceof r&&n.delChildrenFromStorage(this)}return this._elements={},this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,a=t[B];a>e;e++)this.delRoot(t[e]);else{var o;o=typeof t==V?this._elements[t]:t;var s=i[E](this._roots,o);s>=0&&(this.delFromMap(o.id),this._roots[C](s,1),o instanceof r&&o.delChildrenFromStorage(this))}},addToMap:function(t){return t instanceof r&&(t.__storage=this),t.dirty(!1),this._elements[t.id]=t,this},get:function(t){return this._elements[t]},delFromMap:function(t){var e=this._elements,i=e[t];return i&&(delete e[t],i instanceof r&&(i.__storage=null)),this},dispose:function(){this._elements=this._renderList=this._roots=null},displayableSortFunc:e},s}),e("zrender/animation/Animation",[ue,"../core/util","../core/event","./requestAnimationFrame","./Animator"],function(t){var e=t("../core/util"),i=t("../core/event").Dispatcher,n=t("./requestAnimationFrame"),r=t("./Animator"),a=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,i.call(this)};return a[H]={constructor:a,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t[F]=this;for(var e=t.getClips(),i=0;i<e[B];i++)this.addClip(e[i])},removeClip:function(t){var i=e[E](this._clips,t);i>=0&&this._clips[C](i,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e[B];i++)this.removeClip(e[i]);t[F]=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i[B],r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r[B];for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage[k]&&this.stage[k]()},_startLoop:function(){function t(){e._running&&(n(t),!e._paused&&e._update())}var e=this;this._running=!0,n(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var i=new r(t,e.loop,e.getter,e.setter);return i}},e.mixin(a,i),a}),e("zrender/dom/HandlerProxy",[ue,"../core/event","../core/util","../mixin/Eventful","../core/env","../core/GestureMgr"],function(t){function e(t){return"mousewheel"===t&&c.browser.firefox?"DOMMouseScroll":t}function i(t,e,i){var n=t._gestureMgr;"start"===i&&n.clear();var r=n.recognize(e,t.handler.findHover(e.zrX,e.zrY,null),t.dom);if("end"===i&&n.clear(),r){var a=r.type;e.gestureEvent=a,t.handler.dispatchToElement(r[ee],a,r.event)}}function n(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function r(){return c.touchEventsSupported}function a(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}for(var i=0;i<g[B];i++){var n=g[i];t._handlers[n]=l.bind(y[n],t)}for(var i=0;i<m[B];i++){var n=m[i];t._handlers[n]=e(y[n],t)}}function o(t){function i(i,n){l.each(i,function(i){f(t,e(i),n._handlers[i])},n)}u.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new h,this._handlers={},a(this),r()&&i(g,this),i(m,this)}var s=t("../core/event"),l=t("../core/util"),u=t("../mixin/Eventful"),c=t("../core/env"),h=t("../core/GestureMgr"),f=s.addEventListener,d=s.removeEventListener,p=s.normalizeEvent,v=300,m=["click","dblclick","mousewheel",S,"mouseup","mousedown","mousemove","contextmenu"],g=["touchstart","touchend","touchmove"],y={mousemove:function(t){t=p(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=p(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger(S,t)},touchstart:function(t){t=p(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,i(this,t,"start"),y.mousemove.call(this,t),y.mousedown.call(this,t),n(this)},touchmove:function(t){t=p(this.dom,t),t.zrByTouch=!0,i(this,t,"change"),y.mousemove.call(this,t),n(this)},touchend:function(t){t=p(this.dom,t),t.zrByTouch=!0,i(this,t,"end"),y.mouseup.call(this,t),+new Date-this._lastTouchMoment<v&&y.click.call(this,t),n(this)}};l.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){y[t]=function(e){e=p(this.dom,e),this.trigger(t,e)}});var _=o[H];return _.dispose=function(){for(var t=m.concat(g),i=0;i<t[B];i++){var n=t[i];d(this.dom,e(n),this._handlers[n])}},_.setCursor=function(t){this.dom.style.cursor=t||"default"},l.mixin(o,u),o}),e("zrender/Handler",[ue,"./core/util","./mixin/Draggable","./mixin/Eventful"],function(t){function e(t,e,i){return{type:t,event:i,target:e,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch}}function i(){}function n(t,e,i){if(t[t.rectHover?"rectContain":x](e,i)){for(var n=t;n;){if(n[ie]||n.clipPath&&!n.clipPath[x](e,i))return!1;n=n.parent}return!0}return!1}var r=t("./core/util"),a=t("./mixin/Draggable"),o=t("./mixin/Eventful");i[H].dispose=function(){};var s=["click","dblclick","mousewheel",S,"mouseup","mousedown","mousemove","contextmenu"],l=function(t,e,n,l){o.call(this),this.storage=t,this.painter=e,this.painterRoot=l,n=n||new i,this.proxy=n,n.handler=this,this._hovered,this._lastTouchMoment,this._lastX,this._lastY,a.call(this),r.each(s,function(t){n.on&&n.on(t,this[t],this)},this)};return l[H]={constructor:l,mousemove:function(t){var e=t.zrX,i=t.zrY,n=this.findHover(e,i,null),r=this._hovered,a=this.proxy;this._hovered=n,a.setCursor&&a.setCursor(n?n.cursor:"default"),r&&n!==r&&r.__zr&&this.dispatchToElement(r,S,t),this.dispatchToElement(n,"mousemove",t),n&&n!==r&&this.dispatchToElement(n,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,S,t);var e,i=t.toElement||t.relatedTarget;do i=i&&i.parentNode;while(i&&9!=i.nodeType&&!(e=i===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(){this._hovered=null},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,i,n){for(var r="on"+i,a=e(i,t,n),o=t;o&&(o[r]&&(a.cancelBubble=o[r].call(o,a)),o.trigger(i,a),o=o.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(i,a),this.painter&&this.painter.eachOtherLayer(function(t){typeof t[r]==b&&t[r].call(t,a),t.trigger&&t.trigger(i,a)}))},findHover:function(t,e,i){for(var r=this.storage.getDisplayList(),a=r[B]-1;a>=0;a--)if(!r[a][ie]&&r[a]!==i&&!r[a][N]&&n(r[a],t,e))return r[a]}},r.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){l[H][t]=function(e){var i=this.findHover(e.zrX,e.zrY,null);if("mousedown"===t)this._downel=i,this._upel=i;else if("mosueup"===t)this._upel=i;else if("click"===t&&this._downel!==this._upel)return;this.dispatchToElement(i,t,e)}}),r.mixin(l,o),r.mixin(l,a),l}),e("echarts/chart/helper/createListFromArray",[ue,"../../data/List","../../data/helper/completeDimensions",le,"../../util/model","../../CoordinateSystem"],function(t){function e(t){for(var e=0;e<t[B]&&null==t[e];)e++;return t[e]}function i(t){var i=e(t);return null!=i&&!u[w](d(i))}function n(t,e,n){t=t||[];var r=e.get(j),a=g[r],c=f.get(r),p=a&&a(t,e,n),y=p&&p[m];y||(y=c&&c[m]||["x","y"],y=l(y,t,y.concat(["value"])));var _=p?p.categoryIndex:-1,x=new s(y,e),b=o(p,t),w={},M=_>=0&&i(t)?function(t,e,i,n){return h.isDataItemOption(t)&&(x.hasItemOption=!0),n===_?i:v(d(t),y[n])}:function(t,e,i,n){var r=d(t),a=v(r&&r[n],y[n]);h.isDataItemOption(t)&&(x.hasItemOption=!0);var o=p&&p.categoryAxesModels;return o&&o[e]&&typeof a===V&&(w[e]=w[e]||o[e].getCategories(),a=u[E](w[e],a),0>a&&!isNaN(a)&&(a=+a)),a};return x.hasItemOption=!1,x.initData(t,b,M),x}function r(t){return t!==c&&"time"!==t}function a(t){return t===c?p:"time"===t?"time":"float"}function o(t,e){var i,n=[],r=t&&t[m][t.categoryIndex];if(r&&(i=t.categoryAxesModels[r.name]),i){var a=i.getCategories();if(a){var o=e[B];if(u[w](e[0])&&e[0][B]>1){n=[];for(var s=0;o>s;s++)n[s]=a[e[s][t.categoryIndex||0]]}else n=a.slice(0)}}return n}var s=t("../../data/List"),l=t("../../data/helper/completeDimensions"),u=t(le),h=t("../../util/model"),f=t("../../CoordinateSystem"),d=h.getDataItemValue,v=h.converDataValue,g={cartesian2d:function(t,e,i){var n=u.map(["xAxis","yAxis"],function(t){return i.queryComponents({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),o=n[0],s=n[1],h=o.get("type"),f=s.get("type"),d=[{name:"x",type:a(h),stackable:r(h)},{name:"y",type:a(f),stackable:r(f)}],p=h===c,v=f===c;l(d,t,["x","y","z"]);var m={};return p&&(m.x=o),v&&(m.y=s),{dimensions:d,categoryIndex:p?0:v?1:-1,categoryAxesModels:m}},polar:function(t,e,i){var n=i.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],o=n.findAxisModel("angleAxis"),s=n.findAxisModel("radiusAxis"),u=s.get("type"),h=o.get("type"),f=[{name:"radius",type:a(u),stackable:r(u)},{name:"angle",type:a(h),stackable:r(h)}],d=h===c,p=u===c;l(f,t,["radius","angle","value"]);var v={};return p&&(v.radius=s),d&&(v.angle=o),{dimensions:f,categoryIndex:d?1:p?0:-1,categoryAxesModels:v}},geo:function(t){return{dimensions:l([{name:"lng"},{name:"lat"}],t,["lng","lat","value"])}}};return n}),e("zrender/graphic/helper/smoothBezier",[ue,"../../core/vector"],function(t){var e=t("../../core/vector"),i=e.min,n=e.max,r=e.scale,a=e.distance,o=e.add;return function(t,s,l,u){var c,h,f,d,p=[],v=[],m=[],g=[];if(u){f=[1/0,1/0],d=[-1/0,-1/0];for(var y=0,_=t[B];_>y;y++)i(f,f,t[y]),n(d,d,t[y]);
i(f,f,u[0]),n(d,d,u[1])}for(var y=0,_=t[B];_>y;y++){var x=t[y];if(l)c=t[y?y-1:_-1],h=t[(y+1)%_];else{if(0===y||y===_-1){p.push(e.clone(t[y]));continue}c=t[y-1],h=t[y+1]}e.sub(v,h,c),r(v,v,s);var b=a(x,c),w=a(x,h),M=b+w;0!==M&&(b/=M,w/=M),r(m,v,-b),r(g,v,w);var T=o([],x,m),S=o([],x,g);u&&(n(T,T,f),i(T,T,d),n(S,S,f),i(S,S,d)),p.push(T),p.push(S)}return l&&p.push(p.shift()),p}}),e("zrender/graphic/helper/smoothSpline",[ue,"../../core/vector"],function(t){function e(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}var i=t("../../core/vector");return function(t,n){for(var r=t[B],a=[],o=0,s=1;r>s;s++)o+=i.distance(t[s-1],t[s]);var l=o/2;l=r>l?r:l;for(var s=0;l>s;s++){var u,c,h,f=s/(l-1)*(n?r:r-1),d=Math.floor(f),p=f-d,v=t[d%r];n?(u=t[(d-1+r)%r],c=t[(d+1)%r],h=t[(d+2)%r]):(u=t[0===d?d:d-1],c=t[d>r-2?r-1:d+1],h=t[d>r-3?r-1:d+2]);var m=p*p,g=p*m;a.push([e(u[0],v[0],c[0],h[0],p,m,g),e(u[1],v[1],c[1],h[1],p,m,g)])}return a}}),e("zrender/Painter",[ue,"./config","./core/util","./core/log","./core/BoundingRect","./core/timsort","./Layer","./animation/requestAnimationFrame","./graphic/Image"],function(t){function e(t){return parseInt(t,10)}function i(t){return t?t.isBuildin?!0:typeof t.resize!==b||typeof t.refresh!==b?!1:!0:!1}function n(t){t.__unusedCount++}function o(t){1==t.__unusedCount&&t.clear()}function s(t,e,i){return w.copy(t[re]()),t[r]&&w[l](t[r]),M.width=e,M[ne]=i,!w.intersect(M)}function u(t,e){if(t==e)return!1;if(!t||!e||t[B]!==e[B])return!0;for(var i=0;i<t[B];i++)if(t[i]!==e[i])return!0}function c(t,e){for(var i=0;i<t[B];i++){var n=t[i],r=n.path;n.setTransform(e),r.beginPath(e),n.buildPath(r,n.shape),e.clip(),n.restoreTransform(e)}}function h(t,e){var i=document[y]("div");return i.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}var f=t("./config"),d=t("./core/util"),p=t("./core/log"),v=t("./core/BoundingRect"),m=t("./core/timsort"),g=t("./Layer"),_=t("./animation/requestAnimationFrame"),x=5,w=new v(0,0,0,0),M=new v(0,0,0,0),S=function(t,e,i){var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=d[P]({},i||{}),this.dpr=i.devicePixelRatio||f.devicePixelRatio,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],o=this._layers={};if(this._layerConfig={},n){var s=t.width,l=t[ne];this._width=s,this._height=l;var u=new g(t,this,1);u.initContext(),o[0]=u,a.push(0)}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=h(this._width,this._height);t.appendChild(c)}this.pathToImage=this._createPathToImage(),this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};return S[H]={constructor:S,isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._singleCanvas?this._layers[0].dom:this._domRoot},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._paintList(e,t);for(var n=0;n<i[B];n++){var r=i[n],a=this._layers[r];!a.isBuildin&&a.refresh&&a.refresh()}return this.refreshHover(),this._progressiveLayers[B]&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape});i.__from=t,t.__hoverMir=i,i[X](e),this._hoverElements.push(i)}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=d[E](i,e);n>=0&&i[C](n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t[B];e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t[B]=0},refreshHover:function(){var t=this._hoverElements,e=t[B],i=this._hoverlayer;if(i&&i.clear(),e){m(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(1e5));var n={};i.ctx.save();for(var a=0;e>a;){var o=t[a],s=o.__from;s&&s.__zr?(a++,s.invisible||(o[r]=s[r],o.invTransform=s.invTransform,o.__clipPaths=s.__clipPaths,this._doPaintEl(o,i,!0,n))):(t[C](a,1),s.__hoverMir=null,e--)}i.ctx.restore()}},_startProgessive:function(){function t(){i===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,_(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var i=e._progressiveToken=+new Date;e._progress++,_(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,d.each(this._progressiveLayers,function(t){t[a]&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuildinLayer(n),this._doPaintList(t,e),this.eachBuildinLayer(o)},_doPaintList:function(t,e){function i(t){var e=o.dpr||1;o.save(),o.globalAlpha=1,o.shadowBlur=0,n[a]=!0,o.setTransform(1,0,0,1,0,0),o.drawImage(t.dom,0,0,h*e,f*e),o.restore()}for(var n,r,o,s,l,u,c=0,h=this._width,f=this._height,v=this._progress,m=0,g=t[B];g>m;m++){var y=t[m],_=this._singleCanvas?0:y[T],b=y.__frame;if(0>b&&l&&(i(l),l=null),r!==_&&(o&&o.restore(),s={},r=_,n=this.getLayer(r),n.isBuildin||p("ZLevel "+r+" has been used by unkown layer "+n.id),o=n.ctx,o.save(),n.__unusedCount=0,(n[a]||e)&&n.clear()),n[a]||e){if(b>=0){if(!l){if(l=this._progressiveLayers[Math.min(c++,x-1)],l.ctx.save(),l.renderScope={},l&&l.__progress>l.__maxProgress){m=l.__nextIdxNotProg-1;continue}u=l.__progress,l[a]||(v=u),l.__progress=v+1}b===v&&this._doPaintEl(y,l,!0,l.renderScope)}else this._doPaintEl(y,n,e,s);y[a]=!1}}l&&i(l),o&&o.restore(),this._furtherProgressive=!1,d.each(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0)},this)},_doPaintEl:function(t,e,i,n){var o=e.ctx,l=t[r];if(!(!e[a]&&!i||t.invisible||0===t.style[U]||l&&!l[0]&&!l[3]||t.culling&&s(t,this._width,this._height))){var h=t.__clipPaths;(n.prevClipLayer!==e||u(h,n.prevElClipPaths))&&(n.prevElClipPaths&&(n.prevClipLayer.ctx.restore(),n.prevClipLayer=n.prevElClipPaths=null,n.prevEl=null),h&&(o.save(),c(h,o),n.prevClipLayer=e,n.prevElClipPaths=h)),t.beforeBrush&&t.beforeBrush(o),t.brush(o,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(o)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new g("zr_"+t,this,this.dpr),e.isBuildin=!0,this._layerConfig[t]&&d.merge(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var n=this._layers,r=this._zlevelList,a=r[B],o=null,s=-1,l=this._domRoot;if(n[t])return void p("ZLevel "+t+" has been used already");if(!i(e))return void p("Layer of zlevel "+t+" is not valid");if(a>0&&t>r[0]){for(s=0;a-1>s&&!(r[s]<t&&r[s+1]>t);s++);o=n[r[s]]}if(r[C](s+1,0,t),o){var u=o.dom;u.nextSibling?l.insertBefore(e.dom,u.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom);n[t]=e},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r[B];n++)i=r[n],t.call(e,this._layers[i],i)},eachBuildinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[B];r++)n=a[r],i=this._layers[n],i.isBuildin&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[B];r++)n=a[r],i=this._layers[n],i.isBuildin||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,i=this._progressiveLayers,n={},r={};this.eachBuildinLayer(function(t,e){n[e]=t.elCount,t.elCount=0,t[a]=!1}),d.each(i,function(t,e){r[e]=t.elCount,t.elCount=0,t[a]=!1});for(var o,s,l=0,u=0,c=0,h=t[B];h>c;c++){var f=t[c],p=this._singleCanvas?0:f[T],v=e[p],m=f.progressive;if(v&&(v.elCount++,v[a]=v[a]||f[a]),m>=0){s!==m&&(s=m,u++);var y=f.__frame=u-1;if(!o){var _=Math.min(l,x-1);o=i[_],o||(o=i[_]=new g("progressive",this,this.dpr),o.initContext()),o.__maxProgress=0}o[a]=o[a]||f[a],o.elCount++,o.__maxProgress=Math.max(o.__maxProgress,y),o.__maxProgress>=o.__progress&&(v[a]=!0)}else f.__frame=-1,o&&(o.__nextIdxNotProg=c,l++,o=null)}o&&(l++,o.__nextIdxNotProg=c),this.eachBuildinLayer(function(t,e){n[e]!==t.elCount&&(t[a]=!0)}),i[B]=Math.min(l,x),d.each(i,function(t,e){r[e]!==t.elCount&&(f[a]=!0),t[a]&&(t.__progress=0)})},clear:function(){return this.eachBuildinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?d.merge(i[t],e,!0):i[t]=e;var n=this._layers[t];n&&d.merge(n,i[t],!0)}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i[C](d[E](i,t),1))},resize:function(t,e){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n[ne]=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!=t||e!=this._height){i.style.width=t+"px",i.style[ne]=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);d.each(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas)return this._layers[0].dom;var e=new g("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clearColor=t.backgroundColor,e.clear();for(var i=this.storage.getDisplayList(!0),n={},r=0;r<i[B];r++){var a=i[r];this._doPaintEl(a,e,!0,n)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var i=this._opts,n=["width",ne][t],r=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=i[n]&&"auto"!==i[n])return parseFloat(i[n]);var s=this.root,l=document.defaultView.getComputedStyle(s);return(s[r]||e(l[n])||e(s.style[n]))-(e(l[a])||0)-(e(l[o])||0)|0},_pathToImage:function(e,i,n,r,a){var o=document[y]("canvas"),s=o.getContext("2d");o.width=n*a,o[ne]=r*a,s.clearRect(0,0,n*a,r*a);var l={position:i[Y],rotation:i.rotation,scale:i.scale};i[Y]=[0,0,0],i.rotation=0,i.scale=[1,1],i&&i.brush(s);var u=t("./graphic/Image"),c=new u({id:e,style:{x:0,y:0,image:o}});return null!=l[Y]&&(c[Y]=i[Y]=l[Y]),null!=l.rotation&&(c.rotation=i.rotation=l.rotation),null!=l.scale&&(c.scale=i.scale=l.scale),c},_createPathToImage:function(){var t=this;return function(e,i,n,r){return t._pathToImage(e,i,n,r,t.dpr)}}},S}),e("zrender/mixin/Draggable",[ue],function(){function t(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}return t[H]={constructor:t,_dragStart:function(t){var e=t[ee];e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(e,"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(e,"drag",t.event);var o=this.findHover(i,n,e),s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(s,"dragleave",t.event),o&&o!==s&&this.dispatchToElement(o,"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(e,"dragend",t.event),this._dropTarget&&this.dispatchToElement(this._dropTarget,"drop",t.event),this._draggingTarget=null,this._dropTarget=null}},t}),e("echarts/data/helper/completeDimensions",[ue,le],function(t){function e(t,e,a,o){if(!e)return t;var s=i(e[0]),l=n[w](s)&&s[B]||1;a=a||[],o=o||"extra";for(var u=0;l>u;u++)if(!t[u]){var c=a[u]||o+(u-a[B]);t[u]=r(e,u)?{type:"ordinal",name:c}:c}return t}function i(t){return n[w](t)?t:n[z](t)?t.value:t}var n=t(le),r=e.guessOrdinal=function(t,e){for(var r=0,a=t[B];a>r;r++){var o=i(t[r]);if(!n[w](o))return!1;var o=o[e];if(null!=o&&isFinite(o))return!1;if(n.isString(o)&&"-"!==o)return!0}return!1};return e}),e("echarts/data/DataDiffer",[ue],function(){function t(t){return t}function e(e,i,n,r){this._old=e,this._new=i,this._oldKeyGetter=n||t,this._newKeyGetter=r||t}function i(t,e,i,n){for(var r=0;r<t[B];r++){var a=n(t[r],r),o=e[a];null==o?(i.push(a),e[a]=r):(o[B]||(e[a]=o=[o]),o.push(r))}}return e[H]={constructor:e,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,r=this._oldKeyGetter,a=this._newKeyGetter,o={},s={},l=[],u=[];for(i(e,o,l,r),i(n,s,u,a),t=0;t<e[B];t++){var c=l[t],h=s[c];if(null!=h){var f=h[B];f?(1===f&&(s[c]=null),h=h.unshift()):s[c]=null,this._update&&this._update(h,t)}else this._remove&&this._remove(t)}for(var t=0;t<u[B];t++){var c=u[t];if(s.hasOwnProperty(c)){var h=s[c];if(null==h)continue;if(h[B])for(var d=0,f=h[B];f>d;d++)this._add&&this._add(h[d]);else this._add&&this._add(h)}}}},e}),e("zrender/graphic/helper/roundRect",[ue],function(){return{buildPath:function(t,e){var i,r,a,o,s=e.x,l=e.y,u=e.width,c=e[ne],h=e.r;0>u&&(s+=u,u=-u),0>c&&(l+=c,c=-c),"number"==typeof h?i=r=a=o=h:h instanceof Array?1===h[B]?i=r=a=o=h[0]:2===h[B]?(i=a=h[0],r=o=h[1]):3===h[B]?(i=h[0],r=o=h[1],a=h[2]):(i=h[0],r=h[1],a=h[2],o=h[3]):i=r=a=o=0;var f;i+r>u&&(f=i+r,i*=u/f,r*=u/f),a+o>u&&(f=a+o,a*=u/f,o*=u/f),r+a>c&&(f=r+a,r*=c/f,a*=c/f),i+o>c&&(f=i+o,i*=c/f,o*=c/f),t[n](s+i,l),t.lineTo(s+u-r,l),0!==r&&t.quadraticCurveTo(s+u,l,s+u,l+r),t.lineTo(s+u,l+c-a),0!==a&&t.quadraticCurveTo(s+u,l+c,s+u-a,l+c),t.lineTo(s+o,l+c),0!==o&&t.quadraticCurveTo(s,l+c,s,l+c-o),t.lineTo(s,l+i),0!==i&&t.quadraticCurveTo(s,l,s+i,l)}}}),e("zrender/animation/requestAnimationFrame",[ue],function(){return typeof window!==i&&(window.requestAnimationFrame||window.msRequestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)}}),e("zrender/core/event",[ue,"../mixin/Eventful","./env"],function(t){function e(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function n(t,e,i,n){return i=i||{},n||!u[G]?r(t,e,i):u.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):r(t,e,i),i}function r(t,i,n){var r=e(t);n.zrX=i.clientX-r.left,n.zrY=i.clientY-r.top}function a(t,e,i){if(e=e||window.event,null!=e.zrX)return e;var r=e.type,a=r&&r[E]("touch")>=0;if(a){var o="touchend"!=r?e.targetTouches[0]:e.changedTouches[0];o&&n(t,o,e,i)}else n(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;return e}function o(t,e,i){c?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function s(t,e,i){c?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}var l=t("../mixin/Eventful"),u=t("./env"),c=typeof window!==i&&!!window.addEventListener,h=c?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};return{clientToLocal:n,normalizeEvent:a,addEventListener:o,removeEventListener:s,stop:h,Dispatcher:l}}),e("echarts/chart/bar/barItemStyle",[ue,"../../model/mixin/makeStyleMapper"],function(t){var e=t("../../model/mixin/makeStyleMapper")([["fill","color"],[o,"borderColor"],[s,"borderWidth"],[o,"barBorderColor"],[s,"barBorderWidth"],[U],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getBarItemStyle:function(t){var i=e.call(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(i.lineDash=n)}return i}}}),e("zrender/core/GestureMgr",[ue,"./event"],function(t){function e(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function i(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var n=t("./event"),r=function(){this._track=[]};r[H]={constructor:r,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track[B]=0,this},_doTrack:function(t,e,i){var r=t.touches;if(r){for(var a={points:[],touches:[],target:e,event:t},o=0,s=r[B];s>o;o++){var l=r[o],u=n.clientToLocal(i,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},_recognize:function(t){for(var e in a)if(a.hasOwnProperty(e)){var i=a[e](this._track,t);if(i)return i}}};var a={pinch:function(t,n){var r=t[B];if(r){var a=(t[r-1]||{}).points,o=(t[r-2]||{}).points||a;if(o&&o[B]>1&&a&&a[B]>1){var s=e(a)/e(o);!isFinite(s)&&(s=1),n.pinchScale=s;var l=i(a);return n.pinchX=l[0],n.pinchY=l[1],{type:"pinch",target:t[0][ee],event:n}}}}};return r}),e("zrender/Layer",[ue,"./core/util","./config","./graphic/Style","./graphic/Pattern"],function(t){function e(){return!1}function i(t,e,i,n){var r=document[y](e),a=i[te](),o=i[J](),s=r.style;return s[Y]="absolute",s.left=0,s.top=0,s.width=a+"px",s[ne]=o+"px",r.width=a*n,r[ne]=o*n,r.setAttribute("data-zr-dom-id",t),r}var n=t("./core/util"),r=t("./config"),a=t("./graphic/Style"),o=t("./graphic/Pattern"),s=function(t,a,o){var s;o=o||r.devicePixelRatio,typeof t===V?s=i(t,"canvas",a,o):n[z](t)&&(s=t,t=s.id),this.id=t,this.dom=s;var l=s.style;l&&(s.onselectstart=e,l["-webkit-user-select"]="none",l["user-select"]="none",l["-webkit-touch-callout"]="none",l["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",l.padding=0,l.margin=0,l["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=a,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=o};return s[H]={constructor:s,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=i("back-"+this.id,"canvas",this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r.width=t+"px",r[ne]=e+"px",n.width=t*i,n[ne]=e*i,a&&(a.width=t*i,a[ne]=e*i,1!=i&&this.ctxBack.scale(i,i))},clear:function(t){var e=this.dom,i=this.ctx,n=e.width,r=e[ne],s=this.clearColor,l=this.motionBlur&&!t,u=this.lastFrameAlpha,c=this.dpr;if(l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,n/c,r/c)),i.clearRect(0,0,n,r),s){var h;s[I]?(h=s.__canvasGradient||a.getGradient(i,s,{x:0,y:0,width:n,height:r}),s.__canvasGradient=h):s.image&&(h=o[H].getCanvasPattern.call(s,i)),i.save(),i.fillStyle=h||s,i.fillRect(0,0,n,r),i.restore()}if(l){var f=this.domBack;i.save(),i.globalAlpha=u,i.drawImage(f,0,0,n,r),i.restore()}}},s}),e("echarts/preprocessor/helper/compatStyle",[ue,le],function(t){function e(t){var e=t&&t.itemStyle;e&&i.each(n,function(n){var r=e.normal,a=e.emphasis;r&&r[n]&&(t[n]=t[n]||{},t[n].normal?i.merge(t[n].normal,r[n]):t[n].normal=r[n],r[n]=null),a&&a[n]&&(t[n]=t[n]||{},t[n].emphasis?i.merge(t[n].emphasis,a[n]):t[n].emphasis=a[n],a[n]=null)})}var i=t(le),n=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];return function(t){if(t){e(t),e(t.markPoint),e(t.markLine);var n=t.data;if(n){for(var r=0;r<n[B];r++)e(n[r]);var a=t.markPoint;if(a&&a.data)for(var o=a.data,r=0;r<o[B];r++)e(o[r]);var s=t.markLine;if(s&&s.data)for(var l=s.data,r=0;r<l[B];r++)i[w](l[r])?(e(l[r][0]),e(l[r][1])):e(l[r])}}}}),e("echarts/component/axis/AxisView",[ue,le,"../../util/graphic","./AxisBuilder","../../echarts"],function(t){function e(t,e){function i(t){var e=n.getAxis(t);return e.toGlobalCoord(e[v](0))}var n=t[j],r=e.axis,a={},o=r[Y],s=r.onZero?"onZero":o,l=r.dim,u=n.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u[ne]],h=e.get("offset")||0,f={x:{top:c[2]-h,bottom:c[3]+h},y:{left:c[0]-h,right:c[1]+h}};f.x.onZero=Math.max(Math.min(i("y"),f.x[$]),f.x.top),f.y.onZero=Math.max(Math.min(i("x"),f.y.right),f.y.left),a[Y]=["y"===l?f.y[s]:c[0],"x"===l?f.x[s]:c[3]],a.rotation=Math.PI/2*("x"===l?0:1);var d={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=d[o],r.onZero&&(a.labelOffset=f[l][o]-f[l].onZero),e[se]("axisTick").get("inside")&&(a.tickDirection=-a.tickDirection),e[se]("axisLabel").get("inside")&&(a.labelDirection=-a.labelDirection);var p=e[se]("axisLabel").get("rotate");return a.labelRotation="top"===s?-p:p,a.labelInterval=r.getLabelInterval(),a.z2=1,a}var i=t(le),n=t("../../util/graphic"),r=t("./AxisBuilder"),a=r.ifIgnoreOnTick,o=r.getInterval,s=["axisLine","axisLabel","axisTick","axisName"],l=["splitArea","splitLine"],u=t("../../echarts").extendComponentView({type:"axis",render:function(t){this.group.removeAll();var a=this._axisGroup;if(this._axisGroup=new n.Group,this.group.add(this._axisGroup),t.get("show")){var o=t.findGridModel(),u=e(o,t),c=new r(t,u);i.each(s,c.add,c),this._axisGroup.add(c.getGroup()),i.each(l,function(e){t.get(e+".show")&&this["_"+e](t,o,u.labelInterval)},this),n.groupTransition(a,this._axisGroup,t)}},_splitLine:function(t,e,r){var s=t.axis,l=t[se]("splitLine"),u=l[se]("lineStyle"),c=u.get("color"),h=o(l,r);c=i[w](c)?c:[c];for(var f=e[j].getRect(),d=s.isHorizontal(),p=0,v=s.getTicksCoords(),m=s.scale.getTicks(),g=[],y=[],_=u.getLineStyle(),x=0;x<v[B];x++)if(!a(s,x,h)){var b=s.toGlobalCoord(v[x]);d?(g[0]=b,g[1]=f.y,y[0]=b,y[1]=f.y+f[ne]):(g[0]=f.x,g[1]=b,y[0]=f.x+f.width,y[1]=b);var M=p++%c[B];this._axisGroup.add(new n.Line(n.subPixelOptimizeLine({anid:"line_"+m[x],shape:{x1:g[0],y1:g[1],x2:y[0],y2:y[1]},style:i[W]({stroke:c[M]},_),silent:!0})))}},_splitArea:function(t,e,r){var s=t.axis,l=t[se]("splitArea"),u=l[se]("areaStyle"),c=u.get("color"),h=e[j].getRect(),f=s.getTicksCoords(),d=s.scale.getTicks(),p=s.toGlobalCoord(f[0]),v=s.toGlobalCoord(f[0]),m=0,g=o(l,r),y=u.getAreaStyle();c=i[w](c)?c:[c];for(var _=1;_<f[B];_++)if(!a(s,_,g)){var x,b,M,T,S=s.toGlobalCoord(f[_]);s.isHorizontal()?(x=p,b=h.y,M=S-x,T=h[ne]):(x=h.x,b=v,M=h.width,T=S-b);var C=m++%c[B];this._axisGroup.add(new n.Rect({anid:"area_"+d[_],shape:{x:x,y:b,width:M,height:T},style:i[W]({fill:c[C]},y),silent:!0})),p=x+M,v=b+T}}});u[P]({type:"xAxis"}),u[P]({type:"yAxis"})}),e("echarts/chart/helper/Symbol",[ue,le,"../../util/symbol","../../util/graphic","../../util/number"],function(t){function e(t){return t=t instanceof Array?t.slice():[+t,+t],t[0]/=2,t[1]/=2,t}function i(t,e,i){o.Group.call(this),this.updateData(t,e,i)}function n(t,e){this.parent.drift(t,e)}var r=t(le),a=t("../../util/symbol"),o=t("../../util/graphic"),s=t("../../util/number"),l=i[H];l._createSymbol=function(t,i,r){this.removeAll();var s=i.hostModel,l=i[O](r,"color"),u=a.createSymbol(t,-1,-1,2,2,l);u.attr({z2:100,culling:!0,scale:[0,0]}),u.drift=n;var c=e(i[O](r,"symbolSize"));o.initProps(u,{scale:c},s,r),this._symbolType=t,this.add(u)},l.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},l.getSymbolPath=function(){return this.childAt(0)},l.getScale=function(){return this.childAt(0).scale},l.highlight=function(){this.childAt(0).trigger("emphasis")},l.downplay=function(){this.childAt(0).trigger("normal")},l.setZ=function(t,e){var i=this.childAt(0);i[T]=t,i.z=e},l.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},l.updateData=function(t,i,n){this[ie]=!1;var r=t[O](i,"symbol")||"circle",a=t.hostModel,s=e(t[O](i,"symbolSize"));if(r!==this._symbolType)this._createSymbol(r,t,i);else{var l=this.childAt(0);o.updateProps(l,{scale:s},a,i)}this._updateCommon(t,i,s,n),this._seriesModel=a};var c=["itemStyle","normal"],h=["itemStyle","emphasis"],v=["label","normal"],g=["label","emphasis"];return l._updateCommon=function(t,i,n,a){var l=this.childAt(0),y=t.hostModel,_=t[O](i,"color");"image"!==l.type&&l.useStyle({strokeNoScale:!0}),a=a||null;var x=a&&a.itemStyle,b=a&&a.hoverItemStyle,w=a&&a.symbolRotate,M=a&&a.symbolOffset,T=a&&a.labelModel,C=a&&a.hoverLabelModel,A=a&&a.hoverAnimation;if(!a||t.hasItemOption){var L=t[d](i);x=L[se](c).getItemStyle(["color"]),b=L[se](h).getItemStyle(),w=L[f]("symbolRotate"),M=L[f]("symbolOffset"),T=L[se](v),C=L[se](g),A=L[f]("hoverAnimation")}else b=r[P]({},b);var z=l.style;l.attr("rotation",(w||0)*Math.PI/180||0),M&&l.attr(Y,[s.parsePercent(M[0],n[0]),s.parsePercent(M[1],n[1])]),l.setColor(_),l[X](x);var I=t[O](i,U);null!=I&&(z[U]=I);for(var k,D,R=t[m].slice();R[B]&&(k=R.pop(),D=t.getDimensionInfo(k).type,D===p||"time"===D););null!=k&&T[f]("show")?(o.setText(z,T,_),z.text=r[u](y.getFormattedLabel(i,"normal"),t.get(k,i))):z.text="",null!=k&&C[f]("show")?(o.setText(b,C,_),b.text=r[u](y.getFormattedLabel(i,"emphasis"),t.get(k,i))):b.text="";var E=e(t[O](i,"symbolSize"));if(l.off("mouseover").off(S).off("emphasis").off("normal"),l.hoverStyle=b,o.setHoverStyle(l),A&&y.ifEnableAnimation()){var N=function(){var t=E[1]/E[0];this.animateTo({scale:[Math.max(1.1*E[0],E[0]+3),Math.max(1.1*E[1],E[1]+3*t)]},400,"elasticOut")},G=function(){this.animateTo({scale:E},400,"elasticOut")};l.on("mouseover",N).on(S,G).on("emphasis",N).on("normal",G)}},l.fadeOut=function(t){var e=this.childAt(0);this[ie]=!0,e.style.text="",o.updateProps(e,{scale:[0,0]},this._seriesModel,this[R],t)},r.inherits(i,o.Group),i}),e("echarts/chart/line/poly",[ue,"zrender/graphic/Path","zrender/core/vector"],function(t){function e(t){return isNaN(t[0])||isNaN(t[1])}function i(t,i,r,a,p,v,m,g,y,_,x){for(var b=0,w=r,M=0;a>M;M++){var T=i[w];if(w>=p||0>w)break;if(e(T)){if(x){w+=v;continue}break}if(w===r)t[v>0?n:"lineTo"](T[0],T[1]),c(f,T);else if(y>0){var S=w+v,C=i[S];if(x)for(;C&&e(i[S]);)S+=v,C=i[S];var A=.5,P=i[b],C=i[S];if(!C||e(C))c(d,T);else{e(C)&&!x&&(C=T),o.sub(h,C,P);var L,z;if("x"===_||"y"===_){var I="x"===_?0:1;L=Math.abs(T[I]-P[I]),z=Math.abs(T[I]-C[I])}else L=o.dist(T,P),z=o.dist(T,C);A=z/(z+L),u(d,T,h,-y*(1-A))}s(f,f,g),l(f,f,m),s(d,d,g),l(d,d,m),t.bezierCurveTo(f[0],f[1],d[0],d[1],T[0],T[1]),u(f,T,h,y*A)}else t.lineTo(T[0],T[1]);b=w,w+=v}return M}function r(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t[B];r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}var a=t("zrender/graphic/Path"),o=t("zrender/core/vector"),s=o.min,l=o.max,u=o.scaleAndAdd,c=o.copy,h=[],f=[],d=[];return{Polyline:a[P]({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},buildPath:function(t,n){var a=n.points,o=0,s=a[B],l=r(a,n.smoothConstraint);if(n.connectNulls){for(;s>0&&e(a[s-1]);s--);for(;s>o&&e(a[o]);o++);}for(;s>o;)o+=i(t,a,o,s,s,1,l.min,l.max,n.smooth,n.smoothMonotone,n.connectNulls)+1}}),Polygon:a[P]({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},buildPath:function(t,n){var a=n.points,o=n.stackedOnPoints,s=0,l=a[B],u=n.smoothMonotone,c=r(a,n.smoothConstraint),h=r(o,n.smoothConstraint);if(n.connectNulls){for(;l>0&&e(a[l-1]);l--);for(;l>s&&e(a[s]);s++);}for(;l>s;){var f=i(t,a,s,l,l,1,c.min,c.max,n.smooth,u,n.connectNulls);i(t,o,s+f-1,f,l,-1,h.min,h.max,n.stackedOnSmooth,u,n.connectNulls),s+=f+1,t.closePath()}}})}}),e("echarts/chart/helper/SymbolDraw",[ue,"../../util/graphic","./Symbol"],function(t){function e(t){this.group=new n.Group,this._symbolCtor=t||r}function i(t,e,i){var n=t.getItemLayout(e);return!(!n||isNaN(n[0])||isNaN(n[1])||i&&i(e)||"none"===t[O](e,"symbol"))}var n=t("../../util/graphic"),r=t("./Symbol"),a=e[H];return a.updateData=function(t,e){var r=this.group,a=t.hostModel,o=this._data,s=this._symbolCtor,l={itemStyle:a[se]("itemStyle.normal").getItemStyle(["color"]),hoverItemStyle:a[se]("itemStyle.emphasis").getItemStyle(),symbolRotate:a.get("symbolRotate"),symbolOffset:a.get("symbolOffset"),hoverAnimation:a.get("hoverAnimation"),labelModel:a[se]("label.normal"),hoverLabelModel:a[se]("label.emphasis")};t.diff(o).add(function(n){var a=t.getItemLayout(n);if(i(t,n,e)){var o=new s(t,n,l);o.attr(Y,a),t.setItemGraphicEl(n,o),r.add(o)}})[k](function(u,c){var h=o.getItemGraphicEl(c),f=t.getItemLayout(u);return i(t,u,e)?(h?(h.updateData(t,u,l),n.updateProps(h,{position:f},a)):(h=new s(t,u),h.attr(Y,f)),r.add(h),void t.setItemGraphicEl(u,h)):void r[L](h)})[L](function(t){var e=o.getItemGraphicEl(t);e&&e.fadeOut(function(){r[L](e)})}).execute(),this._data=t},a.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,i){var n=t.getItemLayout(i);e.attr(Y,n)})},a[L]=function(t){var e=this.group,i=this._data;i&&(t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e[L](t)})}):e.removeAll())},e}),e("echarts/util/symbol",[ue,"./graphic","zrender/core/BoundingRect"],function(t){var e=t("./graphic"),i=t("zrender/core/BoundingRect"),r=e.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,r=e.cy,a=e.width/2,o=e[ne]/2;t[n](i,r-o),t.lineTo(i+a,r+o),t.lineTo(i-a,r+o),t.closePath()}}),a=e.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,r=e.cy,a=e.width/2,o=e[ne]/2;t[n](i,r-o),t.lineTo(i+a,r),t.lineTo(i,r+o),t.lineTo(i-a,r),t.closePath()}}),s=e.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e[ne]),o=r/2,s=o*o/(a-o),l=n-a+o+s,u=Math.asin(s/o),c=Math.cos(u)*o,h=Math.sin(u),f=Math.cos(u);t.arc(i,l,o,Math.PI-u,2*Math.PI+u);var d=.6*o,p=.7*o;t.bezierCurveTo(i+c-h*d,l+s+f*d,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-c+h*d,l+s+f*d,i-c,l+s),t.closePath()}}),l=e.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e[ne],r=e.width,a=e.x,o=e.y,s=r/3*2;t[n](a,o),t.lineTo(a+s,o+i),t.lineTo(a,o+i/4*3),t.lineTo(a-s,o+i),t.lineTo(a,o),t.closePath()}}),u={line:e.Line,rect:e.Rect,roundRect:e.Rect,square:e.Rect,circle:e.Circle,diamond:a,pin:s,arrow:l,triangle:r},c={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[ne]=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[ne]=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r[ne]=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[ne]=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[ne]=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[ne]=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[ne]=n}},h={};for(var f in u)u.hasOwnProperty(f)&&(h[f]=new u[f]);var d=e.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign=Q,t.textVerticalAlign=K)},buildPath:function(t,e,i){var n=e.symbolType,r=h[n];"none"!==e.symbolType&&(r||(n="rect",r=h[n]),c[n](e.x,e.y,e.width,e[ne],r.shape),r.buildPath(t,r.shape,i))}}),p=function(t){if("image"!==this.type){var e=this.style,i=this.shape;i&&"line"===i.symbolType?e[o]=t:this.__isEmptyBrush?(e[o]=t,e.fill="#fff"):(e.fill&&(e.fill=t),e[o]&&(e[o]=t)),this.dirty(!1)}},v={createSymbol:function(t,n,r,a,o,s){var l=0===t[E]("empty");l&&(t=t.substr(5,1)[q]()+t.substr(6));var u;return u=0===t[E]("image://")?new e.Image({style:{image:t.slice(8),x:n,y:r,width:a,height:o}}):0===t[E]("path://")?e.makePath(t.slice(7),{},new i(n,r,a,o)):new d({shape:{symbolType:t,x:n,y:r,width:a,height:o}}),u.__isEmptyBrush=l,u.setColor=p,u.setColor(s),u}};return v}),e("echarts/chart/line/lineAnimationDiff",[ue],function(){function t(t){return t>=0?1:-1}function e(e,i,n){for(var r,a=e.getBaseAxis(),o=e.getOtherAxis(a),s=a.onZero?0:o.scale[_]()[0],l=o.dim,u="x"===l||"radius"===l?1:0,c=i.stackedOn,h=i.get(l,n);c&&t(c.get(l,n))===t(h);){r=c;break}var f=[];return f[u]=i.get(a.dim,n),f[1-u]=r?r.get(l,n,!0):s,e[g](f)}function i(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})})[k](function(t,e){i.push({cmd:"=",idx:e,idx1:t})})[L](function(t){i.push({cmd:"-",idx:t})}).execute(),i}return function(t,n,r,a,o,s){for(var l=i(t,n),u=[],c=[],h=[],f=[],d=[],p=[],v=[],y=s[m],_=0;_<l[B];_++){var x=l[_],b=!0;switch(x.cmd){case"=":var w=t.getItemLayout(x.idx),M=n.getItemLayout(x.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=M.slice()),u.push(w),c.push(M),h.push(r[x.idx]),f.push(a[x.idx1]),v.push(n.getRawIndex(x.idx1));
break;case"+":var T=x.idx;u.push(o[g]([n.get(y[0],T,!0),n.get(y[1],T,!0)])),c.push(n.getItemLayout(T).slice()),h.push(e(o,n,T)),f.push(a[T]),v.push(n.getRawIndex(T));break;case"-":var T=x.idx,S=t.getRawIndex(T);S!==T?(u.push(t.getItemLayout(T)),c.push(s[g]([t.get(y[0],T,!0),t.get(y[1],T,!0)])),h.push(r[T]),f.push(e(s,t,T)),v.push(S)):b=!1}b&&(d.push(x),p.push(p[B]))}p.sort(function(t,e){return v[t]-v[e]});for(var C=[],A=[],P=[],L=[],z=[],_=0;_<p[B];_++){var T=p[_];C[_]=u[T],A[_]=c[T],P[_]=h[T],L[_]=f[T],z[_]=d[T]}return{current:C,next:A,stackedOnCurrent:P,stackedOnNext:L,status:z}}}),e("echarts/component/helper/listComponent",[ue,"../../util/layout","../../util/format","../../util/graphic"],function(t){function e(t,e,n){i.positionElement(t,e.getBoxLayoutParams(),{width:n[te](),height:n[J]()},e.get("padding"))}var i=t("../../util/layout"),n=t("../../util/format"),r=t("../../util/graphic");return{layout:function(t,n,r){var a=i.getLayoutRect(n.getBoxLayoutParams(),{width:r[te](),height:r[J]()},n.get("padding"));i.box(n.get("orient"),t,n.get("itemGap"),a.width,a[ne]),e(t,n,r)},addBackground:function(t,e){var i=n.normalizeCssArray(e.get("padding")),a=t[re](),o=e.getItemStyle(["color",U]);o.fill=e.get("backgroundColor");var s=new r.Rect({shape:{x:a.x-i[3],y:a.y-i[0],width:a.width+i[1]+i[3],height:a[ne]+i[0]+i[2]},style:o,silent:!0,z2:-1});r.subPixelOptimizeRect(s),t.add(s)}}}),e("echarts/component/tooltip/TooltipContent",[ue,le,"zrender/tool/color","zrender/core/event","../../util/format","zrender/core/env"],function(t){function e(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return a.map(f,function(t){return t+"transition:"+i}).join(";")}function i(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t[ae]()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),u(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function n(t){t=t;var n=[],r=t.get("transitionDuration"),a=t.get("backgroundColor"),s=t[se](oe),f=t.get("padding");return r&&n.push(e(r)),a&&(h[G]?n.push("background-Color:"+a):(n.push("background-Color:#"+o.toHex(a)),n.push("filter:alpha(opacity=70)"))),u(["width","color","radius"],function(e){var i="border-"+e,r=c(i),a=t.get(r);null!=a&&n.push(i+":"+a+("color"===e?"":"px"))}),n.push(i(s)),null!=f&&n.push("padding:"+l.normalizeCssArray(f).join("px ")+"px"),n.join(";")+";"}function r(t,e){var i=document[y]("div"),n=e.getZr();this.el=i,this._x=e[te]()/2,this._y=e[J]()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r.enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r.enterable){var i=n.handler;s.normalizeEvent(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r.enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}var a=t(le),o=t("zrender/tool/color"),s=t("zrender/core/event"),l=t("../../util/format"),u=a.each,c=l.toCamelCase,h=t("zrender/core/env"),f=["","-webkit-","-moz-","-o-"],d="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";return r[H]={constructor:r,enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i[Y]&&"absolute"!==e[Y]&&(i[Y]="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=d+n(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){var e=this.el;e.innerHTML=t,e.style.display=t?"block":"none"},moveTo:function(t,e){var i=this.el.style;i.left=t+"px",i.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this.enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(a.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show}},r}),e("echarts/component/axis/AxisBuilder",[ue,le,"../../util/format","../../util/graphic","../../model/Model","../../util/number","zrender/core/vector"],function(t){function e(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function i(t,e,i){var n,r,a=g(e-t.rotation);return y(a)?(r=i>0?"top":$,n=Q):y(a-T)?(r=i>0?$:"top",n=Q):(r=K,n=a>0&&T>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,verticalAlign:r}}function n(t,e,i,n){var r,a,o=g(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return y(o-T/2)?(a=l?$:"top",r=Q):y(o-1.5*T)?(a=l?"top":$,r=Q):(a=K,r=1.5*T>o&&o>T/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,verticalAlign:a}}function a(t){var e=t.get("tooltip");return t.get(ie)||!(t.get("triggerEvent")||e&&e.show)}var o=t(le),s=t("../../util/format"),f=t("../../util/graphic"),d=t("../../model/Model"),m=t("../../util/number"),g=m.remRadian,y=m.isRadianAroundZero,x=t("zrender/core/vector"),w=x[l],M=o[u],T=Math.PI,S=function(t,e){this.opt=e,this.axisModel=t,o[W](e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new f.Group;var i=new f.Group({position:e[Y].slice(),rotation:e.rotation});i.updateTransform(),this._transform=i[r],this._dumbGroup=i};S[H]={constructor:S,hasBuilder:function(t){return!!C[t]},add:function(t){C[t].call(this)},getGroup:function(){return this.group}};var C={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis[_](),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(w(r,r,n),w(a,a,n)),this.group.add(new f.Line(f.subPixelOptimizeLine({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:o[P]({lineCap:"round"},e[se]("axisLine.lineStyle").getLineStyle()),strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})))}},axisTick:function(){var t=this.axisModel;if(t.get("axisTick.show"))for(var e=t.axis,i=t[se]("axisTick"),n=this.opt,r=i[se]("lineStyle"),a=i.get(B),s=L(i,n.labelInterval),l=e.getTicksCoords(i.get("alignWithLabel")),u=e.scale.getTicks(),c=[],h=[],d=this._transform,p=0;p<l[B];p++)if(!A(e,p,s)){var v=l[p];c[0]=v,c[1]=0,h[0]=v,h[1]=n.tickDirection*a,d&&(w(c,c,d),w(h,h,d)),this.group.add(new f.Line(f.subPixelOptimizeLine({anid:"tick_"+u[p],shape:{x1:c[0],y1:c[1],x2:h[0],y2:h[1]},style:o[W](r.getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")}),z2:2,silent:!0})))}},axisLabel:function(){function t(t,e){var i=t&&t[re]().clone(),n=e&&e[re]().clone();return i&&n?(i[l](t.getLocalTransform()),n[l](e.getLocalTransform()),i.intersect(n)):void 0}var n=this.opt,r=this.axisModel,s=M(n.axisLabelShow,r.get("axisLabel.show"));if(s){var u=r.axis,p=r[se]("axisLabel"),m=p[se](oe),g=p.get("margin"),y=u.scale.getTicks(),_=r.getFormattedLabels(),x=M(n.labelRotation,p.get("rotate"))||0;x=x*T/180;var w=i(n,x,n.labelDirection),S=r.get("data"),C=[],P=a(r),L=r.get("triggerEvent");if(o.each(y,function(t,i){if(!A(u,i,n.labelInterval)){var a=m;S&&S[t]&&S[t][oe]&&(a=new d(S[t][oe],m,r[h]));var o=a.getTextColor()||r.get("axisLine.lineStyle.color"),s=u[v](t),l=[s,n.labelOffset+n.labelDirection*g],c=u.scale.getLabel(t),p=new f.Text({anid:"label_"+t,style:{text:_[i],textAlign:a.get("align",!0)||w.textAlign,textVerticalAlign:a.get("baseline",!0)||w.verticalAlign,textFont:a[ae](),fill:typeof o===b?o(c):o},position:l,rotation:w.rotation,silent:P,z2:10});L&&(p.eventData=e(r),p.eventData.targetType="axisLabel",p.eventData.value=c),this._dumbGroup.add(p),p.updateTransform(),C.push(p),this.group.add(p),p.decomposeTransform()}},this),u.type!==c){if(r.getMin?r.getMin():r.get("min")){var z=C[0],I=C[1];t(z,I)&&(z[N]=!0)}if(r.getMax?r.getMax():r.get("max")){var k=C[C[B]-1],D=C[C[B]-2];t(D,k)&&(k[N]=!0)}}}},axisName:function(){var t=this.opt,r=this.axisModel,l=M(t.axisName,r.get("name"));if(l){var u,c=r.get("nameLocation"),h=t.nameDirection,d=r[se]("nameTextStyle"),p=r.get("nameGap")||0,v=this.axisModel.axis[_](),m=v[0]>v[1]?-1:1,g=["start"===c?v[0]-m*p:"end"===c?v[1]+m*p:(v[0]+v[1])/2,c===K?t.labelOffset+h*p:0],y=r.get("nameRotate");null!=y&&(y=y*T/180);var x;c===K?u=i(t,null!=y?y:t.rotation,h):(u=n(t,c,y||0,v),x=t.axisNameAvailableWidth,null!=x&&(x=Math.abs(x/Math.sin(u.rotation)),!isFinite(x)&&(x=null)));var b=d[ae](),w=r.get("nameTruncate",!0)||{},S=w.ellipsis,C=M(w.maxWidth,x),A=null!=S&&null!=C?s.truncateText(l,C,b,S,{minChar:2,placeholder:w.placeholder}):l,L=r.get("tooltip",!0),z=r.mainType,I={componentType:z,name:l,$vars:["name"]};I[z+"Index"]=r.componentIndex;var k=new f.Text({anid:"name",__fullText:l,__truncatedText:A,style:{text:A,textFont:b,fill:d.getTextColor()||r.get("axisLine.lineStyle.color"),textAlign:u.textAlign,textVerticalAlign:u.verticalAlign},position:g,rotation:u.rotation,silent:a(r),z2:1,tooltip:L&&L.show?o[P]({content:l,formatter:function(){return l},formatterParams:I},L):null});r.get("triggerEvent")&&(k.eventData=e(r),k.eventData.targetType="axisName",k.eventData.name=l),this._dumbGroup.add(k),k.updateTransform(),this.group.add(k),k.decomposeTransform()}}},A=S.ifIgnoreOnTick=function(t,e,i){var n,r=t.scale;return r.type===p&&(typeof i===b?(n=r.getTicks()[e],!i(n,r.getLabel(n))):e%(i+1))},L=S.getInterval=function(t,e){var i=t.get("interval");return(null==i||"auto"==i)&&(i=e),i};return S}),e("zrender",["zrender/zrender"],function(t){return t}),e("echarts",["echarts/echarts"],function(t){return t});var ce=t("echarts");return ce.graphic=t("echarts/util/graphic"),ce.number=t("echarts/util/number"),ce.format=t("echarts/util/format"),t("echarts/chart/bar"),t("echarts/chart/line"),t("echarts/component/grid"),t("echarts/component/title"),t("echarts/component/legend"),t("echarts/component/tooltip"),t("zrender/vml/vml"),ce});