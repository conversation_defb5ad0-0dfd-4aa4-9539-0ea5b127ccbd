/**
 * Bootstrap Table Chinese translation
 * Author: <PERSON><PERSON><PERSON><<EMAIL>>
 */
(function ($) {
    'use strict';
    //获取cookie值  
    var getCookieValue=function (name){  
      var name = escape(name);  
      //读cookie属性，这将返回文档的所有cookie  
      var allcookies = document.cookie;      
      //查找名为name的cookie的开始位置  
       name += "=";  
      var pos = allcookies.indexOf(name);    
      //如果找到了具有该名字的cookie，那么提取并使用它的值  
      if (pos != -1){                       //如果pos值为-1则说明搜索"version="失败  
        var start = pos + name.length;         //cookie值开始的位置  
        var end = allcookies.indexOf(";",start);    //从cookie值开始的位置起搜索第一个";"的位置,即cookie值结尾的位置  
        if (end == -1) end = allcookies.length;    //如果end值为-1说明cookie列表里只有一个cookie  
        var value = allcookies.substring(start,end); //提取cookie的值  
        return unescape(value);              //对它解码     
       }else{
      	 return "";                //搜索失败，返回空字符串 
       }   
    };
    var mes1 = '   Per page   ';
    var mes2 = '   Record';
    var mes3 = 'Total  ';
    $.fn.bootstrapTable.locales['zh-CN'] = {
        formatLoadingMessage: function () {
            return '<div style="margin-top: 20px">loading……</div>';
        },
        formatRecordsPerPage: function (pageNumber) {
            return mes1 + pageNumber + mes2;
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            if (!totalRows) {
                totalRows = 0;
            }
            return mes3 + totalRows + mes2;
        },
        formatSearch: function () {
            return '搜索';
        },
        formatNoMatches: function () {
           // return '<div class="not-font"></div>';
           return '<div style="padding-left:10px;line-height: 42px;">No matching record found</div>';
        },
        formatPaginationSwitch: function () {
            return '隐藏/显示分页';
        },
        formatRefresh: function () {
            return '刷新';
        },
        formatToggle: function () {
            return '切换';
        },
        formatColumns: function () {
            return '列';
        },
        formatExport: function () {
            return '导出数据';
        },
        formatClearFilters: function () {
            return '清空过滤';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['zh-CN']);

})(jQuery);