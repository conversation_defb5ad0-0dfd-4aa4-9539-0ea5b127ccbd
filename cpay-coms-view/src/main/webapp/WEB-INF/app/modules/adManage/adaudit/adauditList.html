<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
            	<div class="input-append input-group"  ng-click="openOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="Organization">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
            	<input type="text" class="top-inp" placeholder="Name" ng-model="condition.name">
               <div class="input-append">
                	<select ng-options="m.type as m.name for m in adTypeList" ng-model="condition.type" style="width:150px;">
                    <option value="">Ad Type</option>
                	</select>
                </div>
                <div class="input-append">
                	<select ng-options="m.type as m.name for m in adFileTypeList" ng-model="condition.adFileType" style="width:150px;">
                    <option value="">Ad File Type</option>
                	</select>
                </div> 
                <button type="button" class="btn-reset" ng-click="reset()">Reset</button>
                <button type="submit" ng-click="search()"><i class="icon-search" has-permission="ad_audit_search"></i>Query</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <table id="table"></table>
        </div>
    </div>
</div>