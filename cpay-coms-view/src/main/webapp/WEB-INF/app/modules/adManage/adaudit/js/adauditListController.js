'use strict';
define([], function () {
    return function ($scope, adauditService,organizationService,BaseApi,toolsService,$state,dialogService, gridService,$http) {
//    		$scope.language = data.adAuditListController;
//    		$scope.html = data.html;
	    	$scope.$parent.firstTitle = "Advertising Management";
	      	$scope.$parent.secondTitle = "Advertising Audit";
	    	$scope.$parent.thirdTitle = "";
	        BaseApi.get("/dict/types/ad_type,ad_file_type,ad_audit",{}, function (data) {
	    	   $scope.adTypeList = data.data1;
	    	   $scope.adFileTypeList = data.data2;
	    	   $scope.adAudit = data.data3;
	    	   
	          	gridService.initTable({
	                url: "/ad/audit",
	                scope: $scope,
	                operator:false,
	                operate: function (value, row, index) {
	                	var el = '';
	                	el = el + '<a  href="javascript:;" has-permission="admange_audit_detail" ng-click="detail(' + index + ')"  title=Audit>Audit</a>';
	                	/*el = el + '<a  href="javascript:;" ng-click="auditpass(' + index + ')"  title="审核通过"> 通过</a>';
	                	el = el + '<a  href="javascript:;" ng-click="auditreject(' + index + ')"  title="审核不通过">不通过</a>';
	                	if(row.adPath){
	                		el = el + '<a  href="javascript:;" ng-click="downloadRow(' + index + ')"  title="下载广告文件"> 下载</a>';
	                	}*/
	                	return el;
	                },
	                uniqueId: 'id',
	                columns: [{
	                    field: 'state',
	                    checkbox: 'true'
	                }, {
	                    field: 'name',
	                    title: "Name"
	                },{
	                    field: 'insName',
	                    title: "Organization",
	                },{
	                    field: 'termGroupName',
	                    title: "Group Name",
	                    formatter: function (value) {
	                    	if(!value){
	                    		return 'No';
	                    	}
	                    	return value;
	                    }	
	                },
	                {
	                    field: 'type',
	                    title: "Ad Type",
	                    formatter: function (value) {
	                    	var type='';
	                    	$.each(data.data1,function(n,obj) {  
	                           if(obj.type==value) {
	                        	   type=obj.name;
	                           } 
	                          });
		                     return type;
	                    }
	                },
	                {
	                    field: 'adFileTypeName',
	                    title: "Ad File Type"
	                },
	                {
	                    field: 'startTime',
	                    title: "Effective Start Time",
	                    formatter: function (value) {
	                    	return toolsService.getDayDate(value);
	                    }
	                }, {
	                    field: 'endTime',
	                    title: "Effective End Time",
	                    formatter: function (value) {
	                    	return toolsService.getDayDate(value);
	                    }
	                },
	                {
	                    field: 'adAudit',
	                    title: "Audit Status",
	                    formatter: function (value) {
	                    	var type='';
	                         if(value=='1'){
	                        	 $.each(data.data2,function(n,obj) {  
	  	                           if(obj.type==value) {
	  	                        	   type=obj.name;
	  	                           } 
	  	                          });
	                        	 return '<font style="color:green;" >'+type+'</font>';
	                         }else if(value=='2'){
	                        	 $.each(data.data2,function(n,obj) {  
		  	                           if(obj.type==value) {
		  	                        	   type=obj.name;
		  	                           } 
		  	                          });
	                        	 return '<font style="color:red;">'+type+'</font>';;
	                         }else{
	                        	 $.each(data.data2,function(n,obj) {  
		  	                           if(obj.type=='0') {
		  	                        	   type=obj.name;
		  	                        	 return type
		  	                           } 
		  	                          });
	                         }
	                         return type;
	                    }
	                }]
	            });
	        });
	        
	        
	        $scope.deleteRows = function () {
	            var rows = gridService.getSelectedRow();
	            if (rows.length == 0) {
	                dialogService.alertInfo("info", "Please select a record!");
	                return;
	            }
	            dialogService.openConfirm("Are you sure you want to delete the selected record?", function () {
	                var ids = "";
	                for (var index in rows) {
	                    ids = ids + rows[index].id + ",";
	                }
	                var param = ids.substr(0, ids.length - 1);
	                adauditService.deleteAd(param, function (data) {
	                    dialogService.closeConfirm();
	                    dialogService.alertInfo("success", "Successfully Deleted!");
	                    gridService.refresh();
	                });
	            });
	        };
	        $scope.openOrganization = function () {
	            organizationService.openOrganization(function (value) {
	                if (!$scope.condition) {
	                    $scope.condition = {};
	                }
	                $scope.condition.insId = value.id;
	                $scope.condition.insName = value.name;
	            });
	        };
	        $scope.deleteRow = function (index) {
	            var row = gridService.getRow(index);
	            if(row.adAudit=='1'){
	           	 dialogService.alertInfo("error", "The review has passed and cannot be deleted!");
	           	 return;
	           }
	            dialogService.openConfirm("Are you sure you want to delete the record? “" + row.id + "”？", function (confirm) {
	                adauditService.deleteAd(row.id, function (data) {
	                    dialogService.closeConfirm();
	                    dialogService.alertInfo("success", "Successfully Deleted!");
	                    gridService.refresh();
	                });
	            });
	        };
	        $scope.downloadRow = function (index) {
	            var row = gridService.getRow(index);
	            var domain = window.location.href.split("#")[0];
	            var baseUrl=domain.substr(0,domain.length-1);
	            var downloadUrl=baseUrl+'/ad/fileDownload?filename='+row.adPath.substring(row.adPath.lastIndexOf("/")+1,row.adPath.length);
	            window.open(downloadUrl,'_blank');
	        };
	        $scope.auditpass=function (index) {
	        	var row = gridService.getRow(index);
	            adauditService.audit(row.id,1 ,function (data) {
	                dialogService.alertInfo("success", "操作成功！");
	                gridService.refresh();
	            });
	       };
	       $scope.auditreject=function (index) {
	       	var row = gridService.getRow(index);
	           adauditService.audit(row.id,2 ,function (data) {
	               dialogService.alertInfo("success", "操作成功！");
	               gridService.refresh();
	           });
	      };
	        $scope.search = function () {
	            gridService.search($scope.condition);
	        };
	        $scope.reset = function () {
	        	$scope.condition = {};
	            gridService.search($scope.condition);
	        };
	        $scope.detail = function (index) {
	            var row = gridService.getRow(index);
	            //adauditService.setAdData(row);
	            $state.go('home.adaudit.view', {id: row.id});
	        };
	        
	         gridService.setPlaceholder();
    };
});