'use strict';
define([], function () {
    return function ($scope,BaseApi,toolsService, adService, $state, $stateParams,baseUrl,$http) {
	    	$scope.$parent.secondTitle = "Ad Management";
	        $scope.$parent.firstTitle = "Ad Management";
    		$scope.$parent.thirdTitle = "Ad detail";
	    	adService.getAdList({'id':$stateParams.id}, function (data) {
	             $scope.ad = data;
	             if (!$scope.ad.id) {
	                 $state.go("home.ad.list");
	                 return;
	             }
	             if($scope.ad.adFileType == '2'){
	            	 $('#h5Div').css("display", "block");
	            	 $("#h5Iframe").attr("src", $scope.ad.h5Iframe); 
	             }else{
	            	 $('#h5Div').css("display", "none");
	             }
	             $scope.ad.startTime=toolsService.getDayDate($scope.ad.startTime);
	             $scope.ad.endTime=toolsService.getDayDate($scope.ad.endTime);
	             BaseApi.get("/dict/types/ad_type,ad_file_type,device_type",{}, function (data) {
	              	//$scope.adTypeList = data.data1;
	              	//$scope.adFileTypeList = data.data2;
	              	//$scope.deviceTypeList = data.data3;
	              	$.each(data.data1,function(n,obj) {  
	                    if(obj.type==$scope.ad.type) {
	                   	 $scope.ad.type=obj.name;
	                   	 return;
	                    } 
	              	});
	              	$.each(data.data2,function(n,obj) {  
	                    if(obj.type==$scope.ad.adFileType) {
	                   	 $scope.ad.adFileType=obj.name;
	                   	 return;
	                    } 
	              	});
	              	$.each(data.data3,function(n,obj) {  
	                    if(obj.type==$scope.ad.deviceType) {
	                   	 $scope.ad.deviceType=obj.name;
	                   	 return;
	                    } 
	              	});
	            });
	             //回填附件名称
	             if($scope.ad.fileName){
	             	 $scope.file={
	                  		name:$scope.ad.fileName
	                  };
	             }
	             //广告文件类型控制显示
	             if($scope.ad.adFileType=='4'){
	           	   $('#relateUrl').css("display","");
	           	   $('#relateFile').css("display","none");
	              }else if($scope.ad.adFileType=='1'){
	            	  $('#relateFile').css("display","none");
	            	  $('#relateUrl').css("display","none");
	              }else{
	            	  $('#relateUrl').css("display","none");
	            	  $('#relateFile').css("display","");
	              }
	             //广告类型控制显示
	             if($scope.ad.type=='2'){
	           	   $('#relateApp').css("display","");
	              }else{
	           	   $('#relateApp').css("display","none");
	              }
	           //显示图片
	             if($scope.ad.adPicPath){
	             	$scope.uploadImg=$scope.ad.adPicPath;
	             }
	         });
	         
	        $scope.disabled = true;
        //$('#datetimepicker1').datetimepicker('remove');
        //$('#datetimepicker2').datetimepicker('remove');
    };
});