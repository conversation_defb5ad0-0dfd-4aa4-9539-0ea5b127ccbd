'use strict';
define([], function () {
    return function ($scope, adService,BaseApi,organizationService,toolsService,$state,dialogService, gridService,$http) {
    		$scope.$parent.secondTitle = "Advertising Management";
            $scope.$parent.firstTitle = "Advertising Management";
    		$scope.$parent.thirdTitle = "";
	        $scope.condition = {};
	        BaseApi.get("/dict/types/ad_type,ad_file_type,ad_audit",{}, function (data) {
	          	$scope.adTypeList = data.data1;
	          	$scope.adFileTypeList = data.data2;
	          	$scope.adAuditList = data.data3;
	          	gridService.initTable({
	                url: "/ad",
	                scope: $scope,
	                //detail:'admanage_view',
	                operator:false,
	                operate: function (value, row, index) {
	                	var el = '<a  href="javascript:;" ng-click="detail(' + index + ')"   title=Detail>Detail</a>';
	                	if (row.adAudit==1) {//审核通过停用状态（isDisabled=1）下可以修改
	                		if(row.isDisabled == 1){
	                			/*el = el + '<a  href="javascript:;" ng-click="detail(' + index + ')"   title="详情"> 详情</a>';*/
	                    		el = el + '<a  href="javascript:;" has-permission="admanage_edit" ng-click="updateRow(' + index + ')"  title=Modify>Modify</a>';
	                    		el = el + '<a  href="javascript:;" has-permission="admanage_delete" ng-click="deleteRow(' + index + ')"  title=Delete>Delete</a>';
	                    	}
	                	}else{//未审核或者审核不通过都可以修改
	                		el = el + '<a  href="javascript:;" has-permission="admanage_edit" ng-click="updateRow(' + index + ')"  title=Modify> Modify</a>';
                    		el = el + '<a  href="javascript:;" has-permission="admanage_delete" ng-click="deleteRow(' + index + ')"  title=Delete>Delete</a>';
	                	}               	
	                	return el;
	                },
	                uniqueId: 'id',
	                columns: [{
	                    field: 'state',
	                    checkbox: 'true'
	                },{
	                    field: 'name',
	                    title: "Name"
	                }, {
	                    field: 'insName',
	                    title: "Organization"
	                }, {
	                    field: 'termGroupName',
	                    title: "Group Name",
	                    formatter: function (value) {
	                    	if(!value){
	                    		return 'No';
	                    	}
	                    	return value;
	                    }
	                },
	                {
	                    field: 'type',
	                    title: "Ad Type",
	                    formatter: function (value) {
	                    	var type='';
	                    	$.each(data.data1,function(n,obj) {  
	                           if(obj.type==value) {
	                        	   type=obj.name;
	                           } 
	                          });
		                     return type;
	                    }
	                }, 
	                {
	                    field: 'adFileTypeName',
	                    title: "Ad File Name"
	                },
	                {
	                    field: 'startTime',
	                    title: "Effective Start Time",
	                    formatter: function (value) {
	                    	return toolsService.getDayDate(value);
	                    }
	                }, {
	                    field: 'endTime',
	                    title: "Effective End Time",
	                    formatter: function (value) {
	                    	return toolsService.getDayDate(value);
	                    }
	                }, 
	                {
	                    field: 'adAudit',
	                    title: "Audit Status",
	                    formatter: function (value) {
                    		 var type='';
	                         if(value=='1'){
	                        	 $.each(data.data3,function(n,obj) {  
	  	                           if(obj.type==value) {
	  	                        	   type=obj.name;
	  	                           } 
	  	                          });
	                        	 return '<font style="color:green;" >'+type+'</font>';
	                         }else if(value=='2'){
	                        	 $.each(data.data3,function(n,obj) {  
		  	                           if(obj.type==value) {
		  	                        	   type=obj.name;
		  	                           } 
		  	                          });
	                        	 return '<font style="color:red;">'+type+'</font>';;
	                         }else{
	                        	 $.each(data.data3,function(n,obj) {  
		  	                           if(obj.type=='0') {
		  	                        	   type=obj.name;
		  	                        	 return type
		  	                           } 
		  	                          });
	                         }
	                         return type;
	                    }
	                }, {
	                    field: 'isDisabled',
	                    title: "Enable/Disable",
	                    align: 'center',
	                    formatter: function (value, row, index) {
		                    if(row.adAudit == '1'){
	                    		if(value == '1'){
		                    		return '<a  href="javascript:;" has-permission="ad_manage_stopAd" ng-click="startAd(' + index + ')"  title="Enable"> Enable</a>';;
		                    	}else{
		                    		return '<a  href="javascript:;" has-permission="ad_manage_stopAd" ng-click="stopAd(' + index + ')"  title="Disable"> Disable</a>';;
		                    	} 
		                    }else{
		                    	return '';
		                    }
	                    }
	                }/*, {
	                    field: 'termTypeName',
	                    title: '终端型号'
	                }*/]
	            });
	        });
	        
	        
	        $scope.deleteRows = function () {
	            var rows = gridService.getSelectedRow();
	            if (rows.length == 0) {
	                dialogService.alertInfo("info", "Please select a record!");
	                return;
	            }
	            dialogService.openConfirm("Are you sure you want to delete the selected record?", function () {
	                var ids = "";
	                for (var index in rows) {
	                    ids = ids + rows[index].id + ",";
	                }
	                var param = ids.substr(0, ids.length - 1);
	                adService.deleteAd(param, function (data) {
	                    dialogService.closeConfirm();
	                    dialogService.alertInfo("success", "Successfully Deleted!");
	                    gridService.refresh();
	                });
	            });
	        };
	        $scope.deleteRow = function (index) {
	            var row = gridService.getRow(index);
	           /* if(row.adAudit=='1'){
	           	 dialogService.alertInfo("error", "审核已通过不能删除！");
	           	 return;
	           }*/
	            dialogService.openConfirm("Are you sure you want to delete the record? " + row.name + "”？", function (confirm) {
	                adService.deleteAd(row.id, function (data) {
	                    dialogService.closeConfirm();
	                    dialogService.alertInfo("success", "Successfully Deleted!");
	                    gridService.refresh();
	                });
	            });
	        };
	        $scope.openOrganization = function () {
	            organizationService.openOrganization(function (value) {
	                if (!$scope.condition) {
	                    $scope.condition = {};
	                }
	                $scope.condition.insId = value.id;
	                $scope.condition.insName = value.name;
	            });
	        };
	        $scope.search = function () {
	            gridService.search($scope.condition);
	        };
	        $scope.reset = function () {
	            $scope.condition = {};
	            gridService.search($scope.condition);
	        };
	        $scope.updateRow = function (index) {
	            var row = gridService.getRow(index);
	            //adService.setAdData(row);
	            $state.go('home.ad.update', {id: row.id});
	        };
	        $scope.detail = function (index) {
	            var row = gridService.getRow(index);
	            //adService.setAdData(row);
	            $state.go('home.ad.view', {id: row.id});
	        };
	        $scope.stopAd = function (index) {
	            var row = gridService.getRow(index);
	            //row.isDisabled = '1';
	            adService.stopAd(row.id, function (data) {
	                dialogService.alertInfo("success", "Successful ad suspension!");
	                gridService.refresh();
	            });
	        };
	        $scope.startAd = function (index) {
	            var row = gridService.getRow(index);
	            //row.isDisabled = '1';
	            adService.startAd(row.id, function (data) {
	                dialogService.alertInfo("success", "Ad enabled successfully!");
	                gridService.refresh();
	            });
	        };
	         gridService.setPlaceholder();
    };
});