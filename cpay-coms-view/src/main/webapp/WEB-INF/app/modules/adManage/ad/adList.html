<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
            	<div class="input-append input-group"  ng-click="openOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="Organization">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
            	<input type="text" class="top-inp" placeholder="name" ng-model="condition.name">
               <div class="input-append">
                	<select ng-options="m.type as m.name for m in adTypeList" ng-model="condition.type" style="width:150px;">
                    <option value="">AD Type</option>
                	</select>
                </div>
                <div class="input-append">
                	<select ng-options="m.type as m.name for m in adFileTypeList" ng-model="condition.adFileType"  style="width:150px;">
                    <option value="">Ad File Type</option>
                	</select>
                </div>
                <!--<div class="input-append">
                	<select ng-options="m.type as m.name for m in adAuditList" ng-model="condition.adAudit"  style="width:150px;">
                    <option value="">审核状态</option>
                	</select>
                </div>-->
	                <select  style="width:150px;"  ng-options="m.type as m.name for m in adAuditList" ng-model="condition.adAudit" >
	                    <option value="">Audit Type</option>
	                </select>
                <button type="button" class="btn-reset" ng-click="reset()">Reset</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>Query</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="" type="button" ng-click="$state.go('home.ad.add')">
                    <i class="icon-plus"></i>Add
                </button>
                <!-- <button class="btn  red" type="button" ng-click="deleteRows()">
                    <i class="icon-remove"></i>批量删除
                </button> -->
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>