'use strict';
define([], function () {
    return function ($scope,BaseApi,baseUrl,toolsService,dialogService, adauditService, $state,$stateParams,$http) {
	    	adauditService.getAdList({'id':$stateParams.id}, function (data) {
	            $scope.ad = data;
		    	if (!$scope.ad.id) {
		            $state.go("home.adaudit.list");
		            return;
		        }
		        $scope.ad.startTime=toolsService.getDayDate($scope.ad.startTime);
		        $scope.ad.endTime=toolsService.getDayDate($scope.ad.endTime);
		        $scope.ad.oldOpinion = $scope.ad.auditOpinion;
		        $scope.ad.opinion = "";
		        if('' != $scope.ad.oldOpinion && null != $scope.ad.oldOpinion){
		        	$scope.showHis = true;
		        }else{
		        	$scope.showHis = false;
		        }
		        BaseApi.get("/dict/types/ad_type,ad_file_type,device_type",{}, function (data) {
		         	//$scope.adTypeList = data.data1;
		         	//$scope.adFileTypeList = data.data2;
		         	//$scope.deviceTypeList = data.data3;
		         	$.each(data.data1,function(n,obj) {  
	                    if(obj.type==$scope.ad.type) {
	                   	 $scope.ad.type=obj.name;
	                   	 return;
	                    } 
	              	});
	              	$.each(data.data2,function(n,obj) {  
	                    if(obj.type==$scope.ad.adFileType) {
	                   	 $scope.ad.adFileType=obj.name;
	                   	 return;
	                    } 
	              	});
	              	$.each(data.data3,function(n,obj) {  
	                    if(obj.type==$scope.ad.deviceType) {
	                   	 $scope.ad.deviceType=obj.name;
	                   	 return;
	                    } 
	              	});
		       });
//		      BaseApi.query("/terminalGroup/selectGroup",{}, function (data) {
//		   		 	$scope.termGroupList = data;
//		   		 $.each(data,function(n,obj) {  
//                     if(obj.id==$scope.ad.termGroupId) {
//                    	 $scope.ad.insName += "("+obj.name+")";
//                    	 return;
//                     } 
//        	 });
//		   	  });
			  BaseApi.query("/terminalGroup/selectGroupByInsId/"+$scope.ad.insId,{}, function (data) {
		 		//$scope.termGroupList = data;
				  $.each(data,function(n,obj) {  
	                     if(obj.id==$scope.ad.termGroupId) {
	                    	 //$scope.ad.termGroupId=obj.name;
	                    	 $scope.ad.insName += "("+obj.name+")";
	                    	 //v=false;
	                    	 return;
	                     } 
            	 });
			 });
		      //回填附件名称
		        if($scope.ad.fileName){
		        	 $scope.file={
		             		name:$scope.ad.fileName
		             };
		        }
		      //广告文件类型控制显示
		        if($scope.ad.adFileType=='4'){
		           	   $('#relateUrl').css("display","");
		           	   $('#relateFile').css("display","none");
		              }else if($scope.ad.adFileType=='1'){
		            	  $('#relateFile').css("display","none");
		            	  $('#relateUrl').css("display","none");
		              }else{
		            	  $('#relateUrl').css("display","none");
		            	  $('#relateFile').css("display","");
		              }
		      //广告类型控制显示
		        if($scope.ad.type=='2'){
		      	   $('#relateApp').css("display","");
		         }else{
		      	   $('#relateApp').css("display","none");
		         }
		      //显示图片
		        if($scope.ad.adPicPath){
		        	$scope.uploadImg=$scope.ad.adPicPath;
		        }
	    	});
	        $scope.download = function (adPath) {
	            var downloadUrl=baseUrl+'/ad/fileDownload?filename='+adPath.substring(adPath.lastIndexOf("/")+1,adPath.length);
	            window.open(downloadUrl,'_blank');
	        };
	        $scope.auditpass=function (id) {
	        	if('' == $scope.ad.opinion || null == $scope.ad.opinion){
	        		$scope.ad.opinion = "Accept";
			    }
	        	var param = {id:id,adAudit:1,isDisabled:0,auditOpinion:$scope.ad.opinion,pubTime:new Date()}
	            adauditService.audit(id,param ,function (data) {
	                dialogService.alertInfo("success", "Successfully!");
	                //刷新消息数量，调用modules/home/<USER>
	                $scope.$parent.$parent.freshMessage();
	                $state.go('home.adaudit.list')
	            });
	       };
	       $scope.auditreject=function (id) {
	    	   if('' == $scope.ad.opinion || null == $scope.ad.opinion){
	    		   dialogService.alertInfo("success", "Please Fill in Audit suggestion");
	    		   return;
		       }
	    	   var param = {id:id,adAudit:2,auditOpinion:$scope.ad.opinion}
	           adauditService.audit(id,param ,function (data) {
	               dialogService.alertInfo("success", "Successfully!");
	               $scope.$parent.$parent.freshMessage();
	               $state.go('home.adaudit.list')
	           });
	      };
	      	$scope.$parent.firstTitle = "Ad Management";
	      	$scope.$parent.secondTitle = "Ad Audit";
	        $scope.$parent.thirdTitle = "Ad Audit";
	        $scope.disabled = true;
    };
});