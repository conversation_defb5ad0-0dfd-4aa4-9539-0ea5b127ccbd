'use strict';
define(function () {
    return function ($scope,$http,$state, dialogService, gridService, driverJobService, BaseApi, toolsService) {
    	$http.get("modules/taskScheduling/driverJob/i18n/en/driverJobList.json").success(function(dataLan) {
        $scope.listJs = dataLan.listJs;
        $scope.listHtml = dataLan.listHtml;
        var user = BaseApi.getUser();
        BaseApi.query("/dict/type/job_action", {}, function (data) {
            $scope.actionTypeList = data;
            gridService.initTable({
                url: "/driverJob",
                scope: $scope,
                detail:true,
                update: 'driverJob_edit',
                deletes: 'driverJob_del',
                singleSelect: true,
                columns: [{
                    field: 'state',
                    checkbox: 'true'
                },
                    {
                        field: 'jobName',
                        title: $scope.listJs.jobName
                    },
                    {
                        field: 'success',
                        title: $scope.listJs.success,
                        formatter: function (value,row) {
                           return '<span style="color: #468847">'+value+'</span>';
                        }
                    },
                    {
                        field: 'failureUpdate',
                        title: $scope.listJs.failure,
                        formatter: function (value,row) {
                            return '<span style="color: #ce3739">'+value+'</span>';
                        }
                    },
                    {
                        field: 'waitSend',
                        title: $scope.listJs.wait,
                        formatter: function (value,row) {
                            return '<span style="color: #8FBC8F">'+value+'</span>';
                        }
                    },
                    {
                        field: 'successDown',
                        title: $scope.listJs.inExecution,
                        formatter: function (value,row) {
                            return '<span style="color: #32CD32">'+value+'</span>';
                        }
                    },
                    {
                        field: 'releaseStatus',
                        title: $scope.listJs.releaseStatus,
                        formatter: function (value,row) {
                                if (value == 1) {
                                    return '<span class="label label-default">Pending</span>';
                                }else if (value == 5) {
                                    return '<span class="label label-success">On Going</span>';
                                }else if (value == 6) {
                                    return '<span class="label label-warning">Pausing</span>';
                                }
                                else if (value == 7) {
                                    return '<span class="label label-info">Completed</span>';
                                }
                            }
                    },
                    {
                        field: 'releaseTime',
                        title: $scope.listJs.releaseTime,
                        formatter: function (value) {
                            return toolsService.getFormatTime(value);
                        }
                    },
                    {
                        field: 'validDate',
                        title: $scope.listJs.validDate,
                        formatter: function (value) {
                            return toolsService.getFormatTime(value);
                        }
                    },
                    {
                        field: 'recordCreateTime',
                        title: $scope.listJs.recordCreateTime,
                        formatter: function (value) {
                            // 解析新加坡时间（假设输入为 "2025-05-09 14:00:00"）
                            const [datePart, timePart] = value.split(' ');
                            const [year, month, day] = datePart.split('-');
                            const [hour, minute, second] = timePart.split(':');

                            // 创建新加坡时间的 UTC 时间（SGT 是 UTC+8）
                            const sgDate = new Date(Date.UTC(
                                parseInt(year),
                                parseInt(month) - 1,
                                parseInt(day),
                                parseInt(hour) - 8, // 减去8小时得到UTC时间
                                parseInt(minute),
                                parseInt(second)
                            ));

                            const localDate = new Date(sgDate); // 转为本地时间

                            // 格式化年月日时分秒
                            const pad = (n) => n.toString().padStart(2, '0');
                            const yyyy = localDate.getFullYear();
                            const MM = pad(localDate.getMonth() + 1);
                            const dd = pad(localDate.getDate());
                            const HH = pad(localDate.getHours());
                            const mm = pad(localDate.getMinutes());
                            const ss = pad(localDate.getSeconds());

                            return `${yyyy}-${MM}-${dd} ${HH}:${mm}:${ss}`;
                        }
                    },
                    {
                        field: 'insName',
                        title: $scope.listJs.insName
                    }]
            });
        });
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.driverJob.view', {id: row.id});
        };
        $scope.search = function () {
            $scope.condition.timeType = null;
            if($scope.condition.jobAction == ""){
                $scope.condition.jobAction = null;
            }
            gridService.search($scope.condition);
        };
        BaseApi.query("/dict/type/job_type", {}, function (data) {
            $scope.jobStatusList = data;
        });
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);

            if (row.releaseStatus == '2'||row.releaseStatus == '5'|| row.releaseStatus == '6') {
                dialogService.alertInfo("success", $scope.listJs.updateRowMsg1);
                return false;
            }else if(row.releaseStatus == '7'){
                dialogService.alertInfo("success", $scope.listJs.updateRowMsg2);
                return false;
            }
            if(row.releaseIns != user.insId){
                dialogService.alertInfo("success", $scope.listJs.updateRowMsg3);
                return false;
            }
            $state.go('home.driverJob.update', {id: row.id});
        };
        $scope.copyRow = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.copyRowMsg1);
                return;
            }else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.copyRowMsg2);
                return;
            }
            $state.go('home.driverJob.copy', {id: rows[0].id});
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            if(row.releaseStatus == '2' || row.releaseStatus == '5'){
                dialogService.alertInfo("success", $scope.listJs.deleteRowMsg1);
                return false;
            }
            dialogService.openConfirm($scope.listJs.deleteRowMsg2+" “" + row.jobName + "”？", function (confirm) {
                driverJobService.deleteDriverJob(row.id, function (data) {
                    dialogService.closeConfirm();
                    if (data.status != 200) {
                        dialogService.alertInfo("error", data.msg);
                        return "";
                    }
                    dialogService.alertInfo("success", $scope.listJs.deleteRowMsg3);
                    gridService.refresh();
                });
            });
        };
        $scope.start = function(){
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.startMsg1);
                return;
            }
            else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.startMsg2);
                return;
            }
            var releaseStatus = rows[0].releaseStatus;
            if (releaseStatus == '1') {
                $scope.subPub(rows[0].id)
            }else if (releaseStatus == '7'){
                dialogService.alertInfo("info", $scope.listJs.startMsg3);
                return;
            }else if (releaseStatus == '2'){
                dialogService.alertInfo("info", $scope.listJs.startMsg4);
                return;
            }else{
                $scope.issuedAgainAll(rows[0].id);
            }
        }
        $scope.subPub = function (index) {
            dialogService.openConfirm($scope.listJs.startMsg2, function () {
                var id = index;
                driverJobService.subJob(id, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", $scope.listJs.subPubMsg1);
                    gridService.refresh();
                });
            });
        };
        $scope.queryForTime = function (str) {
            if (str == "today") {
                $scope.condition.timeType = "1";
                gridService.search($scope.condition);
            }
            if (str == "toweek") {
                $scope.condition.timeType = "7";
                gridService.search($scope.condition);
            }
            if (str == "tomonth") {
                $scope.condition.timeType = "30";
                gridService.search($scope.condition);
            }
        };

        $scope.driverTask = function (index) {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.driverTaskMsg1);
                return;
            }
            else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.driverTaskMsg2);
                return;
            }
            if (rows[0].releaseStatus == 1) {
                dialogService.alertInfo("info", $scope.listJs.driverTaskMsg3);
                return;
            }
            var id = rows[0].id;
            $state.go('home.driverJob.task', {id: id,jobName:rows[0].jobName});
        };
        $scope.pause = function () {
            var rows = gridService.getSelectedRow();
            if(rows.length == 0){
                dialogService.alertInfo("error", $scope.listJs.pauseMsg1);
                return;
            }
            var releaseStatus = rows[0].releaseStatus;
            if (releaseStatus == '1') {
                dialogService.alertInfo("info", $scope.listJs.pauseMsg2);
                return;
            }else if (releaseStatus == '2'){
                dialogService.alertInfo("info", $scope.listJs.pauseMsg3);
                return;
            }else if (releaseStatus == '7'){
                dialogService.alertInfo("info", $scope.listJs.pauseMsg4);
                return;
            }else {
            	
                driverJobService.pause(rows[0].id, function (data) {
                    
                });
                dialogService.alertInfo("success", $scope.listJs.pauseMsg5);
                gridService.refresh();
            }

        };
        $scope.issuedAgainAll = function(id){

            driverJobService.issuedAgainAll(id, function (data) {
                
            });
            dialogService.alertInfo("success", $scope.listJs.issuedAgainAllMsg1);
            gridService.refresh();
        };
        $scope.delay = function() {
            var rows = gridService.getSelectedRow();
            if(rows.length == 0){
                dialogService.alertInfo("error", $scope.listJs.delayMsg1);
                return;
            }
            var id = rows[0].id;
            var validDate = toolsService.getFormatTime(rows[0].validDate);
            $('#dateDelay').minDate = validDate;
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/driverjobDelay.html",
                width: '30%',
                controller: function ($scope, dialogService, formService,driverJobService) {
                    $scope.dialogTitle = "Task delay";
                    $scope.driverJob = {};
                    $scope.minDate = validDate;
                    $scope.driverJob.validDate = validDate;
                    $scope.driverJob.id = id;

                    $scope.form = formService.form(function () {
                        driverJobService.delayDriverJob($scope.driverJob,function () {
                            dialogService.alertInfo("success", "operate success");
                            gridService.refresh();
                        });
                        $scope.closeThisDialog(0);
                    });

                }
            });

        }
        $scope.driverJobUpdate = function(){
            dialogService.openConfirm($scope.listJs.driverJobUpdateMsg1,function() {
                dialogService.closeConfirm();
                driverJobService.driverJobUpdate($scope.condition,function () {
                });
                dialogService.alertInfo("success", $scope.listJs.driverJobUpdateMsg2);
                gridService.refresh();
            });
        }
        gridService.setPlaceholder();
    	});
    };
});