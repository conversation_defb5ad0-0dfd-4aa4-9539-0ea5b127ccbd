<style>
    #table td{
        padding-left: 10px;
        padding-right: 10px;
    }
    h2{
        color: #1a1a1a;
        font-weight: 500;
    }
    .fixed-table-container{
        margin-bottom: 0;
    }
</style>


<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div><h1 style="font-size: 14px; font-weight: bold;">【Terminal information】</h1></div>
            <div class="info-container" style="overflow: hidden">
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Task name </td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.jobName"></span>
                        </td>
                    </tr>
                    
                    <tr class="info-table-row">
                        <td class="info-table-name"> Terminal model</td>

                        <td class="info-table-value">
                            <span ng-bind="termTypes"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Manufacturer </td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(factoryList,driverJob.manufacturerId)"></span>
                        </td>
                    </tr>
                </table>
            </div>
            <div><h1 style="font-size: 14px; font-weight: bold;">【Software information】</h1></div>
            <table id="table"></table>
            <div><h1 style="font-size: 14px; font-weight: bold;">【Mandate period】</h1></div>
            <div class="info-container" style="overflow: hidden">
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> start time </td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.releaseTime"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> end time</td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.validDate"></span>
                        </td>
                    </tr>
                </table>
            </div>
            <div><h1 style="font-size: 14px; font-weight: bold;">【Update type】</h1></div>
            <div class="info-container" style="overflow: hidden">
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Notice Type</td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.updateMethod==0?'Silence':'Not Silence'"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Real-Time</td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.isRealUpdate==0?'Real Time':'Free Time'"></span>
                        </td>
                    </tr>
                </table>
            </div>
            <div><h1 style="font-size: 14px; font-weight: bold;">【Release type】</h1></div>
            <div class="info-container">
                <table class="info-table">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Release type</td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(releaseTypeList,driverJob.releaseType,'type') || '--'"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="driverJob.releaseType==1">
                        <td class="info-table-name"> Organization</td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.insName"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="driverJob.releaseType==1">
                        <td class="info-table-name"> Group filtration method </td>

                        <td class="info-table-value">
                            <span ng-bind="driverJob.isGroupUpdate"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="driverJob.releaseType==1">
                        <td class="info-table-name"> Terminal groups</td>

                        <td class="info-table-value">
                            <span ng-bind="groupIds"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row big-row" ng-show="driverJob.releaseType==2">
                        <td class="info-table-name"> Terminal collection</td>

                        <td class="info-table-value" id = "collectionText">
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

