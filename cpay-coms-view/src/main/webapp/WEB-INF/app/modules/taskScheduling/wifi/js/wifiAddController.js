'use strict';
define(function () {
    return function ($scope, wifiService, toolsService, Upload, terminalTypeService, factoryService, $state, dialogService,
                     formService, BaseApi, organizationService, terminalGroupService, gridService) {
        $scope.$parent.thirdTitle = "Task Add";
        $scope.jobActionType = 1;
        $scope.readOnly = false;
        var parentScope = $scope;
        var domain = window.location.href.split("#")[0];
        var baseUrl = domain.substr(0, domain.length - 6);
        //数据初始化
        $scope.driverJob = {};
        $scope.driverJob.validDateShow = false;
        $scope.termTypeArrWeb = []; //终端型号数组
        $scope.activateTypes = []; //终端类型数组
        $scope.driverJob.updateMethod = 0;
        $scope.driverJob.isRealUpdate = 0;
        $scope.driverJob.isGroupUpdate = 2;
        $scope.errorLength = 0;
        $scope.groupIdArr = [];
        $scope.driverJob.dccSupFlag = "";

        //软件列表 列表中的字段初始化
        // isDependent 是否依赖软件开关，dependents 可依赖软件列表，dependAppIdArr 已选依赖软件id数组
        $scope.driverJobApps = [{isDependent: false, dependents: [], dependAppIdArr: []}];
        $scope.dependentSoft = {};//设定弹窗填充的初始值
        var driverJobAllList = []; //存储应用商店软件列表
        var dependents = [];//存储可依赖软件列表

        //字典列表
        BaseApi.get("/dict/types/driver_type_two,messionType,job_action", {}, function (data) {
            $scope.driverTypeList = data.data1;//软件类型
            $scope.releaseTypeList = data.data2;//发布方式
            $scope.actionTypeListBcm = data.data3; //操作类型
            $scope.actionTypeList = data.data3;
        });
        BaseApi.get("/dict/types/terminal_type,only_update_code,cup_conn_mode,buss_type", {}, function (data) {
            $scope.terminalTypeList = data.data1;//终端类型
            $scope.onlyUpdateCode = data.data2;//收单应用集合
            $scope.cupConnModeList = data.data3;
            $scope.bussTypeList = data.data4;
        });

        //厂商列表
        BaseApi.query("/factory/type", {}, function (data) {
            $scope.factoryList = data;
        });

        //厂商切换
        $scope.changeFactory = function (factoryId) {
            $scope.driverJobApps = [{isDependent: false, dependents: [], dependAppIdArr: []}];
            dependents = [];
            driverJobAllList = [];
            if (factoryId == null || factoryId == '' || factoryId == undefined) {
                $scope.termTypeList = [];
                return;
            } else {
                BaseApi.query("/terminalType/activateType/" + factoryId, {}, function (data) {
                    $scope.termTypeList = data
                });
            }
            $scope.collectionInit();
        };
        //型号修改 重置软件列表
        $scope.changeTermType = function () {
            $scope.driverJobApps = [{isDependent: false, dependents: [], dependAppIdArr: []}];
            dependents = [];
            $scope.collectionInit();
        }


        // 重置终端集合
        $scope.collectionInit = function () {
            $("#tout_acct_list option").remove();
            $("#tout_error_list option").remove();
            $scope.errorLength = 0;
        };
        //根据不同情况显示不同的软件操作

        //判断一个数组是否时另一数组的子集
        function isContained(arr, subArr) {
            if (!(arr instanceof Array) || !(subArr instanceof Array) || (arr.length < subArr.length)) {
                return false;
            }
            for (var i = 0; i < subArr.length; i++) {
                if (arr.indexOf(subArr[i]) == -1) {
                    return false;
                }
            }
            return true;
        }

        $scope.selectAll = function () {
            if ($("#allCheck").attr("checked")) {
                $("input[name='checkVal']").attr("checked", true);
            } else {
                $("input[name='checkVal']").attr("checked", false);
            }
        }
        $scope.formatCheck = function (code, detail) {
            if (detail == undefined) {

            } else {
                return $.inArray(code, detail) != '-1';
            }

        }
        $scope.selectAllType = function () {
            if ($("#allTypeCheck").attr("checked")) {
                $("input[name='checkTypeVal']").attr("checked", true);
            } else {
                $("input[name='checkTypeVal']").attr("checked", false);
            }
        }
        $scope.formatTypeCheck = function (code, typeDetail) {
            if (typeDetail == undefined) {

            } else {
                return $.inArray(code, typeDetail) != '-1';
            }

        }
        $scope.getGroupIdArr = function () {
            $scope.groupIdArr = [];
            $("input:checkbox[name='checkVal']:checked").each(function () {
                $scope.groupIdArr.push($(this).val());
            })

        }
        var getTermTypeArrWeb = function () {
            $scope.termTypeArrWeb = [];
            $("input:checkbox[name='checkTypeVal']:checked").each(function () {
                $scope.termTypeArrWeb.push($(this).val());
            })

        }
        var getActivateType = function () {
            $scope.activateTypes = [];
            $("input:checkbox[name='checkActivateVal']:checked").each(function () {
                $scope.activateTypes.push($(this).val());
            })

        }
        $scope.form = formService.form(function () {
                var collections = "";
                var selectObj = angular.element("#tout_acct_list").get(0);
                var options = selectObj.options;
                for (var i = 0; i < options.length; i++) {
                    collections += options[i].text + ",";
                }
                $scope.driverJob.collection = collections.substring(0, collections.length - 1);
            wifiService.addDriverJob($scope.driverJob, function (data) {
                if (data.status != 200) {
                    if ($scope.driverJob.validDateShow == "T") {
                        $scope.driverJob.validDateShow = true;
                    } else {
                        $scope.driverJob.validDateShow = false;
                    }
                    dialogService.alertInfo("info", data.msg);
                    return;
                }
                $state.go("home.wifi.list");
            });
        });

        $scope.addTermSeq = function () {
            dialogService.openDialog({
                template: "modules/taskScheduling/wifi/showTermSeqList.html",
                    width: '60%',
                    controller: function ($scope, dialogService, $timeout) {
                        $scope.ok = function () {
                            var rows = $("#sn").val();
                            if (angular.element("#tout_acct_list option").size() == 0) {
                                angular.element("#tout_acct_list").append("<option value='" + rows + "'>"
                                    + rows + "</option>");
                            } else {
                                angular.element("#tout_acct_list option").each(function () {
                                    if (rows == $(this).val()) {
                                        return false;
                                    } else {
                                        angular.element("#tout_acct_list").append("<option value='" + rows + "'>"
                                            + rows + "</option>");
                                    }
                                });
                            }
                            $scope.closeThisDialog(0);
                        }
                    }
            }
            );



        };

    $scope.cleanTermSeqCollection = function () {
        $scope.driverJob.collection = "";
    }

    $scope.openSelectExcel = function () {
        var manufacturerId = $scope.driverJob.manufacturerId;
        if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
            dialogService.alertInfo("info", "Please select the terminal manufacturer！");
            return;
        }
        getTermTypeArrWeb();
        if ($scope.termTypeArrWeb.length == 0) {
            dialogService.alertInfo("info", "Please select the terminal model！");
            return;
        }
        var termTypeCodes = $scope.termTypeArrWeb.toString();
        getActivateType();
        var activateTypes = $scope.activateTypes;

        if (activateTypes == undefined || activateTypes == null) {
            activateTypes = "";
        }
        var cupConnMode = $scope.driverJob.cupConnMode;
        if (cupConnMode == undefined || cupConnMode == null) {
            cupConnMode = "";
        }
        var dccSupFlag = $scope.driverJob.dccSupFlag;
        var bussType = $scope.driverJob.bussType;
        if (bussType == undefined || bussType == null) {
            bussType = "";
        }
        var params = "?termMfrId=" + manufacturerId + "&termTypeCodes=" + termTypeCodes
            + "&activateTypes=" + activateTypes.toString() + "&cupConnMode=" + cupConnMode
            + "&bussType=" + bussType + "&dccSupFlag=" + dccSupFlag;
        dialogService.openDialog({
            template: "modules/taskScheduling/driverJob/driverJobExcel.html",
            controller: function ($scope) {
                $scope.dialogTitle = "Serial import";
                $scope.readOnly = false;
                $scope.submitFile = function () {
                    if (!$scope.file || !$scope.file.size) {
                        dialogService.alertInfo("success", "Please select file");
                        return;
                    }
                    var name = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
                    if (!(name == "xls" || name == "xlsx")) {
                        dialogService.alertInfo("info", "Please select the Excel file");
                        return;
                    }
                    Upload.upload({
                        url: baseUrl + '/uploadjob/importExcel' + params,
                        data: {file: $scope.file},
                        method: 'POST'
                    }).then(function (resp) {
                        var collections = resp.data.data[0];

                        for (var i = 0, len = collections.length; i < len; i++) {

                            var j = 0;
                            angular.element("#tout_acct_list option").each(function () {
                                if (collections[i] == $(this).val()) {
                                    return false;
                                }
                                j++;
                            });
                            if (j == angular.element("#tout_acct_list option").size()) {
                                angular.element("#tout_acct_list").append("<option value='" + collections[i] + "'>"
                                    + collections[i] + "</option>");
                            }
                        }
                        var errorCollections = resp.data.data[1];
                        if (errorCollections.length != 0) {
                            parentScope.errorLength = 1;
                        }
                        for (var i = 0, len = errorCollections.length; i < len; i++) {

                            var j = 0;
                            angular.element("#tout_error_list option").each(function () {
                                if (errorCollections[i] == $(this).val()) {
                                    return false;
                                }
                                j++;
                            });
                            if (j == angular.element("#tout_error_list option").size()) {
                                angular.element("#tout_error_list").append("<option value='" + errorCollections[i] + "'>"
                                    + errorCollections[i] + "</option>");
                            }
                        }

                        dialogService.alertInfo("success", "File uploaded successfully");
                    }, function (resp) {
                        dialogService.alertInfo("warning", "File upload failed");
                    }, function (evt) {
                    });
                    $scope.closeThisDialog(0);
                };
                $scope.downModel = function () {
                    var downloadUrl = baseUrl + '/uploadjob/fileDownload';
                    window.open(downloadUrl, '_blank');
                };
            },
            closeByDocument: false
        });
    };

    $scope.openInsDialog = function () {
        organizationService.openOrganization(function (value) {
            if (!$scope.driverJob) {
                $scope.driverJob = {};
            }
            if ($scope.driverJob.insId == value.id) {
                return;
            }
            $scope.driverJob.insId = value.id;
            $scope.driverJob.insName = value.name;
            BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
                $scope.terminalGroupList = data;
                $scope.groupIdArr = [];
            });
        });
    };
    $scope.deleteTerm = function () {
        var oSelect = angular.element("#tout_acct_list");
        var options = oSelect.option;
        var termSeqs = $("#tout_acct_list option:selected").val();
        if (termSeqs == undefined || termSeqs == null || termSeqs == "") {
            dialogService.alertInfo("success", "Please select at least one record！");
            return;
        }
        $("#tout_acct_list option:selected").remove();
    }
    $scope.clearTerm = function () {
        var selectObj = angular.element("#tout_acct_list").get(0);
        var options = selectObj.options;
        if (options.length == 0) {
            dialogService.alertInfo("success", "No terminal information is currently available for clearing！");
            return;
        }
        dialogService.openConfirm("Are you sure you want to empty the current terminal collection？", function () {
            $scope.collectionInit();
            dialogService.closeConfirm();
        });
    }
    $scope.changeRelease = function (releaseType) {
        $scope.collectionInit();
        $scope.driverJob.insId = "";
        $scope.driverJob.insName = "";
        $scope.driverJob.isGroupUpdate = 2;
        $scope.groupIdArr = [];
        if (releaseType == 1) {
            $scope.driverJob.activateType = "0";
        }
    }

    //修改依赖关系
    $scope.changeDepends = function (driverJobApp) {
        changeDependents()
    }

    //查找依赖关系数组中是否存在某id
    var indexOfParamInList = function (ArrList, dependAppId) {

        for (var i = 0, len = ArrList.length; i < len; i++) {
            if (ArrList[i].dependAppId == dependAppId) {
                return i;
            }
        }
        return -1;
    }
    //移除虚拟id为dependAppId的数据
    var removeParamInList = function (ArrList, dependAppId) {

        for (var i = 0, len = ArrList.length; i < len; i++) {
            if (ArrList[i].dependAppId == dependAppId) {
                ArrList.splice(i, 1);
                return;
            }
        }
    }
    //查找针对某软件的可依赖列表
    var findDependentList = function (driverJobApp) {
        //由于软件依赖关系不能造成回环，所以要对每个软件生成对应的可依赖列表

        driverJobApp.dependents = dependents.slice();

        //1.使用递归函数算出软件可依赖的id数组
        recursiveFunc(driverJobApp.dependents, driverJobApp.dependAppId);

    }
    //递归函数
    var recursiveFunc = function (dependents, dependAppId) {

        //1.判断该随机id是否存在数组中存在，不存在时返回
        if (indexOfParamInList(dependents, dependAppId) == -1) {
            return;
        }
        //2.遍历软件列表
        for (var i = 0, len1 = $scope.driverJobApps.length; i < len1; i++) {
            //如果软件的依赖关系中 包含查找的dependAppId,继续递归查找该软件的随机id
            if ($scope.driverJobApps[i].dependAppIdArr.indexOf(dependAppId) != -1) {
                recursiveFunc(dependents, $scope.driverJobApps[i].dependAppId);
                removeParamInList(dependents, dependAppId);
            }
        }
        //3.删除被依赖的软件随机id
        removeParamInList(dependents, dependAppId);
        return;
    }

    //添加软件到可依赖列表中
    var addDependentList = function (driverJobApp) {
        //1.随机生成id唯一标识该软件
        var dependAppId = Math.random().toString().slice(-6);

        //2.判断软件是否存在依赖，存在时 进行删除
        var index = checkDependentList(driverJobApp);
        //3.将新数据加到可依赖软件的数组中
        //不存在时，添加到数组中
        dependents.push({'dependAppId': dependAppId, 'appName': driverJobApp.appName})
        //4.将随机id加入到driverJobApp中
        driverJobApp.dependAppId = dependAppId;

    }
    //判断是否存在
    var checkDependentList = function (driverJobApp) {
        //1.判断软件是否存在随机id,不存在时返回-1
        if (driverJobApp.dependAppId == undefined || driverJobApp.dependAppId == null || driverJobApp.dependAppId == '') {
            return -1;
        }
        //2.遍历可依赖软件列表
        for (var i = 0, len = dependents.length; i < len; i++) {
            if (dependents[i].dependAppId == driverJobApp.dependAppId) {
                return i;
            }
        }
    }
    //删除可依赖列表中的依赖软件
    var deleteDependentList = function (driverJobApp) {
        //1.判断软件是否存在随机id,不存在时直接返回
        if (driverJobApp.dependAppId == undefined || driverJobApp.dependAppId == null || driverJobApp.dependAppId == '') {
            return;
        }
        //2.遍历可依赖软件列表dependents
        for (var i = 0, len = dependents.length; i < len; i++) {
            if (dependents[i].dependAppId == driverJobApp.dependAppId) {
                dependents.splice(i, 1);
                return;
            }
        }
    }

    //软件中多选控件页面刷新
    var changeDependents = function () {
        $('.chosen-select').chosen();
        $scope.$watch('driverJobApps', function (newVal) {
            for (var i = 0, len = $scope.driverJobApps.length; i < len; i++) {
                findDependentList($scope.driverJobApps[i]);
            }
            setTimeout(function () {
                $('.chosen-select').trigger("liszt:updated");
                $('.chosen-select').chosen();
            }, 0)
        })
    }
    }
});
;