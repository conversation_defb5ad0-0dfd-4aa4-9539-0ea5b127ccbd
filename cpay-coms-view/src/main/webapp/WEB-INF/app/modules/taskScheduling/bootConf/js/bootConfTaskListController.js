'use strict';
define(function () {
    return function ($scope,$state,dialogService, gridService,$stateParams,BaseApi,bootConfService,toolsService) {
        $scope.$parent.thirdTitle = "";
        gridService.initTable({
            url: "/apkPush/getBootConfList",
            scope: $scope,
            operator:false,
            columns: [{
                field: 'state',
                checkbox: 'true'	
            }, 
            {
                field: 'termSeq',
                title: '终端序列号',
                width:130,
                
            },
            {
                field: 'appCode',
                title: '应用包名',
                width:100,
            },
	         {
	            field: 'releaseTime',
	            title: '有效期起始日期',
	            width:120,
	        },
	        {
	            field: 'validDate',
	            title: '有效期截止日期',
	            width:120,
	            formatter:function(value){
	            	return toolsService.getFormatTime(value);
	            }
	        },
	        {
	            field: 'dlBeginDate',
	            title: '下载开始时间',
	            width:120,
	            formatter:function(value){
	            	return toolsService.getFormatTime(value);
	            }
	        },
	        {
	            field: 'dlEndDate',
	            title: '下载结束时间',
	            width:120,
	            formatter:function(value){
	            	return toolsService.getFormatTime(value);
	            }
	        },
	        {
	            field: 'downTime',
	            title: '任务下发时间',
	            formatter: function (value) {
                	return toolsService.getFullDate(value);
                }
	        },
	        {
	            field: 'dlFlag',
	            title: '任务执行状态',
	            width:120,
	            formatter : function(value) {
                    if (value==0) {
                        return '<span>等待下发</span>';
                    }else if(value==1){
                        return '<span>下发中</span>';
                    }else if(value==2){
                        return '<span>下发成功</span>';
                    }else if(value==3){
                        return '<span>下载成功</span>';
                    }else if(value==4){
                        return '<span>更新成功</span>';
                    }else if(value==5){
                        return '<span>卸载成功</span>';
                    }else if(value==6){
                        return '<span>下载失败</span>';
                    }else if(value==7){
                        return '<span>更新失败</span>';
                    }else if(value==8){
                        return '<span>卸载失败</span>';
                    }else if(value==9){
                        return '<span>取消下发</span>';
                    }
			    } 
	        }]
        });
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        BaseApi.query("/dict/type/taskStatus",{}, function (data) {
   		 	$scope.taskStatusList = data;
   	 	});
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search($scope.condition);
        };
        $scope.refreshStatus = function () {
            gridService.search();
        };
        $scope.issuedAgain = function () {
        	var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "请选择记录！");
                return;
            }
            for (var i in rows) {
                if(rows[i].dlFlag == 4){
                	dialogService.alertInfo("info", "不得选择已经更新成功的任务进行取消下发！");
                	return false;
                }
            }
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                bootConfService.issuedAgain(param, function (data) {
                    dialogService.alertInfo("success", "重新下发成功！");
                    gridService.refresh();
                });
        };
        $scope.issuedCancel = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "请选择记录！");
                return;
            }
	            for (var i in rows) {
	                if(rows[i].dlFlag == 4){
	                	dialogService.alertInfo("info", "不得选择已经更新成功的任务进行取消下发！");
	                	return false;
	                }
	                if(rows[i].dlFlag == 7){
	                	dialogService.alertInfo("info", "不得选择更新失败的任务进行取消下发！");
	                	return false;
	                }
	                if(rows[i].dlFlag == 3){
	                	dialogService.alertInfo("info", "不得选择下载成功的任务进行取消下发！");
	                	return false;
	                }
	                if(rows[i].dlFlag == 2){
	                	dialogService.alertInfo("info", "不得选择下发成功的任务进行取消下发！");
	                	return false;
	                }
                    if(rows[i].dlFlag == 8){
                        dialogService.alertInfo("info", "不得选择卸载失败的任务进行取消下发！");
                        return false;
                    }
	                 
	            }
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                bootConfService.issuedCancel(param, function (data) {
                	dialogService.alertInfo("success", "取消下发成功！");
                    gridService.refresh();
                });
        };
        $scope.deleteRows = function(){
        	var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "请选择记录！");
                return;
            }
            dialogService.openConfirm("确认要删除当前所选任务？", function () {
            var ids = "";
            for (var index in rows) {
                ids = ids + rows[index].id + ",";
            }
            var param = ids.substr(0, ids.length - 1);
            bootConfService.deleteRows(param, function (data) {
                dialogService.alertInfo("success", "操作成功！");
                dialogService.closeConfirm();
                gridService.refresh();
            	});
            });
        }
        
         gridService.setPlaceholder();
    };
});