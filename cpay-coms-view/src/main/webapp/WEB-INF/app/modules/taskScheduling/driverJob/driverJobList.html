<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class=" top-inp" placeholder="{{listHtml.jobName}}" ng-model="condition.jobName">
                <select ng-options="m.type as m.name for m in jobStatusList" ng-model="condition.releaseStatus">
                    <option value="">{{listHtml.releaseStatus}}</option>
                 </select>
               <button type="button" class="btn" ng-click="reset()">{{listHtml.reset}}</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>{{listHtml.query}}</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="button" type="button" ng-click="$state.go('home.driverJob.add')" has-permission="driverJob_add">
                    <i class="icon-plus"></i>{{listHtml.add}}
                </button>
                <button class="btn-default" type="button" ng-click="copyRow()" has-permission="driverJob_add">
                    <i class="icon-copy"></i>{{listHtml.copy}}
                </button>
                <button class="btn-default" type="button" ng-click="driverTask()">
                    <i class="icon-th-list"></i>{{listHtml.execution}}
                </button>
                <button class="btn-special" type="button" ng-click="start()" has-permission="driverJob_issuedAgainAll">
                    <i class="icon-play"></i>{{listHtml.start}}
                </button>
                <button class="btn-pause" type="button" ng-click="pause()" has-permission="driverJob_issuedCancelAll">
                    <i class="icon-pause"></i>{{listHtml.pause}}
                </button>
                <button class="btn-default" type="button" ng-click="delay()" has-permission="driverJob_edit">
                    <i class="icon-calendar"></i>{{listHtml.delay}}
                </button>
                <button class="btn-default" type="button" ng-click="driverJobUpdate()" has-permission="driverJob_update">
                    <i class="icon-refresh"></i>{{listHtml.refresh}}
                </button>
                <div style="float: right">
                    <ul class="time-choice-tab-container">
                        <li class="time-choice-tab" ng-click="queryForTime('today')" has-permission="driverJob_today" ng-class="{'active':condition.timeType==1}">{{listHtml.toDay}}</li>
                        <li class="time-choice-tab" ng-click="queryForTime('toweek')" has-permission="driverJob_toweek" ng-class="{'active':condition.timeType==7}">{{listHtml.Sday}}</li>
                        <li class="time-choice-tab" ng-click="queryForTime('tomonth')" has-permission="driverJob_tomonth" ng-class="{'active':condition.timeType==30}">{{listHtml.Tday}}</li>
                    </ul>
                </div>
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
