<style>
	.chzn-drop{
		margin-bottom:30px !important
	}
</style>
<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【Terminal information】</h1>
					<span style='position: absolute; right: 10px; top: 8px; color: #666666; cursor: pointer;'></span>
				</div>
				<div>
					<table class="table table-hovers" style="margin-bottom: 0px;">
						<tr>
							<td style="width: 15%; text-align: right;">Task Name<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;">
								<input type="text" name="jobName" maxlength=20 ng-model="driverJob.jobName"
									   ng-readonly="readOnly"
									   ng-disabled="disabled" validator="required,maxlength=20" message-id="jobName" style="width: 268px;"/>
								<span id="jobName" class="help-inline"> </span>
							</td>
							<td style="width: 15%; text-align: right;">Manufacturer<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;">
								<select class="form-control" name="factoryList" ng-options="m.id as m.name for m in factoryList" ng-model="driverJob.manufacturerId"
										validator="required" ng-change="changeFactory(driverJob.manufacturerId)"
										ng-disabled="readOnly"
										message-id="manufacturerId" style="width: 280px;">
									<option value="">Choose</option>
								</select><span id="manufacturerId" class="help-inline"></span>
							</td>
						</tr>
						<tr style="border: 0px;">

							<td style="width: 15%; text-align: right; border: 0px;">Terminal Model<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;">
								<div class="terminal-type-check-container controls no-margin"
									 style="width: 80%;">
									<label class="checkbox">
										<input type="checkbox"
											   class="ace" name="allTypeCheck"
											   ng-click="selectAllType(termType.code,typeDetail)"
											   value="{{termTypeList.chooseAll}}" id="allTypeCheck" /><span
											class="lbl">All</span>
									</label> <br />
									<label class="checkbox line margin-normal"
										   ng-repeat="termType in termTypeList">
										<input type="checkbox" class="ace" ng-value="termType.code"
											   ng-checked="formatTypeCheck(termType.code,typeDetail)"
											   ng-click="changeTermType()"
											   name="checkTypeVal" /> <span
											class="lbl" ng-bind="termType.name"></span>
									</label>
								</div> <span id="termTypeArrWeb" class="help-inline" style="display: none;"></span>
							</td>

						</tr>
					</table>

				</div>

				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【Software information】</h1>
				</div>
				<div style="margin-bottom: 20px">
					<button ng-click="addSoftware()">Add Software</button>
					<div style="margin-top: 20px;border: 1px solid #ddd;" ng-repeat="driverJobApp in driverJobApps track by $index">
						<div style="border-bottom: 1px solid #ddd">
							<div style="font-size:16px;padding:10px;display: inline-block">Software {{$index+1}}</div>
							<a style="float:right;background: url('./images/icon_close.png') no-repeat center center;width:80px;height:40px;display: block;"
							   ng-click="removeSoftware($index)"
							   href="javascript:;"></a>
						</div>
						<div style="padding:10px 0">
							<table class="table table-hovers" style="margin-bottom: 0;">
								<tr>
									<td style="width: 15%; text-align: right;border-top:none">Software Type<span
											style='color: #e02222'>*</span></td>
									<td style="width: 30%;border-top:none">
										<select class="form-control" name="driverTypeList{{$index}}" ng-model="driverJobApp.jobType"
												ng-readonly="readOnly" ng-disabled="readOnly"
												ng-change="changeDriverType(driverJobApp)"
												ng-options="m.type as m.name for m in driverTypeList"
												validator="required" message-id="jobType{{$index}}" style="width: 280px;">
											<option value="">Choose</option>
										</select>
										<span id="jobType{{$index}}" class="help-inline"></span>
									</td>
									<td style="width: 15%; text-align: right; border: 0px;">Operation Type<span
											style='color: #e02222'>*</span></td>
									<td style="width: 30%; border: 0px;">
										<select class="form-control" name="actionTypeList{{$index}}" ng-model="driverJobApp.jobAction"
												ng-readonly="readOnly" ng-disabled="readOnly"
												ng-options="m.type as m.name for m in driverJobApp.actionTypeList"
												validator="required" message-id="jobAction{{$index}}" style="width: 280px;">
											<option value="">Choose</option>
										</select>
										<span id="jobAction{{$index}}" class="help-inline"></span>
									</td>
								</tr>
								<tr style="border: 0px;">
									<td style="width: 15%; text-align: right;border-top:none">Software Name<span
											style='color: #e02222'>*</span></td>
									<td style="width: 30%;border-top:none ">

										<select class="form-control" name="driverList{{$index}}" ng-model="driverJobApp.appId"
												ng-readonly="readOnly" ng-disabled="readOnly"
												ng-options="m.id as (m.name || m.appName) for m in driverJobApp.driverList"
												ng-change="changeApp(driverJobApp)"
												validator="required" message-id="appId{{$index}}" style="width: 280px;">
											<option value="">Choose</option>
										</select>
										<span id="appId{{$index}}" class="help-inline"></span>
									</td>

									<td style="width: 15%; text-align: right; border: 0px;">Software Package</td>
									<td style="width: 30%; border: 0px;">
										<input type="text" name="appCode"  ng-model="driverJobApp.appCode"
											   ng-readonly="true" ng-disabled="disabled" style="width: 268px;"/>
									</td>

								</tr>
								<tr style="border: 0px;" ng-if="driverJobApp.jobAction != '6'">
									<td style="width: 15%; text-align: right; border: 0px;">Internal Software Version<span
											style='color: #e02222'>*</span></td>
									<td style="width: 30%; border: 0px;">
										<select class="form-control" name="appVersionList{{$index}}" ng-model="driverJobApp.appVersionId"
												ng-readonly="readOnly" ng-disabled="readOnly"
												ng-options="m.id as m.appVersion for m in driverJobApp.appVersionList"
												ng-change="changeAppVersion(driverJobApp)"
												validator="required" message-id="appVersionId{{$index}}" style="width: 280px;">
											<option value="">Choose</option>
										</select>
										<span id="appVersionId{{$index}}" class="help-inline"></span>
									</td>
									<td style="width: 15%; text-align: right; border: 0px;">Application Version</td>
									<td style="width: 30%; border: 0px;">
										<input type="text" name="appVersionName" ng-model="driverJobApp.appVersionName"
											   ng-readonly="!false" ng-disabled="disabled" style="width: 268px;"/>
									</td>
								</tr>
								<tr>
									<td style="width: 15%; text-align: right; border: 0px;" ng-if="driverJobApp.jobAction != '6'">Model</td>
									<td style="width: 30%; border: 0px;" ng-if="driverJobApp.jobAction != '6'">
										<input type="text" name="termTypeNames" ng-model="driverJobApp.termTypeNames"
											   ng-readonly="!false" ng-disabled="disabled" style="width: 268px;"/>
									</td>
								</tr>
							</table>
						</div>
					</div>

				</div>
				<div>


				</div>
				<div class='titlediv' >
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【Task time】</h1>
					<div >
						<label style="float: left;">Task Time Modify Switch&nbsp;&nbsp;</label><label style="display: inline-block;"><input
						name="switch-field-1" class="ace ace-switch " type="checkbox"
						ng-model="driverJob.validDateShow" ng-change="validDateChange()">
						<span class="lbl"></span></label>
					</div>
				</div>
				<div ng-show="driverJob.validDateShow">
					<table class="table table-hovers" style="margin-bottom: 0px;">
						<tr>
							<td style="width: 15%; text-align: right;">Validity Start Time<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;">
								<div class="input-append date form_datetime" style="width:280px">
									<input size="16" type="text" value="" readonly class="m-wrap"
										   valid-method="watch"	ng-model="driverJob.releaseTime" name="releaseTime"
										   validator="required" message-id="releaseTime"
										   date-picker format="yyyy-MM-dd HH:mm:ss" style="width: 280px;"/>
								</div>
								<span id="releaseTime" class="help-inline"></span>
							</td>
							<td style="width: 15%; text-align: right;" >Validity End Time<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;" >
								<div class="input-append date form_datetime" style="width:280px">
									<input size="16" type="text" value="" readonly class="m-wrap"
										   valid-method="watch"	ng-model="driverJob.validDate" name="validDate"
										   ng-disabled="disabled" validator="required" message-id="validDate"
										   date-picker format="yyyy-MM-dd HH:mm:ss" style="width: 280px;"/>
								</div>
								<span id="validDate" class="help-inline"></span>
							</td>
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【Update type】</h1>
				</div>
				<div>
					<table class="table table-hovers" style="margin-bottom: 0px;">
						<tr>
							<td style="width: 15%; text-align: right;">Notice Type<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; line-height:40px;" >
								<div class="controls" style="margin-left: 0px;">
									<label class="radio line">
										<input type="radio" name="updateMethod" ng-model="driverJob.updateMethod"
											   validator="required" class="ace" message-id="updateMethod" value="0"
											   required-error-message="{{listHtml.PnoticeType}}"/><span class="lbl">Silence</span>
									</label>
									<label class="radio line">
										<input type="radio" name="updateMethod" ng-model="driverJob.updateMethod" validator="required"
											   class="ace" message-id="updateMethod" value="2"
											   required-error-message="{{listHtml.PnoticeType}}" /><span class="lbl">Not Silence</span>
									</label>
									<span id="updateMethod" class="help-inline"></span>
								</div>
							</td>
							<td style="width: 15%; text-align: right;">Real-Time<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; line-height:40px;">
								<div class="controls" style="margin-left: 0px;">
									<label class="radio line">
										<input type="radio" name="isRealUpdate" ng-model="driverJob.isRealUpdate"
											   validator="required" class="ace" message-id="isRealUpdate" value="0"
											   required-error-message="Please select update real-time control"/><span class="lbl">Real Time</span>
									</label>
									<label class="radio line">
										<input type="radio" name="isRealUpdate" ng-model="driverJob.isRealUpdate" validator="required"
											   class="ace" message-id="isRealUpdate" value="1"
											   required-error-message="Please select update real-time control" /><span class="lbl">Free Time</span>
									</label>
									<span id="isRealUpdate" class="help-inline"></span>
								</div>
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【Release type】</h1>
				</div>
				<div>
					<table class="table table-hovers" style="margin-bottom: 0px;">
						<tr>
							<td style="width: 15%; text-align: right;">Release type<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;">
								<select class="form-control" name="releaseTypeList" ng-model="driverJob.releaseType"
										ng-readonly="readOnly" ng-disabled="readOnly"
										ng-options="m.type as m.name for m in releaseTypeList"
										ng-change="changeRelease(driverJob.releaseType)"
										validator="required" message-id="releaseTypeList" style="width: 280px;">
									<option value="">Choose</option>
								</select><span id="releaseType" class="help-inline"> </span>
							</td>
							<td style="width: 15%; text-align: right;" ng-show="driverJob.releaseType!=1"><span
									style='color: #e02222'></span></td>
							<td style="width: 30%;" ng-show="driverJob.releaseType!=1">
							</td>
							<td style="width: 15%; text-align: right;" ng-show="driverJob.releaseType==1">Orginzation<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; " ng-show="driverJob.releaseType==1">
									<input type="text" name="ins_cd" ng-model="driverJob.insId" ng-show="false"
										   ng-disabled="disabled"
										   message-id="insId"/>
									<div class="input-append input-group" ng-click="openInsDialog()">
										<input type="text" name="insName" ng-model="driverJob.insName" readonly
											   valid-method="watch" ng-disabled="disabled" class="span6 orginput dropdown input-float"
											   message-id="insId" style="width: 280px;"/>
										<button class="choice btn" type="button"  ng-disabled="disabled">
										</button>
									</div>
									<span id="insId" class="help-inline"></span>
							</td>
						</tr>

						<tr style="border: 0px;" ng-show="driverJob.releaseType==1">
							<td style="width: 15%; text-align: right;border: 0px;" >Group filtering method</td>
							<td style="width: 30%; line-height:40px;border: 0px;" >
								<div class="controls" style="margin-left: 0px;">
									<label class="radio line">
										<input type="radio" name="isGroupUpdate" ng-model="driverJob.isGroupUpdate" validator="required"
											   class="ace" message-id="isGroupUpdate" value="2"
											   required-error-message="Please select group filter mode" /><span class="lbl">All</span>
									</label>
									<label class="radio line">
										<input type="radio" name="isGroupUpdate" ng-model="driverJob.isGroupUpdate"
											   validator="required" class="ace" message-id="isGroupUpdate" value="0"
											   required-error-message="Please select group filter mode"/><span class="lbl">Appoint</span>
									</label>
									<label class="radio line">
										<input type="radio" name="isGroupUpdate" ng-model="driverJob.isGroupUpdate" validator="required"
											   class="ace" message-id="isGroupUpdate" value="1"
											   required-error-message="Please select group filter mode" /><span class="lbl">Exclude</span>
									</label>
									<span id="isGroupUpdate" class="help-inline"></span>
								</div>

							</td>
						</tr>

						</tr>
						<tr style="border: 0px;" ng-show="driverJob.releaseType==2">
							<td style="width: 15%; text-align: right; border: 0px;"></td>
							<td style="width: 85%; border: 0px;" colspan="3">
								<button class="btn  blue" type="button" ng-click="showTermSeq()">
									<i class="icon-plus"></i>add
								</button>
								<button class="btn  blue" type="button"
										ng-click="openSelectExcel()" has-permission="driverJob_import">
									<i class="icon-upload"></i>import
								</button>
								<button class="btn  blue" type="button" ng-click="deleteTerm()">
									<i class="icon-remove"></i>delete
								</button>
								<button class="btn  blue" type="button" ng-click="clearTerm()">
									<i class="icon-edit"></i>clean
								</button>
							</td>
						</tr>
						<tr style="border: 0px;" ng-show="driverJob.releaseType==2">
							<td style="width: 15%; text-align: right; border: 0px;">{{listHtml.termCol}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 85%; border: 0px;" colspan="3">
								<select class="span6" id="tout_acct_list" name="tout_acct_list"
										multiple="multiple" style="height: 200px;">
								</select>
							</td>
						</tr>
						<tr style="border: 0px;" ng-show="errorLength != 0">
							<td style="width: 15%; text-align: right; border: 0px;">{{listHtml.failCol}}<span
									style='color: #e02222'></span></td>
							<td style="width: 85%; border: 0px;" colspan="3" >
								<select class="span6" id="tout_error_list" name="tout_error_list"
										multiple="multiple" style="height: 200px;">
								</select>
							</td>
						</tr>
					</table>
					<table class="table table-hovers" style="margin-bottom: 0px;">
						<tr style="border: 0px;" ng-show="driverJob.isGroupUpdate !=2 && driverJob.releaseType==1">
							<td style="width: 15%; text-align: right; border: 0px;">Group</td>
							<td style="width: 100%; border: 0px;">
								<div class="terminal-check-container controls no-margin"
									 style="width: 80%;">
									<label class="checkbox">
										<input type="checkbox"
											   class="ace" name="allCheck"
											   ng-click="selectAll(termGroup.id,detail)"
											   value="{{terminalGroupList.chooseAll}}" id="allCheck" /><span
											class="lbl">All</span>
									</label> <br />
									<label class="checkbox line margin-normal"
										   ng-repeat="termGroup in terminalGroupList">
										<input type="checkbox" class="ace" ng-value="termGroup.id"
											   ng-checked="formatCheck(termGroup.id,detail)"
											   name="checkVal" /> <span
											class="lbl" ng-bind="termGroup.name"></span>
									</label>
								</div> <span id="termGroup" class="help-inline" style="display: none;"></span>
							</td>
						</tr>
					</table>
				</div>
			</form>
		</div>
	</div>
</div>
<div style="padding-left:5.5%" ng-show="!disabled" form-foot
	 goback="$state.go('home.driverJob.list')" submit="form.submit(Form)"
	 reset="form.reset(Form)"></div>

