'use strict';
define(function () {
    return function ($scope,$http,$state,dialogService,organizationService, gridService,clearCacheService,BaseApi,toolsService) {
        $http.get("modules/taskScheduling/clearCache/i18n/en/clearCacheList.json").success(function(dataLan) {
        $scope.listJs = dataLan.listJs;
        $scope.listHtml = dataLan.listHtml;
        $scope.$parent.thirdTitle = "";
        BaseApi.query("/dict/type/operate_instruct",{}, function (data) {
            $scope.operateInstructList = data;

        gridService.initTable({
            url: "/clearCache",
            scope: $scope,
            detail: true,
            update: 'clearCache_update',
            deletes: 'clearCache_del',
            singleSelect: true,
            columns: [{
                field: 'state',
                checkbox: 'true'
            }, 
            {
                field: 'jobName',
                title: $scope.listJs.jobName
            },{
                field: 'jobStatus',
                title: $scope.listJs.jobStatus,
                formatter: function (value) {
                    if (value == 1) {
                            return '<span class="label label-default">initial</span>';
                    }else if (value == 5) {
                        return '<span class="label label-success">start</span>';
                    }else if (value == 6) {
                        return '<span class="label label-warning">stop</span>';
                    }
                    else if (value == 7) {
                        return '<span class="label label-info">end</span>';
                    }
                }
            },/*{
                field: 'insName',
                title: '所属机构'
            },*/{
                field: 'factoryName',
                title: $scope.listJs.factoryName
            },{
                field: 'termTypeName',
                title: $scope.listJs.termTypeName
            },{
	            field: 'operateType',
	            title: $scope.listJs.operateType,
	            formatter: function (value) {
                    var type='';
                    $.each($scope.operateInstructList,function(n,obj) {
                        if(obj.type==value) {
                            type=obj.name;
                        }
                    });
                    return type;
	             }
	        },{
	            field: 'createTime',
	            title: $scope.listJs.createTime,
	            formatter:function(value){
                    // 用本地浏览器的时区创建 Date 对象
                    const localDate = new Date(value);

                    // 格式化输出 yyyy-MM-dd HH:mm:ss
                    const pad = (n) => n.toString().padStart(2, '0');

                    const yyyy = localDate.getFullYear();
                    const MM = pad(localDate.getMonth() + 1);
                    const dd = pad(localDate.getDate());
                    const HH = pad(localDate.getHours());
                    const mm = pad(localDate.getMinutes());
                    const ss = pad(localDate.getSeconds());

                    return `${yyyy}-${MM}-${dd} ${HH}:${mm}:${ss}`;
				}
	        }]
        });
        });
        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
                $scope.condition.terminalGroupId = null;
                BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
           		 	$scope.terminalGroupList = data;
           	 	});
            });
        };
        
        BaseApi.query("/factory/type",{}, function (data) {
   		 	$scope.factoryList = data;
   	 	});
        
        $scope.changeFactory = function (factoryId) {
    		if(factoryId!=null&&factoryId!=""){
    			var param  = {"factoryId":factoryId};
    	        BaseApi.query("/terminalType/activateType/"+factoryId,{}, function (data) {
    	   		 	$scope.termTypeList = data;
    	   	 	});
    		}
    		else{
    			$scope.termTypeList=[];
    		}
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
//        $scope.jobStatusList =[{type: "0", name: "等待发布"},{type: "1", name: "发布成功"}];
        $scope.reset = function () {
            $scope.condition = {};
            $scope.termTypeList = [];
            gridService.search();
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.clearCache.view', {id: row.id});
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            if (row.jobStatus == '5'|| row.jobStatus == '6') {
                dialogService.alertInfo("success", $scope.listJs.msg1);
                return false;
            }else if(row.jobStatus == '7'){
                dialogService.alertInfo("success", $scope.listJs.msg2);
                return false;
            }
            $state.go('home.clearCache.update', {id: row.id});
        };
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm($scope.listJs.msg3 + row.jobName + "？", function (confirm) {
            	clearCacheService.deleteclearCache(row.id, function (data) {
                    dialogService.closeConfirm();
                    if (data.status != 200) {
                        dialogService.alertInfo("error", data.msg);
                        return "";
                    }
                    dialogService.alertInfo("success", $scope.listJs.msg4);
                    gridService.refresh();
                });
            });
        };
        $scope.copyRow = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.msg5);
                return;
            }else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.msg6);
                return;
            }
            $state.go('home.clearCache.copy', {id: rows[0].id});
        };
        $scope.driverTask = function (index) {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.msg5);
                return;
            }
            else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.msg6);
                return;
            }
            if (rows[0].jobStatus == 1) {
                dialogService.alertInfo("info", $scope.listJs.msg7);
                return;
            }
            var id = rows[0].id;
            $state.go('home.clearCache.task', {id: id,jobName:rows[0].jobName});
        };
        $scope.start = function(){
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.msg5);
                return;
            }else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.msg6);
                return;
            }
            var jobStatus = rows[0].jobStatus;
            if (jobStatus == '5') {
                dialogService.alertInfo("info", $scope.listJs.msg8);
                return;
            }else if (jobStatus == '7'){
                dialogService.alertInfo("info", $scope.listJs.msg9);
                return;
            }else{
                clearCacheService.startOperation(rows[0].id, function (data) {
                    dialogService.alertInfo("success", $scope.listJs.msg10);
                    gridService.refresh();
                });
            }
        }
        $scope.pause = function () {
            var rows = gridService.getSelectedRow();
            if(rows.length == 0){
                dialogService.alertInfo("error", $scope.listJs.msg5);
                return;
            }
            var jobStatus = rows[0].jobStatus;
            if (jobStatus == '1') {
                dialogService.alertInfo("info", $scope.listJs.msg6);
                return;
            }else if (jobStatus == '7'){
                dialogService.alertInfo("info", $scope.listJs.msg9);
                return;
            }else {
                clearCacheService.issuedCancelAll(rows[0].id, function (data) {
                    dialogService.alertInfo("success", $scope.listJs.msg11);
                    gridService.refresh();
                });
            }

        };
        gridService.setPlaceholder();
        });
    };
});