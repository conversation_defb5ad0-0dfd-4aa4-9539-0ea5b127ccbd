<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
			<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 13px; font-weight: bold;">【{{clearCacheAddHtml.instructions}}】</h1>
					<span style='position: absolute; right: 10px; top: 8px; color: #666666; cursor: pointer;'></span>
			</div>
			<div>
				<table class="table table-hovers" style="margin-bottom: 0px;">
					<tr>
						<td style="width: 15%; text-align: right;">{{clearCacheAddHtml.jobName}}<span
							style='color: #e02222'>*</span></td>
						<td style="width: 30%;">
							<input type="text" name="jobName" maxlength=30 ng-model="operationJob.jobName"
								   ng-readonly="readOnly"
								   ng-disabled="disabled" validator="required,maxlength=30" message-id="jobName" style="width: 268px;"/>
							<span id="jobName" class="help-inline"> </span>
						</td>
						<td style="width: 15%; text-align: right;">{{clearCacheAddHtml.instructions}}<span
							style='color: #e02222'>*</span></td>
						<td style="width: 30%;">
							<select class="form-control" name="operateInstructList" ng-options="m.type as m.name for m in operateInstructList" 
							ng-model="operationJob.operateType"
								validator="required" 
								ng-disabled="readOnly"
								message-id="operateInstructList" style="width: 280px;">
							<option value="">{{clearCacheAddHtml.choose}}</option>
							</select><span id="operateInstructList" class="help-inline"></span>
						</td>
					</tr>
					<tr style="border: 0px;" ng-if="operationJob.operateType==19">
						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.appPakage}}<span
								style='color: #e02222'>*</span></td>
						<td style="width: 30%;border: 0px;">
							<input type="text" name="appPakage" maxlength=125 ng-model="operationJob.appPakage"
								   ng-readonly="readOnly"
								   ng-disabled="disabled" validator="required,maxlength=125" message-id="appPakage" style="width: 268px;"/>
							<span id="appPakage" class="help-inline"> </span>
						</td>

						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.appActivity}}<span
								style='color: #e02222'>*</span></td>
						<td style="width: 30%;border: 0px;">
							<input type="text" name="appActivity" maxlength=125 ng-model="operationJob.appActivity"
								   ng-readonly="readOnly"
								   ng-disabled="disabled" validator="required,maxlength=125" message-id="appActivity" style="width: 268px;"/>
							<span id="appActivity" class="help-inline"> </span>
						</td>
					</tr>
					<tr style="border: 0px;" ng-if="operationJob.operateType==19">
						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.appPwd}}<span
								style='color: #e02222'>*</span></td>
						<td style="width: 30%;border: 0px;">
							<input type="text" name="appPwd" maxlength=4 minlength="4" ng-model="operationJob.appPwd"
								   ng-readonly="readOnly"
								   ng-disabled="disabled" validator="required,maxlength=4" message-id="appPwd" style="width: 268px;"/>
							<span id="appPwd" class="help-inline"> </span>
						</td>

					</tr>
					<tr style="border: 0px;">
						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.manufacturer}}<span
							style='color: #e02222'>*</span></td>
						<td style="width: 30%;border: 0px;">
							<select class="form-control" name="factoryList" ng-options="m.id as m.name for m in factoryList" ng-model="operationJob.manufacturerId"
								validator="required" ng-change="changeFactory(operationJob.manufacturerId)"
								ng-disabled="readOnly"
								message-id="manufacturerId" style="width: 280px;">
							<option value="">{{clearCacheAddHtml.choose}}</option>
						</select><span id="manufacturerId" class="help-inline"></span>
						</td>
						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.termType}}<span
							style='color: #e02222'>*</span></td>
						<td style="width: 30%;border: 0px;">
							<select class="form-control" name="termTypeList" ng-options="m.code as m.name for m in termTypeList" ng-model="operationJob.termTypeCode"
								validator="required" ng-change="collectionInit()"
								ng-disabled="readOnly"
								message-id="termTypeList" style="width: 280px;">
							<option value="">{{clearCacheAddHtml.choose}}</option>
						</select><span id="termTypeList" class="help-inline"></span>
						</td>
					</tr>
					<tr style="border: 0px;">
						<td style="width: 15%; text-align: right;border: 0px;">{{clearCacheAddHtml.releaseType}}<span
							style='color: #e02222'>*</td>
						<td style="width: 30%;border: 0px;">
							<select class="form-control" name="releaseTypeList" ng-model="operationJob.releaseType"
								ng-readonly="readOnly" ng-disabled="readOnly"
								ng-options="m.type as m.name for m in releaseTypeList"
								ng-change="changeRelease(operationJob.releaseType)"
								validator="required" message-id="releaseTypeList" style="width: 280px;">
							<option value="">{{clearCacheAddHtml.choose}}</option>
							</select><span id="releaseType" class="help-inline"> </span>
						</td>
						<td style="width: 15%;border: 0px; text-align: right;" ng-show="operationJob.releaseType!=1"><span
								style='color: #e02222'></span></td>
						<td style="width: 30%;border: 0px;" ng-show="operationJob.releaseType!=1">
						</td>
						<td style="width: 15%;border: 0px; text-align: right;" ng-show="operationJob.releaseType==1">{{clearCacheAddHtml.insName}}<span
								style='color: #e02222'>*</td>
						<td style="width: 30%; border: 0px;" ng-show="operationJob.releaseType==1">
							<input type="text" name="ins_cd" ng-model="operationJob.insId" ng-show="false"
								   ng-disabled="disabled"
								   message-id="insId"/>
							<div class="input-append input-group" ng-click="openInsDialog()">
								<input type="text" name="insName" ng-model="operationJob.insName" readonly
									   valid-method="watch" ng-disabled="disabled" class="span6 orginput dropdown input-float"
									   message-id="insId" style="width: 280px;"/>
								<button class="choice btn" type="button"  ng-disabled="disabled">
								</button>
							</div>
							<span id="insId" class="help-inline"></span>
						</td>
					</tr>
					<tr style="border: 0px;" ng-show="operationJob.releaseType==1">
						<td style="width: 15%; text-align: right;border: 0px;" >{{clearCacheAddHtml.group}}</td>
						<td style="width: 30%; line-height:40px;border: 0px;" >
							<div class="controls" style="margin-left: 0px;">
								<label class="radio line">
									<input type="radio" name="isGroupUpdate" ng-model="operationJob.isGroupUpdate" validator="required"
										   class="ace" message-id="isGroupUpdate" value="2"
										   required-error-message="{{clearCacheAddHtml.groupType}}" /><span class="lbl">All</span>
								</label>
								<label class="radio line">
									<input type="radio" name="isGroupUpdate" ng-model="operationJob.isGroupUpdate"
										   validator="required" class="ace" message-id="isGroupUpdate" value="0"
										   required-error-message="{{clearCacheAddHtml.groupType}}"/><span class="lbl">{{clearCacheAddHtml.appoint}}</span>
								</label>
								<label class="radio line">
									<input type="radio" name="isGroupUpdate" ng-model="operationJob.isGroupUpdate" validator="required"
										   class="ace" message-id="isGroupUpdate" value="1"
										   required-error-message="{{clearCacheAddHtml.groupType}}" /><span class="lbl">{{clearCacheAddHtml.exclude}}</span>
								</label>
								<span id="isGroupUpdate" class="help-inline"></span>
							</div>
						</td>
					</tr>
					<tr style="border: 0px;" ng-show="operationJob.releaseType==2">
						<td style="width: 15%; text-align: right; border: 0px;">{{clearCacheAddHtml.termCol}}<span
								style='color: #e02222'>*</td>
						<td style="width: 85%; border: 0px;" colspan="3" >
							<button class="btn  blue" type="button" ng-click="showTermSeq()">
								<i class="icon-plus"></i>{{clearCacheAddHtml.add}}
							</button>
							<button class="btn  blue" type="button"
									ng-click="openSelectExcel()" has-permission="clearCache_import">
								<i class="icon-upload"></i>{{clearCacheAddHtml.excel}}
							</button>
							<button class="btn  blue" type="button" ng-click="deleteTerm()">
								<i class="icon-remove"></i>{{clearCacheAddHtml.del}}
							</button>
							<button class="btn  blue" type="button" ng-click="clearTerm()">
								<i class="icon-edit"></i>{{clearCacheAddHtml.clean}}
							</button>
						</td>
					</tr>
					<tr style="border: 0px;" ng-show="operationJob.releaseType==2">
						<td style="width: 15%; text-align: right; border: 0px;">{{clearCacheAddHtml.termCol}}<span
							style='color: #e02222'>*</td>
						<td style="width: 85%; border: 0px;" colspan="3" >
							<select class="span6" id="tout_acct_list" name="tout_acct_list"
									multiple="multiple" style="height: 200px;">
							</select>

						</td>
					</tr>
					<tr style="border: 0px;" ng-show="errorLength != 0">
						<td style="width: 15%; text-align: right; border: 0px;">{{clearCacheAddHtml.errorCol}}<span
							style='color: #e02222'></td>
						<td style="width: 85%; border: 0px;" colspan="3" >
							<select class="span6" id="tout_error_list" name="tout_error_list"
									multiple="multiple" style="height: 200px;">
							</select>
						</td>
					</tr>
				</table>
				<table class="table table-hovers" style="margin-bottom: 0px;">
					<tr style="border: 0px;" ng-show="operationJob.isGroupUpdate !=2 && operationJob.releaseType==1">
						<td style="width: 15%; text-align: right; border: 0px;">{{clearCacheAddHtml.groupName}}</td>
						<td style="width: 100%; border: 0px;">
							<div class="terminal-check-container controls no-margin"
								 style="width: 80%;">
								<label class="checkbox">
									<input type="checkbox"
										   class="ace" name="allCheck"
										   ng-click="selectAll(termGroup.id,detail)"
										   value="{{terminalGroupList.chooseAll}}" id="allCheck" /><span
										class="lbl">{{clearCacheAddHtml.all}}</span>
								</label> <br />
								<label class="checkbox line margin-normal"
									   ng-repeat="termGroup in terminalGroupList">
									<input type="checkbox" class="ace" ng-value="termGroup.id"
										   ng-checked="formatCheck(termGroup.id,detail)"
										   name="checkVal" /> <span
										class="lbl" ng-bind="termGroup.name"></span>
								</label>
							</div> <span id="termGroup" class="help-inline" style="display: none;"></span>
						</td>

					</tr>
				</table>
			</div>
			</form>
		</div>
	</div>
</div>
<div style="padding-left:5.5%" ng-show="!disabled" form-foot
	goback="$state.go('home.operationJob.list')" submit="form.submit(Form)"
	reset="form.reset(Form)"></div>
	
	