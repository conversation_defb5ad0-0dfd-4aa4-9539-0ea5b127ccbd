'use strict';
define(function () {
    return function ($scope, launcherIssuedService,$state, dialogService,formService,BaseApi,organizationService,terminalGroupService,gridService ) {
        $scope.$parent.thirdTitle = "任务新增";
        $scope.readOnly = false;
        var parentScope =$scope ;
        var domain = window.location.href.split("#")[0];
        var baseUrl=domain.substr(0,domain.length-1);
        var launcerInsId="";
        $scope.changeFactory = function (launcherCode) {
        	var launcherList = $scope.launcherList;
    		for(var i=0;i<launcherList.length;i++){
    			if(launcherList[i].launcherCode == launcherCode){
    				launcerInsId = launcherList[i].insId;	
    		    }
    		}
            $scope.driverJob.insId = "";
            $scope.driverJob.insName = "";
            $scope.terminalGroupList="";
       };
       BaseApi.query("/dict/type/messionType",{}, function (data) {
 		 	$scope.releaseTypeList = data;
 	   });
       BaseApi.query("/launcherIssued/app",{}, function (data) {
  		 	$scope.launcherList = data;
  	 	});
        $scope.form = formService.form(function(){
        	 if($scope.driverJob.releaseTime > $scope.driverJob.validDate) {
	        		dialogService.alertInfo("info", "起始日期不能大于截至日期");
	        		return;
	        	}
        	 if($scope.driverJob.releaseType==1){
        		 if($scope.driverJob.insName == "" || $scope.driverJob.insName== null){
        			 dialogService.alertInfo("error", "请选择机构名称");
        			 return ;
        		 }
        		 if($scope.driverJob.groupId == "" || $scope.driverJob.groupId == null){
    				 dialogService.alertInfo("error", "请选择终端组别");
    				 return ;
    			 }
        	 }
        	 if($scope.driverJob.releaseType==2){
        		 if($scope.driverJob.releaseType==2){
	                  	var collections ="";
	                 	var selectObj = angular.element("#tout_acct_list").get(0);
	                 	var options = selectObj.options;
	                 	if(options.length == 0) {
	                 		dialogService.alertInfo("info", "终端集合不能为空！");
	                         return;
	                 	}
	                 	for(var i = 0;i < options.length;i++) {
	                 		collections += options[i].text+",";
	                 	}
                 	$scope.driverJob.collection = collections.substring(0,collections.length-1);
             	 }
        	 }
        	launcherIssuedService.addlauncherIssued($scope.driverJob, function (data) {
                dialogService.alertInfo("success", "新增成功");
                $state.go("home.launcherIssued.list");
            });
        }); 
        $scope.showTermSeq = function(){
        	var param  = {"launcherCode":$scope.driverJob.appCode};
          	BaseApi.query("/launcherIssued/app",param, function (data) {
          		var insId =  data[0].insId;
            	if($scope.driverJob.appCode == null || $scope.driverJob.appCode == '' || $scope.driverJob.appCode == undefined){
            		dialogService.alertInfo("error", "请先选择Launcher参数名称");
            		return false;
            	}
                dialogService.openDialog({
                    template: "modules/taskScheduling/launcherIssued/showTermSeqList.html",
                    width: '60%',
                    controller: function ($scope,dialogService,$timeout) {
                 	   $scope.app={};
                 	   $timeout(function(){
                 		   gridService.initTable({
                 			    url:"/terminal?termStatus=1&merchantStatus=1",
                                scope: $scope,
                                operator:false,
                                uniqueId: 'id',
 							    columns: [
                                  {field: 'state',checkbox: 'true'},
                                  {field: "termSeq", title: "终端序列号"},
                                  {field: "insName", title: "机构"},
                                  {field: "termMfrName", title: "终端厂商"},
                                  {field: "termTypeName", title: "终端型号"},
                                  {field: "merchantName", title: "商户名称"}
                               ]
                            });
                 	    },100)
                 	    $scope.serachTermInfo = function () {
                 		   gridService.search($scope.condition);
                        };
                        $scope.resetTermInfo = function () {
                  		   $scope.condition={};
                  		   gridService.search();
                        };
                        $scope.openOrganization = function () {
                            organizationService.openOrganization(function (value) {
                                if (!$scope.condition) {
                                    $scope.condition = {};
                                }
                                $scope.condition.insId = value.id;
                                $scope.condition.insName = value.name;
                            },launcerInsId);
                        };
                        $scope.ok = function () {
                   		    var rows= $("#table").bootstrapTable("getSelections");
                   		     if (rows.length == 0) {
                                 dialogService.alertInfo("info", "请选择记录！");
                                 return;
                             }
                   		     var ids = "";
                   		     if(angular.element("#tout_acct_list option").size()==0){
                     		 for (var index in rows) {
                     		 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                              		+rows[index].termSeq +"</option>");
                     		 }
                            
                   		     }else{
                     		 for (var index in rows) {
                     		 var j =0;
                     		 angular.element("#tout_acct_list option").each(function () {
                     			 if(rows[index].termSeq==$(this).val()){
                     				 return false;
                     			 }
                     			 j++;
                     	       });
                         		 if(j==angular.element("#tout_acct_list option").size()){
                         			 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                                       		+rows[index].termSeq +"</option>");
                         		 }
                     		 }
                     	 }
                     	  $scope.closeThisDialog(0);
                        };
                    }
                });
      	 	});
        }
        $scope.cleanTermSeqCollection = function(){
        	$scope.driverJob.collection = "";
        }
        $scope.openInsDialog = function () {
        	if(launcerInsId == ""){
        		dialogService.alertInfo("info", "请先选择launcher参数！");
                return;
        	}
            organizationService.openOrganization(function (value) {
                if (!$scope.driverJob) {
                    $scope.driverJob = {};
                }
                $scope.driverJob.insId = value.id;
                $scope.driverJob.insName = value.name;
                BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
    	   		 	$scope.terminalGroupList = data;
    	   	 	});
            },launcerInsId);
        };
//        BaseApi.query("/terminalGroup/selectGroup",{}, function (data) {
//   		 	$scope.terminalGroupList = data;
//   	 	});
        $scope.deleteTerm = function () {
        	var oSelect = angular.element("#tout_acct_list");
        	var options = oSelect.option;
        	var termSeqs = $("#tout_acct_list option:selected").val();
         	if(termSeqs == undefined || termSeqs == null || termSeqs == ""){
         		dialogService.alertInfo("success", "请至少选择一条记录！");
         		return ;
         	}
			$("#tout_acct_list option:selected").remove();
        }
        $scope.clearTerm = function () {
        	var selectObj = angular.element("#tout_acct_list").get(0);
         	var options = selectObj.options;
         	if(options.length == 0) {
         		dialogService.alertInfo("success", "当前没有可清空的终端信息！");
         		return ;
         	}
       	 	 dialogService.openConfirm("确认要清空当前终端集合的数据吗？", function () {
       		 $("#tout_acct_list option").remove();
       		 dialogService.closeConfirm();
       	 	 });
        }
    };
});