'use strict';
define(function () {
    return function ($scope, driverJobService,toolsService,$stateParams, Upload, terminalTypeService, factoryService, $state, dialogService, formService, BaseApi, organizationService, terminalGroupService, gridService) {
        $scope.$parent.thirdTitle = "Task Copy";
        $scope.jobActionType = 1;
        $scope.readOnly = false;
        var parentScope = $scope;
        var domain = window.location.href.split("#")[0];
        var baseUrl = domain.substr(0, domain.length - 6);
        //数据初始化
        $scope.driverJob = {};
        $scope.termTypeArrWeb = []; //终端型号数组
        $scope.driverJob.updateMethod = 0;
        $scope.driverJob.isRealUpdate = 0;
        $scope.errorLength = 0;
        $scope.groupIdArr = [];

        var driverJobAllList = []; //存储软件列表
        var dependents = [];//存储可依赖软件列表
        //字典列表
        BaseApi.get("/dict/types/driver_type_two,messionType,job_action", {}, function (data) {
            $scope.driverTypeList = data.data1;//软件类型
            $scope.releaseTypeList = data.data2;//发布方式
            $scope.actionTypeListBcm = data.data3; //操作类型
            $scope.actionTypeList = data.data3;
        });
        BaseApi.get("/dict/types/terminal_type,only_update_code,cup_conn_mode,buss_type", {}, function (data) {
            $scope.terminalTypeList = data.data1;//终端类型
            $scope.onlyUpdateCode = data.data2;//收单应用集合
            $scope.cupConnModeList = data.data3;
            $scope.bussTypeList = data.data4;
            //厂商列表
            BaseApi.query("/factory/type", {}, function (data1) {
                $scope.factoryList = data1;
            });
            driverJobService.selectByPrimaryKey($stateParams.id, function (data1) {
                $scope.driverJob = data1;

                if(data1.dccSupFlag != 'Y' || data1.dccSupFlag != 'N'){
                    $scope.driverJob.dccSupFlag = "";
                }
                $scope.driverJobApps = data1.driverJobApps;
                $scope.typeDetail = [];
                if(data1.termTypeArr != null){
                    $scope.typeDetail = data1.termTypeArr.split(',');
                }
                $scope.activateDetail = [];
                if(data1.activateTypes != null){
                    $scope.activateDetail = data1.activateTypes.split(',');
                }
                if(data1.validDateShow == "T"){
                    $scope.driverJob.validDateShow = true;
                    var currentDate = new Date();
                    var previousDay = new Date(currentDate);
                    previousDay.setDate(currentDate.getDate() - 1);
                    $scope.driverJob.releaseTime = toolsService.getDate(previousDay, 'yyyy-MM-dd hh:mm:ss');
                    var validDate = new Date();
                    validDate.setFullYear(validDate.getFullYear() + 5);
                    $scope.driverJob.validDate = toolsService.getDate(validDate,'yyyy-MM-dd hh:mm:ss');
                }else if(data1.validDateShow == "F"){
                    $scope.driverJob.validDateShow = false;
                    $scope.validDateChange()
                }

                //终端型号显示
                BaseApi.query("/terminalType/activateType/" + data1.manufacturerId, {}, function (data2) {
                    $scope.termTypeList = data2;
                    TermTypeMult();
                });

                if ($scope.driverJob.isShowNotice == 0) {
                    $scope.driverJob.updateMethod = 0;
                    $scope.driverJob.isRealUpdate = 0;
                } else if ($scope.driverJob.isShowNotice == 1) {
                    $scope.driverJob.updateMethod = 0;
                    $scope.driverJob.isRealUpdate = 1;
                } else if ($scope.driverJob.isShowNotice == 2) {
                    $scope.driverJob.updateMethod = 2;
                    $scope.driverJob.isRealUpdate = 0;
                } else if ($scope.driverJob.isShowNotice == 3) {
                    $scope.driverJob.updateMethod = 2;
                    $scope.driverJob.isRealUpdate = 1;
                }

                //发布方式
                if (data1.releaseType == 1) {
                    if(data1.groupIds != null){
                        $scope.detail = $.map(data1.groupIds.split(','), function(value){
                            return parseInt(value);
                        });
                    }
                    BaseApi.query("/terminalGroup/selectGroupByInsId/" + data1.insId, {}, function (data2) {
                        $scope.terminalGroupList = data2;
                    });
                }else {
                    if (data1.collection) {
                        var collections = data1.collection.split(",");
                        var selectObj = angular.element("#tout_acct_list");
                        for (var i = 0; i < collections.length; i++) {
                            selectObj.append("<option value='" + collections[i] + "'>"
                                + collections[i] + "</option>");
                        }
                    }
                }

                /**
                 * 软件列表处理
                 */
                for(var i = 0,len = $scope.driverJobApps.length;i<len;i++){
                    //console.log(driverJobApps[i])
                    $scope.driverJobApps[i].dependAppIdArr = [];
                    if($scope.driverJobApps[i].dependAppIds != "" && $scope.driverJobApps[i].dependAppIds != null){
                        $scope.driverJobApps[i].dependAppIdArr = $scope.driverJobApps[i].dependAppIds.split(",");
                        $scope.driverJobApps[i].isDependent = true;

                    }
                    dependents.push({'dependAppId':$scope.driverJobApps[i].dependAppId,'appName':$scope.driverJobApps[i].appName})
                    formatDriverJobApp($scope.driverJobApps[i])
                }
                changeDependents();

            });
        });
        //处理单个软件信息 包括对应列表的添加
        function formatDriverJobApp(driverJobApp) {

            if (driverJobApp.jobType != null && driverJobApp.jobType != "") {
                //判断是否为应用商店应用
                if (driverJobApp.jobType == "9") {
                    jobActionCheck(driverJobApp);
                    //应用商店的软件列表和版本列表处理
                    if(driverJobAllList.length == 0){
                        BaseApi.query("/apkInfo/getPubApkList", {"factoryId": $scope.driverJob.manufacturerId}, function (data2) {
                            driverJobAllList = data2;
                            driverJobApp.driverList = data2;
                            driverJobApp.appVersionList = [];
                            //查找选择应用的appId，再次遍历driverJobAllList,查找过滤同一code的不同版本
                            for (var i = 0,len = data2.length; i < len; i++) {
                                if (driverJobApp.appCode == data2[i].appCode) {
                                    driverJobApp.appVersionList.push(data2[i]);
                                    driverJobApp.termTypeNames = data2[i].termTypeNameStr;
                                    driverJobApp.termTypeCodes = data2[i].termTypeCodeStr;
                                    break;
                                }
                            }
                        });
                    }else{
                        driverJobApp.driverList = driverJobAllList;
                        driverJobApp.appVersionList = [];
                        //查找选择应用的appId，再次遍历driverJobAllList,查找过滤同一code的不同版本
                        for (var i = 0,len = driverJobAllList.length; i < len; i++) {
                            if (driverJobApp.appCode == driverJobAllList[i].appCode) {
                                driverJobApp.appVersionList.push(driverJobAllList[i]);
                                driverJobApp.termTypeNames = driverJobAllList[i].termTypeNameStr;
                                driverJobApp.termTypeCodes = driverJobAllList[i].termTypeCodeStr;
                                break;
                            }
                        }

                    }
                    //操作为常规更新时 查找应用支持的终端型号
                } else {
                    //非应用商店软件处理
                    var dccSupFlag = $scope.driverJob.dccSupFlag;
                    if($scope.driverJob.dccSupFlag != 'Y'){
                        dccSupFlag = 'N';
                    }
                    //应用列表driverList获取; 应用code查找
                    BaseApi.query("/driver/getDriverListByType", {"type": driverJobApp.jobType,
                        "factoryId": $scope.driverJob.manufacturerId
                        ,"dccSupFlag":dccSupFlag}, function (data2) {
                        driverJobApp.driverList = data2;
                        for (var i = 0,len=data2.length; i < len; i++) {
                            if (data2[i].id == driverJobApp.appId) {
                                driverJobApp.appCode = data2[i].code;
                                jobActionCheck(driverJobApp);
                                break;
                            }
                        }
                    });

                    BaseApi.query("/driverUpload/getAppVersions",{"appId":driverJobApp.appId,"factoryId": $scope.driverJob.manufacturerId}, function (data) {
                        driverJobApp.appVersionList = data;
                        if (driverJobApp.jobAction != "6") {
                            for (var i = 0, len = driverJobApp.appVersionList.length; i < len; i++) {
                                if (driverJobApp.appVersionList[i].id == driverJobApp.appVersionId) {
                                    driverJobApp.appVersion = driverJobApp.appVersionList[i].appVersion;
                                    driverJobApp.appVersionName = driverJobApp.appVersionList[i].appVersionName;
                                    driverJobApp.termTypeNames = driverJobApp.appVersionList[i].termTypeNameStr;
                                    driverJobApp.termTypeCodes = driverJobApp.appVersionList[i].termTypeCodeStr;
                                    break;
                                }
                            }
                        }
                    });



                }
            }

            //操作判断 非更新操作时隐藏版本和支持的终端型号
            if (driverJobApp.jobAction != "6") {
                driverJobApp.jobActionType = 1;
            } else {
                driverJobApp.jobActionType = 0;
                driverJobApp.appVersionId = "";
                driverJobApp.termTypeNames = "";
                driverJobApp.appVersionName = "";
            }
            jobActionCheck(driverJobApp);
        };

        //厂商切换
        $scope.changeFactory = function (factoryId) {
            $scope.driverJobApps = [{isDependent:false,dependents:[],dependAppIdArr:[]}];
            dependents = [];
            driverJobAllList = [];
            if (factoryId == null || factoryId == '' || factoryId == undefined) {
                $scope.termTypeList = [];
                return;
            } else {
                BaseApi.query("/terminalType/activateType/" + factoryId, {}, function (data) {
                    $scope.termTypeList = data
                });
            }
            TermTypeMult();
            $scope.collectionInit();
        };
        //型号修改 重置软件列表
        $scope.changeTermType = function(){
            $scope.driverJobApps = [{isDependent:false,dependents:[],dependAppIdArr:[]}];
            dependents = [];
            $scope.collectionInit();
        }//软件中终端型号 多选控件页面刷新
        var TermTypeMult= function(){
            $('#termType_inp').chosen();
            $scope.$watch('termTypeList',function (newVal) {
                setTimeout(function(){
                    $('#termType_inp').trigger("liszt:updated");
                    $('#termType_inp').chosen();
                },0)
            })
        }
        //终端型号控件初始化
        TermTypeMult();
        $scope.validDateChange = function(){
            if(!$scope.driverJob.validDateShow){
                var currentDate = new Date();
                var previousDay = new Date(currentDate);
                previousDay.setDate(currentDate.getDate() - 1);
                $scope.driverJob.releaseTime = toolsService.getDate(previousDay, 'yyyy-MM-dd hh:mm:ss');
                var validDate = new Date();
                validDate.setFullYear(validDate.getFullYear()+5);
                $scope.driverJob.validDate =toolsService.getDate(validDate,'yyyy-MM-dd hh:mm:ss')
            }
        }

        // 重置终端集合
        $scope.collectionInit = function(){
            $("#tout_acct_list option").remove();
            $("#tout_error_list option").remove();
            $scope.errorLength = 0;
        };
        //根据不同情况显示不同的软件操作
        var jobActionCheck = function(driverJobApp){
            var jobType = driverJobApp.jobType;
            if (jobType == 2 || jobType == 3 || jobType == 5 ) {
                driverJobApp.actionTypeList = [{type: "7", name: "Regular updates"}];
            } else if(jobType == 1 || jobType == "a") {
                driverJobApp.actionTypeList = [{type: "7", name: "Regular updates"},{type: "8", name: "Version back"}];
            }else {
                if(driverJobApp.appCode == ""){
                    driverJobApp.actionTypeList = $scope.actionTypeListBcm;
                    return;
                }
                //防止收单应用错传到其他软件类型下，执行卸载操作。
                var checkResult = false;
                for(var i = 0,len=$scope.onlyUpdateCode.length;i<len;i++){
                    if($scope.onlyUpdateCode[i].name == driverJobApp.appCode){
                        checkResult = true;
                        break;
                    }
                }
                if ( checkResult) {
                    driverJobApp.actionTypeList = [{type: "7", name: "Regular updates"},{type: "8", name: "Version back"}];
                }else{
                    driverJobApp.actionTypeList = $scope.actionTypeListBcm;
                }
            }
        }

        //软件类型
        $scope.changeDriverType = function (driverJobApp) {
            driverJobApp.termTypeNames = "";
            driverJobApp.appCode = "";
            driverJobApp.appVersionName = "";
            var jobType = driverJobApp.jobType;
            if (jobType != null && jobType != "") {

                jobActionCheck(driverJobApp);
                var manufacturerId = $scope.driverJob.manufacturerId;
                var dccSupFlag = $scope.driverJob.dccSupFlag;
                //dcc交易过滤为无和不支持时 仅筛选出不支持dcc的软件
                if(dccSupFlag != 'Y'){
                    dccSupFlag = 'N';
                }
                var cupConnMode = $scope.driverJob.cupConnMode;
                if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
                    dialogService.alertInfo("info", "Please select the terminal manufacturer");
                    return;
                }
                getTermTypeArrWeb();
                if($scope.termTypeArrWeb.length == 0){
                    dialogService.alertInfo("info", "Please select the terminal model");
                    return;
                }
                if (jobType == 9 ) {
                    if(driverJobAllList.length == 0){
                        driverJobApp.appCode = "";
                        driverJobApp.driverList = [];
                        BaseApi.query("/apkInfo/getPubApkList", {"factoryId": $scope.driverJob.manufacturerId}, function (data) {
                            driverJobAllList = data;
                            driverJobApp.driverList = data;
                        });
                    }else{
                        driverJobApp.driverList = driverJobAllList;
                    }

                } else {
                    BaseApi.query("/driver/getDriverListByType", {
                        "type": jobType,
                        "factoryId": manufacturerId,
                        "dccSupFlag":dccSupFlag,
                        "cupConnMode":cupConnMode,
                    }, function (data) {
                        driverJobApp.driverList = data;
                    });
                    if(["0","1","a"].indexOf(jobType) > -1){
                        $scope.driverJob.validDateShow = false;
                        $scope.validDateChange()
                    }else {
                        $scope.driverJob.validDateShow = true;
                        var currentDate = new Date();
                        var previousDay = new Date(currentDate);
                        previousDay.setDate(currentDate.getDate() - 1);
                        $scope.driverJob.releaseTime = toolsService.getDate(previousDay, 'yyyy-MM-dd hh:mm:ss');
                        var validDate = new Date();
                        validDate.setFullYear(validDate.getFullYear() + 5);
                        $scope.driverJob.validDate = toolsService.getDate(validDate,'yyyy-MM-dd hh:mm:ss');
                    }
                }
            }
        }
        //软件名称
        $scope.changeApp = function (driverJobApp) {
            driverJobApp.termTypeNames = "";
            driverJobApp.appCode = "";
            driverJobApp.appVersionName = "";
            driverJobApp.appVersionList = [];
            if(driverJobApp.appId == null || driverJobApp.appId ==""){
                return;
            }
            if (driverJobApp.jobType == 9) {
                for (var i = 0; i < driverJobAllList.length; i++) {
                    if (driverJobAllList[i].id == driverJobApp.appId) {
                        driverJobApp.appName = driverJobAllList[i].appName;
                        driverJobApp.appCode = driverJobAllList[i].appCode;
                        driverJobApp.appVersionList.push(driverJobAllList[i]);
                        jobActionCheck(driverJobApp);
                        break;
                    }
                }
            } else {

                BaseApi.query("/driverUpload/getAppVersions",{"appId":driverJobApp.appId,"factoryId": $scope.driverJob.manufacturerId}, function (data) {
                    driverJobApp.appVersionList = data;
                });
                for (var i = 0; i < driverJobApp.driverList.length; i++) {
                    if (driverJobApp.driverList[i].id == driverJobApp.appId) {
                        driverJobApp.appName = driverJobApp.driverList[i].name;
                        driverJobApp.appCode = driverJobApp.driverList[i].code;
                        jobActionCheck(driverJobApp);
                        break;
                    }
                }
            }

            var jobAppsLength = $scope.driverJobApps.length;
            var j = 0;
            for(var i=0;i<jobAppsLength;i++){
                if( driverJobApp.appCode == $scope.driverJobApps[i].appCode
                ){
                    j++;
                }
            }
            if(j>=2){
                dialogService.alertInfo("info", "Software repeat, please reselect");
                driverJobApp.appVersionId = "";
                driverJobApp.appId = "";
                driverJobApp.appCode = "";
                return;
            }
            addDependentList(driverJobApp);
            changeDependents();
        };
        //软件版本号
        $scope.changeAppVersion = function (driverJobApp) {
            var appVersionId = driverJobApp.appVersionId;
            var appId = driverJobApp.appId;
            var manufacturerId = $scope.driverJob.manufacturerId;
            if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
                return;
            }
            if (appId == null || appId == '' || appId == undefined) {
                return;
            }
            if (appVersionId == null || appVersionId == '' || appVersionId == undefined) {
                return;
            }

            for (var i = 0; i < driverJobApp.appVersionList.length; i++) {
                if (driverJobApp.appVersionList[i].id == appVersionId) {
                    driverJobApp.appVersion = driverJobApp.appVersionList[i].appVersion;
                    driverJobApp.appVersionName = driverJobApp.appVersionList[i].appVersionName;
                    driverJobApp.termTypeNames = driverJobApp.appVersionList[i].termTypeNameStr;
                    driverJobApp.termTypeCodes = driverJobApp.appVersionList[i].termTypeCodeStr;
                    break;
                }
            }
            if(!checkTermTypeArr(driverJobApp)){
                driverJobApp.appVersionId = "";
                return;
            }
        }

        //获取终端列表
        $scope.showTermSeq = function () {

            var manufacturerId = $scope.driverJob.manufacturerId;
            if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
                dialogService.alertInfo("info", "Please select the terminal manufacturer！");
                return;
            }
            getTermTypeArrWeb();
            if ($scope.termTypeArrWeb.length == 0){
                dialogService.alertInfo("info", "Please select the terminal model！");
                return;
            }
            getActivateType();

            if ($scope.driverJobApps.length == 0) {
                dialogService.alertInfo("info", "Please select the new software！");
                return;
            }

            var termTypeCodes = $scope.termTypeArrWeb;
            var activateTypes = $scope.activateTypes;
            var cupConnMode = $scope.driverJob.cupConnMode;
            var bussType = $scope.driverJob.bussType;
            if(cupConnMode == null || cupConnMode == undefined){
                cupConnMode = "";
            }
            if(bussType == null || bussType == undefined){
                bussType = "";
            }
            var terminalTypeList =  $scope.terminalTypeList;
            var dccSupFlag =  $scope.driverJob.dccSupFlag;
            var showTermTypeList = [];
            for(var i =0,len =$scope.termTypeList.length;i<len;i++){
                if($.inArray($scope.termTypeList[i].code,termTypeCodes) >=0){
                    showTermTypeList.push($scope.termTypeList[i]);
                }
            }
            var showCupConnModeList = [];
            for(var i =0,len =$scope.cupConnModeList.length;i<len;i++){
                if(["",null,undefined].indexOf($scope.driverJob.cupConnMode) > -1
                    || $scope.cupConnModeList[i].type == $scope.driverJob.cupConnMode){
                    showCupConnModeList.push($scope.cupConnModeList[i]);
                }
            }
            var showBussTypeList = [];
            for(var i =0,len =$scope.bussTypeList.length;i<len;i++){
                if(["",null,undefined].indexOf($scope.driverJob.bussType) > -1
                    || $scope.bussTypeList[i].type == $scope.driverJob.bussType ){
                    showBussTypeList.push($scope.bussTypeList[i]);
                }
            }
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/showTermSeqList.html",
                width: '60%',
                controller: function ($scope, dialogService, $timeout) {
                    $scope.app = {};
                    $scope.showTermTypeList = showTermTypeList;
                    $scope.showCupConnModeList = showCupConnModeList;
                    $scope.showBussTypeList = showBussTypeList;
                    var url = "";
                    $scope.condition = {cupConnMode: cupConnMode,bussType:bussType};
                    if(cupConnMode == ""){
                        $scope.showCupConnMode = true;
                    }
                    if(bussType == ""){
                        $scope.showBussType = true;
                    }
                    if(activateTypes == undefined || activateTypes.length == 0) {
                        $scope.termTypeListTwo = terminalTypeList;
                        url = "/terminal?termMfrId=" + manufacturerId + "&termTypeCodes=" + termTypeCodes.toString()
                            + "&dccSupFlag=" + dccSupFlag;
                    }else{
                        $scope.termTypeListTwo = [];
                        for(var i =0,len =terminalTypeList.length;i<len;i++){
                            if($.inArray(terminalTypeList[i].type,activateTypes) >=0){
                                $scope.termTypeListTwo.push(terminalTypeList[i]);
                            }
                        }
                        url = "/terminal?termMfrId=" + manufacturerId + "&termTypeCodes=" + termTypeCodes.toString()
                            +"&activateTypes="+activateTypes.toString()+ "&dccSupFlag=" + dccSupFlag
                        ;
                    }

                    $timeout(function () {
                        gridService.initTable({
                            url: url,
                            scope: $scope,
                            operator: false,
                            uniqueId: 'id',
                            columns: [
                                {field: 'state', checkbox: 'true'},
                                {field: "termSeq", title: "Serial No."},
                                {field: "insName", title: "Organization"},
                                {field: "termMfrName", title: "Manufacturer"},
                                {field: "termTypeName", title: "Terminal Type"},
                            ]
                        });
                    }, 100)
                    $scope.searchTermInfo = function () {
                        gridService.search($scope.condition);
                    };
                    $scope.resetTermInfo = function () {
                        $scope.condition = {cupConnMode: cupConnMode,bussType:bussType};
                        gridService.search();
                    };
                    $scope.openOrganization = function () {
                        organizationService.openOrganization(function (value) {
                            if (!$scope.condition) {
                                $scope.condition = {};
                            }
                            $scope.condition.insId = value.id;
                            $scope.condition.insName = value.name;
                        });
                    };
                    $scope.ok = function () {
                        var rows = $("#table").bootstrapTable("getSelections");
                        if (rows.length == 0) {
                            dialogService.alertInfo("info", "Please select record！");
                            return;
                        }
                        var ids = "";
                        if (angular.element("#tout_acct_list option").size() == 0) {
                            for (var index in rows) {
                                angular.element("#tout_acct_list").append("<option value='" + rows[index].termSeq + "'>"
                                    + rows[index].termSeq + "</option>");
                            }

                        } else {
                            for (var index in rows) {
                                var j = 0;
                                angular.element("#tout_acct_list option").each(function () {
                                    if (rows[index].termSeq == $(this).val()) {
                                        return false;
                                    }
                                    j++;
                                });
                                if (j == angular.element("#tout_acct_list option").size()) {
                                    angular.element("#tout_acct_list").append("<option value='" + rows[index].termSeq + "'>"
                                        + rows[index].termSeq + "</option>");
                                }
                            }
                        }

                        $scope.closeThisDialog(0);

                    };
                }
            });
        }
        //获取发布的应用商店应用
        function getPubApkList(driverJobApp) {
            driverJobApp.appCode = "";
            driverJobApp.driverList = [];
            //名称相同时 合并软件
            BaseApi.query("/apkInfo/getPubApkList", {"factoryId": $scope.driverJob.manufacturerId}, function (data) {
                driverJobAllList = data;
                for (var i = 0; i < driverJobAllList.length; i++) {
                    var temp = true;
                    for (var j = 0; j < driverJobApp.driverList.length; j++) {
                        if (driverJobApp.driverList[j].code == driverJobAllList[i].code) {
                            temp = false;
                            break;
                        }
                    }
                    if (temp) {
                        driverJobApp.driverList.push(driverJobAllList[i]);
                    }
                }
            });

        }

        $scope.addSoftware = function () {
            if($scope.driverJobApps.length >= 5){
                dialogService.alertInfo("info", "A single task supports up to 5 pieces of software！");
                return;
            }
            $scope.driverJobApps.push({isDependent:false,dependents:[],dependAppIdArr:[]});
        }
        $scope.removeSoftware = function (index) {
            //deleteDependIds($scope.driverJobApps[index].dependAppId)
            deleteDependentList($scope.driverJobApps[index])
            $scope.driverJobApps.splice(index,1);

        }
        //检测软件支持的终端类型是否符合 不符合返回false
        function checkTermTypeArr(driverJobApp){
            if (driverJobApp.jobAction != '6') {
                if(driverJobApp.termTypeCodes.length == 0){
                    dialogService.alertInfo("error", "Terminal models are not supported for current applications！");
                    return false;
                }
                var termTypeArrWeb = driverJobApp.termTypeCodes.split(",");
                getTermTypeArrWeb();
                if (!isContained(termTypeArrWeb, $scope.termTypeArrWeb)) {
                    dialogService.alertInfo("error", "The current application support terminal model must include the selected terminal model！");
                    return false;
                }
            }
            return true;
        }
        //判断一个数组是否时另一数组的子集
        function isContained(arr,subArr){
            if(!(arr instanceof Array) || !(subArr instanceof Array)||(arr.length < subArr.length)){
                return false;
            }
            for(var i =0; i< subArr.length;i++){
                if(arr.indexOf(subArr[i]) == -1){
                    return false;
                }
            }
            return true;
        }
        $scope.selectAll = function(){
            if ($("#allCheck").attr("checked")) {
                $("input[name='checkVal']").attr("checked", true);
            } else {
                $("input[name='checkVal']").attr("checked", false);
            }
        }
        $scope.formatCheck=function(code,detail){
            if(detail == undefined){

            }else{
                return $.inArray(code,detail) != '-1';
            }

        }
        $scope.selectAllType = function(){
            if ($("#allTypeCheck").attr("checked")) {
                $("input[name='checkTypeVal']").attr("checked", true);
            } else {
                $("input[name='checkTypeVal']").attr("checked", false);
            }
        }
        $scope.formatTypeCheck=function(code,typeDetail){
            if(typeDetail == undefined){

            }else{
                return $.inArray(code,typeDetail) != '-1';
            }

        }
        $scope.getGroupIdArr = function (){
            $scope.groupIdArr = [];
            $("input:checkbox[name='checkVal']:checked").each(function(){
                $scope.groupIdArr.push($(this).val());
            })

        }
        var getTermTypeArrWeb = function (){
            $scope.termTypeArrWeb = [];
            $("input:checkbox[name='checkTypeVal']:checked").each(function(){
                $scope.termTypeArrWeb.push($(this).val());
            })

        }
        var getActivateType = function (){
            $scope.activateTypes = [];
            $("input:checkbox[name='checkActivateVal']:checked").each(function(){
                $scope.activateTypes.push($(this).val());
            })

        }
        $scope.form = formService.form(function () {
            if ($scope.driverJob.releaseType == 1) {
                if ($scope.driverJob.insName == "" || $scope.driverJob.insName == null) {
                    dialogService.alertInfo("error", "Please select the Organization name");
                    return;
                }
            }
            if ($scope.driverJob.releaseType == 2) {
                var collections = "";
                var selectObj = angular.element("#tout_acct_list").get(0);
                var options = selectObj.options;
                if (options.length == 0) {
                    dialogService.alertInfo("info", "The terminal set cannot be empty！");
                    return;
                }
                for (var i = 0; i < options.length; i++) {
                    collections += options[i].text + ",";
                }
                $scope.driverJob.collection = collections.substring(0, collections.length - 1);
            }

            //处理 上传的数据中应用商店的数据
            var length = $scope.driverJobApps.length;
            if(length == 0){
                dialogService.alertInfo("info", "The software list cannot be empty！");
                return;
            }
            for (var i = 0; i < length; i++) {
                $scope.driverJobApps[i].dependAppIds = $scope.driverJobApps[i].dependAppIdArr.toString();
            }

            $scope.driverJob.isShowNotice = parseInt($scope.driverJob.updateMethod) + parseInt($scope.driverJob.isRealUpdate);
            if($scope.driverJob.isGroupUpdate != 2){
                $scope.getGroupIdArr();
                if($scope.groupIdArr.length > 0){
                    $scope.driverJob.groupIds =  $scope.groupIdArr.toString();
                }else{
                    $scope.driverJob.groupIds = null;
                }
            } else{
                $scope.driverJob.groupIds = null;
            }
            getTermTypeArrWeb();
            if($scope.termTypeArrWeb.length == 0){
                dialogService.alertInfo("info", "The terminal model cannot be empty！");
                return;
            }
            getActivateType();
            $scope.driverJob.activateTypes = $scope.activateTypes.toString();

            $scope.driverJob.termTypeArr = $scope.termTypeArrWeb.toString();
            $scope.driverJob.driverJobApps = $scope.driverJobApps;
            if($scope.driverJob.validDateShow){
                $scope.driverJob.validDateShow = "T";
            }else{
                $scope.driverJob.validDateShow = "F";
            }
            driverJobService.addDriverJob($scope.driverJob, function (data) {
                if(data.status != 200){
                    if($scope.driverJob.validDateShow == "T"){
                        $scope.driverJob.validDateShow = true;
                    }else{
                        $scope.driverJob.validDateShow = false;
                    }
                    dialogService.alertInfo("info", data.msg);
                    return;
                }
                dialogService.alertInfo("success", "add success");
                $state.go("home.driverJob.list");
            });
        });

        $scope.cleanTermSeqCollection = function () {
            $scope.driverJob.collection = "";
        }

        $scope.openSelectExcel = function () {
            var manufacturerId = $scope.driverJob.manufacturerId;
            if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
                dialogService.alertInfo("info", "Please select the terminal manufacturer！");
                return;
            }
            getTermTypeArrWeb();
            if ($scope.termTypeArrWeb.length == 0){
                dialogService.alertInfo("info", "Please select the terminal model！");
                return;
            }
            var termTypeCodes = $scope.termTypeArrWeb.toString();
            getActivateType();
            var activateTypes = $scope.activateTypes;

            if(activateTypes == undefined || activateTypes == null ){
                activateTypes = "";
            }
            var cupConnMode = $scope.driverJob.cupConnMode;
            if(cupConnMode == undefined || cupConnMode == null ){
                cupConnMode = "";
            }
            var dccSupFlag = $scope.driverJob.dccSupFlag;
            var bussType = $scope.driverJob.bussType;
            if(bussType == undefined || bussType == null ){
                bussType = "";
            }
            var params = "?termMfrId=" + manufacturerId + "&termTypeCodes=" + termTypeCodes
                + "&activateTypes=" + activateTypes.toString()+ "&cupConnMode=" +cupConnMode
                + "&bussType="+ bussType + "&dccSupFlag="+ dccSupFlag;
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/driverJobExcel.html",
                controller: function ($scope) {
                    $scope.dialogTitle = "Serial No.导入";
                    $scope.readOnly = false;
                    $scope.submitFile = function () {
                        if (!$scope.file || !$scope.file.size) {
                            dialogService.alertInfo("success", "Please select file");
                            return;
                        }
                        var name = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
                        if (!(name == "xls" || name == "xlsx")) {
                            dialogService.alertInfo("info", "Please select the Excel file");
                            return;
                        }
                        Upload.upload({
                            url: baseUrl + '/uploadjob/importExcel'+ params,
                            data: {file: $scope.file},
                            method: 'POST'
                        }).then(function (resp) {
                            var collections = resp.data.data[0];

                            for (var i = 0,len = collections.length;i<len;i++) {

                                var j = 0;
                                angular.element("#tout_acct_list option").each(function () {
                                    if (collections[i] == $(this).val()) {
                                        return false;
                                    }
                                    j++;
                                });
                                if (j == angular.element("#tout_acct_list option").size()) {
                                    angular.element("#tout_acct_list").append("<option value='" + collections[i] + "'>"
                                        + collections[i] + "</option>");
                                }
                            }
                            var errorCollections = resp.data.data[1];
                            if(errorCollections.length != 0){
                                parentScope.errorLength = 1;
                            }
                            for (var i = 0,len = errorCollections.length;i<len;i++) {

                                var j = 0;
                                angular.element("#tout_error_list option").each(function () {
                                    if (errorCollections[i] == $(this).val()) {
                                        return false;
                                    }
                                    j++;
                                });
                                if (j == angular.element("#tout_error_list option").size()) {
                                    angular.element("#tout_error_list").append("<option value='" + errorCollections[i] + "'>"
                                        + errorCollections[i] + "</option>");
                                }
                            }

                            dialogService.alertInfo("success", "File uploaded successfully");
                        }, function (resp) {
                            dialogService.alertInfo("warning", "File upload failed");
                        }, function (evt) {
                        });
                        $scope.closeThisDialog(0);
                    };
                    $scope.downModel = function () {
                        var downloadUrl = baseUrl + '/uploadjob/fileDownload';
                        window.open(downloadUrl, '_blank');
                    };
                },
                closeByDocument: false
            });
        };

        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.driverJob) {
                    $scope.driverJob = {};
                }
                if($scope.driverJob.insId == value.id){
                    return;
                }
                $scope.driverJob.insId = value.id;
                $scope.driverJob.insName = value.name;
                BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
                    $scope.terminalGroupList = data;
                    $scope.detail = [];
                });
            });
        };
        $scope.deleteTerm = function () {
            var oSelect = angular.element("#tout_acct_list");
            var options = oSelect.option;
            var termSeqs = $("#tout_acct_list option:selected").val();
            if (termSeqs == undefined || termSeqs == null || termSeqs == "") {
                dialogService.alertInfo("success", "Please select at least one record！");
                return;
            }
            $("#tout_acct_list option:selected").remove();
        }
        $scope.clearTerm = function () {
            var selectObj = angular.element("#tout_acct_list").get(0);
            var options = selectObj.options;
            if (options.length == 0) {
                dialogService.alertInfo("success", "No terminal information is currently available for clearing！");
                return;
            }
            dialogService.openConfirm("Are you sure you want to empty the current terminal collection？", function () {
                $scope.collectionInit();
                $scope.cleanTermSeqCollection();
                dialogService.closeConfirm();
            });
        }
        $scope.changeRelease = function(releaseType){

            $scope.collectionInit();
            $scope.cleanTermSeqCollection();
            $scope.driverJob.insId = "";
            $scope.driverJob.insName = "";
            $scope.driverJob.isGroupUpdate = 2;

            $scope.groupIdArr = [];
            $scope.terminalGroupList = [];
            $scope.detail = [];
            if(releaseType==1){
                $scope.driverJob.activateType = "0";
            }
        }
        //修改依赖关系
        $scope.changeDepends = function (driverJobApp){
            changeDependents()
        }

        //查找依赖关系数组中是否存在某id
        var indexOfParamInList = function (ArrList,dependAppId) {

            for (var i = 0,len= ArrList.length;i<len;i++){
                if(ArrList[i].dependAppId == dependAppId){
                    return i;
                }
            }
            return -1;
        }
        //移除虚拟id为dependAppId的数据
        var removeParamInList = function (ArrList,dependAppId) {

            for (var i = 0,len= ArrList.length;i<len;i++){
                if(ArrList[i].dependAppId == dependAppId){
                    ArrList.splice(i,1);
                    return;
                }
            }
        }
        //查找针对某软件的可依赖列表
        var findDependentList = function (driverJobApp){
            //由于软件依赖关系不能造成回环，所以要对每个软件生成对应的可依赖列表

            driverJobApp.dependents = dependents.slice();

            //1.使用递归函数算出软件可依赖的id数组
            recursiveFunc(driverJobApp.dependents,driverJobApp.dependAppId);

        }
        //递归函数
        var recursiveFunc = function (dependents,dependAppId) {

            //1.判断该随机id是否存在数组中存在，不存在时返回
            if(indexOfParamInList(dependents,dependAppId) == -1){
                return;
            }
            //2.遍历软件列表
            for(var i =0,len1 = $scope.driverJobApps.length;i< len1;i++){
                //如果软件的依赖关系中 包含查找的dependAppId,继续递归查找该软件的随机id
                if($scope.driverJobApps[i].dependAppIdArr.indexOf(dependAppId) != -1){
                    recursiveFunc(dependents,$scope.driverJobApps[i].dependAppId);
                    removeParamInList(dependents,dependAppId);
                }
            }
            //3.删除被依赖的软件随机id
            removeParamInList(dependents,dependAppId);
            return;
        }

        //添加软件到可依赖列表中
        var addDependentList = function(driverJobApp){
            //1.随机生成id唯一标识该软件
            var dependAppId = Math.random().toString().slice(-6);

            //2.判断软件是否存在依赖，存在时 进行删除
            var index = checkDependentList(driverJobApp);
            //3.将新数据加到可依赖软件的数组中
            //不存在时，添加到数组中
            dependents.push({'dependAppId':dependAppId,'appName':driverJobApp.appName})

            //4.将随机id加入到driverJobApp中
            driverJobApp.dependAppId = dependAppId;

        }
        //判断是否存在
        var checkDependentList = function (driverJobApp) {
            //1.判断软件是否存在随机id,不存在时返回-1
            if(driverJobApp.dependAppId == undefined||driverJobApp.dependAppId == null||driverJobApp.dependAppId == ''){
                return -1;
            }
            //2.遍历可依赖软件列表
            for(var i = 0,len = dependents.length;i < len;i++){
                if(dependents[i].dependAppId == driverJobApp.dependAppId){
                    return i;
                }
            }
        }
        //删除可依赖列表中的依赖软件
        var deleteDependentList = function(driverJobApp){
            //1.判断软件是否存在随机id,不存在时直接返回
            if(driverJobApp.dependAppId == undefined||driverJobApp.dependAppId == null||driverJobApp.dependAppId == ''){
                return;
            }
            //2.遍历可依赖软件列表dependents
            for(var i = 0,len = dependents.length;i < len;i++){
                if(dependents[i].dependAppId == driverJobApp.dependAppId){
                    dependents.splice(i,1);
                    return;
                }
            }
        }
        //软件中多选控件页面刷新
        var changeDependents = function(){
            $('.chosen-select').chosen();
            $scope.$watch('driverJobApps',function (newVal) {
                for(var i = 0,len= $scope.driverJobApps.length;i< len;i++){
                    findDependentList($scope.driverJobApps[i]);
                }
                setTimeout(function(){
                    $('.chosen-select').trigger("liszt:updated");
                    $('.chosen-select').chosen();
                },0)
            })
        }
    };
});