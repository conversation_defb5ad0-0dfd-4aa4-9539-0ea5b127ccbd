'use strict';
define(function () {
    return function ($scope,$http,$state, dialogService, gridService, wifiService, BaseApi, toolsService) {
    	$http.get("modules/taskScheduling/driverJob/i18n/en/driverJobList.json").success(function(dataLan) {
        $scope.listJs = dataLan.listJs;
        $scope.listHtml = dataLan.listHtml;
        var user = BaseApi.getUser();
        BaseApi.query("/dict/type/job_action", {}, function (data) {
            $scope.actionTypeList = data;
            gridService.initTable({
                url: "/wifi",
                scope: $scope,
                detail:false,
                update: false,
                deletes: 'wifi_del',
                singleSelect: true,
                columns: [{
                    field: 'state',
                    checkbox: 'true'
                },
                    {
                        field: 'sn',
                        title: "Terminal SerNO"
                    },
                    {
                        field: 'ssID',
                        title: "CURRENT"
                    },
                    {
                        field: 'targetSSID',
                        title: "TARGET"
                    }]
            });
        });
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.wifi.view', {id: row.id});
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        BaseApi.query("/dict/type/job_type", {}, function (data) {
            $scope.jobStatusList = data;
        });
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.wifi.update', {id: row.id});
        };
        $scope.copyRow = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.listJs.copyRowMsg1);
                return;
            }else if (rows.length > 1) {
                dialogService.alertInfo("info", $scope.listJs.copyRowMsg2);
                return;
            }
            $state.go('home.wifi.copy', {id: rows[0].id});
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            if(row.releaseStatus == '2'){
                dialogService.alertInfo("success", $scope.listJs.deleteRowMsg1);
                return false;
            }
            dialogService.openConfirm($scope.listJs.deleteRowMsg2+" “" + row.sn + "”？", function (confirm) {
                wifiService.deleteDriverJob(row.id, function (data) {
                    dialogService.closeConfirm();
                    if (data.status != 200) {
                        dialogService.alertInfo("error", data.msg);
                        return "";
                    }
                    dialogService.alertInfo("success", $scope.listJs.deleteRowMsg3);
                    gridService.refresh();
                });
            });
        };







        $scope.driverJobUpdate = function(){
            dialogService.openConfirm($scope.listJs.driverJobUpdateMsg1,function() {
                dialogService.closeConfirm();
                wifiService.driverJobUpdate($scope.condition,function () {
                });
                dialogService.alertInfo("success", $scope.listJs.driverJobUpdateMsg2);
                gridService.refresh();
            });
        }
        gridService.setPlaceholder();
    	});
    };
});