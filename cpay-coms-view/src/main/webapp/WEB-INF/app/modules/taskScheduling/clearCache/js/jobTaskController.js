'use strict';
define(function () {
    return function ($scope,$http,$state,dialogService, gridService,clearCacheService,$stateParams,BaseApi,toolsService) {
    	$http.get("modules/taskScheduling/clearCache/i18n/en/jobTask.json").success(function(dataLan) {
        $scope.jobTaskJs = dataLan.jobTaskJs;
        $scope.jobTaskHtml = dataLan.jobTaskHtml;
        $scope.$parent.thirdTitle = $scope.jobTaskJs.thirdTitle;
        $scope.jobName = $stateParams.jobName;
        $scope.jobStatusList =[{type: "0", name: $scope.jobTaskJs.wait},{type: "1", name: $scope.jobTaskJs.success},{type: "2", name: $scope.jobTaskJs.pause}];
        BaseApi.query("/dict/type/operate_instruct",{}, function (data) {
            $scope.operateInstructList = data;
            gridService.initTable({
                url: "/clearCache/task?jobId=" + $stateParams.id,
                scope: $scope,
                operator: false,
                columns: [{
                    field: 'state',
                    checkbox: 'true'
                },
                    {
                        field: 'termSeq',
                        title: $scope.jobTaskJs.termSeq
                    }, {
                        field: 'operateStatus',
                        title: $scope.jobTaskJs.status,
                        formatter: function (value) {
                            var type='';
                            $.each($scope.jobStatusList,function(n,obj) {
                                if(obj.type==value) {
                                    type=obj.name;
                                }
                            });
                            return type;
                        }
                    },
                    {
                        field: 'operateCommand',
                        title: $scope.jobTaskJs.operateCommand,
                        formatter: function (value) {
                            var type='';
                            $.each($scope.operateInstructList,function(n,obj) {
                                if(obj.type==value) {
                                    type=obj.name;
                                }
                            });
                            return type;
                        }
                    }, {
                        field: 'insName',
                        title: $scope.jobTaskJs.insName
                    }, {
                        field: 'factoryName',
                        title: $scope.jobTaskJs.termMfrName
                    }, {
                        field: 'termTypeName',
                        title: $scope.jobTaskJs.termTypeName
                    },

                    {
                        field: 'recordCreateTime',
                        title: $scope.jobTaskJs.recordCreateTime,
                        formatter: function (value) {
                            // 解析新加坡时间（假设输入为 "2025-05-09 14:00:00"）
                            const [datePart, timePart] = value.split(' ');
                            const [year, month, day] = datePart.split('-');
                            const [hour, minute, second] = timePart.split(':');

                            // 创建新加坡时间的 UTC 时间（SGT 是 UTC+8）
                            const sgDate = new Date(Date.UTC(
                                parseInt(year),
                                parseInt(month) - 1,
                                parseInt(day),
                                parseInt(hour) - 8, // 减去8小时得到UTC时间
                                parseInt(minute),
                                parseInt(second)
                            ));

                            const localDate = new Date(sgDate); // 转为本地时间

                            // 格式化年月日时分秒
                            const pad = (n) => n.toString().padStart(2, '0');
                            const yyyy = localDate.getFullYear();
                            const MM = pad(localDate.getMonth() + 1);
                            const dd = pad(localDate.getDate());
                            const HH = pad(localDate.getHours());
                            const mm = pad(localDate.getMinutes());
                            const ss = pad(localDate.getSeconds());

                            return `${yyyy}-${MM}-${dd} ${HH}:${mm}:${ss}`;
                        }
                    }]
            });
        });
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.refreshStatus = function () {
            $scope.condition = {};
            gridService.search();
        };
	    $scope.deleteRows = function () {
	      var rows = gridService.getSelectedRow();
	      if (rows.length == 0) {
	          dialogService.alertInfo("info", $scope.jobTaskJs.chooseRecord);
	          return;
	      }
	      dialogService.openConfirm($scope.jobTaskJs.comfDel, function () {
	          var ids = "";
	          for (var index in rows) {
	              ids = ids + rows[index].id + ",";
	          }
	          var param = ids.substr(0, ids.length - 1);
	          clearCacheService.deleteTerminalOperater(param, function (data) {
	              dialogService.closeConfirm();
	              if(data.status==100) {
	              	dialogService.alertInfo("success", $scope.jobTaskJs.delSucc);
	              }else{
	              	dialogService.alertInfo("error", data.msg);
	              }
	              gridService.refresh();
	          });
	      });
	    };   
        $scope.issuedAgain = function (index) {
        	var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.jobTaskJs.chooseRecord);
                return;
            }
            var ids = ""+$stateParams.id+",";
            for (var index in rows) {
                ids = ids + rows[index].id + ",";
            }
            var param = ids.substr(0, ids.length - 1);
            clearCacheService.issuedAgain(param, function (data) {
                dialogService.alertInfo("success", $scope.jobTaskJs.operateSuccess);
                gridService.refresh();
            });
        };
        $scope.issuedCancel = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.jobTaskJs.chooseRecord);
                return;
            }
            for (var i in rows) {
            	if(rows[i].operateStatus == 1){
                	dialogService.alertInfo("info", $scope.jobTaskJs.msg1);
                	return false;
                }
            }
            var ids = "";
            for (var index in rows) {
                ids = ids + rows[index].id + ",";
            }
            var param = ids.substr(0, ids.length - 1);
            clearCacheService.issuedCancel(param, function (data) {
                dialogService.alertInfo("success", $scope.jobTaskJs.operateSuccess);
                gridService.refresh();
            });
        };
        $scope.issuedCancelAll = function () {
        	var param =$stateParams.id;
    		clearCacheService.issuedCancelAll(param, function (data) {
                dialogService.alertInfo("success", $scope.jobTaskJs.operateSuccess);
                gridService.refresh();
            });
        };
        $scope.exportExcel=function () {dialogService.openConfirm($scope.jobTaskJs.comEx,function(){
            dialogService.closeConfirm();
            dialogService.alertInfo("success", $scope.jobTaskJs.exporting);
            var domain = window.location.href.split("#")[0];
            var baseUrl=domain.substr(0,domain.lastIndexOf("/"));
            var downloadUrl = "";
            var termSeq = $scope.condition.termSeq;
            var operateStatus = $scope.condition.operateStatus;
            if(termSeq == undefined){
                termSeq = "";
            }

            if(operateStatus == undefined){
                operateStatus = "";
            }
            var str="termSeq="+termSeq+"&operateStatus="+operateStatus+"&jobId="+$stateParams.id+"&jobName="+$scope.jobName;

            downloadUrl=baseUrl+'/clearCache/exportExcel/'+str;
            var iframe = document.createElement("iframe");
            document.body.appendChild(iframe);
            iframe.src = downloadUrl;
            iframe.style.display = "none";
        	})};
    	});
    };
});