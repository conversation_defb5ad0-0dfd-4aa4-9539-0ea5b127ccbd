{"jobTaskJs": {"thirdTitle": "Execution", "wait": "Waiting", "success": "Success", "pause": "Pause", "termSeq": "Serial No.", "status": "Status", "operateCommand": "command", "insName": "Organization", "termMfrName": "Serial No.", "termTypeName": "Serial No.", "recordCreateTime": "Create time", "chooseRecord": "Please choose record", "comfDel": "Confirm to delete it?", "delSucc": "Delete Success", "operateSuccess": "Successful operation!", "msg1": "Should not select record of successful execution", "comEx": "Confirm to export this record?", "exporting": "Exporting now, please wait a minute"}, "jobTaskHtml": {"termSeq": "Serial No.", "status": "Status", "reset": "Reset", "query": "Query", "start": "Start", "pause": "Pause", "refresh": "Refresh", "export": "Export", "jobName": "Task Name", "appoint": "Appoint", "exclude": "Exclude", "termCol": "Serial No.", "add": "Add", "excel": "Excel upload", "del": "Delete", "clean": "Clean", "errorCol": "Error aggregate", "groupName": "Group", "all": "All"}}