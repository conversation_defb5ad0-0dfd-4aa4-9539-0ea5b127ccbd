<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class=" top-inp" placeholder="{{listHtml.jobName}}" ng-model="condition.jobName">
                 <select ng-options="m.id as m.name for m in factoryList" ng-model="condition.manufacturerId" ng-change="changeFactory(condition.manufacturerId)" class="input-big" >
                    <option value="">{{listHtml.manufacturer}}</option>
                 </select>
                <select ng-options="m.code as m.name for m in termTypeList" ng-model="condition.termTypeCode" class="input-big" >
                    <option value="">{{listHtml.model}}</option>
                 </select>
               <button type="button" class="btn" ng-click="reset()">{{listHtml.reset}}</button>
                <button type="submit" ng-click="search()"><i class="icon-search" has-permission="clearCache_query"></i>{{listHtml.query}}</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="button" type="button" ng-click="$state.go('home.clearCache.add')" has-permission="clearCache_add">
                    <i class="icon-plus"></i>{{listHtml.add}}
                </button>
                <button class="btn-default" type="button" ng-click="copyRow()" has-permission="driverJob_add">
                    <i class="icon-copy"></i>{{listHtml.copy}}
                </button>
                <button class="btn-default" type="button" ng-click="driverTask()">
                    <i class="icon-th-list"></i>{{listHtml.execution}}
                </button>
                <button class="btn-special" type="button" ng-click="start()" has-permission="clearCache_start">
                    <i class="icon-play"></i>{{listHtml.start}}
                </button>
                <button class="btn-pause" type="button" ng-click="pause()" has-permission="clearCache_pause">
                    <i class="icon-pause"></i>{{listHtml.pause}}
                </button>
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
