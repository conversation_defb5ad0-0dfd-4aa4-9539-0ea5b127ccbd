<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class=" top-inp" placeholder="任务批次号" ng-model="condition.jobName">
                <input type="text" class=" top-inp" placeholder="应用名称" ng-model="condition.appName">
                <input type="text" class=" top-inp" placeholder="内部版本" ng-model="condition.appVersion">
               <button type="button" class="btn" ng-click="reset()">重置</button>
                <button type="submit" ng-click="search()"><i class="icon-search" has-permission="driverJob_query"></i>查询</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="btn-default" type="button" ng-click="driverTask()" has-permission="apkPush_task">
                    <i class="icon-th-list"></i>执行情况查询
                </button>
                <button class="btn-today" type="button" ng-click="queryForTime('today')" has-permission="driverJob_today">
                    <i class="icon-search" ></i>当日
                </button>
                <button class="btn-toweek" type="button" ng-click="queryForTime('toweek')" has-permission="driverJob_toweek">
                    <i class="icon-search"  ></i>近七天
                </button>
                <button class="btn-tomonth" type="button" ng-click="queryForTime('tomonth')" has-permission="driverJob_tomonth">
                    <i class="icon-search" ></i>一个月内
                </button>
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
