
<div class="container-fluid">
<div class="container-fluid-in">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class="input-big top-inp" placeholder="Serial No." ng-model="condition.termSeq">
                <div class="input-append input-group" ng-click="openOrganization()">
                    <input type="text" class="top-inp input-float" readonly="true" ng-model="condition.insName"
                           placeholder="Orginzation">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button" ></button>
                </div>
                <button type="button" class="btn" ng-click="resetTermInfo()">reset</button>
                <button type="submit" ng-click="serachTermInfo();"><i class="icon-search"></i>query</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <table id="table"></table>
        </div>
    </div>
    <div class="modal-footer">
            <!--<button class="btn" type="button" ng-click="closeThisDialog(0)">取消</button>-->
            <button class="btn blue" type="submit" ng-click="ok()"><i class="icon-ok"></i>save</button>
</div>
</div>
</div>
