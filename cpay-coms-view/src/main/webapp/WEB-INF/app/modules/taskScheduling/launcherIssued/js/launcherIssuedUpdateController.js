'use strict';
define(function () {
    return function ($scope, launcherIssuedService,$state,$stateParams, dialogService, formService,BaseApi,organizationService,terminalGroupService,gridService) {
    	 $scope.$parent.thirdTitle = "Task modification";
         var parentScope =$scope ;
    	 launcherIssuedService.selectByPrimaryKey($stateParams.id, function (data) {
    		 
             $scope.driverJob = data;
             if(data.releaseType == "1"){
            	 $scope.driverJob.collection="";
             }
             if(data.collection){
 	            var collections = data.collection.split(",");
 	            var selectObj = angular.element("#tout_acct_list");
 	            for(var i = 0;i < collections.length;i++) {
 	        		selectObj.append("<option value='"+collections[i]+"'>"
 	                    		+collections[i]+"</option>");
 	        	}
         	}
         });
         BaseApi.query("/launcherIssued/app",{}, function (data) {
   		 	$scope.launcherList = data;
   	 	});
        
        BaseApi.query("/dict/type/LauncherType",{}, function (data) {
        	
  		 	$scope.releaseTypeList = data;
  	 	});
        $scope.cleanTermSeqCollection = function(){
        	$scope.driverJob.collection = "";
        }
        $scope.showTermSeq = function(){
        	var param  = {"launcherCode":$scope.driverJob.appCode};
        	BaseApi.query("/launcherIssued/app",param, function (data) {
    	if($scope.driverJob.appCode == null || $scope.driverJob.appCode == '' || $scope.driverJob.appCode == undefined){
    		dialogService.alertInfo("error", "Please select the desktop parameter name first!");
    		return false;
    	}
    	var insId =  data[0].insId;
  		var termGroupId=data[0].termGroupId;
        dialogService.openDialog({
            template: "modules/taskScheduling/launcherIssued/showTermSeqList.html",
            width: '60%',
            controller: function ($scope,dialogService,$timeout) {
         	   $scope.app={};
         	   $timeout(function(){
         		   gridService.initTable({
        			    url:"/terminal?termStatus=1&merchantStatus=1",
                        scope: $scope,
                        operator:false,
                        uniqueId: 'id',
						    columns: [
                          {field: 'state',checkbox: 'true'},
                          {field: "termSeq", title: "Terminal serial number"},
                          {field: "insName", title: "Organization"},
                          {field: "termMfrName", title: "Terminal manufacturer"},
                          {field: "termTypeName", title: "Terminal type"},
                          {field: "merchantName", title: "Business name"}
                       ]
                    });
         	    },100)
         	    $scope.serachTermInfo = function () {
         		   gridService.search($scope.condition);
                };
                $scope.resetTermInfo = function () {
          		   $scope.condition={};
          		   gridService.search();
                };
                $scope.openOrganization = function () {
                    organizationService.openOrganization(function (value) {
                        if (!$scope.condition) {
                            $scope.condition = {};
                        }
                        $scope.condition.insId = value.id;
                        $scope.condition.insName = value.name;
                    });
                };
                $scope.ok = function () {
        		    var rows= $("#table").bootstrapTable("getSelections");
        		     if (rows.length == 0) {
                      dialogService.alertInfo("info", "Please select the record!");
                      return;
                  }
        		  var ids = "";
          	 if(angular.element("#tout_acct_list option").size()==0){
          		 for (var index in rows) {
          		 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                   		+rows[index].termSeq +"</option>");
          		 }
                 
          	 }else{
          		 for (var index in rows) {
          		 var j =0;
          		 angular.element("#tout_acct_list option").each(function () {
          			 if(rows[index].termSeq==$(this).val()){
          				 return false;
          			 }
          			 j++;
          	       });
              		 if(j==angular.element("#tout_acct_list option").size()){
              			 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                            		+rows[index].termSeq +"</option>");
              		 }
          		 }
          	 }
          	  
          	  $scope.closeThisDialog(0);
          
        };
            }
        });
	 });
	}
		$scope.openInsDialog = function () {
             organizationService.openOrganization(function (value) {
                 if (!$scope.launcherIssued) {
                     $scope.launcherIssued = {};
                 }
                 $scope.driverJob.insId = value.id;
                 $scope.driverJob.insName = value.name;
                 BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
     	   		 	$scope.terminalGroupList = data;
     	   	 	});
             });
         };
//         BaseApi.query("/terminalGroup/selectGroup",{}, function (data) {
//    		 	$scope.terminalGroupList = data;
//    	 });
//        terminalGroupService.getterminalGroupList(function (data) {
//            $scope.terminalGroupList = data.rows;
//        });
         $scope.form = formService.form(function () {
        	 if($scope.driverJob.releaseTime > $scope.driverJob.validDate) {
	        		dialogService.alertInfo("info", "Start date cannot be greater than the due date");
	        		return;
	        	}
	    	 if($scope.driverJob.releaseType==1){
	    		 if($scope.driverJob.insName == "" || $scope.driverJob.insName== null){
	    			 dialogService.alertInfo("error", "Please select organization name!");
	    			 return ;
	    		 }
	    		 if($scope.driverJob.groupId == "" || $scope.driverJob.groupId == null){
					 dialogService.alertInfo("error", "Please select the terminal group");
					 return ;
				 }
	    	 }
	    	 if($scope.driverJob.releaseType==2){
	         	var collections ="";
	        	var selectObj = angular.element("#tout_acct_list").get(0);
	        	var options = selectObj.options;
	        	if(options.length == 0) {
	        		dialogService.alertInfo("info", "The terminal set cannot be empty!");
	                return;
	        	}
	        	for(var i = 0;i < options.length;i++) {
	        		collections += options[i].text+",";
	        	}
	        	$scope.driverJob.collection = collections.substring(0,collections.length-1);
	    	 }
        	 launcherIssuedService.updatelauncherIssued($scope.driverJob, function () {
                 dialogService.alertInfo("success", "The driver task was modified successfully!");
                 $state.go("home.launcherIssued.list");
             });
         });
         $scope.deleteTerm = function () {
          	var oSelect = angular.element("#tout_acct_list");
          	var options = oSelect.option;
          	var termSeqs = $("#tout_acct_list option:selected").val();
          	if(termSeqs == undefined || termSeqs == null || termSeqs == ""){
          		dialogService.alertInfo("success", "Please select at least one record!");
          		return ;
          	}
  			$("#tout_acct_list option:selected").remove();
          }
          $scope.clearTerm = function () {
        	  	var selectObj = angular.element("#tout_acct_list").get(0);
	         	var options = selectObj.options;
	         	if(options.length == 0) {
	         		dialogService.alertInfo("success", "There is currently no terminal information to clear!");
           		return ;
	         }
         	 dialogService.openConfirm("Are you sure you want to clear the data of the current terminal set?", function () {
         		 $("#tout_acct_list option").remove();
         		 dialogService.closeConfirm();
         	 });
          } 
    }
});