{"clearCacheEditJs": {"thirdTitle": "Add", "selectOrg": "Please select the organization", "termColl": "Terminal set cannot be empty!", "updateSuccess": "Updating success", "chooseFactory": "Please choose the terminal manufacturer first!", "chooseModel": "Please choose the terminal model first!", "terminalImport": "Serial No. Import", "chooseFile": "Please choose file", "excelFile": "Please select Excel file", "uploadSuccess": "File upload successful ", "uploadFalied": "File upload failed ", "modelNotEmpty": "Terminal model cannot be empty", "upFirst": "Please upload the file first", "termSeq": "Serial No.", "insName": "Organization", "termMfrName": "Manufacturer", "termTypeName": "Terminal Type", "merchantName": "Serial No.", "chooseRecord": "Please choose record", "chooseOneRecord": "Please select at least one record!", "termEmpty": "There is currently no empty terminal information!", "cleanTerm": "Are you sure you want to clear the current data?"}, "clearCacheAddHtml": {"instructions": "Instructions", "jobName": "Task Name", "choose": "<PERSON><PERSON>", "manufacturer": "Manufacturer", "termType": "Model", "releaseType": "Release type", "insName": "Ins Name", "group": "Group filtering method", "groupType": "Please choose group type", "appoint": "Appoint", "exclude": "Exclude", "termCol": "Serial No.", "add": "Add", "excel": "Excel upload", "del": "Delete", "clean": "Clean", "errorCol": "Error aggregate", "groupName": "Group", "all": "All", "appPakage": "App pakage", "appActivity": "App start activity", "appPwd": "App unlock password"}}