<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div><h1 style="font-size: 14px; font-weight: bold;">【Remote Operation Detail】</h1></div>
            <div>
                <table class="info-table info-table-short double"
                       style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Task Name</td>

                        <td class="info-table-value">
                            <span ng-bind="operationJob.jobName"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> Terminal Manufacturer</td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(factoryList,operationJob.manufacturerId,'id') || '--'"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double"
                       style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Operational Instruction</td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(operateInstructList,operationJob.operateType,'type') || '--'"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> Terminal Type</td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(termTypeList,operationJob.termTypeCode,'code') || '--'"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double"
                       style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;" ng-show="operationJob.operateType==19">
                    <tr class="info-table-row">
                        <td class="info-table-name"> App pakage</td>
                        <td class="info-table-value">
                            <span ng-bind="operationJob.appPakage"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> App start activity</td>
                        <td class="info-table-value">
                            <span ng-bind="operationJob.appActivity"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double"
                       style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;" ng-show="operationJob.operateType==19">
                    <tr class="info-table-row">
                        <td class="info-table-name"> App unlock password</td>
                        <td class="info-table-value">
                            <span ng-bind="operationJob.appPwd"></span>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="info-container">
                <table class="info-table" style="max-width: 1060px;clear: both;width: calc(100% - 1px);border-top: 0;border-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> Publication Type</td>

                        <td class="info-table-value">
                            <span ng-bind="formatManu(releaseTypeList,operationJob.releaseType,'type') || '--'"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="operationJob.releaseType==1">
                        <td class="info-table-name"> Organization Name</td>

                        <td class="info-table-value">
                            <span ng-bind="operationJob.insName"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="operationJob.releaseType==1">
                        <td class="info-table-name"> Group Filtering Method</td>

                        <td class="info-table-value">
                            <span ng-bind="operationJob.isGroupUpdate"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row" ng-show="operationJob.releaseType==1">
                        <td class="info-table-name"> Terminal Type</td>

                        <td class="info-table-value">
                            <span ng-bind="groupIds"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row big-row" ng-show="operationJob.releaseType==2">
                        <td class="info-table-name"> Terminal Set</td>

                        <td class="info-table-value" id="collectionText">
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

	