'use strict';
define(['js/app'], function (app) {
    app.factory("apkPushService", function (BaseApi) {
        var apkPushData = {};
        return {
        	getapkPushList: function (data, success) {
                BaseApi.get("/apkPush", data, success);
            },
            setdDiverJobData: function (apkPush) {
            	apkPushData = apkPush;
            },
            addapkPush: function (data, success) {
                BaseApi.post("/apkPush", data, success);
            },
            updateapkPush: function (data, success) {
                BaseApi.patch("/apkPush", data, success);
            },
            deleteapkPush: function (id, success) {
                BaseApi["delete"]("/apkPush/" + id, success);
            },
            selectByPrimaryKey: function (id, success) {
                BaseApi.get("/apkPush/:id", {
                    id: id
                }, success);
            },
            subJob:function(id,success){
            	BaseApi["delete"]("/apkPush/subJob/" + id, success);
            },
            issuedAgain: function (ids, success) {
                BaseApi["delete"]("/apkPush/issuedAgain?ids=" + ids, success);
            },
            issuedCancel: function (ids, success) {
                BaseApi["delete"]("/apkPush/issuedCancel?ids=" + ids, success);
            },
            deleteRows: function (id, success) {
                BaseApi["delete"]("/apkPush/deleteApkPush?ids="+id,success);
            }
        };
    });
});