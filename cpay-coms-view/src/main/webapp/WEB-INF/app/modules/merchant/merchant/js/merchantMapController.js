'use strict';
define(function () {
    return function ($scope, merchantService, $state,$stateParams, dialogService) {
        $scope.$parent.thirdTitle = "终端用户位置显示";
        if($stateParams.type=='1'){
        	  merchantService.getMerchantById($stateParams.id, function (data) {
              	merchantService.operBMap1(data);    
              });
        }else{
        	merchantService.getMerchantById($stateParams.id, function (data) {
            	merchantService.operBMap(data);    
            });
        }
        $scope.returnMerchant = function(){
        	$state.go('home.merchant.update', {id: $stateParams.id});
        }
    };
});