'use strict';
define(function () {
    return function ($scope, merchantAuditService, $state,$stateParams, dialogService) {
        $scope.$parent.thirdTitle = "终端用户位置详情";
        merchantAuditService.getMerchantAuditById($stateParams.id, function (data) {
        	merchantAuditService.operBMap(data);    
        });
        $scope.returnMerchantAudit = function(){
        	$state.go('home.merchantAudit.check', {id: $stateParams.id});
        }
    };
});