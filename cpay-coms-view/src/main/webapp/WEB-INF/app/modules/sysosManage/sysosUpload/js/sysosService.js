'use strict';
define(['js/app'], function (app) {
    app.factory("sysosService", function (BaseApi) {
        var sysosData = {};
        return {
            getSysosList: function (data, success) {
                BaseApi.get("/sysos", data, success);
            },
            setSysosData: function (sysos) {
                sysosData = sysos;
            },

            getSysosData: function () {
                return sysosData;
            },
            addSysos: function (data, success) {
                BaseApi.post("/sysos", data, success);
            },
            updateSysos: function (data, success) {
                BaseApi.patch("/sysos", data, success);
            },
            deleteSysos: function (ids, success) {
                BaseApi["delete"]("/sysos?ids=" + ids, success);
            }
        };
    });
});