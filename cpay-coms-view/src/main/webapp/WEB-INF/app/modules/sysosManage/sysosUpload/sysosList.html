<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class="input" placeholder="版本名称" ng-model="condition.osVerNm">
                <button type="button" class="btn" ng-click="reset()">重置</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>查询</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="btn blue" type="button" ng-click="$state.go('home.sysos.add')">
                    <i class="icon-plus"></i>Add
                </button>
                <button class="btn  red" type="button" ng-click="deleteRows()">
                    <i class="icon-remove"></i>Batch Delete
                </button>
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>