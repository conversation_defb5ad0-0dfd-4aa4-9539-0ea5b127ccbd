<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">

				<div class="control-group">
					<label class="control-label">版本名称<span
						class="required">*</span></label>
					<div class="controls">
						<input type="text" name="osVerNm" ng-model="sysos.osVerNm"
							class="span6" ng-disabled="disabled" validator="required"
							message-id="osVerNm" /> <span id="osVerNm" class="help-inline"></span>
					</div>
				</div>

				<div class="control-group">
					<label class="control-label">版本号<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="osVer" ng-model="sysos.osVer"
							class="span6" ng-disabled="disabled" validator="required"
							message-id="osVer" /> <span id="osVer" class="help-inline"></span>
					</div>
				</div>
				
				<div class="control-group">
					<label class="control-label">终端厂商<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="factoryList" ng-model="sysos.factoryId"
						    ng-readonly="readOnly" ng-disabled="readOnly"
							ng-options="m.id as m.name for m in factoryList"
							validator="required" message-id="factoryList">
							<option value="">请选择</option>
						</select> 
						<span id="factoryList" class="help-inline"></span>
					</div>
				</div>


				<div class="control-group">
					<label class="control-label">终端型号<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="termTypeList" ng-model="sysos.termTypeId"
							ng-readonly="readOnly" ng-disabled="readOnly"
							ng-options="m.id as m.name for m in termTypeList"
							validator="required" message-id="termTypeList">
							<option value="">请选择</option>
						</select>
						<span id="termTypeList" class="help-inline"></span>
					</div>
				</div>
				
				
				<div class="control-group">
					<label class="control-label">版本文件<span class="required">*</span></label>
					<div class="controls">
					
					<input type="text" name="osPath" ng-model="sysos.osPath" ng-show="false"
					       ng-disabled="disabled"  message-id="osPath"/>
					       
					<div class="uneditable-input">
						<i class="icon-file fileupload-exists"></i> 
						<span class="fileupload-preview" ng-bind="file.name"></span>
					</div>
					
				   <div class="btn btn-file"  ngf-select ng-model="file" name="file">选择</div>
  				   <div class="btn blue"  ng-click="submitFile()">上传</div>
					<span id="osPath" class="help-inline"></span>
					</div>
				</div>
				

			</form>

		</div>
	</div>
</div>

<div ng-show="!disabled" form-foot
	goback="$state.go('home.sysos.list')" submit="form.submit(Form)"
	reset="form.reset(Form)"></div>
