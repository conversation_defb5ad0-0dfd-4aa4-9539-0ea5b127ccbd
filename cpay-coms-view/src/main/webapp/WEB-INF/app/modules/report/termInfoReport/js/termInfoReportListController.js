'use strict';
define(function () {
    return function ($scope, BaseApi,organizationService, $state, dialogService, gridService, toolsService,$http) {
        $scope.$parent.thirdTitle = "";
        $http.get("modules/report/termInfoReport/i18n/en/termInfoReport.json").success(function(dataLan) {
        $scope.list = dataLan.list
        $scope.exportData = dataLan.exportData;
        var echarts = $scope.$parent.echarts;
        $scope.currentuser = BaseApi.getUser();
        var seriesData = [];
        var names = [];
        $scope.data = {};
        if($scope.condition == undefined) {
            $scope.condition = {};
            if(($scope.condition.startTime == undefined || $scope.condition.startTime == null)
                && ($scope.condition.endTime == undefined || $scope.condition.endTime == null)){
                $scope.condition.startTime = getFormatDate();
            }
        }
        function getFormatDate() {
            var date = new Date();
            var year = date.getFullYear() - 1;
            var month = date.getMonth() + 1;
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            var currentdate = year +""+ month;
            return currentdate;
        }
        $scope.condition.isShowAll = 1;
        $scope.condition.insId = $scope.currentuser.insId;
        $scope.condition.insName = $scope.currentuser.insName;

        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
                $scope.condition.terminalGroupId = null;
                BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
                    $scope.terminalGroupList = data;
                });
                data($scope.condition);
            });

        };
        $scope.search = function () {
            if($scope.condition.startTime > $scope.condition.endTime) {
                dialogService.alertInfo("info", $scope.list.conMsg2);
                return;
            }
            data($scope.condition)
        };
        $scope.reset = function () {
            $scope.condition = {};
            $scope.condition.isShowAll = 1;
            $scope.condition.insId = $scope.currentuser.insId;
            $scope.condition.insName = $scope.currentuser.insName;
            $scope.condition.startTime = getFormatDate();
            data($scope.condition);
        };

        data($scope.condition);
        function data(params){
            if ($scope.condition.startTime == undefined || $scope.condition.startTime == "" || $scope.condition.startTime == null ) {
                dialogService.alertInfo("info", $scope.list.queryDate);
                return;
            }
            if ($scope.condition.startTime > $scope.condition.endTime) {
                dialogService.alertInfo("info", $scope.list.conMsg2);
                return;
            }
            BaseApi.get("/report/getTermInfoReport", params, function (data) {
                $scope.data = data;
                seriesData = [];
                names = [];
                $scope.allData = [];
                $.each(data.factoryData, function(i) {
                    if(i == "allData"){
                        $scope.allData = data.factoryData[i];
                        delete data.factoryData[i]
                        return true;
                    }
                    seriesData.push({
                        name: i,
                        type: 'line',
                        smooth: true,
                        itemStyle: {normal: {areaStyle: {type: 'default'}}},
                        data: data.factoryData[i]
                    });
                    names.push(i);
                });
                var term_chartsTarget = document.getElementById('term_charts');
                // 指定图表的配置项和数据
                var option = {
                    title: {
                        text: '',
                        subtext: ''
                    },
                    tooltip: {
                        trigger: 'axis',
                        position : function(p) {
                            return [p[0] + 30, p[1] - 10];
                        },
                    },
                    legend: {
                        data: names
                    },
                    toolbox: {
                        show: true,
                        orient: 'vertical',
                        feature: {
                            mark: {show: true},
                            magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    calculable: true,
                    xAxis: [
                        {
                            name: $scope.list.month,
                            type: 'category',
                            boundaryGap: false,
                            data: data.month
                        }
                    ],
                    yAxis: [
                        {
                            name: $scope.list.quantity,
                            type: 'value'
                        }
                    ],
                    series: seriesData
                };
                var firstEc = echarts.init(term_chartsTarget);
                firstEc.setOption(option);

            });
        }
        // 使用刚指定的配置项和数据显示图表。
        });

        //【导出】Excel（报表数据）
        $scope.exportExcel=function() {
            dialogService.openConfirm($scope.exportData.confirmMsg,
                function(){
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Exporting now, please wait for a minute...");
                    const domain = window.location.href.split("#")[0];
                    const baseUrl = domain.substr(0, domain.lastIndexOf("/"));
                    let downloadUrl;

                    //【配置】请求参数
                    let endTime = $scope.condition.endTime;
                    let startTime = $scope.condition.startTime;
                    let insId = $scope.condition.insId;
                    let rangeType = $scope.condition.isShowAll;
                    if (startTime === undefined) {
                        startTime = "";
                    }
                    if (endTime === undefined) {
                        endTime = "";
                    }

                    if (insId === undefined) {
                        insId = "";
                    }

                    if (rangeType === undefined) {
                        rangeType = "";
                    }

                    //【请求】Excel导出接口
                    const str = "insId=" + insId + "&startTime=" + startTime + "&endTime=" + endTime + "&rangeType=" + rangeType;
                    downloadUrl = baseUrl + '/excelExport/exportTerminal/' + str;
                    const iframe = document.createElement("iframe");
                    document.body.appendChild(iframe);
                    iframe.src = downloadUrl;
                    iframe.style.display = "none";
                }
            )
        };
    };
});