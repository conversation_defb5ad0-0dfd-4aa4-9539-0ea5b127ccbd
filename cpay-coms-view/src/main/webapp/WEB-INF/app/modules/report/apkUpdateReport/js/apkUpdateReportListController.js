'use strict';
define(function () {
    return function ($scope, BaseApi,organizationService, $state, dialogService, gridService, toolsService,$http) {
        $scope.$parent.thirdTitle = "";
        $http.get("modules/report/apkUpdateReport/i18n/en/apkUpdateReport.json").success(function(dataLan) {
        $scope.list = dataLan.list
        $scope.exportData = dataLan.exportData;
        var echarts = $scope.$parent.echarts;
        $scope.currentuser = BaseApi.getUser();
        var seriesData = [];
        var names = [$scope.list.all,$scope.list.updateSuccess,$scope.list.downloadSuccess,
        	$scope.list.downloadFail,$scope.list.updateFail,$scope.list.waitRelease];
        $scope.names = [$scope.list.all,$scope.list.updateSuccess,$scope.list.downloadSuccess,
        	$scope.list.downloadFail,$scope.list.updateFail,$scope.list.waitRelease,$scope.list.updateSuccessRate,$scope.list.view];
        $scope.data = {};
        if($scope.condition == undefined) {
            $scope.condition = {};
            if(($scope.condition.startTime == undefined || $scope.condition.startTime == null)
                && ($scope.condition.endTime == undefined || $scope.condition.endTime == null)){
                $scope.condition.startTime = getNowFormatDate();
            }
        }
        function getNowFormatDate() {
            var date = new Date();
            date.setDate(date.getDate() - 90);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            var currentdate = year +""+ month;
            return currentdate;
        }
        $scope.condition.id = 1;
        $scope.condition.insId = $scope.currentuser.insId;
        $scope.condition.insName = $scope.currentuser.insName;

        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
                data($scope.condition);
            });

        };
        $scope.search = function () {
            data($scope.condition)
        };
        $scope.reset = function () {
            $scope.condition = {};
            $scope.condition.id = 1;
            $scope.condition.insId = $scope.currentuser.insId;
            $scope.condition.insName = $scope.currentuser.insName;
            $scope.condition.startTime = getNowFormatDate();
            data($scope.condition);
        };
        $scope.getDetail = function(id,jobName){
        	 $state.go('home.apkUpdateReport.detail', {id: id,jobName:jobName,insId:$scope.condition.insId});
        }
        data($scope.condition);
        function data(params){
            if (($scope.condition.startTime == undefined || $scope.condition.startTime == null)
                && ($scope.condition.endTime == undefined || $scope.condition.endTime == null)) {
                dialogService.alertInfo("info", $scope.list.queryData);
                return;
            }
            if ($scope.condition.startTime > $scope.condition.endTime) {
                dialogService.alertInfo("info", $scope.list.conMsg2);
                return;
            }
            seriesData.number = [];
            seriesData.success = [];
            seriesData.successDown = [];
            seriesData.failureDown = [];
            seriesData.failureUpdate = [];
            seriesData.waitSend = [];
            $scope.all = {};
            $scope.all.numbers = 0;
            $scope.all.successs = 0;
            $scope.all.successDowns = 0;
            $scope.all.failureDowns = 0;
            $scope.all.failureUpdates = 0;
            $scope.all.waitSends = 0;
            $scope.all.rate = 0;
            BaseApi.query("/report/getApkUpdateReport",params,function (result) {
                var jobName = [];
                $scope.data.jobData = result;
                for ( var i= 0; i < result.length; i++) {
                    seriesData.number.push(result[i].number);
                    seriesData.success.push(result[i].success);
                    seriesData.successDown.push(result[i].successDown);
                    seriesData.failureDown.push(result[i].failureDown);
                    seriesData.failureUpdate.push(result[i].failureUpdate);
                    seriesData.waitSend.push(result[i].waitSend);
                    $scope.all.numbers += result[i].number;
                    $scope.all.successs += result[i].success;
                    $scope.all.successDowns += result[i].successDown;
                    $scope.all.failureDowns += result[i].failureDown;
                    $scope.all.failureUpdates += result[i].failureUpdate;
                    $scope.all.waitSends += result[i].waitSend;
                    if(result[i].number != 0){
                        $scope.data.jobData[i].rate = Math.round((result[i].success/result[i].number)*100)
                    }else {
                        $scope.data.jobData[i].rate = 0;
                    }
                    jobName.push(result[i].jobName);
                }
                if($scope.all.numbers != 0){
                    $scope.all.rate = Math.round(($scope.all.successs/$scope.all.numbers)*100)
                }else {
                    $scope.all.rate = 0;
                }
                var apk_chartsTarget = document.getElementById('apk_charts');
                // 指定图表的配置项和数据
                var option = {
                    //color: ['#2d547c','#67b1cc', '#fa8461','#2d7ca3',  '#fa8461'],
                    title: {
                        text: '',
                        subtext: ''
                    },
                    tooltip: {
                        trigger: 'axis',
                        position : function(p) {
                            return [p[0] + 30, p[1] - 10];
                        },
                    },

                    legend: {
                        data: names
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            mark: {show: true},
                            magicType: {show: true, type: ['line', 'bar', 'stack', 'tiled']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },

                    calculable: true,
                    xAxis: [
                        {
                            name: $scope.list.taskName,
                            type: 'category',
                            data: jobName
                        }
                    ],
                    yAxis: [
                        {
                            name: $scope.list.taskNum,
                            type: 'value'
                        }
                    ],
                    dataZoom: [
                        {
                            type: 'slider',
                            start: 1,
                            end: 35,
                            height: 20,
                            top: '90%',
                        },
                        {
                            type: 'inside',
                            start: 1,
                            end: 35
                        },


                    ],
                    series : [
                        {
                            name: $scope.list.taskNum,
                            type: 'bar',
                            smooth: true,
                            data:seriesData.number
                        },
                        {
                            name: $scope.list.updateSuccess,
                            type: 'bar',
                            smooth: true,
                            data:seriesData.success
                        },
                        {
                            name: $scope.list.donwloadSuccess,
                            type: 'bar',
                            smooth: true,
                             data:seriesData.successDown
                        },
                        {
                            name: $scope.list.donwloadFail,
                            type: 'bar',
                            smooth: true,
                            data:seriesData.failureDown
                        },
                        {
                            name: $scope.list.updateFail,
                            type: 'bar',
                            smooth: true,
                            data:seriesData.failureUpdate
                        },
                        {
                            name:$scope.list.waitRelease,
                            type:'bar',
                            smooth: true,
                            data:seriesData.waitSend
                        }
                    ]
                };
                var firstEc = echarts.init(apk_chartsTarget);
                firstEc.setOption(option);

            });
        }

        //【导出】Excel（报表数据）
        $scope.exportExcel=function() {
            dialogService.openConfirm($scope.exportData.confirmMsg,
                function(){
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Exporting now, please wait for a minute...");
                    const domain = window.location.href.split("#")[0];
                    const baseUrl = domain.substr(0, domain.lastIndexOf("/"));
                    let downloadUrl;

                    //【配置】请求参数
                    let endTime = $scope.condition.endTime;
                    let startTime = $scope.condition.startTime;
                    let insId = $scope.condition.insId;
                    let rangeType = $scope.condition.id;
                    if (startTime === undefined) {
                        startTime = "";
                    }
                    if (endTime === undefined) {
                        endTime = "";
                    }

                    if (insId === undefined) {
                        insId = "";
                    }

                    if (rangeType === undefined) {
                        rangeType = "";
                    }

                    //【请求】Excel导出接口
                    const str = "insId=" + insId + "&startTime=" + startTime + "&endTime=" + endTime + "&rangeType=" + rangeType;
                    downloadUrl = baseUrl + '/excelExport/exportUpdate/' + str;
                    const iframe = document.createElement("iframe");
                    document.body.appendChild(iframe);
                    iframe.src = downloadUrl;
                    iframe.style.display = "none";
                })};
        });
    };
});