{"driverAddJs": {"thirdTitle": "Add", "selectFile": "Please select a file", "chooseType": "Please choose software type", "zipFile": "Please select ZIP format file", "fileType": "File names only support English, numbers,'.'and'_'", "apkFile": "Please select APK format file", "warning": "Please select files not exceeding 2000M", "uploadSuccess": "Upload success", "uploadFalied": "Upload failed", "modelNotEmpty": "Terminal model cannot be empty", "upFirst": "Please upload the file first", "addSuccess": "Adding success"}, "driverAddHtml": {"type": "Type", "choose": "<PERSON><PERSON>", "file": "File", "upload": "Upload", "code": "Code", "name": "Name", "innerVersion": "Internal version", "appVersionName": "Version", "icon": "Icon", "dccSupFlag": "Dcc Flag", "cupConnMode": "UinonPay model", "factory": "Manufacturer", "termType": "Model", "all": "All", "remark": "Remark", "uploadStrategy": "Upload Strategy"}}