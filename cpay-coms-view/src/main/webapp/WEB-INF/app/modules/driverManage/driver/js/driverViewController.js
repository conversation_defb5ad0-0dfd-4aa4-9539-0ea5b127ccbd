'use strict';
define([], function () {
    return function ($scope,BaseApi, driverService, $state,$stateParams,gridService,dialogService,driverUploadService) {
        var record = {};
        driverService.getDriver($stateParams.id,function(data){
            $scope.comsAppVersion=data;

            $scope.comsAppVersion.appCode=data.code;
            $scope.comsAppVersion.appName=data.name;
            if(data.dccSupFlag == "Y"){
                $scope.comsAppVersion.dccSupFlag = "是";
            }else {
                $scope.comsAppVersion.dccSupFlag = "否";
            }
            $scope.comsAppVersion.cupConnMode = data.cupConnMode;
            record = data;

            $scope.$parent.thirdTitle = "Detail";
            $scope.disabled = true;
            $scope.condition = {appId:data.id};

            BaseApi.query("/factory/type",{}, function (data) {
                $scope.factoryList = data;
            });
            BaseApi.get("/dict/types/driver_type,cup_conn_mode",{}, function (data) {
                $scope.typeList = data.data1;
                $scope.cupConnModeList = data.data2;
                $.each(data.data1,function(n,obj) {
                    if(obj.type==$scope.comsAppVersion.type) {
                        $scope.comsAppVersion.type=obj.name;
                    }
                    if($scope.comsAppVersion.type == 'd'){
                        $scope.comsAppVersion.type='定制ROM';
                    }
                    return;
                });
                $.each(data.data2,function(n,obj) {
                    if(obj.type==$scope.comsAppVersion.cupConnMode) {
                        $scope.comsAppVersion.cupConnMode=obj.name;
                    }
                    return;
                });

            });
            var remark ='';
            if($scope.comsAppVersion.type =='d'){
                var remark ='机构标识'
            }else{
                var remark='Version Desc';
            }
            var k;
	        gridService.initTable({
	            url: "/driverUpload/getBelow",
	            scope: $scope,
	            uniqueId: 'id',
	            operator:false,
	            columns: [
                    {
                        field: 'uploadStatus',
                        title: 'Upload Status',
                        formatter: function (value) {
                            if (value === '0') {
                                return '<span class="label label-default">Pending</span>';
                            } else if (value === '1') {
                                return '<span class="label label-warning">On Going</span>';
                            } else if (value === '2') {
                                return '<span class="label label-success">Completed</span>';
                            } else if (value === '3') {
                                return '<span class="label label-warning">Failed</span>';
                            }
                        }
                    },
                    {
                        field: 'uploadStrategy',
                        title: 'Upload Strategy',
                        formatter: function (value) {
                            if (value === '0') {
                                return 'Cloud Storage';
                            } else if (value === '1') {
                                return 'Local Storage';
                            }
                        }
                    },
                    {
	                field: 'appVersion',
	                title: 'Internal Version'
	                },
                    {
                        field: 'appVersionName',
                        title: 'Version Name'
                    },
                    {
	                field: 'factoryName',
	                title: 'Manufacturer'
	                },
                    {
	                field: 'termTypeNameStr',
	                title: 'Model',
					format:function (value) {
						if(!value){
							return "无"
						}
						return value;
                    }
	            }, {
	                field: 'mainActivity',
	                title:  'Startup Path',
	                hide:true
	            },{
	                field: 'remark',
	                title:  remark
	            },
                    {
                        field: 'uploadFileName',
                        title: "File Name"
                    }
	            ]
	        });
        });
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {appId:record.id};
            gridService.search($scope.condition);
        };

    };
});