'use strict';
define([], function () {
    return function ($scope,BaseApi, driverService, $state,$stateParams,gridService,dialogService,driverUploadService) {
    	 var record = {};
    	 driverService.getDriver($stateParams.id,function(data){
         	$scope.comsAppVersion=data;
         	
         	$scope.comsAppVersion.appCode=data.code;
         	$scope.comsAppVersion.appName=data.name;
         	if(data.dccSupFlag == "Y"){
                $scope.comsAppVersion.dccSupFlag = "Yes";
			}else {
                $scope.comsAppVersion.dccSupFlag = "No";
			}
         	$scope.comsAppVersion.cupConnMode = data.cupConnMode;
         	record = data;
         	
	        $scope.$parent.thirdTitle = "Modify";
	        $scope.disabled = true;
	        $scope.condition = {appId:data.id};
	        
	        BaseApi.query("/factory/type",{}, function (data) {
	   		 	$scope.factoryList = data;
	   	 	});
	        BaseApi.get("/dict/types/driver_type,cup_conn_mode",{}, function (data) {
                $scope.typeList = data.data1;
                $scope.cupConnModeList = data.data2;
	           	$.each(data.data1,function(n,obj) {
	                if(obj.type==$scope.comsAppVersion.type) {
	               	 $scope.comsAppVersion.type=obj.name;
	                } 
	            	if($scope.comsAppVersion.type == 'd'){
	            	 $scope.comsAppVersion.type='定制ROM';
		            }
		            return;
	          	});
	           	$.each(data.data2,function(n,obj) {
                    if(obj.type==$scope.comsAppVersion.cupConnMode) {
                        $scope.comsAppVersion.cupConnMode=obj.name;
                    }
                    return;
                });

	         });
	        var remark ='';
	        if($scope.comsAppVersion.type =='d'){
	        	var remark ='机构标识'
	        }else{
	        	var remark='Version Desc';
	        }
	        var k;
	        gridService.initTable({
	            url: "/driverUpload/getBelow",
	            scope: $scope,
	            uniqueId: 'id',
	            operator:false,
	            columns: [
					{
						field: 'uploadStatus',
						title: 'Upload Status',
						formatter: function (value) {
							if (value === '0') {
								return '<span class="label label-default">Pending</span>';
							} else if (value === '1') {
								return '<span class="label label-warning">On Going</span>';
							} else if (value === '2') {
								return '<span class="label label-success">Completed</span>';
							} else if (value === '3') {
								return '<span class="label label-warning">Failed</span>';
							}
						}
					},
					{
						field: 'uploadStrategy',
						title: 'Upload Strategy',
						formatter: function (value) {
							if (value === '0') {
								return 'Cloud Storage';
							} else if (value === '1') {
								return 'Local Storage';
							}
						}
					},
					{
	                field: 'appVersion',
	                title: 'Internal Version'
	            	},
                    {
                        field: 'appVersionName',
                        title: 'Version Name'
                    },
					{
	                field: 'factoryName',
	                title: 'Manufacturer'
	            	},
					{
	                field: 'termTypeNameStr',
	                title: 'Model',
					format:function (value) {
						if(!value){
							return "无"
						}
						return value;
                    }
	            	},
					{
	                field: 'mainActivity',
	                title:  'Startup Path',
	                hide:true
	            	},
					{
	                field: 'remark',
	                title:  remark
	            	},
                    {
                        field: 'uploadFileName',
                        title: "File Name"
                    }
	            ],
				operate: function (value, row, index) {
					var downloadLink = '<a href="javascript:;" ng-click="download(\'' + row.appPath + '\')" title="Download" has-permission="driverVer_download">Download</a>';
					if (row.uploadStatus !== '2') {  // 如果上传状态不是Completed
						downloadLink = '';  // 隐藏下载链接
					}
					return '<a href="javascript:;" ng-click="updateComsVersion(' + row.id + ')" title="Update" has-permission="driverVer_edit">Update</a>'
						+ '<a href="javascript:;" ng-click="deleteComsVersion(' + row.id + ')" title="Delete" has-permission="driverVer_del">Delete</a>'
						+ downloadLink;
				},
            operatorLen:140
	        });
    	 });
    	 $scope.search = function () {
             gridService.search($scope.condition);
         };
         $scope.reset = function () {
         	$scope.condition = {appId:record.id};
             gridService.search($scope.condition);
         };
         $scope.download = function(appPath){
         	window.location=appPath; 
         }
         /*$scope.updateComsVersion = function(id){
        	 dialogService.openDialog({
                 template: "modules/driverManage/driverUpload/driverUploadEdit.html",
                 width:550,
                 controller: function ($scope, formService) {
                     $scope.dialogTitle = "软件版本修改";
                     $scope.readOnly = false;
                     $scope.driverUpload={};
                     BaseApi.get("/driverUpload/queryByJobId/:id",{id:id},function (data){
                         $scope.driverUpload = data;
                         BaseApi.query("/factory/type",{}, function (data) {
              	   		 	$scope.factoryList = data;
              	   	 	});
                          BaseApi.query("/terminalType/activateType/"+data.factoryId,{}, function (data) {
       	    	   		 	$scope.termTypeList = data;
       	    	   	 	});
                     });
         	    	$scope.changeFactory = function (factoryId) {
         	    		if(factoryId!=null&&factoryId!=""){
         	    			var param  = {"factoryId":factoryId};
         	    	        BaseApi.query("/terminalType/activateType/"+factoryId,{}, function (data) {
         	    	   		 	$scope.termTypeList = data;
         	    	   	 	});
         	    		}
         	    		else{
         	    			$scope.termTypeList=[];
         	    		}
         	        };

                     $scope.form = formService.form(function (){
                     	BaseApi.patch("/driverUpload/updateDriverUpload",$scope.driverUpload,function (data) {
                     		if(data.status!=200){
                     		  dialogService.alertInfo("error",data.msg);
                     		  return;
                     		}
                     		$scope.closeThisDialog(0);
                             dialogService.alertInfo("success", "软件版本信息修改成功！");
                             gridService.refresh();
                         });
                     });
                 },
                 closeByDocument: false
             });
         };
         */
         $scope.deleteComsVersion = function(id){
        	 dialogService.openConfirm("Conform to delete this record？", function (confirm) {
                 driverUploadService.deleteOneDriverUpload(id, function (data) {
                     dialogService.closeConfirm();

                     if(data.status == 2) {
                         dialogService.alertInfo("success", data.msg);
                         return;
                     }else if(data.status == 0){
                         dialogService.alertInfo("success", data.msg);
                         $state.go("home.driver.list");
					 }else{
                         dialogService.alertInfo("success", data.msg);
                         gridService.refresh();
					 }
                 });
             });
         };
        $scope.updateComsVersion = function (id) {
            $state.go('home.driver.termEdit', {id: id});
        };
         $scope.saveComsAppInfo = function(){
        	 $scope.comsAppVersion.appId=$stateParams.id;
        	 if($scope.comsAppVersion.appCode == null || $scope.comsAppVersion.appCode == ""){
       		  dialogService.alertInfo("success", "software code cannot be empty");
       	  	}
        	 if($scope.comsAppVersion.appName == null || $scope.comsAppVersion.appName == ""){
       		  dialogService.alertInfo("success", "software name cannot be empty");
       	  	}
        	 driverService.updateDriverUpload($scope.comsAppVersion, function (data) {
           	  if(data.status == 200){
                     dialogService.alertInfo("success", "Successfully modified");
                     $state.go("home.driver.list");
           	  }else{
           		  dialogService.alertInfo("info", data.msg);
           	  }	  
           }); 
        }
    };
});