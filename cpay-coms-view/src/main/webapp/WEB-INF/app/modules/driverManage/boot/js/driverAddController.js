'use strict';
define([], function () {
    return function ($scope,BaseApi,baseUrl,securityService,driverService, $state, dialogService,formService,driverUploadService,gridService) {
        $scope.$parent.thirdTitle = "Software information added";
        var record={};
        var parentScope =$scope;
        BaseApi.query("/dict/type/driver_type",{}, function (data) {
          	$scope.typeList = data;
        });
        $scope.readOnly = false;
        $scope.form = formService.form(function(){
        	if ($(".ng-invalid").length > 0) {
                dialogService.alertInfo("warning", "There are also unverified verification items!");
                return;
            }
        	driverService.addDriver($scope.driver, function (data) {
            	record=data.data;
                dialogService.alertInfo("success", "Software information added successfully");
                //$state.go("home.driver.list");
            });
        });
        $scope.onlyName = function () {
            if (!$scope.driver || !$scope.driver.name) {
                return;
            }
            driverService.validate({name:$scope.driver.name}, "Software name already exists");
        };
        $scope.onlyCode = function () {
        	if (!$scope.driver || typeof ($scope.Form.code.$invalid) == "undefined"
				|| $scope.Form.code.$invalid
				|| typeof ($scope.Form.code.$viewValue) == "undefined"
				|| $scope.Form.code.$viewValue == "") {
			return;
        	}
            driverService.validate({code:$scope.driver.code}, "Software code already exists");
        };
        //---------------------------------系统应用上传列表-----------------------------------
        //$scope.$parent.thirdTitle = "";
        if(record.hasOwnProperty('id')){
        	$scope.condition = {appId:record.id};
        }else{
        	$scope.condition = {};
        }
        BaseApi.get("/factory",{page:1,rows:10}, function (data) {
        	$scope.factoryList = data.rows;
        });
        $scope.changeFactory = function (factoryId) {
    		if(factoryId!=null&&factoryId!=""){
    			var param  = {"factoryId":factoryId};
    	        BaseApi.query("/terminalType/type/"+factoryId,{}, function (data) {
    	   		 	$scope.termTypeList = data;
    	   	 	});
    		}
    		else{
    			$scope.termTypeList=[];
    		}
        };
        gridService.initTable({
            url: "/driverUpload/getBelow",
            scope: $scope,
            //operator:false,
            update:true,
            deletes:true,
            operatorLen:'15%',
            uniqueId: 'id',
            columns: [{
                field: 'state',
                checkbox: 'true'
            }/*, {
                field: 'appName',
                title: '软件名称'
            }*/,{
                field: 'appVersion',
                title: 'Software Version'
            }, {
                field: 'factoryName',
                title: 'Terminal Manufacturer'
            }, {
                field: 'termTypeName',
                title: 'Terminal Type'
            }, {
                field: 'operFlag',
                title: 'Operational Sign',
                formatter: function (value) {
                	var type=value==0?'Add':'Update';
                     return type;
                }
            }/*, {
                field: 'versionStatus',
                title: 'Version Status'
            }*/]
        });
        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select a record!");
                return;
            }
            dialogService.openConfirm("Are you sure you want to delete the selected record?", function () {
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                driverUploadService.deleteDriverUpload(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    gridService.refresh();
                });
            });
        };
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to delete the selected record “" + row.id + "”？", function (confirm) {
                driverUploadService.deleteDriverUpload(row.id, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    gridService.refresh();
                });
            });
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
       	 if(record.hasOwnProperty('id')){
            	$scope.condition = {appId:record.id};
            }else{
            	$scope.condition = {};
            }
           gridService.search($scope.condition);
       };
      //---------------------------------系统应用新增-----------------------------------
        $scope.add = function (){
        	 if(record.hasOwnProperty('id')){
        		 dialogService.openDialog({
                     template: "modules/driverManage/driver/driverUploadEdit.html",
                     width:'50%',
                     controller: function ($scope,BaseApi,Upload,driverUploadService, $state, dialogService,formService) {
                         $scope.driverUpload={};
                         BaseApi.query("/driver/getDriverList", {}, function (data) {
                         	$scope.appList = data;
                         });
                         $scope.driverUpload.appId=record.id;
                         BaseApi.get("/factory", {page:1,rows:10}, function (data) {
                         	$scope.factoryList = data.rows;
                         });
                        BaseApi.get("/terminalType", {page:1,rows:10}, function (data) {
                             $scope.termTypeList = data.rows;
                         });
                        $scope.readOnly = false;
                        //附件上传
                        $scope.submitFile = function() {
                                if (!$scope.file||!$scope.file.size) {
                                	dialogService.alertInfo("success", "Please select a file");
                                	return;
                                }
                                Upload.upload({
                                    url:  baseUrl+'/driverUpload/uploadFile',
                                    data: {file: $scope.file},
                                    headers: securityService.getHeader(),
                                    method: 'POST'
                                }).then(function (resp) {
                             	   	$scope.driverUpload.appPath=resp.data.data.split(',')[0];
                                	$scope.driverUpload.fileName=resp.data.data.split(',')[1];
                                	dialogService.alertInfo("success", "Successfully uploading files");
                                }, function (resp) {
                                	dialogService.alertInfo("warning", "Fail to upload file");
                                }, function (evt) {
                               	$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                                	$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                                });
                              };
                         $scope.selectFile = function(){
                            	  $('.progress>.bar').width('0%');
                            	  $('.progress').css("display","inline");
                              }
                         $scope.form = formService.form(function(){
                             driverUploadService.addDriverUpload($scope.driverUpload, function (data) {
                                 dialogService.alertInfo("success", "Software version added successfully");
                                 parentScope.reset();
                                 $scope.closeThisDialog(0);
                             });
                         });
                     
                     }
                 });
              }else{
            	  dialogService.alertInfo("error", "请先保存软件信息！");
              }
        }
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            driverUploadService.setDriverUploadData(row);
            dialogService.openDialog({
                template: "modules/driverManage/driver/driverUploadEdit.html",
                width:'50%',
                controller: function ($scope, driverUploadService,Upload,BaseApi, $state, dialogService, formService) {
                    $scope.$parent.thirdTitle = "Software upload modification";
                    BaseApi.query("/driver/getDriverList", {}, function (data) {
                    	$scope.appList = data;
                    });
                    BaseApi.get("/terminalType", {page:1,rows:10}, function (data) {
                        $scope.termTypeList = data.rows;
                    });
                    BaseApi.get("/factory", {page:1,rows:10}, function (data) {
                    	$scope.factoryList = data.rows;
                    });
                    $scope.driverUpload = driverUploadService.getDriverUploadData();
                    if (!$scope.driverUpload.id) {
                        $state.go("home.driver.list");
                        return;
                    }
                    $scope.readOnly = false;
                    //回填附件名称
                    $scope.file={
                    		name:$scope.driverUpload.fileName
                    };
                  //附件上传
                    $scope.submitFile = function() {
                        if (!$scope.file||!$scope.file.size) {
                        	dialogService.alertInfo("success", "Please select a file");
                        	return;
                        }
                        Upload.upload({
                            url:  baseUrl+'/driverUpload/uploadFile',
                            data: {file: $scope.file},
                            headers: securityService.getHeader(),
                            method: 'POST'
                        }).then(function (resp) {
                        	$scope.driverUpload.appPath=resp.data.data.split(',')[0];
                        	$scope.driverUpload.fileName=resp.data.data.split(',')[1];
                        	dialogService.alertInfo("success", "Successfully uploading file");
                        }, function (resp) {
                        	dialogService.alertInfo("warning", "Fail to upload file");
                        }, function (evt) {
                        	$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                        	$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                        });
                      };
                      $scope.selectFile = function(){
                    	  $('.progress>.bar').width('0%');
                    	  $('.progress').css("display","inline");
                      }
                    $scope.form = formService.form(function () {
                        driverUploadService.updateDriverUpload($scope.driverUpload, function (data) {
                        	dialogService.alertInfo("success", "The software version was successfully modified!");
                            parentScope.reset();
                            $scope.closeThisDialog(0);
                        });
                    });
                }
            });
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            driverUploadService.setDriverUploadData(row);
            $state.go('home.driverUpload.view');
        };
    };
});