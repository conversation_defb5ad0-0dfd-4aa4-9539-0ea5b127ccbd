'use strict';
define([], function () {
    return function ($scope,$http,baseUrl, driverService,driverUploadService,securityService,Upload,BaseApi, $state, dialogService, formService,anguloaderService) {
        $http.get("modules/driverManage/driver/i18n/en/driverAdd.json").success(function(dataLan) {
        $scope.driverListJs = dataLan.driverAddJs;
        $scope.driverAddHtml = dataLan.driverAddHtml;
    	$scope.$parent.thirdTitle = $scope.driverListJs.thirdTitle;
        $scope.comsAppVersion={};
        $scope.comsAppVersion.dccSupFlag = false;
        $scope.dccReadOnly = false;
        $scope.comsAppVersion.uploadStrategy = '0';
        BaseApi.query("/driver/getDriverList", {}, function (data) {
        	$scope.appList = data;
        });
        $scope.comsAppVersion.auto="0";
        BaseApi.query("/factory/type",{}, function (data) {
   		 	$scope.factoryList = data;
   	 	});
        $scope.selectAll = function(){
            if ($("#allCheck").attr("checked")) {
                $("input[name='checkVal']").attr("checked", true);
            } else {
                $("input[name='checkVal']").attr("checked", false);
            }
        }

        $scope.changeFactory = function (factoryId) {
            if(factoryId!=null&&factoryId!=""){
                BaseApi.query("/terminalType/activateType/"+factoryId,{}, function (data) {
                    $scope.termTypeList = data
                    $("#allCheck").attr("checked",false);
                });
            }
            else{
                $scope.termTypeList=[];
            }
        };
        
        BaseApi.get("/dict/types/driver_type_two,cup_conn_mode",{}, function (data) {
          	$scope.typeList = data.data1;
          	$scope.cupConnModeList = data.data2;
        });
        $scope.readOnly = false;
        //附件上传
        $scope.submitFile = function() {
              if (!$scope.file||!$scope.file.size) {
                	dialogService.alertInfo("success", $scope.driverListJs.selectFile);
                	return;
              }
              if($scope.comsAppVersion.type == "" || $scope.comsAppVersion.type == null || $scope.comsAppVersion.type == undefined){
            	  dialogService.alertInfo("success", $scope.driverListJs.chooseType);
              		return;
              }
              var name = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
              if($scope.comsAppVersion.type == '2' || $scope.comsAppVersion.type == '3'){
                  if(name!="zip"){
                      dialogService.alertInfo("error", $scope.driverListJs.zipFile);
                      $scope.file = ""
                      return;
                  }
                  var reg=new RegExp(/[^\w.]/);
                  if(reg.test($scope.file.name)){
                      dialogService.alertInfo("error", $scope.driverListJs.fileType);
                      $scope.file = ""
                      return;
                  }
              }
              if($scope.comsAppVersion.type == '0' || $scope.comsAppVersion.type == '1' ||
                  $scope.comsAppVersion.type == '5'|| $scope.comsAppVersion.type == "a"){
                  if(name!="apk"){
                      dialogService.alertInfo("error", $scope.driverListJs.apkFile);
                      $scope.file = ""
                      return;
                  }
              }
              if($scope.file.size>2000*1024*1024){
                  dialogService.alertInfo("error", $scope.driverListJs.warning);
                  $scope.file = ""
                  return;
              }
              anguloaderService.show(120000,true);
              var param = "?type="+$scope.comsAppVersion.type;
                Upload.upload({
                    url:  baseUrl+'/driverUpload/uploadDriver'+param,
                    data: {file: $scope.file},
                    headers: securityService.getHeader(),
                    method: 'POST'
                },120000).then(function (resp) {
                	var arr = resp.data.data;
                    anguloaderService.hide(1);
                    if(resp.data.status != 200){
                        $scope.file="";
                        dialogService.alertInfo("error",resp.data.msg);
                        $('#progress').hide();
                        anguloaderService.hide(1);
                        $scope.selectFile();
                        return;
                    }
                	$scope.comsAppVersion.appName=arr[0];
                	$scope.comsAppVersion.appCode=arr[1];
                	$scope.comsAppVersion.appVersion=arr[2];
                	$scope.comsAppVersion.appPath=arr[3];
                	$scope.comsAppVersion.fileName=arr[4];
                	if(arr[5] == null ||arr[5] == ""){
                		$scope.iconPath="images/default_icon.jpg";
                	}else{
                		$scope.iconPath=arr[6]+arr[5];
                    	$scope.comsAppVersion.iconPath=arr[5];
                	}
                    $scope.comsAppVersion.appVersionName=arr[7];
                    $scope.comsAppVersion.uploadFileName=arr[8];
                    if(arr[11] == "T"){
                        $scope.comsAppVersion.dccSupFlag = arr[9];
                        if($scope.comsAppVersion.dccSupFlag == "Y"){
                            $scope.comsAppVersion.dccSupFlag=true;
                        }else {
                            $scope.comsAppVersion.dccSupFlag=false;
                        }
                        $scope.comsAppVersion.cupConnMode = arr[10];
                        $scope.dccReadOnly = true;
                    }else {
                        $scope.dccReadOnly = false;
                    }
                	dialogService.alertInfo("success", $scope.driverListJs.uploadSuccess);
                    anguloaderService.hide();
                }, function (resp) {
                	dialogService.alertInfo("warning", $scope.driverListJs.uploadFalied);
                }, function (evt) {
             	$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                	$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                });
                anguloaderService.hide(1);
              };
              $scope.selectFile = function(){
            	  $('.progress>.bar').width('0%');
            	  $('.progress').css("display","inline");
              }
              
              $scope.form = formService.form(function(){

                  var termTypeCodes = [];
                  var termTypeNames = [];
                  $("input:checkbox[name='checkVal']:checked").each(function(){
                      termTypeCodes.push($(this).val());
                      termTypeNames.push($(this).next().text());
                  })

                  if(termTypeCodes.length == 0){
                      dialogService.alertInfo("error", $scope.driverListJs.modelNotEmpty);
                      return false;
                  }
                  $scope.comsAppVersion.termTypeCodeStr = termTypeCodes.toString();
                  $scope.comsAppVersion.termTypeNameStr = termTypeNames.toString();
                  if($scope.comsAppVersion.dccSupFlag == true){
                      $scope.comsAppVersion.dccSupFlag="Y";
                  }else{
                      $scope.comsAppVersion.dccSupFlag="N";
                  }
//            	  anguloaderService.show(120000,true);
//            	  driverService.addDriverUpload($scope.comsAppVersion, function (data) {
//            	  if(data.status == 200){
//                      dialogService.alertInfo("success", "软件信息新增成功");
//                      $state.go("home.driver.list");
//            	  }else{
//            		  dialogService.alertInfo("info", data.msg);
//            	 }	  
//             },120000);
            	  if (!$scope.file||!$scope.file.size) {
                  	dialogService.alertInfo("success", $scope.driverListJs.upFirst);
                  	return;
                  }
            	  if($scope.comsAppVersion.type == '6'){
            		  if($scope.comsAppVersion.appCode.indexOf("#") <0){
            			  dialogService.alertInfo("info", "默认Launcher软件编码格式为:包名#路径");
            			  return false;
            		  }
            	  }
            	  //anguloaderService.show(12000,true);
                  BaseApi.post("/driverUpload/driverUploadAdd",$scope.comsAppVersion,function(data){
                	  anguloaderService.hide();
                      if(data.status!=200){
                          if($scope.comsAppVersion.dccSupFlag == "Y"){
                              $scope.comsAppVersion.dccSupFlag=true;
                          }
                          if($scope.comsAppVersion.dccSupFlag == "N"){
                              $scope.comsAppVersion.dccSupFlag=false;
                          }
                          dialogService.alertInfo("error",data.msg);
                          return;
                      }else {
                          dialogService.alertInfo("success", $scope.driverListJs.addSuccess);
                          $state.go("home.driver.list");
                          anguloaderService.hide();
                      }

                	  anguloaderService.hide();
               	},12000);
            });
        });
    };
});