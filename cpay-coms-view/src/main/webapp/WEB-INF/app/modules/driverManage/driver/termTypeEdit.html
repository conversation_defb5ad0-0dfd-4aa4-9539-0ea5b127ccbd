<div class="container-fluid" id="breadcrumb-scroll">
    <div class="row-fluid">
        <div class="nav-breadcrumb">
            <div class="breadcrumb-icon"
                 style="background-image: url(images/default/menu/pic_menu_setting_grey.png)"></div>
            <ul class="breadcrumb">
                <li><span class="navigation_home ng-binding">Remote Update</span>
                    <i class="icon-angle-right"></i></li>
                <li ng-class="{active:!thirdTitle}" class=""><a ui-sref="home.driver.list" class="ng-binding" href="#/home/<USER>/list">Software Manage</a><i class="icon-angle-right"></i></li>
                <li ng-class="{active:!thirdTitle}"><a  class="ng-binding" href="#/home/<USER>/update/{{parentId}}">Modify</a><i class="icon-angle-right"></i></li>
                <li class="active ng-binding">Version Modify</li>
            </ul>

        </div>
        <div class="hr"></div>
    </div>
</div>
<div class="container-fluid">
        <div class="row-fluid">
            <div class="span12">
                <form name="Form" class="form-horizontal" id="editform">
                    <div class="control-group">
                        <label class="control-label">Software Type</label>
                        <div class="controls">
                            <input type="text" name="typeName" ng-model="comsAppInf.typeName"
                                   ng-disabled="disabled" readonly="readonly" message-id="typeName"/>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Software Name</label>
                        <div class="controls">
                            <input type="text" name="name" ng-model="comsAppInf.name"
                                   class="span6" ng-disabled="disabled" readonly="readonly"
                                   message-id="name" />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Software Code</label>
                        <div class="controls">
                            <input type="text" name="code" ng-model="comsAppInf.code"
                                   class="span6" ng-disabled="disabled" readonly="readonly"
                                   message-id="code" />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Internal Version</label>
                        <div class="controls">
                            <input type="text" name="appVersion" ng-model="comsAppVersion.appVersion"
                                   class="span6" ng-disabled="disabled" readonly="readonly"
                                   message-id="appVersion" />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Version Name</label>
                        <div class="controls">
                            <input type="text" name="appVersionName" ng-model="comsAppVersion.appVersionName"
                                   class="span6" ng-disabled="disabled" readonly="readonly"
                                   message-id="appVersionName" />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Icon</label>
                        <td><a href='{{iconPath}}' class='fresco'> <img
                                style='width: 50px; height: 50px;' src='{{iconPath}}' alt='' />
                        </a>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Upload Strategy</label>
                        <div class="controls">
                            <select class="form-control span6" name="uploadStrategy"
                                    ng-model="comsAppVersion.uploadStrategy"
                                    ng-readonly="readOnly" ng-disabled="disabled"
                                    validator="required" message-id="uploadStrategy">
                                <option value="0">Cloud Storage</option>
                                <option value="1">Local Storage</option>
                            </select>
                        </div>
                    </div>

                    <div class="control-group" ng-if="comsAppVersion.type == 5">
                        <label class="control-label">Startup Path<span class="required">*</span></label>
                        <div class="controls">
                            <input type="text" name="mainActivity" ng-model="comsAppVersion.mainActivity"
                                   class="span6" ng-disabled="disabled"
                                   message-id="mainActivity" ng-pattern="/^[^#]+$/" />
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Manufacturer<span class="required">*</span></label>
                        <div class="controls">
                            <select class="form-control span6" name="factoryList" ng-model="comsAppVersion.factoryId"
                                    ng-readonly="readOnly" ng-disabled="readOnly"
                                    ng-options="m.id as m.name for m in factoryList"
                                    validator="required" message-id="factoryList"
                                    ng-change="changeFactory(comsAppVersion.factoryId)">
                                <option value="">choose</option>
                            </select>
                            <span id="factoryList" class="help-inline"></span>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label">Model<span
                                class="required">*</span></label>
                        <div class="controls">
                            <div class="terminal-check-container controls no-margin"
                                 style="width: 39%;">
                                <label class="checkbox">
                                    <input type="checkbox"
                                           class="ace" name="allCheck"
                                           ng-click="selectAll(termType.code,detail)"
                                           value="{{termTypeList.chooseAll}}" id="allCheck" /><span
                                        class="lbl">All</span>
                                </label> <br />
                                <label class="checkbox line margin-normal"
                                       ng-repeat="termType in termTypeList">
                                    <input type="checkbox" class="ace" ng-value="termType.code"
                                           name="checkVal" ng-checked="formatCheck(termType.code,detail)"
                                            /> <span
                                        class="lbl" ng-bind="termType.name"></span>
                                </label>
                            </div> <span id="termType" class="help-inline" style="display: none;"></span>

                        </div>
                    </div>

                    <div class="control-group" >
                        <label class="control-label">Version Desc<span class="required">*</span></label>
                        <div class="controls">
						<textarea  row="15" maxlength="100" name="remark" ng-model="comsAppVersion.remark"
                                   ng-disabled="disabled" validator="required,maxlength=100"
                                   message-id="remark" style="height: 100px;" class="form-control span6"/></textarea>
                            <span id="remark" class="help-inline"></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
</div>
<div ng-show="true" form-foot goback="$state.go('home.driver.update')"
     submit="form.submit(Form)"
     reset="form.reset(Form)">
</div>