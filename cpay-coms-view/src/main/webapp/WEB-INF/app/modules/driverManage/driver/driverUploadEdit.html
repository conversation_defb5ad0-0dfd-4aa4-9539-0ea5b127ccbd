<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.type}}<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="typeList" ng-model="comsAppVersion.type"
							ng-readonly="dccReadOnly" ng-disabled="dccReadOnly"
							ng-options="m.type as m.name for m in typeList"
							validator="required" message-id="typeList" >
							<option value="">{{driverAddHtml.choose}}</option>
						</select>
						<span id="typeList" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.file}}<span class="required">*</span></label>
					<div class="controls">
					<input type="text" name="appPath" ng-model="comsAppVersion.appPath" ng-show="false"
					       ng-disabled="disabled"  message-id="appPath"/>   
					<div class="uneditable-input">
					<input type="text" name="fileName" ng-model="comsAppVersion.fileName" ng-show="false"
					       ng-disabled="disabled"  message-id="fileName"/>
						<i class="icon-file fileupload-exists"></i> 
						<span class="fileupload-preview" ng-bind="file.name"></span>
					</div>
				   <div class="btn btn-file" ng-click="selectFile()"  ngf-select ng-model="file" name="file">{{driverAddHtml.choose}}</div>
  				   <div class="btn blue"  ng-click="submitFile()">{{driverAddHtml.upload}}</div>
						<span id="appPath" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label"></label>
					<div class="controls">
						<div style="display:none" class="progress progress-success progress-striped span6" role="progressbar" aria-valuemin="0" aria-valuemax="100">
							<div class="bar" style="width:0%;"></div>
						</div>
					</div>
				</div>
				<!--附件结束  -->
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.name}}<span class="required">*</span></label>
					<div class="controls">
						 <input type="text" name="appName" ng-model="comsAppVersion.appName"
							class="span6" ng-disabled="disabled" validator="required,maxlength=100"
							message-id="appName" /> <span id="appName" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.code}}<span class="required">*</span></label>
					<div class="controls">
					<input type="text" name="appCode" ng-model="comsAppVersion.appCode"
							class="span6" ng-readonly="true" ng-disabled="disabled" validator="required,maxlength=100"
							message-id="appCode" /> <span id="appCode" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.innerVersion}}<span class="required">*</span></label>
					<div class="controls">
						 <input type="text" name="appVersion" ng-model="comsAppVersion.appVersion"
							class="span6" ng-readonly="true"  ng-disabled="disabled" validator="required,maxlength=14"
							message-id="appVersion" /> <span id="appVersion" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.appVersionName}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="appVersionName" ng-model="comsAppVersion.appVersionName"
							   class="span6" ng-readonly="true"  ng-disabled="disabled" validator="required,maxlength=50"
							   message-id="appVersionName" /> <span id="appVersionName" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.icon}}<span
						class="required">*</span></label>
						<td><a href='{{iconPath}}' class='fresco'> <img
								style='width: 50px; height: 50px;' src='{{iconPath}}' alt='' />
						</a>
				</div>

				<div class="control-group">
					<label class="control-label">{{driverAddHtml.uploadStrategy}}<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="uploadStrategy"
								ng-model="comsAppVersion.uploadStrategy"
								ng-readonly="readOnly" ng-disabled="readOnly"
								validator="required" message-id="uploadStrategy">
							<option value="0">Cloud Storage</option>
							<option value="1">Local Storage</option>
						</select>
					</div>
				</div>

				<div class="control-group">
					<label class="control-label">{{driverAddHtml.factory}}<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="factoryList" ng-model="comsAppVersion.factoryId"
						    ng-readonly="readOnly" ng-disabled="readOnly"
							ng-options="m.id as m.name for m in factoryList"
							validator="required" message-id="factoryList"
							 ng-change="changeFactory(comsAppVersion.factoryId)">
							<option value="">{{driverAddHtml.choose}}</option>
						</select>
						<span id="factoryList" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{driverAddHtml.termType}}<span
							class="required">*</span></label>
					<div class="controls">
							<div class="terminal-check-container controls no-margin"
								 style="width: 39%;">
								<label class="checkbox">
									<input type="checkbox"
										   class="ace" name="allCheck"
										   ng-click="selectAll(termType.code,detail)"
										   value="{{termTypeList.chooseAll}}" id="allCheck" /><span
										class="lbl">{{driverAddHtml.all}}</span>
								</label> <br />
								<label class="checkbox line margin-normal"
									   ng-repeat="termType in termTypeList">
									<input type="checkbox" class="ace" ng-value="termType.code"
										   name="checkVal" /> <span
										class="lbl" ng-bind="termType.name"></span>
								</label>
							</div> <span id="termType" class="help-inline" style="display: none;"></span>

						</div>
				</div>

				<div class="control-group" ng-show="comsAppVersion.auto == 0">
					<label class="control-label">{{driverAddHtml.remark}}<span class="required">*</span></label>
					<div class="controls">
						<textarea  row="15" maxlength="100" name="remark" ng-model="comsAppVersion.remark"
							ng-disabled="disabled" validator="required,maxlength=100"
							message-id="remark" style="height: 100px;" class="form-control span6"/></textarea>
							<span id="remark" class="help-inline"></span>
					</div>
				</div>
				<!--附件结束  -->
			</form>
		</div>
	</div>

</div>
<div ng-show="!disabled" form-foot
	 goback="$state.go('home.driver.list')" submit="form.submit(Form)"
	 reset="form.reset(Form)"></div>