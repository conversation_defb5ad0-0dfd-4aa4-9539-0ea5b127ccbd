<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div class="info-container">
                <form name="Form" class="form-horizontal" id="editform">
                <table class="info-table info-table-normal">
                
                    <tr class="info-table-row">
                        <td class="info-table-name"> Software Name </td>
                        <td class="info-table-value">
						 	<input type="text" name="appName" ng-model="comsAppVersion.appName" maxlength="200" ng-disabled="disabled"/>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> Software Code </td>
                            
                        <td class="info-table-value">
                        <input type="text" name="appCode" ng-model="comsAppVersion.appCode" maxlength="200" ng-disabled="disabled"/>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> Software Type </td>
                            
                        <td class="info-table-value">
							<input type="text" name="type" ng-model="comsAppVersion.type"
							 ng-disabled="disabled"/>
                        </td>
                        </td>
                    </tr>
                </table>
                </form>
            </div>
        </div>
    </div>
    <div class="modal-title-vice" style="margin-top:20px;">
        <h3 class="modal-title ng-binding" id="ngdialog2-aria-labelledby">Version Info</h3>
    </div>

	 <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="text" class="input top-inp" placeholder="Internal Version" ng-model="condition.appVersion">
                <input type="text" class="input top-inp" placeholder="Version Name" ng-model="condition.appVersionName">
                <select ng-options="m.id as m.name for m in factoryList" name="factoryList"  ng-model="condition.factoryId">
                    <option value="">Manufacturer</option>
                </select>
                <button type="button" class="btn-reset" ng-click="reset()">Reset</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>Query</button>
            </form>
        </div>
    </div>

    <div class="row-fluid">
        <div class="span12">
            <table id="table"></table>
        </div>
    </div>
</div>