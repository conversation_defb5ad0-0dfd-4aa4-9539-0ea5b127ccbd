'use strict';
define(['js/app'], function (app) {
    app.factory("driverUploadService", function (BaseApi) {
        var driverUploadData = {};
        return {
            getDriverUploadList: function (data, success) {
                BaseApi.get("/driverUpload", data, success);
            },
            setDriverUploadData: function (driverUpload) {
                driverUploadData = driverUpload;
            },

            getDriverUploadData: function () {
                return driverUploadData;
            },
            addDriverUpload: function (data, success) {
                BaseApi.post("/driverUpload", data, success);
            },
            updateDriverUpload: function (data, success) {
                BaseApi.patch("/driverUpload", data, success);
            },
            deleteDriverUpload: function (ids, success) {
                BaseApi["delete"]("/driverUpload?ids=" + ids, success);
            },
            deleteOneDriverUpload: function (id, success) {
                BaseApi["delete"]("/driverUpload/" + id, success);
            }
        };
    });
});