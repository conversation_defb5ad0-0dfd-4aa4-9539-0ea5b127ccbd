'use strict';
define([], function () {
    return function ($scope, orderedPackageService,toolsService,BaseApi, $state,dialogService, gridService) {
        $scope.$parent.thirdTitle = "";
        $scope.condition = {};
        
        BaseApi.get("/dict/types/order_package_status",{}, function (data) {
        	gridService.initTable({
                url: "/orderedPackage",
                scope: $scope,
                operator:false,
                uniqueId: 'id',
                columns: [/*{
                    field: 'checkbox',
                    checkbox: 'false'
                },*/{
                    field: 'orderNo',
                    title: 'Order Number'
                },{
                    field: 'unicomCard.cardNo',
                    title: 'IoT Card Number'
                },/*{
                    field: 'imsi',
                    title: 'IMSI'
                },{
                    field: 'packageId',
                    title: 'Package Number'
                }, */{
                    field: 'orderDate',
                    title: 'Order Date',
                    formatter: function (value) {
                    	return toolsService.getFullDate(value);
                    }
                },
                {
                	field: 'packageName',
                	title: 'Package Name'
                },
                {
                	field: 'packageType',
                	title: 'Package Type',
                	formatter: function (value) {
                		if(value == "1"){
                			return "Basic Package";
                		}else if(value == "2"){
                			return "Fuel Package";
                		}
                    }
                },
                {
                	field: 'flowCount',
                	title: 'Number of Traffic(MB)'
                },{
                	field: 'amt',
                	title: 'Package Amount(Yuan)'
                },{
                	field: 'activeMonth',
                	title: 'Activation Month'
                }]
            });
    	});
        /*$scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "请选择记录！");
                return;
            }
            dialogService.openConfirm("确认要删除选中的记录？", function () {
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id+ ",";
                }
                var param = ids.substr(0, ids.length - 1);
                orderedPackageService.deleteOrderedPackage(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "删除成功！");
                    gridService.refresh();
                });
            });
        };*/
        /*$scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("确认要删除记录 “" + row.id + "”？", function (confirm) {
                orderedPackageService.deleteOrderedPackage(row.id, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "删除成功！");
                    gridService.refresh();
                });
            });
        };*/
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search($scope.condition);
        };
        
        gridService.setPlaceholder();
    };
});