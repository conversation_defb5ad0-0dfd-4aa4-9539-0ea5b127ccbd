'use strict';
define([], function () {
    return function ($scope, packageService,Upload,BaseApi, $state,$stateParams, dialogService, formService) {
        $scope.$parent.thirdTitle = "Package modification";
        BaseApi.get("/dict/types/service_provider,start_type,package_type,telecomRefueling",{}, function (data) {
         	$scope.providerList = data.data1;
         	$scope.startTypeList = data.data2;
         	$scope.packageTypeList = data.data3;
         	$scope.telecomRefueling = data.data4;
      });
        packageService.getPackage($stateParams.id,function(data){
	        $scope.package = data;
	        if (!$scope.package.id) {
	            $state.go("home.package.list");
	            return;
	        }
	        $scope.package.operatorId = data.operatorId + "";
	        $scope.$watch('package.serviceProvider + package.packageType',function(){
	        	if($scope.package.serviceProvider == 46003 && $scope.package.packageType == 2){
	        		$scope.operatorId_show = true;
	        	}else{
	        		$scope.operatorId_show = false;
	        		$scope.package.operatorId = '';
	        	}
	        });
        });   
        
        $scope.validate = function(){
        	if (!$scope.package || !$scope.package.flowCount) {
                return;
            }
        	if($scope.package.flowCount>1000000){
        		$scope.package.flowCount=1000000;
        	}
        	/*if($scope.package.flowCount>1000000){
        		$("#flowCount").html('<p class="validation-invalid">不能超过1000000</p>');
                $("#flowCountInput").removeClass("ng-valid").addClass("ng-invalid");
        	}else{
        		$("#flowCount").html('<p class="validation-valid"><i class="icon-ok"></i></p>');
                $("#flowCountInput").removeClass("ng-invalid").addClass("ng-valid");
        	}*/        	
    	}
        $scope.readOnly = false;
        $scope.form = formService.form(function () {
        	if ($(".ng-invalid").length>0) {
                dialogService.alertInfo("warning","There are also unverified verification items!");
                return;
            }
        	if($scope.operatorId_show && !$scope.package.operatorId){
        		dialogService.alertInfo("warning","Carrier package ID is required!");
                return;
        	}
            packageService.updatePackage($scope.package, function (data) {
            	if(data.status !=200){
            		dialogService.alertInfo("error", data.msg);
            	}else{
            		dialogService.alertInfo("success", "Package modified successfully");
            		$state.go("home.package.list");
            	}  
            });
        });
    };
});