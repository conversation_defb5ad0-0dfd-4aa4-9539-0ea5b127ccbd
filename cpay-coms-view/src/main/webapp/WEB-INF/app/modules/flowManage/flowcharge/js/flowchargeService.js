'use strict';
define(['js/app'], function (app) {
    app.factory("flowchargeService", function (BaseApi) {
        return {            
            addflowcharge: function (data, success) {
                BaseApi.post("/flowcharge", data, success);
            },
            updateflowcharge: function (data, success) {
                BaseApi.patch("/flowcharge", data, success);
            },
            deleteflowcharge: function (id, success) {
            	BaseApi["delete"]("/flowcharge?id="+id, success);
            },
            getflowcharge: function (id, success) {
                BaseApi.get("/flowcharge/"+id, {id: id}, success);
            },
        };
    });
});