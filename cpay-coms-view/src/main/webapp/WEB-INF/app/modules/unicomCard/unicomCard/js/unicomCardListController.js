'use strict';
define(function () {
    return function ($scope,$state,dialogService,anguloaderService, gridService,unicomCardService,BaseApi,baseUrl,Upload,securityService) {
        $scope.$parent.thirdTitle = "";
        //var parentScope =$scope ;
        var domain = window.location.href.split("#")[0];
        BaseApi.query("/dict/type/cardStatus",{}, function (data) {
 		 	$scope.cardStatusList = data;
 	 	});
        BaseApi.query("/dict/type/service_provider",{}, function (data) {
 		 	$scope.orgList = data;
 	 	});
        /*$scope.condition={
        		serviceProviderName:'中国电信'
        };*/
        gridService.initTable({
            url: "/unicomCard",
            scope: $scope,
            //operator:false,
            detail: 'unicomCard_dtail',
            columns: [{
                field: 'state',
                checkbox: 'true'	
            },
            /*{
                field: 'serviceProvider',
                title: 'Carrier number'
            },*/
            {
                field: 'serviceProviderName',
                title: 'Carrier name'
            }, {
                field: 'cardNo',
                title: 'Internet of Things card number'
            },
            {
                field: 'iccid',
                title: 'ICCID'
            },
            {
                field: 'imsi',
                title: 'IMSI'
            },
	        {
	            field: 'status',
	            title: 'Status',
	            formatter: function (value) {
	            	if (value=='0') {
	            		return '<span>Inactivated</span>';
					}
	            	if (value=='1') {
	            		return '<span>Normal</span>';
					}
					else if(value=='2'){
						return '<span>Downtime</span>';
					}
					else if(value=='3'){
						return '<span>Arrears</span>';
					}
	               }
	        }
            ,
            {
                field: 'balance',
                title: 'Account Balance(Yuan)'
            }
            ,
            {
                field: 'masterPlanName',
                title: 'Main package'
            }
            ]
        });
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition={};
            gridService.search();
        };
        $scope.changeStatus = function(){
          var rows = gridService.getSelectedRow();
          
          if (rows.length == 0) {
              dialogService.alertInfo("info", "Please select a record!");
              return;
          }
           var iccids = "";
           var cardNos = "";
           var serviceProvider = "";
           for (var index in rows) {
            	  iccids = iccids + rows[index].iccid + ",";
            	  cardNos = cardNos + rows[index].cardNo + ",";
            	  serviceProvider = rows[index].serviceProvider;
           }
           iccids = iccids.substr(0, iccids.length - 1);
           cardNos = cardNos.substr(0, cardNos.length - 1);
           dialogService.openDialog({
                template: "modules/unicomCard/unicomCard/unicomCardChange.html",
                controller: function ($scope,formService) {
                    $scope.dialogTitle = "状态修改";
                    $scope.unicomCard={
                        	iccid:iccids,
                        	serviceProvider:serviceProvider,
                        	cardNo:cardNos
                    }
                    BaseApi.query("/dict/type/cardStatus",{}, function (data) {
                    	 var resultData = new Array();
                    	 for(var i =0;i<data.length;i++){
                    		 if(data[i].type == '1'){
                    			 resultData[0]=data[i];
                    		 }
                    		 if(data[i].type == '2'){
                    			 resultData[1]=data[i];
                    		 }
                    		 
                    	 }
                		 $scope.cardStatusList = resultData;
                   });
                    $scope.form=formService.form(function () {
                    	anguloaderService.show(6000,true);
                    	unicomCardService.changeUnicomCardStatus($scope["unicomCard"], function (data) {
                    		if(data.status == 200){
                    			$scope.closeThisDialog(0);
                                dialogService.alertInfo("success", "The status has been modified successfully!");
                                gridService.refresh();
                                anguloaderService.hide(1);
                    		}else{
                                dialogService.alertInfo("error", data.msg);
                                anguloaderService.hide();
                    		}
                            
                        },6000);
                    	
                    }); 
                }
            });
        	
        }
        $scope.searchPackage =function (condition) {
            if (!condition) {
                condition = {};
            }
            condition.page = 1;
            $("#table1").bootstrapTable('refresh', {
                query: condition
            });
        },
        $scope.packageInfoBind = function(){
        	var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "请选择记录！");
                return;
            }
            if (rows.length >1) {
                dialogService.alertInfo("info", "只能选择一条记录！");
                return;
            }
            dialogService.openDialog({
                template: "modules/unicomCard/unicomCard/packageInfoShow.html",
                width: '60%',
                controller: function ($scope,dialogService,$timeout) {
             	   $scope.app={};
             	   $timeout(function(){
             		   gridService.initTable({
             			    url:"/package?openFlag=1&packageType=1&validity=1&=startType&serviceProvider"+rows[0].serviceProvider,
                            scope: $scope,
                            id:"#table1",
                            operator:false,
                            singleSelect:true,
                            columns: [
                                      {field: 'status',checkbox: 'true'},
                                      {field: "name", title: "套餐名称"},
                                      {field: "flowCount", title: "流量数(MB)"},
                                      {field: "amt", title: "套餐金额(元)"},
                                      {field: "startType", title: "生效类型",
                          	            formatter: function (value) {
                          	            	if (value==1) {
                          						return '<span>当月生效</span>';
                          					}
                          					else if(value==2){
                          						return '<span>后续生效</span>';
                          					}
                          	               }
                                      },
                                      {field: "validity", title: "有效期(月)"},
                                      {field: "packageType", title: "套餐类型",
                                   	   formatter: function (value) {
                             	            	if (value==1) {
                             						return '<span>普通流量包</span>';
                             					}
                             					else if(value==2){
                             						return '<span>加油包</span>';
                             					}
                             	            }
                                      }
                                ]
                        });
             	    },100)
              	    $scope.ok = function () {
              		  var rows2= $("#table1").bootstrapTable("getSelections");
              		  if (rows2.length == 0) {
                            dialogService.alertInfo("info", "请选择记录！");
                            return;
                       }
              		 var param ={"id":rows2[0].id,"imsi":rows[0].imsi};
              		 
                      dialogService.openConfirm("确定要绑定当前选中的套餐信息吗？", function (confirm) {
                     	 unicomCardService.packageBind(param, function (data) {
                              dialogService.closeConfirm();
                              dialogService.alertInfo("success", "绑定成功！");
                              gridService.refresh();
                          });
                      });
                     };
                }
            });
        }
        	
        $scope.packageDataSyn = function(){
        	 dialogService.openConfirm("数据只能同步联通物联网卡信息,确认要同步当前信息吗？", function (confirm) {
        		 	var param ={"serviceProvider":40016};
             		unicomCardService.packageDataSyn(param, function (data) {
             		dialogService.closeConfirm();
                    dialogService.alertInfo("success", "数据同步成功！");
                    gridService.refresh();
                });
            });
        }
        $scope.initRecharge = function(){
        	dialogService.openDialog({
                template: "modules/unicomCard/unicomCard/unicomCardInitRecharge.html",
                width: '55%',
                controller: function ($scope, unicomCardService, formService) {
                	$scope.readOnly = false;
                	$scope.balance = function(){
                    	var m = $scope.unicomCard.balance;
                    	if(m.indexOf(".")<0){
                    		$("#balance").val(m+".00");
                    	}
                    }
                    $scope.startRecharge = function(){
                    	var param={"cardNo":$scope.list,"balance":$scope.unicomCard.balance};
                    	if($scope.list == "" || $scope.list == null){
                    		dialogService.alertInfo("error", "物联网卡筛选集合不得为空!!!");
                    		return "";
                    	}
                    	if($scope.unicomCard.balance == "" || $scope.unicomCard.balance == null){
                    		dialogService.alertInfo("error", "预充金额不得为空!!!");
                    		return "";
                    	}
                    	dialogService.openConfirm("确认要给当前筛选出来的物联网卡号进行充值操作吗？<font style='color:red'>请慎重操作！！！<font>", function (confirm) {
                    		BaseApi.post("/unicomCard/startRecharge", param, function (data) {
                       		 if(data.status == 200){
                       			 	dialogService.closeConfirm();
                                    dialogService.alertInfo("success", data.msg);
                                    $scope.closeThisDialog(0);
                                    gridService.refresh();
                       		 }else{
                       			 	dialogService.alertInfo("error", data.msg);
                            		return ""; 
                       		 }
                       	 });
                    	});
                    },
                    $scope.queryUincomCard = function(){
                    	var tvalue="";
                    	BaseApi.query("/unicomCard/queryUincomCard",$scope.unicomCard, function (data) {
                    		if(data == "" || data == null ||data.length == 0){
                    			$scope.list="";
                    			dialogService.alertInfo("error", "未找到范围内的物联网卡号码");
                    		}else{
                    			for(var i = 0;i<data.length;i++){
                    				tvalue+=data[i].cardNo+",";
                    			}
                    			$scope.list=tvalue.substring(0,tvalue.length-1)
                    		}
                    	});
                    }
                },
                closeByDocument: false
            });
        }
        $scope.bindingMasterPackage = function(){
        	dialogService.openDialog({
                template: "modules/unicomCard/unicomCard/unicomCardBindingMasterPackage.html",
                width: '55%',
                controller: function ($scope, unicomCardService, formService) {
                	var parentScope = $scope;
                	$scope.unicomCard = {};
                	$scope.readOnly = false;
                	$scope.balance = function(){
                    	var m = $scope.unicomCard.balance;
                    	if(m.indexOf(".")<0){
                    		$("#balance").val(m+".00");
                    	}
                    }
                    $scope.startRecharge = function(){
                    	var param={"cardNo":$scope.list,"masterPlan":$scope.unicomCard.masterPlan};
                    	if($scope.list == "" || $scope.list == null){
                    		dialogService.alertInfo("error", "物联网卡筛选集合不得为空!!!");
                    		return "";
                    	}
                    	if($scope.unicomCard.masterPlan == "" || $scope.unicomCard.masterPlan == null){
                    		dialogService.alertInfo("error", "请选择套餐!!!");
                    		return "";
                    	}
                    	dialogService.openConfirm("确认要给当前筛选出来的物联网卡号进行绑定套餐操作吗？", function (confirm) {
                    		BaseApi.post("/unicomCard/bindingMasterPackage", param, function (data) {
                       		 if(data.status == 200){
                       			 	dialogService.closeConfirm();
                                    dialogService.alertInfo("success", data.msg);
                                    $scope.closeThisDialog(0);
                                    gridService.refresh();
                       		 }else{
                       			 	dialogService.alertInfo("error", data.msg);
                            		return ""; 
                       		 }
                       	 });
                    	});
                    },
                    $scope.queryUincomCard = function(){
                    	var tvalue="";
                    	if($scope.unicomCard.serviceProvider == "" 
                    		|| $scope.unicomCard.serviceProvider == null 
                    		|| $scope.unicomCard.serviceProvider == undefined){
                    		dialogService.alertInfo("error", "请先选择运营商！！！");
                    		return ;
                    	}
                    	BaseApi.query("/unicomCard/queryUincomCard",$scope.unicomCard, function (data) {
                    		if(data == "" || data == null ||data.length == 0){
                    			$scope.list="";
                    			dialogService.alertInfo("error", "未找到范围内的物联网卡号码");
                    		}else{
                    			for(var i = 0;i<data.length;i++){
                    				tvalue+=data[i].cardNo+",";
                    			}
                    			$scope.list=tvalue.substring(0,tvalue.length-1);
                    		}
                    	});
                    },
                    BaseApi.query("/dict/type/service_provider",{}, function (data) {
             		 	$scope.orgList = data;
             	 	});
                    $scope.chooseMasterPackage = function(){
                    	var serviceProvider = $scope.unicomCard.serviceProvider;
                    	if(serviceProvider == null || serviceProvider == "" || serviceProvider == undefined){
                    		dialogService.alertInfo("error", "请选择运营商");
                    		return ;
                    	}
                    	dialogService.openDialog({
                            template: "modules/unicomCard/unicomCard/packageInfoShow.html",
                            width: '60%',
                            controller: function ($scope,dialogService,$timeout) {
                         	   $scope.app={};
                         	   $timeout(function(){
                         		   gridService.initTable({
                         			    url:"/package?openFlag=1&packageType=1&serviceProvider="+serviceProvider,
                                        scope: $scope,
                                        id:"#table1",
                                        operator:false,
                                        singleSelect:true,
                                        columns: [
                                                  {field: 'status',checkbox: 'true'},
                                                  {field: "name", title: "套餐名称"},
                                                  {field: "flowCount", title: "流量数(MB)"},
                                                  {field: "amt", title: "套餐金额(元)"},
                                                  {field: "packageType", title: "套餐类型",
                                               	   formatter: function (value) {
                                         	            	if (value==1) {
                                         						return '<span>普通流量包</span>';
                                         					}
                                         					else if(value==2){
                                         						return '<span>加油包</span>';
                                         					}
                                         	            }
                                                  }
                                            ]
                                    });
                         	    },100)
                          	    $scope.ok = function () {
                          		  var rows2= $("#table1").bootstrapTable("getSelections");
                          		  if (rows2.length == 0) {
                                        dialogService.alertInfo("info", "请选择记录！");
                                        return;
                                  }if(rows2.length >1){
                                	  dialogService.alertInfo("info", "只能选择一条记录！");
                                      return;
                                  }
                          		  parentScope.unicomCard.masterPlan=rows2[0].id;
                          		  parentScope.unicomCard.masterPlanName=rows2[0].name;
                          		  $scope.closeThisDialog(0);
                                 };
                            }
                        });
                    }
                },
                closeByDocument: false
            });
        }
        $scope.telecomUpload = function () {
        	dialogService.openDialog({
                template: "modules/unicomCard/unicomCard/unicomCardUpload.html",
                width: '55%',
                controller: function ($scope, unicomCardService, formService) {
                    $scope.readOnly = false;                    
                    BaseApi.query("/dict/type/service_provider",{}, function (data) {
	         		 	$scope.serviceList = data;
	         	 	});
                    $scope.telcomCardModel = function(){
    	        		var downloadUrl=baseUrl+'/unicomCard/telcomCardModelDownload';
    	                window.open(downloadUrl,'_blank');
    	        	},
    	        	$scope.fileSelected = function(){
    	        		if(!$scope.file){
    	        			return;
    	        		}
    	        		var fileType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
        	            if(fileType != 'xls' && fileType != 'xlsx'){
        	        		dialogService.alertInfo("error", "请上传Excel文件！");
        	        		$scope.file = null;
        	        		return;
        	        	}
    	        	},
                    $scope.import = function(){
    	        		$scope.doingClose = false;
                    	if($scope.file ==  null ){
                    		dialogService.alertInfo("error", "请上传Excel文件！");
        	        		$scope.file = null;
        	        		return ;
        	        	}
        	            /*if ($scope.file.type.indexOf('excel') < 0) {
        	                dialogService.alertInfo("error", "请上传xls文件！");
        	                return;
        	            }*/
        	            var fileType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
        	            if(fileType != 'xls' && fileType != 'xlsx'){
        	        		dialogService.alertInfo("error", "请上传Excel文件！");
        	        		$scope.file = null;
        	        		return;
        	        	}
        	            if($scope.serviceProviderCode ==  null 
    	        				|| $scope.serviceProviderCode ==  "" 
    	        					|| $scope.serviceProviderCode ==  undefined){
    	        			dialogService.alertInfo("error", "请选择物联网卡运营商");
        	        		return ;
        	        	}
        	            $scope.$watch('anguloader.show+doingClose',function(){
        	            	if(!$scope.anguloader.show && !$scope.doingClose){
        	            		$scope.anguloader.show = true;
        	            	}
        	            });
                        Upload.upload({
                            url:  baseUrl+'/unicomCard/uploadCardFile',
                            data: {file: $scope.file,"code":$scope.serviceProviderCode},
                            method: 'POST',
                            headers: securityService.getHeader()
                        }).then(function (resp) {
                        	$scope.list = resp.data.data.toString();
                        	$scope.anguloader.show = false;
                        	$scope.doingClose = true;
                        	if($scope.list == null || $scope.list == ""){
                        		dialogService.alertInfo("success","导入物联网卡信息失败");
                        		return "";
                        	}else{
                        		//dialogService.alertInfo("success", "导入物联网卡信息成功");
                        	}
                        }, function (resp) {
                        	dialogService.alertInfo("warning", "导入物联网卡信息失败");
                        });
                    }
                                            
                },
                closeByDocument: false
            });
          }; 
        $scope.detail = function(index){
        	var row = gridService.getRow(index);
        	$state.go("home.unicomCard.view",{id:row.id});
        }
        
        gridService.setPlaceholder();
    };
});