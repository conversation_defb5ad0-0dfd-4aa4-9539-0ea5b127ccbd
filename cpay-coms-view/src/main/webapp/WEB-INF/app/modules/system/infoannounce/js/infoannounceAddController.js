'use strict';
define(["lib/jquery.form.js"], function () {
    return function ($scope, infoannounceService, $state, dialogService, formService,organizationService,$compile,BaseApi,Upload,baseUrl,securityService, $http) {    	
    	$http.get("modules/system/infoannounce/i18n/en/infoannounceList.json").success(function(dataLan) {
        $scope.infoAnnounceInfo = dataLan.infoAnnounceInfo;
        
        $scope.$parent.thirdTitle = $scope.infoAnnounceInfo.thirdTitle;
    	$scope.update = 2;
    	$scope.infoannounce = {};
//    	$scope.infoannounce.picId = " ";
    	$scope.infoannounce.type = "2";
    	$scope.infoannounce.universal = "1";
        $scope.form = formService.form(function () {
        	$scope.infoannounce.stick = 0;
        	var num = $("#screenshotArrays>li").length;
	           if(num>4){
	        	   dialogService.alertInfo("error", "Please upload up to four images");
	        	   return;
	           }
	       
	       if($scope.infoannounce.type == '2'){
	     	    if($scope.infoannounce.content.length>50){
	     	    		dialogService.alertInfo("error", "The message type is up to 50 words when the terminal informs the announcement.");
	     	    		return ;
	     		} 
	     	}
            infoannounceService.addinfoannounce($scope.infoannounce, function (data) {
                dialogService.alertInfo("success", "Successfully added information announcement!");
                $state.go("home.infoannounce.list");
            });
        });
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.infoannounce) {
                    $scope.infoannounce = {};
                }
                $scope.infoannounce.insId = value.id;
                $scope.infoannounce.insName = value.name;
            });
        };
        
        $scope.setUniversal = function (value) {
        	if(value == 1){
        		if($("#insId").attr("validator")){
        			$("#insId").removeAttr("validator");
        		}
        		
        		if($("#insName").attr("validator")){
        			$("#insName").removeAttr("validator");
        		}
        		$scope.update = 1;
        	}else{
        		if(!$("#insId").attr("validator")){
        			$("#insId").attr("validator","required");
        		}
        		
        		if(!$("#insName").attr("validator")){
        			$("#insName").attr("validator","required");
        		}
        		
        		$scope.update = 2;
        	}
        };
        $scope.uploadFiles = function(files, errFiles) {
	        $scope.files = files;
	        $scope.errFiles = errFiles;
	        if(files.length>4){
	        	 dialogService.alertInfo("error", "Please upload up to four images");
	        	 return;
	        }
	        angular.forEach(files, function(file,index) {
	        	var imgname = file.name;
	        	var imgtype = imgname.substring(imgname.lastIndexOf('.') + 1);
	        	
	        	  if(imgtype !="jpg" && imgtype !="png" && imgtype !="jpeg"){
    	    		   dialogService.alertInfo("error", "Please select a png, jpeg or jpg format image.");
    	    		   return;
    	    	   }
    	           if(file.size>5*1024*1024){
    	    		   dialogService.alertInfo("error", "Please select a picture no more than 5M");
    	    		   return;
    	    	   }
    	         	$scope.pic_num = $("#screenshotArrays>li").length;
        	        var num= $scope.pic_num+1;
        			if (num > 4) {
        				dialogService.alertInfo("error","Upload up to 4 images");
        				return false;
        			}
	            file.upload = Upload.upload({
	            	headers: securityService.getHeader(),
	                url: baseUrl+'/infoannounce/uploadImg',
	                data: {file: file}
	            });

	            file.upload.then(function (resp) {
	             	if(resp.data.status!=3){
    	        		dialogService.alertInfo("error", resp.data.msg);
    	        		return;
    	        	}
    	            var obj = resp.data.data;
    	          	var imgUUID = obj.split(";")[0];
//    	           	if(index==0){
//    	           		$scope.infoannounce.picId = imgUUID
//    	           	}else{
//    	           		$scope.infoannounce.picId = $scope.infoannounce.picId +","+imgUUID
//    	           	}
    				var formId = imgUUID.substr(0, 5);
    				$scope.imgPath = obj.split(";")[1];
    				 var btn_change = '<form id="'+formId +'" action="" class="filename" method="post" enctype="multipart/form-data">'+
    	             '  <a href="javascript:void(0);" class="file">update'+
    	             ' <input type="file" name="' + imgUUID+'" id="'+imgUUID+'" />' +
    	             ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0);" class="file"'+'ng-click=deleteImg("'+ imgUUID +'") >删除</a>'+'</form>';
    				 //var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+$scope.imgPath+"'>";
    				 //var imgPath = $scope.imgPath;
    				 var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
    				 var caption = 'Information related picture';
    				 var img = "<a href='" + imgPath + "'" +
    	             "class='fresco'" +
    	             "data-fresco-group='" + 'Information related picture'+ "'" +
    	             "data-fresco-caption='" + caption + "'>" +
    	             "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
    	             "</a>";	
    				 var li = "<li id='"+imgUUID+"' style='float:left;width: 140px;height: 120px;text-align:center;margin-left:5px;'>" + img
    							+ "<span style='color:black;'>" + btn_change 
    							 + "</span></li>";
    				$('#screenshotArrays').append(li);
    				var el = angular.element(screenshotArrays);
    				$compile(el)($scope);
    				$scope.reset(imgUUID);
    	           	
	            }, function (resp) {
	            	dialogService.alertInfo("error",'Failed to upload image');
	            }, function (evt) {
	                file.progress = Math.min(100, parseInt(100.0 * 
	                                         evt.loaded / evt.total));
	            });
	        });
	    };
	    BaseApi.get("/infoannounce/updatePicList","",function(data){
        });  

   $scope.deleteImg = function(imgUUID){
		//deletePicIds(imgUUID);
	   BaseApi.query("/infoannounce/deleteImg?uuid="+imgUUID,"",function(data){
		   $("#" + imgUUID).remove();
        });  
   }
   function deletePicIds(imgUUID){
   	var picIds = $scope.infoannounce.picId;
   	if(picIds.indexOf(imgUUID+',')>=0){
   		$scope.infoannounce.picId = picIds.replace(imgUUID+',',"");
   	}else if(picIds.indexOf(','+imgUUID)>=0){
   		$scope.infoannounce.picId = picIds.replace(','+imgUUID,"");
   	}else if(picIds.indexOf(imgUUID)>=0){
   		$scope.infoannounce.picId = picIds.replace(imgUUID,"");
   	}
   }
  $scope.reset = function(imgUUID){
	  angular.forEach($scope.fileImg, function(file) {
		  var name = file.name.substring(file.name.lastIndexOf('.') + 1);
    	  name = name.toLocaleLowerCase();
	   	  if(name !="jpg" && name != "png" && name !="jpeg"){
	   		   dialogService.alertInfo("error", "Please select a png, jpeg or jpg format image.");
	   		   return;
	   	  }
	  });
	  var formId = imgUUID.substr(0, 5);
	  $("input[name="+imgUUID+"]").change(function(){
			$("#"+formId).ajaxSubmit({
	            type: 'post',
	            data: {"uuid":imgUUID},
	            headers: securityService.getHeader(),
	            url: baseUrl+'/infoannounce/changeImg',
	            /* dataType:  'json',  */
	            resetForm: false,
	            // 成功提交后，重置所有表单元素的值
	            success: function(data) {
	            	if(data.status!=5){
	            		dialogService.alertInfo("error", data.status);
	            		return;
	            	}
	            	var obj = data.data;
	               	var nimgUUID = obj.split(";")[0];
	     			var formId = nimgUUID.substr(0, 5);
	     			$scope.imgPath = obj.split(";")[1];
	     			 var btn_change = '<span style="color:black;"><form id="'+formId +'" action="" class="filename" method="post" enctype="multipart/form-data">'+
	                  '  <a href="javascript:void(0)" class="file">update'+
	                  ' <input type="file" name="' + nimgUUID + '" id="' + nimgUUID+'" />' +
	                  ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0)" class="file"'+'ng-click=deleteImg("'+ nimgUUID +'") >删除</a>'+'</form></span>';
	     	
	     			//var imgPath = $scope.imgPath;
	     			var caption = "Information related picture";
	     			var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
     				var img = "<a href='" + imgPath + "'" +
                    "class='fresco'" +
                    "data-fresco-group='" + 'Information related picture'+ "'" +
                    "data-fresco-caption='" + caption + "'>" +
                    "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
                    "</a>";	
	     			/* var li = "<li id='"+nimgUUID+"' style='float:left;width: 110px;height: 120px;text-align:center;margin-left:5px;'>" + img
	     						+ "<span style='color:black;'>" + btn_change 
	     						 + "</span></li>";*/
	     			$("#" + imgUUID).empty();
	     			//$("#" + imgUUID).attr("id", nimgUUID).append(li);
	     			$("#" + imgUUID).attr("id", nimgUUID).append(img).append(btn_change);
	     			var el = angular.element(screenshotArrays);
	     			$compile(el)($scope);
	     			$scope.reset(nimgUUID);
	     			
	            },
	            error: function(XmlHttpRequest, textStatus, errorThrown) {
	            	
	            }

	        });
			
		});
  }
    };
});