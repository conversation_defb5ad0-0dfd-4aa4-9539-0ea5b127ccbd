'use strict';
define(["../js/organizationRest", "treeService", "organizationService"], function () {
    return function ($scope, organizationRest, organizationService, $state, dialogService, gridService, treeService,$timeout,$stateParams, $http) {
    	$scope.secondTitle = "机构管理";
        $scope.firstTitle = "系统管理";
    	$scope.$parent.thirdTitle = "";
    	
    	$http.get("modules/system/organization/i18n/en/organizationList.json").success(function(dataLan) {
		$scope.organizationInfo = 	dataLan.organizationInfo;
        	
    	$scope.secondSref = "home.organization.list({id:'1'})";
    	var treeObj = {};
        (function (){
        	initTree();         	
 		})();
        
        var clickItem = function (event, treeId, treeNode) {
            $scope.pId = treeNode.id;
            $state.go('home.organization.list', {id: treeNode.id});            
        };
        var bfDrop = function (treeId, treeNodes, targetNode, moveType){
        	if(!targetNode){
        		return false;
        	}
        	
        	var targetNodeTemp = targetNode;
        	if(moveType=='prev' || moveType=='next'){
        		targetNodeTemp = targetNode.getParentNode();
        	}
        	//移动层级不能超过五层
        		var i = 0;
        		while(targetNode.getParentNode()){
        			targetNode = targetNode.getParentNode();
        			i++;
        			if(i>4){
        				return false;
        			}
        		}
        	var data={id:treeNodes[0].id,parentId:targetNodeTemp.id};
        	organizationRest.updateOrganization(data,function(){});
        }
        
        $scope.initTree = function(){
        	initTree();
        }
        
        function initTree(pId){
        	organizationRest.getOrganizationTree("", function (data) {
	        	treeService.initTree({
	                treeId: "orgTree",
	                nodes: data,
	                setting: {
	                    data: {
	                        simpleData: {
	                            enable: true,
	                            idKey: "id",
	                            pIdKey: "parentId"
	                        },
	                        key: {
	                            url: "xUrl",
	                            title : "code"
	                        }
	                    }, callback: {
	                    	onClick: clickItem,
	                    	beforeDrop: false
	                    },edit:{
	                		enable:true,
	                		drag:{
	                			isCopy:false,
	                			inner:true,
	                			isMove:false
	                			
	                		},
	                		showRemoveBtn:false,
	                		showRenameBtn:false,
	                	}
	                }
	            });
	        	treeObj = treeService.getZtree();
                var node = treeObj.getNodes()[0];
                treeObj.selectNode(node);
                treeObj.setting.callback.onClick(null, treeObj.setting.treeId, node);
            });
        }

        $scope.addRow = function () { 
        	dialogService.openDialog({
				template : "modules/system/organization/organizationAdd.html",
				width:"720px",
				controller : function($scope, formService,$http) {
					$http.get("modules/system/organization/i18n/en/organizationList.json").success(function(dataLan) {
					$scope.organizationInfo = 	dataLan.organizationInfo;
					$scope.$parent.$parent.thirdTitle = $scope.organizationInfo.add_organization;
			        	if (!$scope.organization) {
			                 $scope.organization = {"moveMonitorType":"1","indexType":"0","enabled":"0"};
			            }
				        $scope.openInsDialog = function () {
				        	
				            organizationService.openOrganization(function (value) {
				                $scope.organization.parentId = value.id;
				                $scope.organization.parentName = value.name;
				                
				            });
				            
				        };
				        $scope.onlyCode1 = function () {
				            if (!$scope.organization || !$scope.organization.code) {
				                return;
				            }
				            organizationRest.validate($scope.organization.code,"机构编号已存在","code1");
				        };
				        
				        $scope.onlyName1 = function () {
				            if (!$scope.organization || !$scope.organization.name) {
				                return;
				            }
				            organizationRest.validate($scope.organization.name,"机构名称已存在","name1");
				        };
				        
				        $scope.form = formService.form(function () {
				        	if ($(".ng-invalid").length>0) {
				                dialogService.alertInfo("warning","还有未通过的验证项！");
				                return;
				            }
				        	if (!$scope.organization.parentId) {
				                $scope.organization.parentId = 0;
				            }
				        	if($scope.organization.auto == true){
				        		$scope.organization.auto = "1";
				        	}else{
				        		$scope.organization.auto = "0";
				        	}
				        	if($scope.organization.startLimitIp == true){
				        		$scope.organization.startLimitIp = "1";
				        	}else{
				        		$scope.organization.startLimitIp = "0";
				        	}
				        	if($scope.organization.isMonitor == true){
				        		$scope.organization.isMonitor = "1";
				        	}else{
				        		$scope.organization.isMonitor = "0";
				        	}
				        	if($scope.organization.isLock == true){
				        		$scope.organization.isLock = "1";
				        	}else{
				        		$scope.organization.isLock = "0";
				        	}
				            organizationRest.addOrganization($scope.organization, function () {
				            	$scope.closeThisDialog(0);
				                dialogService.alertInfo("success", "Add organization successfully");
				                $state.go("home.organization");
				                initTree();
				            });
				        })
					});
				}
			});
        }
        gridService.setPlaceholder();
        $timeout(function(){
            //$('.content-scroller').scrollTop(0);
            //alert($('.content-scroller').length)
        },300)
    	});
    };
});