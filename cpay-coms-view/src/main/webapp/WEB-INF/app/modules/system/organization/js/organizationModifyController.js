'use strict';
define([], function () {
    return function ($scope,BaseApi, organizationRest, $state,$stateParams,Upload,baseUrl,securityService, organizationService,formService, dialogService, $http) {
        
    	$http.get("modules/system/organization/i18n/en/organizationList.json").success(function(dataLan) {
    	$scope.organizationInfo = 	dataLan.organizationInfo;
    	
    	$scope.$parent.$parent.thirdTitle = "";
        $scope.organization = {};
//        $scope.organization.moveMonitorType = "1";
        $scope.organization.auto = false;
        var user = BaseApi.getUser();
        $scope.organization.startLimitIp = false;
        $scope.organization.isMonitor = false;
        $scope.organization.isLock = false;
    	if($scope.$parent.pId != $stateParams.id){
    		$scope.$parent.initTree();
    	}
    	
    	$scope.show2 = true;
        var oName="",oCode="";
        $scope.file={};
        $scope.file2={};
        if ($stateParams.id) {
        	organizationRest.getOrganization($stateParams.id,function(data){
	            $scope.organization= data;
	        	if($scope.organization.auto == "1"){
	        		$scope.organization.auto = true;
	        	}else if($scope.organization.auto == "0"){
	        		$scope.organization.auto = false;
	        	}
	        	if($scope.organization.startLimitIp == "1"){
	        		$scope.organization.startLimitIp = true;
	        	}else if($scope.organization.startLimitIp == "0"){
	        		$scope.organization.startLimitIp = false;
	        	}
	        	if($scope.organization.isMonitor == "1"){
	        		$scope.organization.isMonitor = true;
	        	}else if($scope.organization.isMonitor == "0"){
	        		$scope.organization.isMonitor = false;
	        	}
	        	if($scope.organization.isLock == "1"){
	        		$scope.organization.isLock = true;
	        	}else if($scope.organization.isLock == "0"){
	        		$scope.organization.isLock = false;
	        	}
	            if(data.auto == 1){
	            	$scope.show1 = true;
	            }else{
	            	$scope.show1 = false;
	            }
	            oName = data.name;
	            oCode = data.code;
	            organizationRest.getOrganization(data.parentId,function(data){
	                $scope.organization.parentName = data.name;
	            })

	        });
        }
        $scope.openInsDialog = function () {
            if($scope.organization.id == user.insId){
                dialogService.alertInfo("info", $scope.organizationInfo.not_allowed_modified);
                return false;
            }
            organizationService.openOrganization(function (value) {
                if (!$scope.organization) {
                    $scope.organization = {};
                }
                $scope.organization.parentId = value.id;
                $scope.organization.parentName = value.name;
            });
        };
        $scope.onlyCode = function () {
            if (!$scope.organization || !$scope.organization.code || oCode ==$scope.organization.code) {
                return;
            }
            organizationRest.validate({code:$scope.organization.code},"The code of organization already exists!");
        };
        
        $scope.onlyName = function () {
            if (!$scope.organization || !$scope.organization.name || oName ==$scope.organization.name) {
                return;
            }
            organizationRest.validate({name:$scope.organization.name},"The name of organization already exists!");
        };
        $scope.clearDialog = function () {
            $scope.organization.parent_ins_cd = "";
            $scope.organization.parent_ins_nm = "";
        };
        $scope.form = formService.form(function(){

        	if ($(".ng-invalid").length>0) {
                dialogService.alertInfo("warning","There are also unverified verification items!");
                return;
            }
        	if($scope.organization.enabled=='2'){
        		if($scope.organization.publickeyPath ==null || $scope.organization.publickeyPath ==""){
        			dialogService.alertInfo("warning","Please upload the public key certificate file!");
        			return;
        		}
        		if($scope.organization.privatekeyPath ==null || $scope.organization.privatekeyPath ==""){
        			dialogService.alertInfo("warning","Please upload the private key certificate file!");
        			return;
        		}
        	}
        	if($scope.organization.publickeyPath !=null && $scope.organization.publickeyPath !=""){
        		if($scope.organization.publickeyPath.indexOf("appsystem") !='-1'){
            		$scope.organization.publickeyPath=null;
            	}
        	}
        	if($scope.organization.privatekeyPath !=null && $scope.organization.privatekeyPath !=""){
        		if($scope.organization.privatekeyPath.indexOf("appsystem") !='-1'){
        			$scope.organization.privatekeyPath=null;
            	}
        	}
        	if($scope.organization.auto == true){
        		$scope.organization.auto = "1";
        	}else if($scope.organization.auto == false){
        		$scope.organization.auto = "0";
        	}
        	if($scope.organization.startLimitIp == true){
        		$scope.organization.startLimitIp = "1";
        	}else if($scope.organization.startLimitIp == false){
        		$scope.organization.startLimitIp = "0";
        	}
        	if($scope.organization.isMonitor == true){
        		$scope.organization.isMonitor = "1";
        	}else if($scope.organization.isMonitor == false){
        		$scope.organization.isMonitor = "0";
        	}
        	if($scope.organization.isLock == true){
        		$scope.organization.isLock = "1";
        	}else if($scope.organization.isLock == false){
        		$scope.organization.isLock = "0";
        	}
        	organizationRest.updateOrganization($scope.organization, function () {
                dialogService.alertInfo("success", "Successful Organization Modification!");
                $scope.$parent.initTree();
                /*organizationRest.getOrganization($stateParams.id,function(data){
    	            $scope.organization= data;
    	            oName = data.name;
    	            oCode = data.code;
    	        });*/
                oName=$scope.organization.name;
                oCode=$scope.organization.code;
	        	if($scope.organization.auto == "1"){
	        		$scope.organization.auto = true;
	        	}else if($scope.organization.auto == "0"){
	        		$scope.organization.auto = false;
	        	}
	        	if($scope.organization.startLimitIp == "1"){
	        		$scope.organization.startLimitIp = true;
	        	}else if($scope.organization.startLimitIp == "0"){
	        		$scope.organization.startLimitIp = false;
	        	}
	        	if($scope.organization.isMonitor == "1"){
	        		$scope.organization.isMonitor = true;
	        	}else if($scope.organization.isMonitor == "0"){
	        		$scope.organization.isMonitor = false;
	        	}
	        	if($scope.organization.isLock == "1"){
	        		$scope.organization.isLock = true;
	        	}else if($scope.organization.isLock == "0"){
	        		$scope.organization.isLock = false;
	        	}
                $state.go("home.organization.list",{id:$stateParams.id});
            });
        });
        $scope.submitPublicFile = function() {
            if (!$scope.file||!$scope.file.size) {
             	dialogService.alertInfo("success", "Please select a file");
             	return;
            }
//            if ($scope.file.name.indexOf('cert') < 0 && $scope.file.name.indexOf('pem') < 0 
//         		   && $scope.file.name.indexOf('CERT') < 0 && $scope.file.name.indexOf('PEM') < 0) {
//                     dialogService.alertInfo("error", "请上传.cert 或者.pem格式的证书文件");
//                     return;
//            }
             Upload.upload({
                 url:  baseUrl+'/terminalCert/uploadTerminalCert',
                 data: {file: $scope.file},
                 headers: securityService.getHeader(),
                 method: 'POST'
             },120000).then(function (resp) {
             	var arr = resp.data.data;
             	$scope.organization.publickeyPath=arr[3];
             	$scope.organization.publickeyPathName=arr[4]+"."+arr[2];
             	dialogService.alertInfo("success", "Successful file upload!");
             }, function (resp) {
             	dialogService.alertInfo("warning", "Failed file upload!");
             }, function (evt) {
          	$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
             	$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
             });
           };
           $scope.submitPrivateFile = function() {
               if (!$scope.file2||!$scope.file2.size) {
                	dialogService.alertInfo("success", "Please select a file");
                	return;
               }
//               if ($scope.file.name.indexOf('cert') < 0 && $scope.file.name.indexOf('pem') < 0 
//            		   && $scope.file.name.indexOf('CERT') < 0 && $scope.file.name.indexOf('PEM') < 0) {
//                        dialogService.alertInfo("error", "请上传.cert 或者.pem格式的证书文件");
//                        return;
//               }
                Upload.upload({
                    url:  baseUrl+'/terminalCert/uploadTerminalCert',
                    data: {file: $scope.file2},
                    headers: securityService.getHeader(),
                    method: 'POST'
                },120000).then(function (resp) {
                	var arr = resp.data.data;
                	$scope.organization.privatekeyPath=arr[3];
                	$scope.organization.privatekeyPathName=arr[4]+"."+arr[2];
                	dialogService.alertInfo("success", "Successful file upload!");
                }, function (resp) {
                	dialogService.alertInfo("warning", "Failed file upload!");
                }, function (evt) {
             	$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                	$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
                });
              };
           $scope.selectFile = function(){
         	  $('.progress>.bar').width('0%');
         	  $('.progress').css("display","inline");
           }
        $scope.addDefaultMerchant = function(){
        	$state.go('home.organization.addDefaultMerchant', {id: $stateParams.id});
        };
        $scope.addDefaultCert = function(){
        	$state.go('home.organization.addDefaultCert', {id: $stateParams.id});
        };
        $scope.configureElements = function(){
        	$state.go('home.organization.organizationElements', {id: $stateParams.id});
        }
    	});
    };
});