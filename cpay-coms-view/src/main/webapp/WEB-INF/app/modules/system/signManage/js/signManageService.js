'use strict';
define(['js/app'], function (app) {
    app.factory("signManageService", function (BaseApi,toolsService) {
        var adData = {};
        return {
            getAdList: function (data, success) {
                BaseApi.get("/ad/"+data.id, data, success);
            },
            setAdData: function (ad) {
                adData = ad;
            },

            getAdData: function () {
                return adData;
            },
            addAd: function (data, success) {
            	data.startTime=new Date(data.startTime);
            	data.endTime=new Date(data.endTime);
                BaseApi.post("/ad", data, success);
            },
            updateAd: function (data, success) {
            	data.startTime=new Date(data.startTime);
            	data.endTime=new Date(data.endTime);
                BaseApi.patch("/ad", data, success);
            },
            deleteByPrimaryKey: function (id, success) {
                BaseApi["delete"]("/signManage/" + id, success);
            },
            stopAd: function(id, success){
            	BaseApi.patch("/ad/adEnabled?id=" + id + "&param=1", null, success);
            },
            startAd: function(id, success){
            	BaseApi.patch("/ad/adEnabled?id=" + id + "&param=0", null, success);
            },
        };
    });
});