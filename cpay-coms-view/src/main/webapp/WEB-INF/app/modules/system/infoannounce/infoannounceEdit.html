<div>
	<style>
#screenshotArrays li {
	color: white;
}

#screenshotArrays {
	overflow: hidden;
	height: auto;
	width: auto;
	padding-left: 0px;
}

.file {
	position: relative;
	display: inline-block;
	background: #D0EEFF;
	border: 1px solid #99D3F5;
	border-radius: 4px;
	padding: 2px 8px;
	top: 8px;
	overflow: hidden;
	color: #1E88C7;
	text-decoration: none;
	text-indent: 0;
	line-height: 20px;
}

.file a {
	filter: (opacity=100);
}

.file input {
	cursor: pointer;
	position: absolute;
	/*   left:0;
  top:0;
  width:100%;
  height:100%; */
	z-index: 100;
	opacity: 0;
	filter: alpha(opacity = 0);
	margin-left: -800px;
	font-size: 1000px !important;
	margin-top: -2px;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
	.file input {
		margin-top: -24px;
	}
}

.file:hover {
	background: #AADFFD;
	border-color: #78C3F3;
	color: #004974;
	text-decoration: none;
}
</style>
</div>
<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <form name="Form" class="form-horizontal" id="editform">
            	<div class="control-group">
                    <label class="control-label">{{infoAnnounceInfo.type}}<span class="required">*</span></label>
                    <div class="controls">
                        <label class="radio line margin-normal">
                            <input type="radio" name="type" ng-model="infoannounce.type"
                                   class="ace" validator="required"
                                   ng-disabled="disabled"
                                   message-id="type" value="2" required-error-message="请选择信息类型"/>
                            <span  class="lbl">{{infoAnnounceInfo.term_info_notification}}</span>
                        </label>
                        <label class="radio line margin-normal">
                            <input type="radio" name="type" ng-model="infoannounce.type"
                                   class="ace" validator="required"
                                   ng-disabled="disabled"
                                   message-id="type" value="0" required-error-message="请选择信息类型"/>
                            <span  class="lbl">{{infoAnnounceInfo.platform_information_show}}</span>
                        </label>
                       <!-- <label class="radio line margin-normal">
                            <input type="radio" name="type" ng-model="infoannounce.type"
                                   class="ace" validator="required"
                                   ng-disabled="disabled"
                                   message-id="type" value="1" required-error-message="请选择信息类型"/>
                            <span class="lbl">帮助中心</span>
                        </label>-->
                        <span id="type" class="help-inline"></span>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">{{infoAnnounceInfo.title}}<span class="required">*</span></label>
                    <div class="controls">
                        <input type="text" maxlength="50" name="title" ng-model="infoannounce.title" class="span6"
                               initial-validity="false" ng-disabled="disabled" required-error-message={{infoAnnounceInfo.please_write_title}}
                               validator="required" message-id="title"/>
                        <span id="title" class="help-inline"> </span>
                    </div>
                </div>
                 <div class="control-group" ng-show="infoannounce.type == 1 || infoannounce.type == 0">
                    <label class="control-label">{{infoAnnounceInfo.introduction}}</label>
                    <div class="controls">
                        <textarea rows="10" name="introduction" ng-model="infoannounce.introduction" class="span8"
                                  initial-validity="false" ng-disabled="disabled" 
                                  message-id="introduction"></textarea>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">{{infoAnnounceInfo.content}}<span class="required">*</span></label>
                    <div class="controls">
                        <textarea rows="10" name="content" ng-model="infoannounce.content" class="span8"
                                  initial-validity="false" ng-disabled="disabled" required-error-message={{infoAnnounceInfo.please_write_content}}
                                  validator="required" message-id="content"></textarea>
                        <span id="content" class="help-inline"> </span>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label">{{infoAnnounceInfo.is_general}}<span class="required">*</span></label>
                    <div class="controls">
                        <label class="radio line margin-normal">
                            <input type="radio" name="universal" ng-model="infoannounce.universal"
                                   class="ace" ng-click="setUniversal(1)"
                                   validator="required"
                                   ng-disabled="disabled"
                                   message-id="universal" value="1" required-error-message="请选择是否通用"/>
                            <span class="lbl">{{infoAnnounceInfo.yes}}</span>
                        </label>
                        <label class="radio line margin-normal">
                            <input type="radio" name="universal" ng-model="infoannounce.universal"
                                   class="ace" ng-click="setUniversal(0)"
                                   validator="required"
                                   ng-disabled="disabled"
                                   message-id="universal" value="0" required-error-message="请选择是否通用"/>
                            <span class="lbl">{{infoAnnounceInfo.no}}</span>
                        </label>
                        <span id="universal" class="help-inline"></span>
                    </div>
                </div>         
                <div class="control-group" ng-show="infoannounce.universal == 0 ">
					<label class="control-label">Organization<span class="required">*</span></label>
					<div class="controls">
					<input type="text" id="insId" name="insId" ng-model="infoannounce.insId" ng-show="false"
					       ng-disabled="disabled"
					       message-id="insId"/>
					       
					<div class="input-append input-group" style="margin-left: 0px" ng-click="openInsDialog()">
					    <input type="text" id="insName" name="insName" ng-model="infoannounce.insName" readonly
					    valid-method="watch"       ng-disabled="disabled" class=" top-inp"
					           message-id="insName"/>
					    <button class="choice btn" type="button"  ng-disabled="disabled"></button>
					    <!--<button class="btn" ng-click="clearDialog()" ng-disabled="disabled" >清空</button>-->
					</div>
					    <span id="insName" class="help-inline"></span>
					</div>
			</div>
			 <div class="control-group" ng-show="infoannounce.type == 1 || infoannounce.type == 0"> 
					<label class="control-label">{{infoAnnounceInfo.images}}</label>
					<div class="controls">
						<button ngf-select="uploadFiles($files, $invalidFiles)" multiple
							ng-model="fileImg" name="fileImg">{{infoAnnounceInfo.upload}}</button>
						<span>{{infoAnnounceInfo.images_tip}}</span>
					</div>

				</div>
				<div class="control-group" >
					<label class="control-label"></label>
					<div class="controls">
						<ul id="screenshotArrays" class="screenshot">

						</ul>
					</div>
				</div>
            </form>
        </div>
    </div>
</div>
<div ng-show="!disabled" form-foot goback="$state.go('home.infoannounce.list')"
     submit="form.submit(Form)"
     reset="form.reset(Form)">
</div>