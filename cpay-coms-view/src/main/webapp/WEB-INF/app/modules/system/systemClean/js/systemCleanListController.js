'use strict';
define(function () {
	return function ($scope, systemCleanService,organizationService, $state,dialogService, gridService) {
		$scope.$parent.thirdTitle = "";
		var parentScope =$scope ;
		$scope.openOrganization = function () {
			organizationService.openOrganization(function (value) {
				$scope.systemClean.insId = value.id;
				$scope.systemClean.insName = value.name;
			});
		};
		$scope.change = function(){
			if($scope.systemClean.type == 0){
				$('#orgDiv').css("display", "none");
				$('#termDiv').css("display", "none");
			}else if($scope.systemClean.type == '1'){
				$('#orgDiv').css("display", "block");
				$('#termDiv').css("display", "none");
			}else if($scope.systemClean.type == '2'){
				$('#orgDiv').css("display", "none");
				$('#termDiv').css("display", "block");
			}
		}
		$scope.showTermSeq = function(){
			dialogService.openDialog({
				template: "modules/system/systemClean/showTermSeqList.html",
				width: '60%',
				controller: function ($scope,dialogService,$timeout) {
					$timeout(function(){
						gridService.initTable({
							url:"/terminal",
							scope: $scope,
							operator:false,
							uniqueId: 'id',
							columns: [
								{field: 'state', checkbox: 'true'},
								{field: "termSeq", title: "Serial No"},
								{field: "insName", title: "Organization"},
								{field: "termMfrName", title: "Terminal Factory"},
								{field: "termTypeName", title: "Terminal Type"}

							]
						});
					},100)
					$scope.searchTermInfo = function () {
						gridService.search($scope.condition);
					};
					$scope.resetTermInfo = function () {
						$scope.condition={};
						gridService.search($scope.condition);
					};
					$scope.openOrganization = function () {
						organizationService.openOrganization(function (value) {
							if (!$scope.condition) {
								$scope.condition = {};
							}
							$scope.condition.insId = value.id;
							$scope.condition.insName = value.name;
						});
					};
					$scope.ok = function () {
						var rows= $("#table").bootstrapTable("getSelections");
						if (rows.length == 0) {
							dialogService.alertInfo("info", "Please select record！");
							return;
						}
						var ids = "";
						if(angular.element("#tout_acct_list option").size()==0){
							for (var index in rows) {
								angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
									+rows[index].termSeq +"</option>");
							}

						}else{
							for (var index in rows) {
								var j =0;
								angular.element("#tout_acct_list option").each(function () {
									if(rows[index].termSeq==$(this).val()){
										return false;
									}
									j++;
								});
								if(j==angular.element("#tout_acct_list option").size()){
									angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
										+rows[index].termSeq +"</option>");
								}
							}
						}

						$scope.closeThisDialog(0);

					};
				}
			});
		}

		$scope.deleteTerm = function () {
			var oSelect = angular.element("#tout_acct_list");
			var options = oSelect.option;
			var termSeqs = $("#tout_acct_list option:selected").val();
			if(termSeqs == undefined || termSeqs == null || termSeqs == ""){
				dialogService.alertInfo("success", "Please select at least one record!");
				return ;
			}
			$("#tout_acct_list option:selected").remove();
		}
		$scope.clearTerm = function () {
			var selectObj = angular.element("#tout_acct_list").get(0);
			var options = selectObj.options;
			if(options.length == 0) {
				dialogService.alertInfo("success", "No terminal information is currently available for clearing！");
				return ;
			}
			dialogService.openConfirm("Are you sure want to empty the current terminal collection？", function () {
				$("#tout_acct_list option").remove();
				dialogService.closeConfirm();
			});
		}

		$scope.ok = function(){
			var type = $scope.systemClean.type;
			var insId = $scope.systemClean.insId;
			if(type == null || type == ""){
				dialogService.alertInfo("info", "Please select object！");
				return;
			}else if(type == '1'){
				var collections ="";
				var selectObj = angular.element("#tout_acct_list").get(0);
				var options = selectObj.options;
				if(options.length == 0) {
					dialogService.alertInfo("info", "The terminal set cannot be empty！");
					return;
				}
				for(var i = 0;i < options.length;i++) {
					collections += options[i].text+",";
				}
				$scope.systemClean.termCollection = collections.substring(0,collections.length-1);

				dialogService.openConfirm("The deletion of terminal information cannot be restored. Confirm whether to continue？", function () {
					dialogService.closeConfirm();
					dialogService.openConfirm("Confirm again whether the cleanup operation is performed？", function () {
						systemCleanService.systemClean($scope.systemClean, function (data) {
							$("#msg").val(data.msg);
							dialogService.closeConfirm();
							dialogService.alertInfo("success", "Delete Success！");
						},10000);
					});
				});


			}else if(type == '2'){
				if(insId == null || insId == ""){
					dialogService.alertInfo("info", "The  institution shall not be empty！");
					return;
				}
				dialogService.openConfirm("Delete the organization, will delete all the terminal and application information under the organization, whether to confirm the deletion？", function () {
					dialogService.closeConfirm();
					dialogService.openConfirm("Confirm again whether the cleanup operation is performed？", function () {
						systemCleanService.systemClean($scope.systemClean, function (data) {
							$("#msg").val(data.msg);
							dialogService.closeConfirm();
							dialogService.alertInfo("success", "Delete Success！");
						},10000);
					});
				});
			}
		}
	};
});