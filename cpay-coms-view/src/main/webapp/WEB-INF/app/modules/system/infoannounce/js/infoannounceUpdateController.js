'use strict';
define(['lib/raty/jquery.raty.js','lib/jquery.form.js'], function () {
    return function ($scope, infoannounceService,baseUrl,BaseApi,Upload, $state, $stateParams, dialogService, formService, organizationService,toolsService,$compile,securityService) {
        $scope.$parent.thirdTitle = "Announcement modification";
        if($state.is("home.infoannounce.view")){
              $scope.disabled =true;
              $scope.$parent.thirdTitle = "Announcement details";
        }
        infoannounceService.getinfoannounce($stateParams.id, function (data) {
            $scope.infoannounce = data;
        	var picList = data.picList;  
            if($scope.infoannounce.universal == 0){
            	$scope.update = 2;
            }else{
            	$scope.update = 1;
            }
            if($state.is("home.infoannounce.view")){
                if ($scope.infoannounce.type == 0) {
                	$scope.infoannounce.type = "Announcement";
                } else {
                	$scope.infoannounce.type = "Help center";
                }
                
                if ($scope.infoannounce.universal == 0) {
                	$scope.infoannounce.universal = "No";
                } else {
                	$scope.infoannounce.universal = "Yes";
                }
                
                if ($scope.infoannounce.stick == 0) {
                	$scope.infoannounce.stick = "No";
                } else {
                	$scope.infoannounce.stick = "Yes";
                }
                
                if(!$scope.infoannounce.insId){
                	$scope.infoannounce.insName = "all";
                }
                
                $scope.infoannounce.createTime = toolsService.getFullDate($scope.infoannounce.createTime);
                $scope.infoannounce.modifyTime = toolsService.getFullDate($scope.infoannounce.modifyTime);
            }
            var imgUUIDs = {};
         	if( $scope.infoannounce.picId!=null){
         		imgUUIDs = $scope.infoannounce.picId.split(",");
         	}
         	
        	if(picList!=null){
        		for(var i=0;i<picList.length;i++){
        			var imgPath = $scope.infoannounce.ngsPicPath + picList[i];
        			//var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+imgPath+"'>";
        			
        			var imgUUID = imgUUIDs[i];
       			var formId = imgUUID.substr(0, 5);
       			
       			 var btn_change = '<form action="" class="filename" method="post" enctype="multipart/form-data">'+
                    '  <a href="javascript:void(0);" class="file">update'+
                    ' <input type="file" name="' + imgUUID + '" id="' + imgUUID + '" />' +
                    ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0);" class="file" ng-click=deleteImg("'+ imgUUID +'"); >删除</a>'+'</form>';
        			
        			var caption = "Announcement information picture";
    				var img = "<a href='" + imgPath +'?'+new Date()+ "'" +
                   "class='fresco'" +
                   "data-fresco-group='" + "Announcement information picture" + "'" +
                   "data-fresco-caption='" + caption + "'>" +
                   "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath +"?t=" + Math.random() +"'>" +
                   "</a>";
        			var li = "<li id='"+imgUUID+"' style='float:left;width: 110px;height: 120px;text-align:center;margin-left:5px;'>" + img
        					+ "<span style='color:black;'>" + btn_change + "</span></li>";
        			$('#screenshotArrays').append(li);
        			$scope.reset(imgUUID);
        		}
        		var el = angular.element(screenshotArrays);
           	$compile(el)($scope);
        	}
        	
            
        });
        
        $scope.reset = function(imgUUID){
      	  $("input[name=" + imgUUID + "]").change(function(){
      		  var newFile = $("input[name=" + imgUUID + "]").val();
      		  var type = newFile.substring(newFile.lastIndexOf(".")+1);
      		  type = type.toLocaleLowerCase();
      		  if(type!="png" && type != "jpg" && type != "jpeg"){
      			dialogService.alertInfo("error", "Please select a png, jpeg or jpg format image.");
      		   return;
      		  }
      		  var formElement = $(this).parent().parent();
      		  formElement.ajaxSubmit({
  		            type: 'post',
  		            data: {"uuid":imgUUID},
  		            headers: securityService.getHeader(),
  		            url: baseUrl + '/infoannounce/changeStoredImg',
  		            resetForm: false,
  		            // 成功提交后，重置所有表单元素的值
  		            success: function(data) {
  		            	if(data.status != 200){
  		            		dialogService.alertInfo("error", data.msg);
  		            		return;
  		            	}
  		            	var obj = data.data;
  		               	var newImgUUID = obj.split(";")[0];
  		              $scope.infoannounce.picId = $scope.infoannounce.picId ;
  		             var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
  		     			 var btn_change = '<span style="color:black;"><form action="" class="filename" method="post" enctype="multipart/form-data">'+
  		                  '  <a href="javascript:void(0)" class="file">update'+
  		                  ' <input type="file" name="' + newImgUUID + '" />' +
  		                  ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0)" class="file"'+'ng-click=deleteImg("'+ newImgUUID +'") >删除</a>'+'</form></span>';
  		     			var caption = "Announcement information picture";
  	     				var img = "<a href='" + imgPath + "'" +
  	                    "class='fresco'" +
  	                    "data-fresco-group='" + "Announcement information picture" + "'" +
  	                    "data-fresco-caption='" + caption + "'>" +
  	                    "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath +"&t=" + Math.random()+ "'>" +
  	                    "</a>";	
  		     			$("#" + imgUUID).empty();
  		     			$("#" + imgUUID).attr("id", newImgUUID).append(img).append(btn_change);
  		     			var el = angular.element(screenshotArrays);
  		     			$compile(el)($scope);
  		     			$scope.reset(newImgUUID);
  		            }
  		        });
      		  
  			});
        };
        
        $scope.deleteImg = function(imgUUID){
        		deletePicIds(imgUUID);
      		   $("#" + imgUUID).remove();
      		  BaseApi.query("/infoannounce/deleteImg?uuid="+imgUUID,"",function(data){
	            }); 
            	/*BaseApi['delete']("/apk/deleteStoredImg?uuid=" + imgUUID,function(data){
            		dialogService.closeConfirm();
                    dialogService.alertInfo("info",data.msg);
          		   $("#" + imgUUID).remove();
                  }); */
        };
        $scope.uploadFiles = function(files, errFiles) {
	        $scope.files = files;
	        $scope.errFiles = errFiles;
	        if(files.length>4){
	        	 dialogService.alertInfo("error", "Please upload up to 4 images");
	        	 return;
	        }
	        angular.forEach(files, function(file,index) {
	        	var imgname = file.name;
	        	var imgtype = imgname.substring(imgname.lastIndexOf('.') + 1);
	        	
	        	  if(imgtype !="jpg" && imgtype !="png" && imgtype !="jpeg"){
    	    		   dialogService.alertInfo("error", "Please select a png, jpeg or jpg format image.");
    	    		   return;
    	    	   }
    	           if(file.size>5*1024*1024){
    	    		   dialogService.alertInfo("error", "Please select a picture no more than 5M");
    	    		   return;
    	    	   }
    	         	$scope.pic_num = $("#screenshotArrays>li").length;
        	        var num= $scope.pic_num+1;
        			if (num > 4) {
        				dialogService.alertInfo("error","Upload up to 4 images");
        				return false;
        			}
	            file.upload = Upload.upload({
	            	headers: securityService.getHeader(),
	                url: baseUrl+'/infoannounce/uploadImg',
	                data: {file: file}
	            });

	            file.upload.then(function (resp) {
	             	if(resp.data.status!=3){
    	        		dialogService.alertInfo("error", resp.data.msg);
    	        		return;
    	        	}
    	            var obj = resp.data.data;
    	          	var imgUUID = obj.split(";")[0];
//    	           	if(index==0){
//    	           		$scope.infoannounce.picId = imgUUID
//    	           	}else{
//    	           		$scope.infoannounce.picId = $scope.infoannounce.picId +","+imgUUID
//    	           	}
    				var formId = imgUUID.substr(0, 5);
    				$scope.imgPath = obj.split(";")[1];
    				 var btn_change = '<form id="'+formId +'" action="" class="filename" method="post" enctype="multipart/form-data">'+
    	             '  <a href="javascript:void(0);" class="file">update'+
    	             ' <input type="file" name="' + imgUUID+'" id="'+imgUUID+'" />' +
    	             ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0);" class="file"'+'ng-click=deleteImg("'+ imgUUID +'") >删除</a>'+'</form>';
    				 //var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+$scope.imgPath+"'>";
    				 //var imgPath = $scope.imgPath;
    				 var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
    				 var caption = 'Information related picture';
    				 var img = "<a href='" + imgPath + "'" +
    	             "class='fresco'" +
    	             "data-fresco-group='" + 'Information related picture'+ "'" +
    	             "data-fresco-caption='" + caption + "'>" +
    	             "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
    	             "</a>";	
    				 var li = "<li id='"+imgUUID+"' style='float:left;width: 140px;height: 120px;text-align:center;margin-left:5px;'>" + img
    							+ "<span style='color:black;'>" + btn_change 
    							 + "</span></li>";
    				$('#screenshotArrays').append(li);
    				var el = angular.element(screenshotArrays);
    				$compile(el)($scope);
    				$scope.reset(imgUUID);
    	           	
	            }, function (resp) {
	            	dialogService.alertInfo("error",'Image upload failed');
	            }, function (evt) {
	                file.progress = Math.min(100, parseInt(100.0 * 
	                                         evt.loaded / evt.total));
	            });
	        });
	    };
        
        function deletePicIds(imgUUID){
        	var picIds = $scope.infoannounce.picId;
//        	console.log(picIds);
//        	console.log(imgUUID);
        
        	if(picIds.indexOf(imgUUID+',')>=0){
        		$scope.infoannounce.picId = picIds.replace(imgUUID+',',"");
        	}else if(picIds.indexOf(','+imgUUID)>=0){
        		$scope.infoannounce.picId = picIds.replace(','+imgUUID,"");
        	}else if(picIds.indexOf(imgUUID)>=0){
        		$scope.infoannounce.picId = picIds.replace(imgUUID,"");
        	}
        	
        }
        $scope.form = formService.form(function () {
        if($scope.infoannounce.type == '2'){
  	    	  if($scope.infoannounce.content.length>50){
  	    		  dialogService.alertInfo("error", "The message type is up to 50 words when the terminal informs the announcement.");
  	    		  return ;
  		      } 
  	      	}
            infoannounceService.updateinfoannounce($scope.infoannounce, function () {
                dialogService.alertInfo("success", "Successfully modified!");
                $state.go("home.infoannounce.list");
            });
        });
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.infoannounce) {
                    $scope.infoannounce = {};
                }
                $scope.infoannounce.insId = value.id;
                $scope.infoannounce.insName = value.name;
            });
        };
        
        $scope.setUniversal = function (value) {
        	if(value == 1){
        		$scope.update = 1;
        	}else{
        		$scope.update = 2;
        	}
        };
    };
});