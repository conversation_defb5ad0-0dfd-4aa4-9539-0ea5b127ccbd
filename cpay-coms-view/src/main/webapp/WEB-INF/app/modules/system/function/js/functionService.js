'use strict';
define(['js/app'], function (app) {
    app.factory("functionService", function (BaseApi) {
        return {
            /*getMenuList: function (data, success) {
                BaseApi.query("/menu/tree", {}, success);
            },*/
            getOrganizationName: function (roleCd, success) {
                BaseApi.get("/role/:roleCd", {
                    roleCd: roleCd
                }, success);
            },
            deleteFunction: function (functionCd, success) {
                BaseApi["delete"]("/function/" + functionCd, success);
            },
            getFunctionList: function (data, success) {
                BaseApi.get("/function", data, success);
            },
            addFunction: function (data, success) {
                BaseApi.post("/function", data, success);
            },
            updateFunction: function (data, succes) {
                BaseApi.patch("/function/", data, succes);
            },
            getFunction: function (data, success) {
                BaseApi.get("/function/functionId",data,success);
            },
            
            
            getMenuByMenuCd: function (MenuCd, success) {
                BaseApi.get("/menu/:MenuCd", {
                    MenuCd: MenuCd
                }, success);
            },
            geMenutree: function (pId,success) {
                BaseApi.query("/menu/tree", {pId:pId}, success);
            },
            getMenuList: function (data, success) {
                BaseApi.get("/menu", data, success);
            },
            addMenu: function (data, success) {
                BaseApi.post("/menu", data, success);
            },
            deleteMenu: function (MenuCd, success) {
                BaseApi["delete"]("/menu/del/" + MenuCd, success);
            },
            updateMenu: function (data, success) {
                BaseApi.patch("/menu/" + data.id, data, success);
            },
            getMenuParents: function (success) {
                BaseApi.query("/menu/parents", {}, success);
            },updateMenuSeq: function (data, success) {
                BaseApi.patch("/menu", data, success);
            },
        };
    });
});