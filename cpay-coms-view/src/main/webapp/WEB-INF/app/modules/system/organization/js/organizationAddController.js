'use strict';
define([""], function () {
    return function ($scope, $injector, organizationRest, $state, organizationService, dialogService, formService, $http) {
    	
    	$http.get("modules/system/organization/i18n/en/organizationList.json").success(function(dataLan) {
    	$scope.organizationInfo = dataLan.organizationInfo;
        
        $scope.$parent.$parent.thirdTitle = "机构新增";
        $scope.show1 = false;
        $scope.show2 = false;
        $scope.openInsDialog = function () {
        	 if (!$scope.organization) {
                 $scope.organization = {};
             }
            organizationService.openOrganization(function (value) {
                $scope.organization.parentId = value.id;
                $scope.organization.parentName = value.name;
            });
           
        };

        $scope.onlyCode = function () {
            if (!$scope.organization || !$scope.organization.code) {
                return;
            }
            organizationRest.validate({code:$scope.organization.code},"Organization Number has existed");
        };
        
        $scope.onlyName = function () {
            if (!$scope.organization || !$scope.organization.name) {
                return;
            }
            organizationRest.validate({name:$scope.organization.name},"Organization Name has existed");
        };
        
        $scope.form = formService.form(function () {
        	console.log("");
        	return null;
        	if ($(".ng-invalid").length>0) {
                dialogService.alertInfo("warning","还有未通过的验证项！");
                return;
            }
        	if (!$scope.organization.parentId) {
                $scope.organization.parentId = 0;
            }
        	//默认设置自动激活为否
        	
        	if($scope.organization.auto == true){
        		$scope.organization.auto = "1";
        	}else if($scope.organization.auto == false){
        		$scope.organization.auto = "0";
        	}
        	//默认设置是否限制IP为否
        	
        	if($scope.organization.startLimitIp == true){
        		$scope.organization.startLimitIp = "1";
        	}else if($scope.organization.startLimitIp == false){
        		$scope.organization.startLimitIp = "0";
        	}
        	if($scope.organization.isMonitor == true){
        		$scope.organization.isMonitor = "1";
        	}else if($scope.organization.isMonitor == false){
        		$scope.organization.isMonitor = "0";
        	}
        	if($scope.organization.isLock == true){
        		$scope.organization.isLock = "1";
        	}else if($scope.organization.isLock == false){
        		$scope.organization.isLock = "0";
        	}
            organizationRest.addOrganization($scope.organization, function () {
                dialogService.alertInfo("success", "Add organization successfully！");
                $scope.$parent.initTree();
                $state.go("home.organization");
            });
        })
    	});
    };
});