<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<div class="info-container">
				<table class="info-table info-table-normal">
					<tr class="info-table-row">
						<td class="info-table-name">Ttile</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.title"></span></td>
					</tr>
					<tr class="info-table-row big-row">
						<td class="info-table-name">Content</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.content"></span></td>
					</tr>
					<tr class="info-table-row big-row">
						<td class="info-table-name">Introduction</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.introduction"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Organization</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.insName"></span></td>
					</tr>

					<tr class="info-table-row">
						<td class="info-table-name">Type</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.type"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">General</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.universal"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Pin</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.stick"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Publisher</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.authorName"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">CreateTime</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.createTime"  format="yyyy-MM-dd"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">UpdateTime</td>
						<td class="info-table-value"><span
							ng-bind="infoannounce.modifyTime"  format="yyyy-MM-dd"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Information Image</td>
						<td class="info-table-value">
							<ul id="screenshotArrays" class="screenshot">
							</ul>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>