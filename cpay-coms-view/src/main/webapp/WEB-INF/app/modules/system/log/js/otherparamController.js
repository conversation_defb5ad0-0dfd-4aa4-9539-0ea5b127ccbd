'use strict';
define(function () {
    return function ($scope, logService,toolsService, $state,dialogService, gridService, $http) {   
    	$http.get("modules/system/log/i18n/en/logList.json").success(function(dataLan) {
        $scope.logInfo = dataLan.logInfo;
    	$scope.log4jClick = function(){
        	$state.go('home.log.list.log4j');
        };
        $scope.log4jSettingsClick = function(){
        	$state.go('home.log.list.log4jSettings');
        };
        $scope.sysClick = function(){
        	$state.go('home.log.list.sysLog');
        };
        $scope.tmsClick = function(){
        	$state.go('home.log.list.tmsLog');
        };
        $scope.amsClick = function(){
        	$state.go('home.log.list.amsLog');
        };
        $scope.casClick = function(){
        	$state.go('home.log.list.casLog');
        };
        $scope.heartClick = function(){
        	$state.go('home.log.list.heart');
        };
        $scope.paramClick = function(){
            $state.go('home.log.list.termParamlog');
        };
       
        /*********ams服务日志显示列表*******/
        gridService.initTable({
            url: "/log/termParamlog",
            scope: $scope,
            operator:false,
            id:"#table3",
            columns: [{
                field: 'termSeq',
                title: $scope.logInfo.serial_no,
            }, {
                field: 'timestamp',
                title: $scope.logInfo.msg_head_timestamp,
                formatter:function(value){
                	return toolsService.getFormatTime(value);
                }
            }, {
                field: 'random',
                title: $scope.logInfo.msg_head_random,
            }, {
                field: 'url',
                title: $scope.logInfo.request_url,
            },
            {
                field: 'requestLength',
                title: $scope.logInfo.request_lenth,
            },{
                field: 'reponseLength',
                title: $scope.logInfo.respone_lenth,
            },{
                field: 'result',
                title: $scope.logInfo.result,
            }
            ]
        });
        
        $scope.amsSearch = function () {
        	//$scope.condition.termSeq = $scope.ams.termSeq;
        	//$scope.condition.timestamp = $scope.ams.timestamp;
            gridService.search($scope.condition,"#table3");
        };
        $scope.amsReset = function () {
        	$scope.condition = {};
        	gridService.search($scope.condition,"#table3");
        };        
        
        $scope.amsClear = function(){
        	if(!$scope.condition.timestamp || $scope.condition.timestamp == ""){
    			dialogService.alertInfo("success", "请选择开始时间！");
    			return;
    		}
        	dialogService.openConfirm("确认要清除开始时间之前的日志？",function(){
        		logService.clearAmsLog($scope.condition,function(data){        		
	        		dialogService.alertInfo("success", data.msg);
	        		dialogService.closeConfirm();
	        		gridService.refresh("#table3");
	        	});
        	});
        };
        
        gridService.setPlaceholder();
    	});
    };
});