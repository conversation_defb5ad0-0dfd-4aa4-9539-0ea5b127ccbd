<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<!--<div class="control-group">-->
				<!--<label class="control-label">角色编码<span class="required">*</span></label>-->
				<!--<div class="controls">-->
				<!--<input type="text" maxlength=15 name="code" ng-model="role.code" class="span6 "-->
				<!--initial-validity="false" ng-disabled="disabled"-->
				<!--validator="required,maxlength=15" message-id="code"/>-->
				<!--<span id="code" class="help-inline"> </span>-->
				<!--</div>-->
				<!--</div>-->
				<div class="control-group">
					<label class="control-label">{{roleInfo.role_name}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" maxlength=15 name="name" ng-model="role.name"
							class="span6 " initial-validity="false" ng-disabled="disabled"
							valid-callback="onlyRoleName()" valid-method="blur"
							validator="required,maxlength=15" message-id="name" /> <span
							id="name" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{roleInfo.remark}}</label>
					<div class="controls">
						<textarea ng-model="role.remark" maxlength=200 rows="5"
							class="span6" message-id="remark" ng-disabled="disabled"></textarea>
						<span id="remark" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group" ng-show="add">
					<label class="control-label">{{roleInfo.mune_copy}}</label>
					<div class="controls">
						<select class="form-control"
							ng-options="m.id as m.name for m in roleList" name="menuRoleId"
							ng-model="role.menuRoleId" message-id="menuRoleId">
							<option value="">{{roleInfo.please_select}}</option>
						</select> <span id="menuRoleId" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group" ng-show="add">
					<label class="control-label">{{roleInfo.function_copy}}</label>
					<div class="controls">
						<select class="form-control"
							ng-options="m.id as m.name for m in roleList" name="functionRoleId"
							ng-model="role.functionRoleId" message-id="functionRoleId">
							<option value="">{{roleInfo.please_select}}</option>
						</select> <span id="functionRoleId" class="help-inline"></span>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<div ng-show="!disabled" form-foot goback="$state.go('home.role.list')"
	submit="form.submit(Form)" reset="form.reset(Form)"></div>
