'use strict';
define([], function () {
    return function ($scope, $state, dialogService, gridService, infoannounceService, toolsService,organizationService, $http) {    	
    	$http.get("modules/system/infoannounce/i18n/en/infoannounceList.json").success(function(dataLan) {
        $scope.infoAnnounceInfo = dataLan.infoAnnounceInfo;
                
        $scope.$parent.thirdTitle = "";
        gridService.initTable({
            url: "/infoannounce",
            scope: $scope,
            deletes:"infoannounce_delete",
            detail:"infoannounce_query",
            update:"infoannounce_update",
            columns: [{
                field: 'state',
                checkbox: 'true'
            }, {
                field: 'title',
                title: $scope.infoAnnounceInfo.title,
                cellStyle: function (value, row, index, field) {
                    return {
                        classes: 'big'
                    }
                }
            }, {
                field: 'insName',
                title: $scope.infoAnnounceInfo.insName,
                formatter: function (value) {
                	if(!value){
                		return "All";
                	}else{
                		return value;
                	}
                }
            }, {
                field: 'type',
                title: $scope.infoAnnounceInfo.type,
                formatter: function (value) {
                    if (value == 0) {
                        return "Platform information announcement";
                    } else if (value == 1) {
                        return "Help center";
                    } else if (value == 2) {
                        return "Terminal message notification";
                    } 
                }
            },{
                field: 'createTime',
                title: $scope.infoAnnounceInfo.create_time,
                formatter: function (value) {
                    return toolsService.getDayDate(value);
                }
            }],
            operate: function (value, row, index) {
            	var stickBtn = '';
            	if(row.stick == 1){
            		stickBtn = '<a  href="javascript:;" ng-click="cancelStick(' + index + ')"  has-permission="infoannounce_query2" title="unpin">unpin</a>';
            	}else{
            		stickBtn = '<a  href="javascript:;" ng-click="stick(' + index + ')" has-permission="infoannounce_query"  title="pin">pin</a>';
            	}
                return stickBtn;
            }
        });
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to delete the title?", function () {
                infoannounceService.deleteinfoannounce(row.id, function (data) {
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.infoannounce.update', {id: row.id});
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.infoannounce.view', {id: row.id});
        };
        $scope.stick = function (index) {
        	var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to top the title?", function () {
                row.stick = 1;
                infoannounceService.updateinfoannounce(row, function () {
                	dialogService.alertInfo("success", "Successful topping!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        
        $scope.cancelStick = function (index) {
        	var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to unpin the title?", function () {
                row.stick = 0;
                infoannounceService.updateinfoannounce(row, function () {
                	dialogService.alertInfo("success", "Successfully canceled the top!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        
        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
            });
        };
        
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        
        $scope.typeObjList = [{name:"Terminal message notification",value:2},{name:"Platform information announcement",value:0},{name:"帮助中心",value:1}];
        
        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.infoAnnounceInfo.please_select_a_record);
                return;
            }
            dialogService.openConfirm("Are you sure you want to delete the selected row?", function () {
                dialogService.closeConfirm();
                //dialogService.alertInfo("warning", "批量删除接口还没实现！");
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                infoannounceService.deleteinfoannounce(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    gridService.refresh();
                });
            });
        };
        $scope.getRoleTree = function () {
            var row = gridService.getSelectedRow();
            if (row.length == 0) {
                dialogService.alertInfo("info", "Please select the role to be authorized");
                return;
            }
            $state.go('home.role.tree', {roleId: row[0].id});
            //roleService.setRoleSelected(row[0]);
            //roleService.getRoletree(row[0].id, function (data) {
            //    roleService.setTreeData(data);
            //
            //});
        };
        
        gridService.setPlaceholder();
    	});
    };
});