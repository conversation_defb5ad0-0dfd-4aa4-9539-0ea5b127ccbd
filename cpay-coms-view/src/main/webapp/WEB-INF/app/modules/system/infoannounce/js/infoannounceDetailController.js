'use strict';
define([], function () {
    return function ($scope, infoannounceService,baseUrl, $state, $stateParams, dialogService, formService, organizationService,toolsService) {
        $scope.$parent.thirdTitle = "公告修改";
        if($state.is("home.infoannounce.view")){
              $scope.disabled =true;
              $scope.$parent.thirdTitle = "公告详情";
        }
        infoannounceService.getinfoannounce($stateParams.id, function (data) {
            $scope.infoannounce = data;
        	var picList = data.picList;  
            if($scope.infoannounce.universal == 0){
            	$scope.update = 2;
            }else{
            	$scope.update = 1;
            }
            if($state.is("home.infoannounce.view")){
                if ($scope.infoannounce.type == 0) {
                	$scope.infoannounce.type = "平台信息公告";
                } else  if ($scope.infoannounce.type == 1){
                	$scope.infoannounce.type = "帮助中心";
                }else if ($scope.infoannounce.type == 2) {
                    $scope.infoannounce.type = "终端消息通知";
                }
                if ($scope.infoannounce.universal == 0) {
                	$scope.infoannounce.universal = "否";
                } else {
                	$scope.infoannounce.universal = "是";
                }
                
                if ($scope.infoannounce.stick == 0) {
                	$scope.infoannounce.stick = "否";
                } else {
                	$scope.infoannounce.stick = "是";
                }
                
                if(!$scope.infoannounce.insId){
                	$scope.infoannounce.insName = "全部";
                }
                
                $scope.infoannounce.createTime = toolsService.getDayDate($scope.infoannounce.createTime);
                $scope.infoannounce.modifyTime = toolsService.getDayDate($scope.infoannounce.modifyTime);
            }
            if(picList!=null){
        		for(var i=0;i<picList.length;i++){
        			var imgPath = $scope.infoannounce.ngsPicPath + picList[i];
        			//var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+imgPath+"'>";
     				var caption = "公告信息图片";
     				var img = "<a href='" + imgPath + "'" +
                    "class='fresco'" +
                    "data-fresco-group='" + "公告信息图片" + "'" +
                    "data-fresco-caption='" + caption + "'>" +
                    "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
                    "</a>";
        			
        			var li = "<li  style='float:left;color:white;text-align:center;margin-left:15px;'>" + img
     						+ "<span style='color:black;'>"  + "</span></li>";
     			$('#screenshotArrays').append(li);
        		}
        	}
            
        });
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.infoannounce) {
                    $scope.infoannounce = {};
                }
                $scope.infoannounce.insId = value.id;
                $scope.infoannounce.insName = value.name;
            });
        };
        
        $scope.setUniversal = function (value) {
        	if(value == 1){
        		$scope.update = 1;
        	}else{
        		$scope.update = 2;
        	}
        };
    };
});