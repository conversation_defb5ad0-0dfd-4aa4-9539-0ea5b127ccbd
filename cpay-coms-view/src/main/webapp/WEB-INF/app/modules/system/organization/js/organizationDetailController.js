'use strict';
define([], function () {
    return function ($scope, organizationRest,organizationService, $state, dialogService, gridService, $stateParams) {
        $scope.$parent.thirdTitle = "";
        var url = "/institution";
		if ($stateParams.id) {
			url = "/institution?id=" + $stateParams.id;
		}
        gridService.initTable({
            url: url,
            scope: $scope,
            update: "institution_update",
            columns: [
                {field: "code", title: "The code of organization"},
                {field: "name", title: "The name of organization"},
                {
                    field: "auto", 
                    title: "Whether to activate automatically?",
                    width:100,
                    formatter: function (value) {
                        if (value == 1) {
                            return "Yes";
                        } else {
                            return "No"
                        }
                    }
                },
                {
                    field: "isMonitor", 
                    title: "Whether to monitor?",
                    width:100,
                    formatter: function (value) {
                        if (value == 1) {
                            return "Yes";
                        } else {
                            return "No"
                        }
                    }
                },
                {
                    field: "isLock", 
                    title: "Whether the transfer machine unlocks the machine?",
                    width:100,
                    formatter: function (value) {
                        if (value == 1) {
                            return "Yes";
                        } else {
                            return "No"
                        }
                    }
                },
                {field: "parentName", title: "Parent Organization"}
            ]
        });
               
        
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Confirm that you want to delete the organization?", function (confirm) {
                organizationRest.deleteOrganization(row.id, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "successfully deleted!");
                    gridService.refresh();
                });
            });
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.addRow = function () {            
            dialogService.openDialog({
                template: "modules/system/organization/organizationEdit.html",
                controller: function ($scope, organizationRest, $state, organizationService,formService, dialogService) {
                	$scope.dialogTitle = "Add Organization";
                	$scope.organization = {};
                	$scope.openInsDialog = function () {
                        organizationService.openOrganization(function (value) {
                            if (!$scope.organization) {
                                $scope.organization = {};
                            }
                            $scope.organization.parentId = value.id;
                            $scope.organization.parentName = value.name;
                        });
                    };
                    $scope.organization.auto = 0;
                    $scope.onlyCode = function () {
                        if (!$scope.organization || !$scope.organization.code) {
                            return;
                        }
                        organizationRest.validate({code:$scope.organization.code},"The code of organization already exists!");
                    };
                    
                    $scope.onlyName = function () {
                        if (!$scope.organization || !$scope.organization.name) {
                            return;
                        }
                        organizationRest.validate({name:$scope.organization.name},"The name of organization already exists!");
                    };
                    $scope.form = formService.form(function () {
                    	if ($(".ng-invalid").length>0) {
                            dialogService.alertInfo("warning","There are also unverified verification items!");
                            return;
                        }
                    	if (!$scope.organization.parentId) {
                            $scope.organization.parentId = 0;
                        }
                        organizationRest.addOrganization($scope.organization, function () {
                            dialogService.alertInfo("success", "Successful organization modification!");
                            $scope.closeThisDialog(0);
                            getPage();
                        });
                    });
                }
            });
        }
        function getPage() {
            gridService.refresh();
            $scope.$parent.initTree();
        }
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openDialog({
                template: "modules/system/organization/organizationEdit.html",
            //    width:'40%',
                controller: function ($scope, organizationRest, $state, organizationService,formService, dialogService) {
                	$scope.dialogTitle = "Organization Update";
                	var oName="",oCode=""; 
                	organizationRest.getOrganization(row.id,function(data){
                        $scope.organization= data;
	                    oName = data.name;
	                    oCode = data.code;
	                    organizationRest.getOrganization(row.parentId,function(data){
	                        $scope.organization.parentName = data.name;
	                    })
	                    $scope.openInsDialog = function () {
	                        organizationService.openOrganization(function (value) {
	                            if (!$scope.organization) {
	                                $scope.organization = {};
	                            }
	                            $scope.organization.parentId = value.id;
	                            $scope.organization.parentName = value.name;
	                        });
	                    };
	                    $scope.onlyCode = function () {
	                        if (!$scope.organization || !$scope.organization.code || oCode ==$scope.organization.code) {
	                            return;
	                        }
	                        organizationRest.validate({id:$scope.organization.id,code:$scope.organization.code},"The code of organization already exists!");
	                    };
	                    
	                    $scope.onlyName = function () {
	                        if (!$scope.organization || !$scope.organization.name || oName ==$scope.organization.name) {
	                            return;
	                        }
	                        organizationRest.validate({id:$scope.organization.id,name:$scope.organization.name},"The name of organization already exists!");
	                    };
	                    $scope.clearDialog = function () {
	                        $scope.organization.parent_ins_cd = "";
	                        $scope.organization.parent_ins_nm = "";
	                    };
	                    $scope.form = formService.form(function(){
	                    	if ($(".ng-invalid").length>0) {
	                            dialogService.alertInfo("warning","There are also unverified verification items!");
	                            return;
	                        }
	                    	organizationRest.updateOrganization($scope.organization, function () {
	                            dialogService.alertInfo("success", "Successful organization modification!");
	                            $scope.closeThisDialog(0);
	                            getPage();
	                        });
	                    });
                	});
                }
        });
        };
        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select an organization!");
                return;
            }
            $state.go('home.organization.list');
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        }
        
        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.parentId = value.id;
                $scope.condition.parentName = value.name;
            });
        };
        $scope.addDefaultMerchant = function(){
        	var rows = gridService.getRow(0);
        	if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select an organization!");
                return;
            }
        	if (rows.length >1) {
                dialogService.alertInfo("info", "You can only choose one record!");
                return;
            }
        	if(rows.auto == 0){
        		dialogService.alertInfo("info", "Please select a record that is automatically activated!");
                return;	
        	}
            $state.go('home.organization.addDefaultMerchant', {id: rows.id});
        };
        $scope.configureElements = function(){
        	var rows = gridService.getRow(0);
        	if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select an organization!");
                return;
            }
        	if (rows.length >1) {
                dialogService.alertInfo("info", "You can only choose one record!");
                return;
            }
            $state.go('home.organization.organizationElements', {id: rows.id});
        }
        gridService.setPlaceholder();
    };
});