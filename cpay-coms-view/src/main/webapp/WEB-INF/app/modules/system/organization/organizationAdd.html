<div class="modal-header">
	<!--<button type="button" ng-click="cancel()" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>-->
	<h3 class="modal-title">{{organizationInfo.add_organization}}</h3>
</div>
<div class="container-fluid" style="margin-top: 20px;">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal">
				<div class="control-group">
					<label class="control-label">{{organizationInfo.organization_number}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" maxlength="30" name="code"
							ng-model="organization.code" class="dropdown"
							valid-callback="onlyCode()" valid-method="blur"
							validator="required" message-id="code1"/> <span id="code1"
							class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{organizationInfo.organization_name}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" maxlength="15" name="name"
							ng-model="organization.name" class="dropdown"
							valid-callback="onlyName()" valid-method="blur"
							validator="required,maxlength=15" message-id="name1" /> <span
							id="name1" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{organizationInfo.superior_organization}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="insId" class="dropdown"
							ng-model="organization.parentId" ng-show="false"
							ng-disabled="disabled" validator="required" message-id="insId" />

						<div class="input-append input-group" style="margin-left: 0px"
							ng-click="openInsDialog()">
							<input type="text" name="insName"
								ng-model="organization.parentName" readonly
								ng-disabled="disabled" class="dropdown input-float"
								validator="required" message-id="insName" />
							<button class="choice btn" type="button" ng-disabled="disabled"></button>
							<!--<button class="btn" ng-click="clearDialog()" ng-disabled="disabled" >清空</button>-->
						</div>
						<span id="insName1" class="help-inline"></span>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<div class="ng-scope modal-footer">
	<div style="padding: 20px 0 0 105px;">
		<div class="edit-button">
			<button class="btn blue" type="submit" has-permission="institution_add" ng-click="form.submit(Form)">
				<i class="icon-ok"></i>{{organizationInfo.save}}
			</button>
		</div>
	</div>
</div>
