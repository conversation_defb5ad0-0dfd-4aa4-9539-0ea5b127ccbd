<div class="container-fluid">
      <div class="row-fluid">
        <div class="span12">
            <ul class="nav nav-tab-head">
                <!-- <li  ng-class="{active:$state.is('home.log.list.log4j')}">
                     <a  ng-click="log4jClick();" style="cursor: pointer;">Log4j日志</a>                    
                </li> -->
               <!--  <li ng-class="{active:$state.is('home.log.list.log4jSettings')}">
                	<a  ng-click="log4jSettingsClick();" style="cursor: pointer;">日志配置</a>
                </li> -->
                <li ng-class="{active:$state.is('home.log.list.sysLog')}">
                	<a  ng-click="sysClick();" style="cursor: pointer;">{{logInfo.sys_log}}</a>
                </li>
                <li ng-class="{active:$state.is('home.log.list.tmsLog')}">
                	<a  ng-click="tmsClick();" style="cursor: pointer;">{{logInfo.tms_log}}</a>
                </li>
                <li ng-class="{active:$state.is('home.log.list.amsLog')}">
                	<a  ng-click="amsClick();" style="cursor: pointer;">{{logInfo.appstore_log}}</a>
                </li>
                <li ng-class="{active:$state.is('home.log.list.termParamlog')}">
                    <a  ng-click="paramClick();" style="cursor: pointer;">CardIssue Param Log</a>
                </li>
            </ul>
        </div>
    </div>
 <div ui-view></div>
</div>



