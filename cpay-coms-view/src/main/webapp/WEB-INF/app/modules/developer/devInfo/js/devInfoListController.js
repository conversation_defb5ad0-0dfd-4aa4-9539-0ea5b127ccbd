'use strict';
define([], function () {
    return function ($scope, $state, dialogService, gridService, devInfoService, toolsService,organizationService) {
        $scope.$parent.thirdTitle = "";
        gridService.initTable({
            url: "/devInfo",
            scope: $scope,
            deletes:"devInfo_delete",
            detail:false,
            update:"devInfo_update",
            columns: [{
                field: 'state',
                checkbox: 'true'
            }, {
                field: 'title',
                title: 'Title',
                cellStyle: function (value, row, index, field) {
                    return {
                        classes: 'big'
                    }
                },
            },
            {
                field: 'stick',
                title: 'Whether To Top',
                formatter:function(value){
                	if(value == '0'){
                		return "No"
                	}else{
                		return "Yes"
                	}
                },
                width:"100px"
            	
            },
            {
                field: 'isDisplay',
                title: 'Whether To Display',
                formatter:function(value){
                    	if(value == '0'){
                    		return "No"
                    	}else{
                    		return "Yes"
                    	}
                },
                width:"100px"
            },
            {
                field: 'authorName',
                title: 'Author',
                width:"100px"
            },{
                field: 'createTime',
                title: 'Create Time',
                formatter: function (value) {
                    return toolsService.getFullDate(value);
                },
                width:"100px"
            },
            {
                field: 'modifyTime',
                title: 'Modify Time',
                formatter: function (value) {
                    return toolsService.getFullDate(value);
                },
                width:"100px"
            }
            ],
            operate: function (value, row, index) {
            	var stickBtn = '';
            	var isDisplayBtn = '';
            	if(row.stick == 1){
            		stickBtn = '<a  href="javascript:;" ng-click="cancelStick(' + index + ')"  has-permission="devInfo_query" title="Cancer Topping"> Cancel Topping</a>';
            	}else{
            		stickBtn = '<a  href="javascript:;" ng-click="stick(' + index + ')" has-permission="devInfo_query"  title="Topping"> Topping</a>';
            	}
            	if(row.isDisplay == 1){
            		isDisplayBtn = '<a  href="javascript:;" ng-click="cancelDisplay(' + index + ')"  has-permission="devInfo_query" title="Cancel Display"> Cancel Display</a>';
            	}else{
            		isDisplayBtn = '<a  href="javascript:;" ng-click="display(' + index + ')" has-permission="devInfo_query"  title="Display"> Display</a>';
            	}
                return stickBtn+isDisplayBtn;   
            }
        });
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to delete the title?", function () {
                devInfoService.deletedevInfo(row.id, function (data) {
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.stick = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to cap the title?", function () {
                devInfoService.stick(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful operation!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.cancelStick = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to unpin the title?", function () {
                devInfoService.cancelStick(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful Operation!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.cancelDisplay = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to turn off the display of the title?", function () {
                devInfoService.cancelDisplay(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful Operation");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.display = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure to open the title?", function () {
                devInfoService.display(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful Operation!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.devInfo.update', {id: row.id});
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.devInfo.view', {id: row.id});
        };
        $scope.stick = function (index) {
        	var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to top the title?", function () {
                devInfoService.stick(row.id, function () {
                	dialogService.alertInfo("success", "Topping success!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        
        $scope.cancelStick = function (index) {
        	var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to unpin the title?", function () {
                row.stick = 0;
                devInfoService.cancelStick(row.id, function () {
                	dialogService.alertInfo("success", "Cancel the top!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        
        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
            });
        };
        
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        
        $scope.typeObjList = [{name:"Announcement",value:0},{name:"Help center",value:1}];
        
        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select the line!");
                return;
            }
            dialogService.openConfirm("Are you sure you want to delete the selected row?", function () {
                dialogService.closeConfirm();
                //dialogService.alertInfo("warning", "批量删除接口还没实现！");
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                devInfoService.deleteDevInfo(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Successfully Deleted!");
                    gridService.refresh();
                });
            });
        };
        $scope.getRoleTree = function () {
            var row = gridService.getSelectedRow();
            if (row.length == 0) {
                dialogService.alertInfo("info", "Please select the role to be authorized!");
                return;
            }
            $state.go('home.role.tree', {roleId: row[0].id});
            //roleService.setRoleSelected(row[0]);
            //roleService.getRoletree(row[0].id, function (data) {
            //    roleService.setTreeData(data);
            //
            //});
        };
        
        gridService.setPlaceholder();
    };
});
