'use strict';
define(["treeService"], function () {
    return  function ($scope, $timeout, roleService, $state, dialogService, $stateParams,treeService) {
        $scope.$parent.thirdTitle = "资源授权";
        var zTree=null;
        roleService.getRoleResource($stateParams.roleId, function (data) {
            data.splice(0, 0, {
                id: 0,
                name: 'All/Reverse',
                seq: 0,
                checkState: 'indeterminate'
            });
            treeService.initTree({
                treeId: "insTree",
                nodes: data,
                setting: {
                    data: {
                        simpleData: {
                            enable: true,
                            idKey: "id",
                            pIdKey: "pid",
                            rootPId: 0
                        }
                    }, check:{
                        enable: true
                    },callback:{
                    	onClick: onClick
                    }
                }
            });
        });
        function onClick(e, treeId, treeNode, clickFlag) {            		
       		if(zTree==null){
       			zTree = treeService.getZtree();
       		}
       		zTree.checkNode(treeNode, !treeNode.checked, true); 
   		}
        roleService.getRoleMenu($stateParams.roleId,function(data){
            for(var index in data){
                var node = treeService.getZtree().getNodeByParam("id", data[index], null);
                if(node && node.parentId!=0){
                    treeService.getZtree().checkNode(node, true, true);
                }
            }
        });
        $scope.saveSelected = function () {
            var checkList = treeService.getCheckNode();
            var childrenNode = [];
            for (var index in checkList) {
                if (checkList[index].level==2) {

                    childrenNode.push(checkList[index]['id']);
                }
            }
            var param = {
                functions: childrenNode.join(','),
                roleId: $stateParams.roleId
            };
            roleService.postRoleResource(param, function (data) {
                dialogService.alertInfo('success', '资源授权成功！');
                $state.go("home.devrole.list");
            });

        };
    };
});