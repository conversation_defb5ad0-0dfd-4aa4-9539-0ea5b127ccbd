'use strict';
define(["../js/devUserNoticeService"], function () {
    return function ($scope,$state,devUserNoticeService,gridService,toolsService,BaseApi,dialogService) {
    	$scope.$parent.thirdTitle = "";
    	gridService.initTable({
			url : "/userNotice",
			scope : $scope,
			detail:false,
			deletes:true,
			update:true,
			columns : [{
				field : "title",
				title : "Title",
				width:"400px"
			},
			{
				field : "content",
				title : "Content",
				formatter:function(value,row,index){
                	if(!value){
                		return "No";
                	}
                	var str = '<label style="width:400px;overflow:hidden; white-space:nowrap; text-overflow:ellipsis;" title="' + value + '">' + 
                				value + '</label>';
                	return str;
                }
			},
			{
				field : "stick",
				title : "Whether to Topping",
				width:"120px",
				formatter:function(value){
					if(value == '1'){
						return "Yes";
					}else if(value == '0'){
						return "No";
					}
				}
			},
			{
				field : "createTime",
				title : "Create Time",
				width:"120px",
				formatter:function(value){
					return toolsService.getFullDate(value);
				}
			}
			],
			operate: function (value, row, index) {
            	var stickBtn = '';
                if(row.stick == 1){
            		stickBtn = '<a  href="javascript:;" ng-click="cancelStick(' + index + ')"  has-permission="devInfo_query" title="Cancel Topping"> Cancel Topping</a>';
            	}else{
            		stickBtn = '<a  href="javascript:;" ng-click="stick(' + index + ')" has-permission="devInfo_query"  title="Topping"> Topping</a>';
            	}
                return stickBtn;
            }
		});
        BaseApi.query("/dict/type/devrole",{}, function (data) {
   		 	$scope.devroleList = data;
   	 	});
    	$scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search($scope.condition);
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.devUserNotice.update', {id: row.id});
        };
        $scope.stick = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to cap the title?", function () {
            	devUserNoticeService.stick(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful Operation!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
        $scope.cancelStick = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm("Are you sure you want to unpin the title?", function () {
            	devUserNoticeService.cancelStick(row.id, function (data) {
                    dialogService.alertInfo("success", "Successful Operation!");
                    dialogService.closeConfirm();
                    gridService.refresh();
                });
            });
        };
		$scope.deleteRow = function(index) {
			var row = gridService.getRow(index);
			if (row.length == 0) {
				dialogService.alertInfo("info", "record");
				return;
			}
			dialogService.openConfirm("Are you sure you want to delete the current record", function(confirm) {
				devUserNoticeService.deleteUserNotice(row.id, function(data) {
					dialogService.closeConfirm();
					dialogService.alertInfo("success", "Successfully Deleted!");
					$scope.condition = {};
		            gridService.search($scope.condition);
				});
			});
		};
    };    
});