'use strict';
define(['lib/raty/jquery.raty.js'], function () {
    return function ($scope, $state, $stateParams,dialogService, formService,BaseApi,organizationService,$http) {
    	$http.get("modules/storeApk/apkPub/i18n/en/apkpubList.json").success(function(dataLan) {
        $scope.apkpubInfo = dataLan.apkpubInfo;
    	$scope.$parent.thirdTitle = "app";
    	        $scope.readOnly = false;
    	        $scope.disabled = true;
    	        $scope.domain = window.location.href.split("#")[0];
    	        BaseApi.get("/apk/getstoreApkById?id="+$stateParams.id,{},function(data){
    	        	$scope.storeApkInfo = data;
    	        	console.log($scope.storeApkInfo);
                    var param= {appVersion:data.appVersion,
                        appCode:data.appCode,
                        userId:data.userId,
                        apkId:data.apkId};
                    BaseApi.query("/apk/getApkAuditOpinion",param, function (data) {
    	        		$scope.apkAuditInfo = data;
    	        		$scope.count = data.length;
    	       	 	});
                    if($scope.storeApkInfo.upFlag==0){
                        $scope.storeApkInfo.upFlag="Cannot Force Update";
                    }else{
                        $scope.storeApkInfo.upFlag="Forced Update"
                    }
                    if($scope.storeApkInfo.uninstallFlag==1){
                        $scope.storeApkInfo.uninstallFlag="Can Be Uninstalled"
                    }else
                    {
                        $scope.storeApkInfo.uninstallFlag="Cannot Be Uninstalled"
                    }
                    if($scope.storeApkInfo.recSt==0){
                        $scope.storeApkInfo.recSt = $scope.apkpubInfo.unSubmitted;
                    }
                    if($scope.storeApkInfo.recSt==1){
                        $scope.storeApkInfo.recSt = $scope.apkpubInfo.pending;
                    }
                    if($scope.storeApkInfo.recSt==2){
                        $scope.storeApkInfo.recSt = $scope.apkpubInfo.unpublished;
                    }
                    if($scope.storeApkInfo.recSt==3){
                        $scope.storeApkInfo.recSt = $scope.apkpubInfo.published;
                    }
                    if($scope.storeApkInfo.recSt==4){
                        $scope.storeApkInfo.recSt = $scope.apkpubInfo.offShelf;
                    }

    	        	$scope.iconPath = $scope.storeApkInfo.ngsPicPath+$scope.storeApkInfo.iconUrl;
    	        	BaseApi.get("/institution/"+$scope.storeApkInfo.insId,"",function(data){
    	        		$scope.storeApkInfo.insName = data.name;
    	        	});
    	        	BaseApi.query("/apk/getAppType?insId="+ $scope.storeApkInfo.insId,"",function(data){
    	        		 for(var i=0;i<data.length;i++){
    	 	   		 		if(data[i].id==$scope.storeApkInfo.appTypeId){
    	 	   		 		 $scope.storeApkInfo.appTypeId = data[i].tpNm;
    	 	   		 		}
    	 	   		 	}
    	        		if($scope.storeApkInfo.appTypeId==""||$scope.storeApkInfo.appTypeId==null||!isNaN($scope.storeApkInfo.appTypeId)){
                            $scope.storeApkInfo.appTypeId = "No";
    	        		}
    	               });
    	        	BaseApi.query("/terminalGroup/selectGroupByInsId/"+$scope.storeApkInfo.insId,{}, function (data) {
    	        		if($scope.storeApkInfo.groupId==""||$scope.storeApkInfo.groupId==null||isNaN($scope.storeApkInfo.groupId) || $scope.storeApkInfo.groupId==0){
    		   			 	$scope.storeApkInfo.groupName="No";
     	   		 		}else{
        		   		 	for(var i=0;i<data.length;i++){
        		   		 		if(data[i].id==$scope.storeApkInfo.groupId){
        		   		 		 $scope.storeApkInfo.groupName = data[i].name;
        		   		 		}
        		   		 	}
     	   		 		}
    		   	 	});
                    if($scope.storeApkInfo.factoryId == 0){
                        	$scope.storeApkInfo.factoryName = "No";
                        	$scope.storeApkInfo.termTypeNameStr = "No";
                    }
    	        	$scope.storeApkInfo.id = $stateParams.id;
    	        	 $('#degree').raty({
    	        		 	hints: ['1', '2', '3', '4', '5'],
    	     			path:"lib/raty/css",
    	     			starOff: 'star-off-big.png',
    	                 starOn: 'star-on-big.png',
    	                 size: 24,
    	                 readOnly: true, 
    	                 score: $scope.storeApkInfo.reDegree
    	         });
    	        var picList = $scope.storeApkInfo.picList  
    	      	var permisList = $scope.storeApkInfo.app_pms
    	     	if(picList!=null){
    	     		for(var i=0;i<picList.length;i++){
    	     			var imgPath = $scope.storeApkInfo.ngsPicPath + picList[i];
    	     			//var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+imgPath+"'>";
    	     			var caption = $scope.storeApkInfo.appName + "app pic";
    	 				var img = "<a href='" + imgPath + "'" +
    	                "class='fresco'" +
    	                "data-fresco-group='" + $scope.storeApkInfo.appName + "'" +
    	                "data-fresco-caption='" + caption + "'>" +
    	                "<img style='margin-bottom:5px;width:80px;height:60px;' src='" + imgPath + "'>" +
    	                "</a>";
    	     			var li = "<li  style='float:left;color:white;text-align:center;margin-left:15px;'>" + img
    	  						+ "<span style='color:black;'>"  + "</span></li>";
    	  			$('#screenshotArrays').append(li);
    	     		}
    	     	}
    	     	
    	     	if(permisList!=null){
    	     		for(var i=0;i<permisList.length;i++){
    	     			var permission = permisList[i];
    	     			var li = "<li style='list-style-type:none;'>"+permission+"</li>";
    	     			$('#permission').append(li);
    	     		}
    	     	}
    	    	});
    	        var parentScope = $scope
    	        $scope.showHistory = function(){
					dialogService.openDialog({
		                template: "modules/storeApk/apkAudit/auditOpinionView.html",
		                width: '60%',
		                controller: function ($scope) {
		                	var param= {appVersion:parentScope.storeApkInfo.appVersion,appCode:parentScope.storeApkInfo.appCode,userId:parentScope.storeApkInfo.userId};
		                	BaseApi.query("/apk/getApkAuditOpinion",param, function (data) {
		                		$scope.apkAuditInfo = data;
		                		$scope.appName=parentScope.storeApkInfo.appName;
		                		$scope.count = data.length;
		               	 		});
		                	}
						});
				}
    	        
    	        $scope.goback = function(){
    	        	$state.go("home.storeApk.manage");
    	        }
    	});
    };
});
