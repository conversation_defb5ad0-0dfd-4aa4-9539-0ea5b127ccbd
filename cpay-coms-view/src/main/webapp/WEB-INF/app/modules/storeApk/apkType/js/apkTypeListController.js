'use strict';
define(["organizationService"], function () {
    return function ($scope, $state, dialogService, gridService,toolsService,BaseApi,organizationService,$http) {
          $scope.$parent.thirdTitle = "";
          $http.get("modules/storeApk/apkType/i18n/en/apkTypeList.json").success(function(data) {
		  $scope.apkTypeListHtml = data.apkTypeListHtml;
		  $scope.apkTypeListJs = data.apkTypeListJs;
		  var apkTypeAddHtml = data.apkTypeAddHtml;
		  var addJs = data.apkTypeListJs;
          gridService.initTable({
                url:"/apkInfo/getApkTypePage",
                scope: $scope,
                operate: function (value, row, index) {
                    var el = '';
                    el = el + '<a  href="javascript:;" has-permission="apk_type_update" ng-click="updateType(' + index + ')"  title="Modify"> '+$scope.apkTypeListJs.mes+'</a>';
                    el = el + '<a  href="javascript:;" has-permission="apk_type_delete" ng-click="deleteRow(' + index + ')"  title="Delete">'+$scope.apkTypeListJs.mes1+'</a>';
                    return el;
                },
                operator:false,
                columns: [{
                    field: 'tpNm',
                    title: $scope.apkTypeListJs.mes2
                },{
                    field: 'tpCode',
                    title: $scope.apkTypeListJs.mes3
                }, {
                    field: 'iconPath',
                    title: $scope.apkTypeListJs.mes5,
                    formatter:function(value,row,index){
                        var url = row.ngsPicPath + row.iconPath;
                        var caption = row.tpNm;
                        return "<a href='" + url + "'" +
                            "class='fresco'" +
                            //"data-fresco-group='example'" +
                            "data-fresco-caption='" + caption + "'>" +
                            "<img style='max-width:20px;max-height:20px;' src='" + url + "'>" +
                            "</a>";
                    }
                    }]
            });

            $scope.search = function () {
                gridService.search($scope.condition);
            };
            $scope.detail = function (index) {
                var row = gridService.getRow(index);
                $state.go('home.apkType.view', {id: row.id});
            };
            $scope.reset = function () {
                $scope.condition = {};
                gridService.search($scope.condition);
            };
            $scope.deleteRow = function (index) {
                var row = gridService.getRow(index);
                dialogService.openConfirm($scope.apkTypeListJs.mes6+ row.tpNm + $scope.apkTypeListJs.mes7, function (confirm) {
                    BaseApi['delete']("/apkInfo/deleteApkTypeById?id="+row.id,function (data) {
                        dialogService.closeConfirm();
                        dialogService.alertInfo("info","delete Success");
                        gridService.refresh();
                    });
                });
            };
            $scope.deleteRows = function () {
                var rows = gridService.getSelectedRow();
                if (rows.length == 0) {
                    dialogService.alertInfo("info", $scope.apkTypeListJs.mes8);
                    return;
                }
                dialogService.openConfirm($scope.apkTypeListJs.mes9, function () {
                    var ids = "";
                    for (var index in rows) {
                        ids = ids + rows[index].id + ",";
                    }
                    var param = ids.substr(0, ids.length - 1);
                    BaseApi['delete']("/apkInfo/deleteApkTypeByIds?ids="+param,function (data) {
                        dialogService.closeConfirm();
                        dialogService.alertInfo("info", "delete Success");
                        gridService.refresh();
                    });
                });
            };

            $scope.addType = function() {
                dialogService.openDialog({
                    template : "modules/storeApk/apkType/apkTypeAdd.html",
                    controller : function($scope, formService, Upload,BaseApi,  baseUrl, securityService) {
                    	$scope.apkTypeAddHtml = data.apkTypeAddHtml;
                        $scope.dialogTitle = addJs.mes10;
                        $scope.readOnly = false;
                        $scope.foot = true;
                        $scope.apkTypeInfo={};
                        $scope.$watch('fileIcon',function(newValue,oldValue, scope){
                            if(newValue==null||newValue == ""||newValue == undefined){
                                return;
                            }

                            if($scope.fileIcon.type.indexOf('image')<0){
                                dialogService.alertInfo("error", addJs.mes11);
                                return;
                            }
                            if($scope.fileIcon.size>5*1024*1024){
                                dialogService.alertInfo("error", addJs.mes12);
                                return;
                            }
                            var lilength = $("#screenshotArrays li").length;
                            if(lilength >0){
                                dialogService.alertInfo("error", addJs.mes20);
                                return "";
                            }
                            Upload.upload({
                                url:  baseUrl+'/apk/uploadIcon',
                                data: {file: newValue},
                                headers: securityService.getHeader(),
                                method: 'POST'
                            }).then(function (resp) {
                                if(resp.data.status != 2){
                                    dialogService.alertInfo("error", resp.data.msg);
                                    return;
                                }
                                $scope.iconPath = baseUrl + '/ad/fileView?imagePath='+resp.data.data.filePath;
                                $scope.apkTypeInfo.iconId = resp.data.data.iconId;
                                $scope.apkTypeInfo.iconPath = resp.data.data.filePath;
                                dialogService.alertInfo("success",addJs.mes13);

                            }, function (resp) {
                                dialogService.alertInfo("error", addJs.mes14);
                            })
                        });

                        $scope.save = function (){
                            if($scope.apkTypeInfo.iconPath == "" || $scope.apkTypeInfo.iconPath == null){
                                dialogService.alertInfo("error",addJs.mes15);
                                return;
                            }
                            BaseApi.post("/apkInfo/addApkType",$scope.apkTypeInfo,function(data){
                                if(data.status==1){
                                    dialogService.alertInfo("error",addJs.mes16);
                                    return;
                                }
                                if(data.status==2){
                                    dialogService.alertInfo("error", "各个机构新增分类不能超过10个。");
                                    return;
                                }
                                dialogService.alertInfo("success", addJs.mes18);
                                $scope.closeThisDialog(0);
                                getPage();
                            });
                        };
                    }
                });
            };

            $scope.updateType = function(index) {
                dialogService.openDialog({
                    template : "modules/storeApk/apkType/apkTypeUpdate.html",
                    controller : function($scope, formService, Upload,baseUrl, securityService) {
                        $scope.dialogTitle = addJs.mes;
                        $scope.apkTypeAddHtml = data.apkTypeAddHtml;
                        $scope.readOnly = false;
                        $scope.disabled = true;
                        $scope.apkTypeInfo={};
                         var row = gridService.getRow(index);
                        BaseApi.get("/apkInfo/getApkTypeById?id="+row.id,"",function(data){
                            $scope.apkTypeInfo = data.data;
                            $scope.iconPath = $scope.apkTypeInfo.ngsPicPath+$scope.apkTypeInfo.iconPath;
                            $scope.firsticonPath = $scope.apkTypeInfo.ngsPicPath+$scope.apkTypeInfo.iconPath;
                        });
                        $scope.$watch('fileIcon',function(newValue,oldValue, scope){

                            if(newValue==null||newValue==""||!newValue){
                                return;
                            }
                            if($scope.fileIcon.type.indexOf('image')<0){
                                dialogService.alertInfo("error", addJs.mes11);
                                return;
                            }
                            if($scope.fileIcon.size>5*1024*1024){
                                dialogService.alertInfo("error", addJs.mes12);
                                return;
                            }
                            Upload.upload({
                                url:  baseUrl+'/apk/uploadIcon',
                                data: {file: newValue},
                                headers: securityService.getHeader(),
                                method: 'POST'
                            }).then(function (resp) {
                                if(resp.data.status != 2){
                                    dialogService.alertInfo("error", resp.data.msg);
                                    return;
                                }
                                $scope.iconPath = baseUrl + '/ad/fileView?imagePath=' +resp.data.data.filePath;
                                $scope.apkTypeInfo.iconId = resp.data.data.iconId;
                                $scope.apkTypeInfo.iconPath = resp.data.data.filePath;
                                dialogService.alertInfo("success",addJs.mes13);

                            }, function (resp) {
                                dialogService.alertInfo("error", addJs.mes14);
                            })
                        });

                        $scope.save = function(){
                            if($scope.apkTypeInfo.iconPath == "" || $scope.apkTypeInfo.iconPath == null){
                                dialogService.alertInfo("error",addJs.mes15);
                                return;
                            }
                            BaseApi.patch("/apkInfo/updateApkType",$scope.apkTypeInfo,function(data){
                                $scope.closeThisDialog(0);
                                getPage();
                            });
                        }


                    }
                });
            };

            function getPage() {
                gridService.refresh();
            }
            gridService.setPlaceholder();
          });
    };

});