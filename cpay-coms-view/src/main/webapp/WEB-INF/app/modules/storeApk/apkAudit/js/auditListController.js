'use strict';
define(["organizationService"], function () {
    return function ($scope, BaseApi,$state, dialogService, gridService,toolsService,organizationService,$http) {
    	   $http.get("modules/storeApk/apkAudit/i18n/en/auditList.json").success(function(data) {
    	   $scope.auditListJs = data.auditList;
    	   $scope.auditListHtml = data.auditListHtml;
    	   $scope.$parent.thirdTitle = "";
           gridService.initTable({
               url:"/apk/getAuditList",
               scope: $scope,
               operator:false,
               operate: function (value, row, index){
                   return '<a  href="javascript:;" has-permission="apk_audit_doAudit" ng-click="audit(' + row.id + ')"  title="Audit">'+ $scope.auditListJs.mes3+'</a>';
               },
               columns: [
            	   {
		                field: 'appIcon',
		                title:  $scope.auditListJs.mes4,
		                formatter:function(value,row,index){
		                	var url = row.filePath;
		                	var caption = row.appName;
		                	return "<a href='" + url + "'" +
		                        "class='fresco'" +
		                        "data-fresco-caption='" + caption + "'>" +
		                        "<img style='max-width:20px;max-height:20px;' src='" + url + "'>" +
		                     "</a>"+" "+caption;
		                }
		            },
            	   {
                   field: 'appTypeName',
                   title: $scope.auditListJs.mes5,
                   formatter:function(value){
                   	if(!value){
                   		return "";
                   	}
                   	return value;
                   }
               },
               {
	                field: 'appCode',
	                title: $scope.auditListJs.appCode,    
	            },
               {
                   field: 'appVersionName',
                   title: $scope.auditListJs.appVersionName,
               },
               {
                   field: 'appVersion',
                   title: $scope.auditListJs.mes7,
               },
              
               {
                   field: 'insName',
                   title:  $scope.auditListJs.mes8,
               },
                   {
                       field: 'groupName',
                       title: $scope.auditListJs.mes,
                       formatter:function(value){
                           if(!value){
                               return "";
                           }
                           return value;
                       }
                   },

                   {
                       field: 'factoryName',
                       title: $scope.auditListJs.mes1,
                       formatter:function(value){
                           if(!value){
                               return "无";
                           }
                           return value;
                       }
                   },{
                       field: 'termTypeNameStr',
                       title: $scope.auditListJs.mes2,
                       formatter: function (value, row) {
                           if (!value) {
                               return "";
                           }
                           return value;
                       }
                   }, {
                   field: 'recSt',
                   title: $scope.auditListJs.mes6,
                   formatter:function(value){
//                   	if(value=='0'){
//                   		return "应用状态";
//                   	}
                   	if(value=="1"){
                   		return $scope.auditListJs.mes12;
                   	}
                   	if(value=="2"){
                   		return $scope.auditListJs.mes13;
                   	}
                   	if(value=="3"){
                   		return $scope.auditListJs.mes14;
                   	}
                   	if(value=="4"){
                   		return $scope.auditListJs.mes23;
                   	}
                   }
               },{
                   field: 'createTime',
                   title: $scope.auditListJs.mes24,
                   formatter:function(value){
                   	return toolsService.getFullDate(value);
                   }
               }
               ]
           });

           $scope.openInsDialog = function () {
               organizationService.openOrganization(function (value) {
                   if (!$scope.condition) {
                       $scope.condition = {};
                   }
                   $scope.condition.insId = value.id;
                   $scope.condition.insName = value.name;
               });
           };
           
           $scope.openInsDialogType = function () {
               organizationService.openOrganization(function (value) {
                   if (!$scope.condition) {
                       $scope.condition = {};
                   }
                   $scope.condition.insId = value.id;
                   $scope.condition.insName = value.name;
                   BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
              		 	$scope.apkGroupList = data;
              	 	});
               });
           };
           
           BaseApi.query("/apkInfo/getApkTypeList",{}, function (data) {
      		 	$scope.apkTypeList = data;
      	 	});
           
           
           $scope.search = function () {
               gridService.search($scope.condition);
           };
           
           $scope.updateRow = function (index) {
               var row = gridService.getRow(index);
               $state.go('home.audit.edit', {id: row.id});
           };
           $scope.detail = function(index){
           	   var row = gridService.getRow(index);
               $state.go('home.audit.view', {id: row.id});
           }
           $scope.audit = function(id){
           	
           	$state.go('home.audit.edit', {id:id});
           }
           $scope.reset = function () {
               $scope.condition = {};
               gridService.search($scope.condition);
           };
           
//           BaseApi.query("/terminalGroup/selectGroup",{}, function (data) {
//     		 	$scope.apkGroupList = data;
//     	 	});
           
            gridService.setPlaceholder();
    	   });
    };
});