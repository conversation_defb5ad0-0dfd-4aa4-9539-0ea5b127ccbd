{"auditList": {"mes": "Group", "mes1": "Manufacturer", "mes2": "Model", "mes3": "Audit", "mes4": "App Name", "mes5": "App Type", "mes6": "Status", "mes7": "Internal Version", "appVersionName": "App Version", "mes8": "Organization", "mes9": "Terminal Type", "mes10": "App Status", "mes11": "Waiting for submission", "mes12": "Waiting for review", "mes13": "Pending release", "mes14": "Released", "mes15": "Removed", "mes16": "Application Date", "mes17": "Company", "mes18": "Developer", "mes19": "Tel.", "mes20": "App Type", "mes21": "App Identification", "mes22": "App Icon", "mes23": "Unlist", "mes24": "Create Time", "auditSuccess": "Audit success"}, "auditListHtml": {"mes": "Organization", "mes1": "<PERSON><PERSON>", "mes2": "App Name", "mes3": "App Version", "mes4": "Terminal Type", "mes5": "Reset", "mes6": "Query", "mes7": "App Type", "mes8": "App Code", "mes9": "Group"}, "auditViewHtml": {"thirdTitle": "View", "appVersion": "Internal version", "iconPath": "Icon", "appVersionName": "AppVersion", "appTypeName": "App Type", "factoryName": "Manufacturer", "termTypeName": "Model", "permission": "Permission", "keyWord": "KeyWord", "appDesc": "App Describe", "verDesc": "Version Describe", "pic": "Picture", "appCode": "App Code", "appName": "App Name"}, "auditJs": {"thirdTitle": "Audit", "appVersion": "Internal version", "iconPath": "Icon", "appVersionName": "AppVersion", "appTypeName": "App Type", "factoryName": "Manufacturer", "termTypeName": "Model", "permission": "Permission", "keyWord": "KeyWord", "appDesc": "App Describe", "verDesc": "Version Describe", "pic": "Picture", "appCode": "App Code", "appName": "App Name", "pass": "Pass", "organization": "Organization"}, "auditHtml": {"thirdTitle": "View", "appVersion": "Internal version", "iconPath": "Icon", "agreeBtn": "Agree", "disagreeBtn": "Disagree", "appVersionName": "AppVersion", "appTypeName": "App Type", "factoryName": "Manufacturer", "termTypeName": "Model", "permission": "Permission", "keyWord": "KeyWord", "appDesc": "App Describe", "verDesc": "Version Describe", "pic": "Picture", "appCode": "App Code", "appName": "App Name", "groupName": "Group", "auditMsg": "Audit opinions", "enterAuditMsg": "Please write audit opinions with less than 300 words", "hisMsg": "History audit opinions"}}