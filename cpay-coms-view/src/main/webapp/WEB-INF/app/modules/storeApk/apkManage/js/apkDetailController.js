'use strict';
define(["organizationService"], function () {
    return function ($scope, $state, dialogService, BaseApi,$stateParams,$http) {
    	$http.get("modules/storeApk/apkManage/i18n/en/apkManage.json").success(function(data) {
        $scope.apkManage = data.apkManage;	
    	$scope.$parent.thirdTitle = "Detail";//
            $scope.domain = window.location.href.split("#")[0];
            $scope.disabled = true;
            $scope.apkInfo={};
            BaseApi.get("/apk/getApkTempById?id="+$stateParams.id,"",function(data){
            	$scope.apkTemporary = data;
            	$scope.apkInfo.id = $stateParams.id;
            	$scope.iconPath =  $scope.apkTemporary.ngsPicPath+$scope.apkTemporary.icon_path
            	var picList = data.picList  
            	var permisList = data.app_pms

            	if($scope.apkTemporary.recSt==0){
            		$scope.apkTemporary.recSt =$scope.apkManage.unSubmitted;//待提交
            	}
            	if($scope.apkTemporary.recSt==1){
            		$scope.apkTemporary.recSt =$scope.apkManage.pending;//待审核
            	}
            	if($scope.apkTemporary.recSt==2){
            		$scope.apkTemporary.recSt =$scope.apkManage.unpublished;//待发布
            	}
            	if($scope.apkTemporary.recSt==3){
            		$scope.apkTemporary.recSt =$scope.apkManage.published;//已发布
            	}
            	if($scope.apkTemporary.recSt==4){
            		$scope.apkTemporary.recSt =$scope.apkManage.offShelf;//已下架
            	}
            	if($scope.apkTemporary.recSt==5){
            		$scope.apkTemporary.recSt =$scope.apkManage.unAudit;//审核未通过
            	}
            	BaseApi.get("/institution/"+$scope.apkTemporary.insId,"",function(data){
               		$scope.apkTemporary.insName = data.name;
               	});
                if($scope.apkTemporary.factoryId == 0){
                    $scope.apkTemporary.factoryName = "无";
                    $scope.apkTemporary.termTypeNameStr = "无";
                }

            	BaseApi.query("/terminalGroup/selectGroupByInsId",{}, function (data2) {
    	   		 	if(data != null){
          			 for(var i = 0;i<data2.length;i++){
          				 if(data2[i].id == data.groupId){
          					$scope.apkTemporary.appGroupId=data2[i].name;
          				 }
          			 	}
          		 	}
    	   		 	if($scope.apkTemporary.appGroupId==""||$scope.apkTemporary.appGroupId==null||!isNaN($scope.apkTemporary.appGroupId)){
    	   		 		$scope.apkTemporary.appGroupId = "无";
    	   		 	}
    	   	 	});
            	 BaseApi.query("/apk/getAppType?insId="+ $scope.apkTemporary.insId,"",function(data){
            		for(var i=0;i<data.length;i++){
      	   		 		if(data[i].id==$scope.apkTemporary.appTypeId){
      	   		 		 $scope.apkTemporary.appTypeId = data[i].tpNm;
      	   		 		}
      	   		 	}
             		if($scope.apkTemporary.appTypeId==""||$scope.apkTemporary.appTypeId==null||!isNaN($scope.apkTemporary.appTypeId)){
             			$scope.apkTemporary.appTypeId = "无";
             		}
                    });

            	if(picList!=null){
            		for(var i=0;i<picList.length;i++){
            			var imgPath = $scope.apkTemporary.ngsPicPath + picList[i];
            			//var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+imgPath+"'>";
         				var caption = $scope.apkTemporary.appName + "应用截图";
         				var img = "<a href='" + imgPath + "'" +
                        "class='fresco'" +
                        "data-fresco-group='" + $scope.apkTemporary.appName + "'" +
                        "data-fresco-caption='" + caption + "'>" +
                        "<img style='margin-bottom:5px;width:80px;height:60px;' src='" + imgPath + "'>" +
                        "</a>";
            			
            			var li = "<li  style='float:left;color:white;text-align:center;margin-left:15px;'>" + img
         						+ "<span style='color:black;'>"  + "</span></li>";
         			$('#screenshotArrays').append(li);
            		}
            	}
            	
            	if(permisList!=null){
            		for(var i=0;i<permisList.length;i++){
            			var permission = permisList[i];
            			var li = "<li style='list-style-type:none;'>"+permission+"</li>";
            			$('#permission').append(li);
            		}
            	}
                var param= {appVersion:data.appVersion,
                    appCode:data.appCode,
                    userId:data.userId,
                    apkId:data.apkId};
                BaseApi.query("/apk/getApkAuditOpinion",param, function (data) {
            		$scope.apkAuditInfo = data;
            		$scope.appName=$scope.apkTemporary.appName;
            		$scope.count = data.length;
           	 		});
        	});

            $scope.showHistory = function(){
            	$state.go('home.storeApk.showDetailHistory', {id: $stateParams.id});
			}
            $scope.goback = function(){
            	$state.go('home.storeApk.manage');
            }
    	});
    };
});
