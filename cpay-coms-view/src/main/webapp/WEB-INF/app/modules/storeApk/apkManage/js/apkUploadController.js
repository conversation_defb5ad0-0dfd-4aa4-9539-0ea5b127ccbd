'use strict';
define(["lib/jquery.form.js"], function () {
    return function ($scope, $state, $stateParams,dialogService, formService,BaseApi,organizationService,Upload,$compile,baseUrl,securityService,anguloaderService,$http) {
    	$http.get("modules/storeApk/apkManage/i18n/en/apkUpload.json").success(function(data) {
    	$scope.apkUploadJs = data.apkUpload;
    	$scope.apkUploadHtml = data.apkUploadHtml;
    	$scope.$parent.thirdTitle = $scope.apkUploadJs.mes	;
    	        $scope.readOnly = false;
    	        $scope.pic_num=0;
    	        $scope.apkTemporary = {};
        		$scope.apkTemporary.ins_nm = BaseApi.getUser().insName
        		$scope.domain = window.location.href.split("#")[0];
       			$scope.formatCheck=function(code,detail){
       			    if(detail == undefined){

       			    }else{
       			        return $.inArray(code,detail) != '-1';
       			    }

       			}
    	        BaseApi.query("/apk/getApkTypeInfo","",function(data){
    	         	$scope.typeList = data;
    	         });
    	        BaseApi.query("/terminalGroup/selectGroupByInsId/",{}, function (data) {
       	   		 	$scope.terminalGroupList = data;
       	   	 	});
       			BaseApi.query("/factory/type",{}, function (data) {
       			    $scope.factoryList = data;
       			});
           		$scope.selectAll = function(){
           		    if ($("#allCheck").attr("checked")) {
           		        $("input:checkbox[name='checkVal']").attr("checked", true);
           		    } else {
           		        $("input:checkbox[name='checkVal']").attr("checked", false);
           		    }
           		}
           		$scope.changeFactory = function (factoryId) {
           		    if(factoryId!=null&&factoryId!=""){
           		        BaseApi.query("/terminalType/activateType/"+factoryId,{}, function (data) {
           		            $scope.termTypeList = data
           		            $("#allCheck").attr("checked",false);
           		        });
           		    }
           		    else{
           		        $scope.termTypeList=[];
           		    }
           		};
    	        $scope.flag = false;
    	        $scope.form = formService.form(function () {

                    var termTypeCodes = [];
                    var termTypeNames = [];
                    $("input:checkbox[name='checkVal']:checked").each(function(){
                    	termTypeCodes.push($(this).val());
                        termTypeNames.push($(this).next().text());
                    })

                    if(termTypeCodes.length == 0){
                        dialogService.alertInfo("error", $scope.apkUploadHtml.mes14);
                        return false;
                    }
                    $scope.apkTemporary.termTypeCodeStr = termTypeCodes.toString();
                    $scope.apkTemporary.termTypeNameStr = termTypeNames.toString();
    	           if(!$scope.file){
    	        	   dialogService.alertInfo("error", $scope.apkUploadHtml.mes1);
    	        	   return;
    	           }
    	           var num = $("#screenshotArrays>li").length;
    	           if(num>5||num<3){
    	        	   dialogService.alertInfo("error", $scope.apkUploadHtml.mes22);
    	        	   return;
    	           }
    	           anguloaderService.show(120000,true);
    	           var appCode = $scope.apkTemporary.appCode;
    	           var appName = $scope.apkTemporary.appName;
    	           var appVersionName = $scope.apkTemporary.appVersionName;
    	           if(appCode.length>50){
    	        	   dialogService.alertInfo("error", $scope.apkUploadHtml.msg15);
    	        	   return;
    	           }
    	           if(appName.length>20){
    	        	   dialogService.alertInfo("error", $scope.apkUploadHtml.msg16);
    	        	   return;
    	           }
    	           if(appVersionName.length>20){
    	        	   dialogService.alertInfo("error", $scope.apkUploadHtml.msg17);
    	        	   return;
    	           }
    	           BaseApi.post("/apk/insert",$scope.apkTemporary,function(data){
    	        	      if(data.status!=4){
    	        	    	  dialogService.alertInfo("error",data.msg);
    	        	    	  $('#screenshotArrays').empty();
    	        	    	  return;
    	        	      }
    	        	      dialogService.alertInfo("success",data.msg);
    	        	      $state.go("home.storeApk.manage");
    	        	},120000); 
    	        });   
    	       $scope.$watch('file',function(newValue,oldValue, scope){
    	    	   if(newValue==null||newValue==""||!newValue){
    	        	   return;
    	           }
    	    	   
    	    	   var name = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
    	    	   if(name!="apk"){
    	    		   dialogService.alertInfo("error", $scope.apkUploadHtml.mes2);
    	    		   $scope.file = ""
    	    		   return;
    	    	   }
    	    	   if (!$scope.file||!$scope.file.size) {
    	            	dialogService.alertInfo("error", $scope.apkUploadHtml.mes3);
    	            	return;
    	            }
    	    	   if($scope.file.size>500*1024*1024){
    	    		   dialogService.alertInfo("error", $scope.apkUploadHtml.mes1);
    	    		   $scope.file = ""
    	    		   return;
    	    	   }
    	    	   $scope.form.reset($scope.Form);
    	    	   $scope.selectFile();
					$('#progress').hide();
					$('.progress>.bar').html('0%');
					$('.progress>.bar').width('0% ');
    	    	    anguloaderService.show(120000,true);
    	            Upload.upload({
    	                url:  baseUrl+'/apk/uploadApk',
    	                data: {file: $scope.file},
    	                headers: securityService.getHeader(),
    	                method: 'POST'
    	            },120000).then(function (resp) {
    	                $scope.apkTemporary = {};
    	                $scope.apkTemporary.ins_nm = BaseApi.getUser().insName
						$scope.apkTemporary = $.extend({},resp.data.data, $scope.apkTemporary);
//    	            	$scope.apkTemporary.appTypeId = $scope.typeList[0].id;
    	            	if(resp.data.status != 6){
    	            		//$scope.apkTemporary = {};
    	            		$scope.file="";
    	            		dialogService.alertInfo("error",resp.data.msg);
    	            		$('#progress').hide();
    	            		anguloaderService.hide(1);
    	            		return;
    	            	}else if(resp.data.status == 6){
    	            		dialogService.alertInfo("success",$scope.apkUploadHtml.success);
                            anguloaderService.hide(1);
    	            		$scope.flag = true;
    	            		$("#screenshotArrays").empty();
    	                	$scope.pic_num=0;
    	            		$scope.iconPath = baseUrl + '/ad/fileView?imagePath=' + $scope.apkTemporary.icon_path;
    	            		//$scope.iconPath = $scope.apkTemporary.ngsPicPath + $scope.apkTemporary.icon_path;
    	            		
    	            	}
    	            }, function (resp) {
    	            	dialogService.alertInfo("error", $scope.apkUploadHtml.mes6);
    	            }, function (evt) {
    						$('#progress').show();
    						$('.progress>.bar').html(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
    						$('.progress>.bar').width(parseInt(100.0 * evt.loaded / evt.total)+ '% ');
    	            }
    	            );
    	            anguloaderService.hide();
    	       });
    	       $scope.selectFile = function(){
    			   $('#progress').hide();
    			   $('.progress>.bar').width('0');
    	       };
    	       
    	 	  $scope.uploadFiles = function(files, errFiles) {
			        $scope.files = files;
			        $scope.errFiles = errFiles;
			        if(files.length>5){
			        	 dialogService.alertInfo("error", $scope.apkUploadHtml.mes22);
	    	        	 return;
			        }
			        angular.forEach(files, function(file) {
			        	var imgname = file.name;
			        	var imgtype = imgname.substring(imgname.lastIndexOf('.') + 1);
			        	
			        	  if(imgtype.toLowerCase()  !="jpg" && imgtype.toLowerCase() !="png"){
		    	    		   dialogService.alertInfo("error", $scope.apkUploadHtml.mes7);
		    	    		   return;
		    	    	   }
		    	           if(file.size>5*1024*1024){
		    	    		   dialogService.alertInfo("error", $scope.apkUploadHtml.mes8);
		    	    		   return;
		    	    	   }
		    	         	$scope.pic_num = $("#screenshotArrays>li").length;
		        	        var num= $scope.pic_num+1;
		        			if (num > 5) {
		        				dialogService.alertInfo("error",$scope.apkUploadHtml.mes22);
		        				return false;
		        			}
			            file.upload = Upload.upload({
			                url: baseUrl+'/apk/uploadImg',
			                headers: securityService.getHeader(),
			                data: {file: file}
			            });

			            file.upload.then(function (resp) {
			             	if(resp.data.status!=3){
		    	        		dialogService.alertInfo("error", resp.data.msg);
		    	        		return;
		    	        	}
		    	            var obj = resp.data.data;
		    	          	var imgUUID = obj.split(";")[0];
		    				var formId = imgUUID.substr(0, 5);
		    				$scope.imgPath = $scope.apkTemporary.ngsPicPath+obj.split(";")[1];
		    				 var btn_change = '<form id="'+formId +'" action="" class="filename" method="post" enctype="multipart/form-data">'+
		    	             '  <a href="javascript:void(0);" class="file">'+$scope.apkUploadHtml.change+
		    	             ' <input type="file" name="' + imgUUID+'" id="'+imgUUID+'" />' +
		    	             ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0);" class="file"'+'ng-click=deleteImg("'+ imgUUID +'") >'+$scope.apkUploadHtml.del+'</a>'+'</form>';
		    				 //var img = "<img  style='margin-bottom:5px;width:90px;height:60px' src='"+$scope.imgPath+"'>";
		    				 //var imgPath = $scope.imgPath;
		    				 var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
		    				 var caption = $scope.apkTemporary.appName + $scope.apkUploadHtml.mes10;
		    				 var img = "<a href='" + imgPath + "'" +
		    	             "class='fresco'" +
		    	             "data-fresco-group='" + $scope.apkTemporary.appName + "'" +
		    	             "data-fresco-caption='" + caption + "'>" +
		    	             "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
		    	             "</a>";	
		    				 var li = "<li id='"+imgUUID+"' style='float:left;width: 140px;height: 120px;text-align:center;margin-left:5px;'>" + img
		    							+ "<span style='color:black;'>" + btn_change 
		    							 + "</span></li>";
		    				$('#screenshotArrays').append(li);
		    				var el = angular.element(screenshotArrays);
		    				$compile(el)($scope);
		    				$scope.reset(imgUUID);
		    	           	
			            }, function (resp) {
			            	dialogService.alertInfo("error",+$scope.apkUploadHtml.mes11);
			            }, function (evt) {
			                file.progress = Math.min(100, parseInt(100.0 * 
			                                         evt.loaded / evt.total));
			            });
			        });
			    };

			$scope.openInsDialog = function () {
				organizationService.openOrganization(function (value) {
					$scope.apkTemporary.insId = value.id;
					$scope.apkTemporary.insName = value.name;
					BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
						$scope.terminalGroupList = data;
					});
				});
			};
    	    	   BaseApi.get("/apk/updatePicList","",function(data){
    	            });  

    	       $scope.deleteImg = function(imgUUID){
    	    	   BaseApi.query("/apk/deleteImg?uuid="+imgUUID,"",function(data){
    	    		   $("#" + imgUUID).remove();
    	            });  
    	       }
    	       
    	      $scope.reset = function(imgUUID){
    	    	  angular.forEach($scope.fileImg, function(file) {
    	    		  var name = file.name.substring(file.name.lastIndexOf('.') + 1);
        	    	  name = name.toLocaleLowerCase();
                      if(name !="jpg" && name !="png" &&
                          name !="JPG" && name !="PNG"){
                          dialogService.alertInfo("error", $scope.apkUploadHtml.mes7);
                          return;
                      }
    	    	  });
    	    	  var formId = imgUUID.substr(0, 5);
    	    	  $("input[name="+imgUUID+"]").change(function(){
    					$("#"+formId).ajaxSubmit({
    			            type: 'post',
    			            data: {"uuid":imgUUID},
    			            headers: securityService.getHeader(),
    			            url: baseUrl+'/apk/changeImg',
    			            /* dataType:  'json',  */
    			            resetForm: false,
    			            // 成功提交后，重置所有表单元素的值
    			            success: function(data) {
    			            	if(data.status!=5){
    			            		dialogService.alertInfo("error", data.status);
    			            		return;
    			            	}
    			            	var obj = data.data;
    			               	var nimgUUID = obj.split(";")[0];
    			     			var formId = nimgUUID.substr(0, 5);
    			     			$scope.imgPath = $scope.apkTemporary.ngsPicPath+obj.split(";")[1];
    			     			 var btn_change = '<span style="color:black;"><form id="'+formId +'" action="" class="filename" method="post" enctype="multipart/form-data">'+
    			                  '  <a href="javascript:void(0)" class="file">'+$scope.apkUploadHtml.change+
    			                  ' <input type="file" name="' + nimgUUID + '" id="' + nimgUUID+'" />' +
    			                  ' </a>' +'&nbsp;&nbsp;'+' <a href="javascript:void(0)" class="file"'+'ng-click=deleteImg("'+ nimgUUID +'") >'+$scope.apkUpload.del+'</a>'+'</form></span>';
    			     	
    			     			//var imgPath = $scope.imgPath;
    			     			var caption = $scope.apkTemporary.appName + $scope.apkUploadHtml.mes10;
    			     			var imgPath =  baseUrl + '/ad/fileView?imagePath=' + obj.split(";")[1];
    		     				var img = "<a href='" + imgPath + "'" +
    		                    "class='fresco'" +
    		                    "data-fresco-group='" + $scope.apkTemporary.appName + "'" +
    		                    "data-fresco-caption='" + caption + "'>" +
    		                    "<img style='margin-bottom:5px;width:90px;height:60px;' src='" + imgPath + "'>" +
    		                    "</a>";	
    			     			/* var li = "<li id='"+nimgUUID+"' style='float:left;width: 110px;height: 120px;text-align:center;margin-left:5px;'>" + img
    			     						+ "<span style='color:black;'>" + btn_change 
    			     						 + "</span></li>";*/
    			     			$("#" + imgUUID).empty();
    			     			//$("#" + imgUUID).attr("id", nimgUUID).append(li);
    			     			$("#" + imgUUID).attr("id", nimgUUID).append(img).append(btn_change);
    			     			var el = angular.element(screenshotArrays);
    			     			$compile(el)($scope);
    			     			$scope.reset(nimgUUID);
    			     			
    			            },
    			            error: function(XmlHttpRequest, textStatus, errorThrown) {
    			            	
    			            }

    			        });
    					
    				});
    	      }
    	});
    };
});