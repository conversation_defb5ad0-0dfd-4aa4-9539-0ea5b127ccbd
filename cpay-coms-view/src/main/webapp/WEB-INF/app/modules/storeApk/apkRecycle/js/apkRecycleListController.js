'use strict';
define(["organizationService"], function () {
    return function ($scope, $state, dialogService, gridService,toolsService,organizationService,BaseApi,$http) {
    	$http.get("modules/storeApk/apkRecycle/i18n/en/apkrecycleList.json").success(function(dataLan) {
        $scope.apkrecycleInfo = dataLan.apkrecycleInfo;
    			$scope.$parent.thirdTitle = $scope.apkrecycleInfo.thirdTitle;
    	        gridService.initTable({
    	            url:"/apkInfo/getApkRecycleList",
    	            scope: $scope,
    	            operate: function (value,row, index){
    	            	var el="";
    	            	  el += '<a  href="javascript:;" has-permission="apk_recycle_restore" ng-click="restore('+index+')"  title="Restore">'+"Restore"+'</a>';
    	            	  el += '<a  href="javascript:;" has-permission="apk_recycle_deleteApk" ng-click="deleteApk(' +index+')"  title="Delete">'+"Delete"+'</a>';
    	                return el;
    	                
    	            },
    	            operator:false,
    	            columns: [{
    	                field: 'state',
    	                checkbox: 'true'
    	            },
    	            {
		                field: 'appIcon',
		                title:  $scope.apkrecycleInfo.app_icon,
		                formatter:function(value,row,index){
		                	var url = row.filePath;
		                	var caption = row.appName;
		                	return "<a href='" + url + "'" +
		                        "class='fresco'" +
		                        "data-fresco-caption='" + caption + "'>" +
		                        "<img style='max-width:20px;max-height:20px;' src='" + url + "'>" +
		                     "</a>"+" "+caption;
		                }
		            },
    	            {
    	                field: 'appTypeName',
    	                title: $scope.apkrecycleInfo.app_type,
    	                formatter:function(value){
    	                	if(!value){
    	                		return "";
    	                	}
    	                	return value;
    	                }
    	            },
                        {
                            field: 'factoryName',
                            title: $scope.apkrecycleInfo.manufacturer,
                            formatter:function(value){
                                if(!value){
                                    return "";
                                }
                                return value;
                            }
                        },
                        {
                            field: 'termTypeNameStr',
                            title: $scope.apkrecycleInfo.mode,
                            formatter:function(value){
                                if(!value){
                                    return "";
                                }
                                return value;
                            }
                        },
						{
    	                field: 'appCode',
    	                title:  $scope.apkrecycleInfo.app_code,
    	            }, 
    	            {
    	                field: 'appVersion',
    	                title:  $scope.apkrecycleInfo.internal_version,
    	            }, {
    	                field: 'insName',
    	                title: $scope.apkrecycleInfo.organization,
    	            },{
    	                field: 'groupName',
    	                title: $scope.apkrecycleInfo.group,
    	                formatter:function(value){
    	                	if(!value){
    	                		return "";
    	                	}
    	                	return value;
    	                }
    	            }, {
    	                field: 'createTime',
    	                title: $scope.apkrecycleInfo.upload_time,
    	                formatter:function(value){
    	                	return toolsService.getFullDate(value);
    	                }
    	            }, /*{
    	                field: 'pubTime',
    	                title: '发布时间',
    	                formatter:function(value){
    	                	if(!value){
    	                		return '未发布';
    	                	}
    	                	return toolsService.getFullDate(value);
    	                }
    	            }*/{
    	                field: 'isDelete',
    	                title: $scope.apkrecycleInfo.isDelete,
    	                width:120,
    	                formatter: function (value) {
    	                          return "waitting for delete";
    	                    }	
    	            	}]
    	        });
    	        $scope.deleteApk = function (index) {
    	            var row = gridService.getRow(index);
    	            dialogService.openConfirm($scope.apkrecycleInfo.confirm_delete_msg+row.appName+"？", function (){
    	                var ids = "";
    	                var temIds="";
    	                if(row.recSt == '3' || row.recSt == '4'){
    	                	ids = row.id;
    	                }else{
    	                	temIds = row.id;
    	                }
    	                BaseApi['delete']("/apk/deleteApks?ids="+ids+"&temIds="+temIds,function (data) {
    	                    dialogService.closeConfirm();
    	                    dialogService.alertInfo("info",data.msg);
    	                    gridService.refresh();
    	                });
    	                
    	           });
    	            
    	        };
    	        
    	        $scope.restore = function(index){
    	          var row = gridService.getRow(index);
    	          var id = row.id;
    	          var recSt = row.recSt;
    	          delete row.auditUserId;
    	          delete row.auditTime;
    	          delete row.auditMsg;
    	          BaseApi.patch("/apkInfo/restoreApk",row,function (data) {
    	              dialogService.alertInfo("info","operate success");
    	              gridService.refresh();
    	          });
    	        };
    	        
    	        $scope.search = function () {
    	            gridService.search($scope.condition);
    	        };
    	        
    	        $scope.openInsDialog = function () {
    	            organizationService.openOrganization(function (value) {
    	                if (!$scope.condition) {
    	                    $scope.condition = {};
    	                }
    	                $scope.condition.insId = value.id;
    	                $scope.condition.insName = value.name;
    	            });
    	        };
    	        
    	        $scope.deleteRows = function () {
    	            var rows = gridService.getSelectedRow();
    	            if (rows.length == 0) {
    	                dialogService.alertInfo("info", $scope.apkrecycleInfo.select_record);
    	                return;
    	            }
    	            dialogService.openConfirm($scope.apkrecycleInfo.delete_record_tip, function () {
    	                var ids = "";
    	                var temIds="";
    	                for (var index in rows) {
    	                	if(rows[index].recSt == '3' || rows[index].recSt == '4'){
    	                		ids = ids + rows[index].id + ",";
    	                	}else{
    	                		temIds = temIds + rows[index].id + ",";
    	                	}
    	                }
    	                var param1 = ids.substr(0, ids.length - 1);
    	                var param2 = temIds.substr(0, temIds.length - 1);
    	                BaseApi['delete']("/apk/deleteApks?ids="+param1+"&temIds="+param2,function (data) {
    	                    dialogService.closeConfirm();
    	                    dialogService.alertInfo("info","operate success");
    	                    gridService.refresh();
    	                });
    	                
    	           });
    	       };
    	       
    	        $scope.reset = function () {
    	            $scope.condition = {};
    	            gridService.search($scope.condition);
    	        };
    	});

    };
});