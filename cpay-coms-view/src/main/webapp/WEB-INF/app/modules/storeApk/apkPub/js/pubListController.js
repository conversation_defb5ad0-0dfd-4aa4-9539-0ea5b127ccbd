'use strict';
define(["organizationService"], function () {
    return function ($scope, $state, dialogService, gridService, toolsService, organizationService, BaseApi, $http, $timeout) {
    	$http.get("modules/storeApk/apkPub/i18n/en/apkpubList.json").success(function(dataLan) {
        $scope.apkpubInfo = dataLan.apkpubInfo;
        
    	$scope.$parent.thirdTitle = "";
        BaseApi.query("/apkInfo/getApkTypeList", {}, function (data) {
            $scope.apkTypeList = data;
        });
        gridService.initTable({
            url: "/apkInfo/getPubApk",
            scope: $scope,
            operator: false,
            singleSelect: true,
            operate: function (value, row, index) {
                var e1 = '<a  href="javascript:;" ng-click="detail(' + index + ')"  title="Detail">' + $scope.apkpubInfo.detail + '</a>';
                if (row.recSt == '2' || row.recSt == '4') {
                    e1 = e1 + '<a  href="javascript:;" has-permission="apk_pub_publish" ng-click="publish(' + index + ')"  title="Release">' + $scope.apkpubInfo.publish + '</a>';
                }

                if (row.recSt == '3') {
                    e1 = e1 + '<a  href="javascript:;" has-permission="apk_pub_offLine" ng-click="offLine(' + index + ')"  title="Unpublish">' + $scope.apkpubInfo.offShelf + '</a>';
                }
                return e1;
            },
            columns: [

                {
                    field: 'appIcon',
                    title: $scope.apkpubInfo.app_name,
                    formatter: function (value, row, index) {
                        var url = row.filePath;
                        var caption = row.appName;
                        return "<a href='" + url + "'" +
                            "class='fresco'" +
                            "data-fresco-caption='" + caption + "'>" +
                            "<img style='max-width:20px;max-height:20px;' src='" + url + "'>" +
                            "</a>" + " " + caption;
                    }
                },
                {
                    field: 'appTypeName',
                    title: $scope.apkpubInfo.type_name,
                    formatter: function (value) {
                        if (!value) {
                            return "Null";
                        }
                        return value;
                    }
                },

                {
                    field: 'appCode',
                    title: $scope.apkpubInfo.app_code,
                },

                {
                    field: 'appVersionName',
                    title: $scope.apkpubInfo.version_name,
                },
                {
                    field: 'appVersion',
                    title: $scope.apkpubInfo.internal_version,
                },
                {
                    field: 'insName',
                    title: $scope.apkpubInfo.organization,
                }, {
                    field: 'groupName',
                    title:  $scope.apkpubInfo.group,
                    formatter: function (value) {
                        if (!value) {
                            return "Null";
                        }
                        return value;
                    }
                },
                {
                    field: 'factoryName',
                    title: $scope.apkpubInfo.manufacturer,
                    formatter: function (value) {
                        if (!value) {
                            return "Null";
                        }
                        return value;
                    }
                },
                {
                    field: 'termTypeNameStr',
                    title: $scope.apkpubInfo.mode,
                    formatter: function (value) {
                        if (!value) {
                            return "Null";
                        }
                        return value;
                    }
                },
                {
                    field: 'recSt',
                    title: $scope.apkpubInfo.app_status,
                    formatter: function (value) {
                        if (value == '0') {
                            return $scope.apkpubInfo.unSubmitted;
                        }
                        if (value == "1") {
                            return $scope.apkpubInfo.pending;
                        }
                        if (value == "2") {
                            return $scope.apkpubInfo.unpublished;
                        }
                        if (value == "3") {
                            return $scope.apkpubInfo.published;
                        }
                        if (value == "4") {
                            return $scope.apkpubInfo.offShelf;
                        }
                    }
                }/*,
                {
                    field: 'auditMsg',
                    title: '审核意见',
                    formatter:function(value){
                    	if(!value){
                    		return '无';
                    	}
                    	var str = '<label style="width:200px;overflow:hidden; white-space:nowrap; text-overflow:ellipsis;" title="' + value + '">' + 
        						value + '</label>';
                    	return str;
                    }
                }*/]
        });

        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            if (row.recSt != '3' && row.recSt != '4') {
                $state.go('home.pub.view', {id: row.id});
                return;
            }
            $state.go('home.pub.detail', {id: row.id});
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
                BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
                    $scope.apkGroupList = data;
                });
            });
        };
        $scope.publish = function (index) {
            var row = gridService.getRow(index);
            var appCode = row.appCode;
            var appCodeArr = appCode.split(".");
            if (appCodeArr.length > 4) {
                if ((appCodeArr[0] + appCodeArr[1] + appCodeArr[2] == 'comcentermcpay') && appCodeArr[appCodeArr.length - 1] == "update") {
                    dialogService.alertInfo("info", "当前应用不能发布！");
                    return;
                }
            }
            var id = row.id;
            var recSt = row.recSt;
            $state.go('home.pub.edit', {id: id, recSt: recSt});
        }
        $scope.calcelApkYL = function () {
            var rows = gridService.getSelectedRow();
            var appId = rows[0].id;
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.apkpubInfo.select_a_record);
                return;
            }
            dialogService.openConfirm("确认要撤销当应用的依赖关系吗？", function () {
                BaseApi.patch("/apkInfo/calcelApkYL?appId=" + appId, {}, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("info", data.msg);
                    gridService.refresh();
                    $scope.closeThisDialog(0);
                });
            });
        }
        $scope.apkYL = function () {
            var rows = gridService.getSelectedRow();
            var appId = rows[0].id;
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.apkpubInfo.select_a_record);
                return;
            }
            var appCode = rows[0].appCode;
            var appCodeArr = appCode.split(".");
            if (appCodeArr.length > 4) {
                if ((appCodeArr[0] + appCodeArr[1] + appCodeArr[2] == 'comcentermcpay') && appCodeArr[appCodeArr.length - 1] == "update") {
                    dialogService.alertInfo("info", "当前应用不能进行依赖关联！");
                    return;
                }
            }
            $timeout(function () {
                dialogService.openDialog({
                    template: "modules/storeApk/apkPub/showAppYlList.html",
                    width: '60%',
                    controller: function ($scope, dialogService, $timeout, gridService, formService) {
                        gridService.initTable({
                            url: "/apkInfo/getPubApk",
                            scope: $scope,
                            id: "#table1",
                            operator: false,
                            singleSelect: true,
                            columns: [
                                {field: 'state', checkbox: 'true'},
                                {field: "appName", title: "应用名称"},
                                {field: "appCode", title: "编号"},
                                {field: "appVersion", title: "应用版本号"},
                                {field: "insName", title: "所属机构"}
                            ]
                        });
                        $scope.ok = function () {
                            var rows = $("#table1").bootstrapTable("getSelections");
                            if (rows.length == 0) {
                                dialogService.alertInfo("info", "请选择一条记录！");
                                return;
                            } else {
                                var appCode = rows[0].appCode;
                                var appCodeArr = appCode.split(".");
                                if (appCodeArr.length > 4) {
                                    if ((appCodeArr[0] + appCodeArr[1] + appCodeArr[2] == 'comcentermcpay') && appCodeArr[appCodeArr.length - 1] == "update") {
                                        dialogService.openConfirm("确认要设定当前选择的应用吗？", function () {
                                            BaseApi.patch("/apkInfo/appYL?appId=" + appId + "&appIdYl=" + rows[0].id, {}, function (data) {
                                                dialogService.closeConfirm();
                                                dialogService.alertInfo("info", data.msg);
                                                gridService.refresh();
                                                $scope.closeThisDialog(0);
                                            });
                                        });
                                    } else {
                                        dialogService.alertInfo("info", "当前应用不得进行关联操作！");
                                        return;
                                    }
                                } else {
                                    dialogService.alertInfo("info", "当前应用不得进行关联操作！");
                                    return;
                                }
                            }

                        };
                    }
                });
            }, 200);
        }
        //}
        $scope.reset = function () {
            $scope.condition = {};
            $scope.apkGroupList = {};
            gridService.search($scope.condition);
        };

        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            var appName = row.appName;
            var recSt = row.recSt;
            dialogService.openConfirm("Confirm to delete current application？", function (confirm) {
                BaseApi.patch("/apkInfo/recycleApkInfoById?ids=" + row.id + "&recSts=" + recSt, {}, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("info", data.msg);
                    gridService.refresh();
                });
            });
        };

        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.apkpubInfo.select_a_record);
                return;
            }
            dialogService.openConfirm("确认要删除选中的记录吗？", function () {
                var ids = "";
                var recSts = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                    recSts = recSts + rows[index].recSt + ",";
                }
                var param1 = ids.substr(0, ids.length - 1);
                var param2 = recSts.substr(0, recSts.length - 1);
                BaseApi.patch("/apkInfo/recycleApkInfoById?ids=" + param1 + "&recSts=" + param2, {}, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("info", data.msg);
                    gridService.refresh();
                });
            });
        };

        $scope.offLine = function (index) {
            var row = gridService.getRow(index);
            var appName = row.appName;
            dialogService.openConfirm("Confirm the current application of Off-Shelf？", function (confirm) {
                BaseApi.patch("/apkInfo/offLineApk?id=" + row.id, row, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("info", "operate success");
                    gridService.refresh();
                });
            });
        };

        gridService.setPlaceholder();
    });
    };
});