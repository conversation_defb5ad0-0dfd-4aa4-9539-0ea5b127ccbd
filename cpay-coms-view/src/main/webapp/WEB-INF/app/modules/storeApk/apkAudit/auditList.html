<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                 <div class="input-append input-group" ng-click="openInsDialog()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="{{auditListHtml.mes}}">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button" ></button>
                </div>  
                <input type="text" class=" top-inp" placeholder="{{auditListHtml.mes2}}" ng-model="condition.appName">
                <input type="text" class=" top-inp" placeholder="{{auditListHtml.mes8}}" ng-model="condition.appCode">

                <select ng-options="m.id as m.tpNm for m in apkTypeList" ng-model="condition.appTypeId" class="input-big" >
                    <option value="">{{auditListHtml.mes7}}</option>
                </select>
            <!--     <select ng-options="m.id as m.name for m in apkGroupList" ng-model="condition.groupId" class="input-big" >
                    <option value="">所属组别</option>
                </select> -->
            
                
                <button type="button" class="btn-reset" ng-click="reset()">{{auditListHtml.mes5}}</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>{{auditListHtml.mes6}}</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <table id="table"></table>
        </div>
    </div>
</div>
