{"apkUpload": {"mes": "Upload the App", "mes1": "Please upload the apk file", "mes2": "Please select the file of apk format", "mes3": "Please select the apk file", "mes4": "Please select the apk file smaller than 100M", "mes5": "Uploading the App successfully.", "mes6": "Fail to upload the App.", "mes7": "Please select images of jpg, jpeg or jpg format.", "mes8": "Please select the image no more than 5M", "mes9": "Please upload 3-5 screenshots", "change": "Modify", "del": "Delete", "mes10": "App Screenshot", "mes11": "Fail to upload the image", "mes12": "Fail to update the icon, the image is more than 1280px × 800px!", "mes13": "Fail to upload the file", "mes14": "Terminal models shall not be empty", "mes15": "Over length of application code", "mes16": "Over length of application name", "mes17": "Over length of application version"}, "apkUploadHtml": {"mes": "Upload", "mes1": "(Please upload an APK smaller than 500M)", "mes2": "App Name", "mes3": "App Identification", "mes4": "Internal Version", "mes5": "App Version", "mes6": "App Icon", "mes7": "Upload the icon", "mes8": "(If not uploaded,the default icon will be used. The icon shall not exceed 1280px * 800px and no more than 5M)", "mes9": "App Type", "mes10": "Please choose", "mes11": "Terminal Type", "mes12": "Select the terminal type", "mes13": "Landscape Screen", "mes14": "Portrait Screen", "mes15": "Landscape and Portrait Screen", "mes16": "Keywords", "mes17": "(Please separate multiple key words with commas.)", "mes18": "Version Update Note", "mes19": "App Description", "mes20": "App Screenshot", "mes21": "Upload", "mes23": "Organization", "mes24": "Group", "mes25": "Manufacturer", "mes26": "Model", "mes27": "All", "mes22": "Suggested to upload 3-5 screenshots of png or jpg format with resolution of 960px × 540px, and each one shall not exceed 5M.", "del": "Delete", "change": "Update"}}