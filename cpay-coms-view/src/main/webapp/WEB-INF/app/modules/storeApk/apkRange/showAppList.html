
<div class="container-fluid">
	<div class="container-fluid-in">
		<div class="row-fluid" id="condition-scroll">
			<div class="input-append input-group" ng-click="openInsDialog()">
                    <input type="text" class=" top-big" style="width: 170px;" readonly="true" ng-model="condition.insName"
                           placeholder="选择需要进行应用筛选的机构">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice"  type="button"></button>
            </div>
            <div class="input-append input-group">
                  <select ng-options="m.id as m.name for m in terminalGroupList" ng-model="condition.groupId" class="input-big" style="width: 220px;">
                    <option value="">选择需要进行应用筛选的组别</option>
                 </select>
            </div>
		</div>
		<div class="row-fluid">
			<div class="span12">
				<table id="table1"></table>
			</div>
		</div>
		<div class="modal-footer">
			<button class="btn" type="button" ng-click="closeThisDialog(0)">取消</button>
			<button class="btn blue" type="submit" ng-click="ok()">
				<i class="icon-ok"></i>确定
			</button>
		</div>
	</div>
</div>
