<div class="container-fluid">
	<div class="row-fluid">
		<div class="feedback-view">
			<div class="info-container">
				<table class="info-table info-table-normal">

					<tr class="info-table-row">
						<td class="info-table-name">Can I Uninstall</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.uninstallFlag"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Whether To Force An Update</td>

						<td class="info-table-value"><span
								ng-bind="storeApkInfo.upFlag"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Group</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.groupId"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Organization</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.insName"></span></td>
					</tr>
					
					<tr class="info-table-row">
						<td class="info-table-name">Application Classification</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appTypeId"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Terminal Manufacturer</td>
						<td class="info-table-value"><span
								ng-bind="storeApkInfo.factoryName"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Terminal Type</td>
						<td class="info-table-value"><span
								ng-bind="storeApkInfo.termTypeNameStr"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Application Recommendation</td>
						<td class="info-table-value">
							<div id="degree"></div> <input type="hidden" id="re_degree"
							name="reDegree" ng-disabled="disabled"
							ng-model="storeApkInfo.reDegree" message-id="reDegree">
						</td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Application Name</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appName"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Application Package Name</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appCode"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Application Version</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appVersionName"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Internal Version</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appVersion"></span></td>
					</tr>
					
					<tr class="info-table-row">
						<td class="info-table-name">Application Icon</td>
						<td class="info-table-value"><a href='{{iconPath}}'
							class='fresco'> <img style='width: 48px; height: 48px;'
								src='{{iconPath}}' alt='' />
						</a></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Application Status</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.recSt"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Key Words</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.keyWord"></span></td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Permission</td>
						<td class="info-table-value">
							<ul id="permission">
							</ul>
						</td>
					</tr>
					<tr class="info-table-row">

						<td class="info-table-name">Application Introduction</td>
						<td class="info-table-value">
							<p ng-bind="storeApkInfo.appDesc"></p>
						</td>
					</tr>
					<tr class="info-table-row">
						<td class="info-table-name">Version Update Instructions</td>
						<td class="info-table-value">
							<p ng-bind="storeApkInfo.verDesc"></p>
						</td>
					</tr>

					<tr class="info-table-row">
						<td class="info-table-name">Application Screenshot</td>
						<td class="info-table-value">
							<ul id="screenshotArrays" class="screenshot">
							</ul>
						</td>
					</tr>
					<!-- <tr class="info-table-row">
						<td class="info-table-name">Audit Opinion</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.auditMsg"></span></td>
					</tr> -->
					<tr class="info-table-row">
						<td class="info-table-name">Reviewer</td>
						<td class="info-table-value"><span
							ng-bind="auditUser.insName||'No'"></span></td>
					</tr>
					<!-- <tr class="info-table-row">
						<td class="info-table-name">Developer</td>
						<td class="info-table-value"><span
							ng-bind="storeApkInfo.appPromulgator"></span></td>
					</tr> -->
					<tr class="info-table-row big-row">
						<td class="info-table-name">Historical Review Opinion</span>
						</td>
						<td class="info-table-value info-top">
							<div class="answer-block no-margin"
								ng-repeat="data in apkAuditInfo">
								<div class="user-info">
									<div class="info-container" style="float: none;">
										<div class="name" style="display: inline">{{data.auditUserName}}</div>
										<div class="time pull-right"
											style="display: inline; float: right;">{{data.createTime|
											datefmt}}</div>
									</div>
								</div>
								<div class="content">{{data.auditOpinion}}</div>
							</div>
						</td>
					</tr>

				</table>
			</div>
		</div>
	</div>
</div>
<!-- <div class="edit-head">
    <div class="edit-button">
        <button type="button" class="btn" ng-click="goback()">return</button>
    </div>
</div> -->
