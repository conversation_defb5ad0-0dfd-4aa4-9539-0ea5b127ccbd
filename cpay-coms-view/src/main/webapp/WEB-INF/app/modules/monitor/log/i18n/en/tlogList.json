{"tlogInfo": {"firstTitle": "Remote Update", "secondTitle": "Remote Extract", "add_extract_log_task": "Add Remote Extract Task", "modify_task": "Modify Task", "reset": "Reset", "query": "Query", "add": "Add", "whether_to_enable": "Is Enable", "ok": "OK", "yes": "YES", "no": "NO", "name": "Name", "code": "Code", "save": "Save", "batch_delete": "<PERSON><PERSON> Delete", "query_performance": "Query Performance", "perform_task": "Perform The Task", "select_task": "Please select a task!", "task_name": "Task Name", "state": "Task State", "start_time": "Task Start Time", "end_time": "Task End Time", "log_start_time": "Log Start Time", "log_end_time": "Log End Time", "create_time": "Create Time", "type": "Type", "app_code": "App Code", "path": "Path", "select": "Select", "manufacturer": "Manufacturer", "mode": "Model", "terminal_list": "Terminal List", "add_terminal": "Add Terminal", "EXCEL_import": "EXCEL Import", "delete": "Delete", "select_please": "Select Please!", "import_fial_list": "Import Fail List", "terminal_list_is_null": "Terminal list is null!", "add_task_success": "Add Task Success!", "time_format_error": "The start date shall not exceed the end date", "confirm_delete_tasks": "Confirm to delete the tasks ", "confirm_delete_task": "Confirm to delete the task named ", "delete_success": "Delete Success", "confirm_task_performed": "Confirm that the task is to be performed?", "perform_current_task_first": "Please perform the current task first", "task_detail": "Task Detail", "cancel": "Cancel"}}