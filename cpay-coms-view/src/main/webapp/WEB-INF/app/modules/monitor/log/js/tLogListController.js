'use strict';
define(["modules/terminal/factory/js/factoryService"], function () {
    return function ($scope, tLogService, BaseApi, toolsService, factoryService, $state, dialogService, gridService,$http) {
    	$http.get("modules/monitor/log/i18n/en/tlogList.json").success(function(dataLan) {
        $scope.tlogInfo = dataLan.tlogInfo;
        
        $scope.$parent.thirdTitle = "";
        BaseApi.query("/dict/type/job_type2",{}, function (data) {
	        gridService.initTable({
	            url: "/uploadjob",
	            scope: $scope,
	            detail:'uploadjob_view',
                operate: function (value, row, index) {
                	var el = '';          	
                	if (row.releaseStatus==2) {
                		
                	}else{
                		el = el + '<a  href="javascript:;" ng-click="updateRow(' + index + ')"  title="update" has-permission="uploadjob_edit"> update</a>';
                		el = el + '<a  href="javascript:;" ng-click="deleteRow(' + index + ')"  title="delete" has-permission="uploadjob_del"> delete</a>';
                	}                	
                	return el;
                },
	            columns: [
	            	{
	                field: 'state',
	                checkbox: 'true'
	            	}, {
	            	field: "jobName", 
	            	title: $scope.tlogInfo.task_name,
	            	}, 
	            	{
		            	field: "releaseStatus", 
		            	title: $scope.tlogInfo.state,
	                    formatter: function (value) {
	                    	var type='';
	                    	$.each(data,function(n,obj) {  
	                           if(obj.type==value) {
	                        	   type=obj.name;
	                           } 
	                          });
		                     return type;
	                    }
		            },
	            	{
	            	field: "releaseTime",
	            	title: $scope.tlogInfo.start_time,
                    formatter: function (value) {
                    	return toolsService.getFullDate(value);
                    }
	            	}, {
	            	field: "validDate",
	            	title: $scope.tlogInfo.end_time,
                    formatter: function (value) {
                    	return toolsService.getFullDate(value);
                    }
	            	}, {
	            	field: "recordCreateTime", 
	            	title: $scope.tlogInfo.create_time,
                    formatter: function (value) {
                        // 用本地浏览器的时区创建 Date 对象
                        const localDate = new Date(value);

                        // 格式化输出 yyyy-MM-dd HH:mm:ss
                        const pad = (n) => n.toString().padStart(2, '0');

                        const yyyy = localDate.getFullYear();
                        const MM = pad(localDate.getMonth() + 1);
                        const dd = pad(localDate.getDate());
                        const HH = pad(localDate.getHours());
                        const mm = pad(localDate.getMinutes());
                        const ss = pad(localDate.getSeconds());

                        return `${yyyy}-${MM}-${dd} ${HH}:${mm}:${ss}`;
                    }
	            	}]
            });
        });
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm($scope.tlogInfo.confirm_delete_task + row.jobName + "？", function (confirm) {
                tLogService.deleteJob(row.id, function (data) {
                    dialogService.closeConfirm();
                    if(data.status==100) {
                    	dialogService.alertInfo("success", $scope.tlogInfo.delete_success);
                    }else{
                    	dialogService.alertInfo("error", data.msg);
                    }
                    gridService.refresh();
                });
            });
        };
        $scope.deleteRows = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", $scope.tlogInfo.select_task);
                return;
            }
            dialogService.openConfirm($scope.tlogInfo.confirm_delete_tasks, function () {
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                tLogService.deleteJob(param, function (data) {
                    dialogService.closeConfirm();
                    if(data.status==100) {
                    	dialogService.alertInfo("success", $scope.tlogInfo.delete_success);
                    }else{
                    	dialogService.alertInfo("error", data.msg);
                    }
                    gridService.refresh();
                });
            });
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.monitorlog.update', {id: row.id});
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.monitorlog.view', {id: row.id});
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.task = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length > 1 || rows.length == 0) {
                dialogService.alertInfo("info",  $scope.tlogInfo.select_task);
                return;
            }
            if(rows[0].releaseStatus == 1){
            	dialogService.alertInfo("info", $scope.tlogInfo.perform_current_task_first);
                return;
            }
            $state.go('home.monitorlogtask.list', {id: rows[0].id});
        };
        $scope.subPub = function() {
        	var rows = gridService.getSelectedRow();
            if (rows.length > 1 || rows.length == 0) {
                dialogService.alertInfo("info",  $scope.tlogInfo.select_task);
                return;
            }
            dialogService.openConfirm($scope.tlogInfo.confirm_task_performed, function () {
	            tLogService.subPub(rows[0], function (data) {
	            	 dialogService.closeConfirm();
	                 dialogService.alertInfo("success", data.msg);
	                 gridService.refresh();
	            });
            });
        }
        
         gridService.setPlaceholder();
    	});
    };
});