<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div class="info-container">
                <table class="info-table info-table-short " style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name">printer</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.printer"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">codeKeyBoard</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.codeKeyBoard"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">icCard</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.icCard"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">cardReader</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cardReader"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">nCardReader</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.nCardReader"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">sdCard</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.sdCard"></span>
                        </td>
                    </tr>
               
                    <tr class="info-table-row">
                        <td class="info-table-name">storageSpace</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.storageSpace"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">pasmCard</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.pasmCard"></span>
                        </td>
                    </tr>


                    <tr class="info-table-row">
                        <td class="info-table-name">serialPort</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.serialPort"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name">frontCamera</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.frontCamera"></span>
                        </td>
                    </tr>

                    <tr class="info-table-row">
                        <td class="info-table-name">rearCamera</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.rearCamera"></span>
                        </td>
                    </tr>


                    <tr class="info-table-row">
                        <td class="info-table-name">cpaySDK</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cpaySDK"></span>
                        </td>
                    </tr>

                    <tr class="info-table-row">
                        <td class="info-table-name">cpaySecurityComponent</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cpaySecurityComponent"></span>
                        </td>
                    </tr>
               
                    <tr class="info-table-row">
                        <td class="info-table-name">cpayRegisterComponent</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cpayRegisterComponent"></span>
                        </td>
                    </tr>
                
                    <tr class="info-table-row">
                        <td class="info-table-name">cpayLauncher</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cpayLauncher"></span>
                        </td>
                    </tr>
                
                    <tr class="info-table-row">
                        <td class="info-table-name">cpaymentComponent</td>

                        <td class="info-table-value">
                            <span ng-bind="dyn.cpaymentComponent"></span>
                        </td>
                    </tr>
                </table>
                
                
            </div>
        </div>
    </div>
</div>


