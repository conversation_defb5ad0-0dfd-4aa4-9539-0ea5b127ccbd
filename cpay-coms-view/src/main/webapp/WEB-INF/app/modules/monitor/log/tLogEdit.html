
<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class="control-group">
					<label class="control-label">{{tlogInfo.task_name}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="jobName" ng-model="job.jobName"
							class="span6" ng-disabled="disabled"
							validator="required,maxlength=32" message-id="jobName"
							maxlength="32" maxLength-error-message="The job name does not exceed 32 characters" /> <span
							id="jobName" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.type}}<span class="required">*</span></label>
					<div class="controls">
						<select ng-options="m.type as m.name for m in fileTypeList"
							ng-model="job.type" class="span6 " name="type"
							ng-disabled="disabled" validator="required" message-id="type">
							<option value="">{{tlogInfo.type}}</option>
						</select> <span id="type" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group" ng-show="job.type ==01">
					<label class="control-label">{{tlogInfo.app_code}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="appCode" ng-model="job.appCode"
							class="span6 " ng-disabled="disabled" message-id="appCode"
							validator="maxlength=100" maxLength-error-message="The name of the application package shall not exceed 100" />
						<button class="button" type="button" ng-click="openLogSet()"
							ng-disabled="disabled">
							<i class="icon-large"></i>{{tlogInfo.select}}
						</button>
						<span id="appCode" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group" ng-show="job.type ==02">
					<label class="control-label">{{tlogInfo.path}}<span class="required">*</span></label>
					<div class="controls">
						<input type="text" name="fileUrl" ng-model="job.fileUrl"
							class="span6 " ng-disabled="disabled" message-id="fileUrl"
							validator="maxlength=200" maxLength-error-message="The file path does not exceed 200 characters" />
						<button class="button" type="button" ng-click="openLogSet()"
							ng-disabled="disabled">
							<i class="icon-large"></i>{{tlogInfo.select}}
						</button>
						<span id="fileUrl" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.manufacturer}}<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="factoryList"
							ng-options="m.id as m.name for m in factoryList"
							ng-model="job.manufacturerId" validator="required"
							ng-disabled="disabled" message-id="factoryList"
							ng-change="changeFactory(job.manufacturerId)">
							<option value="">{{tlogInfo.manufacturer}}</option>
						</select> <span id="factoryList" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.mode}}<span class="required">*</span></label>
					<div class="controls">
						<select class="form-control span6" name="termTypeList"
							ng-model="job.terminalTypeId" ng-change="collectionInit()"
							ng-options="m.id as m.name  for m in termTypeList"
							validator="required" ng-disabled="disabled"
							message-id="termTypeList">
							<option value="">{{tlogInfo.select_please}}</option>
						</select> <span id="termTypeList" class="help-inline"> </span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.start_time}}<span class="required">*</span></label>
					<div class="controls">
						<div class="input-append date" id="datetimepicker1">
							<input size="16" type="text" value="" readonly
								ng-disabled="disabled" date-picker format="yyyy-MM-dd HH:mm:ss"
								valid-method="watch" ng-model="job.releaseTime"
								name="releaseTime" validator="required" message-id="releaseTime" />
						</div>
						<span id="releaseTime" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.end_time}} <span class="required">*</span></label>
					<div class="controls">
						<div class="input-append date form_datetime" id="datetimepicker2">
							<input size="16" type="text" value="" readonly date-picker
								format="yyyy-MM-dd HH:mm:ss" valid-method="watch"
								ng-model="job.validDate" name="validDate" ng-disabled="disabled"
								validator="required" message-id="validDate" /> <span
								class="add-on"><i class="icon-remove"></i></span> <span
								class="add-on"><i class="icon-calendar"></i></span>
						</div>
						<span id="validDate" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group" ng-show="job.type == 02 || job.type == 01">
					<label class="control-label">{{tlogInfo.log_start_time}}<span class="required">*</span></label>
					<div class="controls">
						<div class="input-append date form_date" id="datetimepicker3">
							<input size="16" type="text" value="" readonly
								ng-disabled="disabled" date-picker format="yyyy-MM-dd"
								valid-method="watch" ng-model="job.getBeginDate"
								name="getBeginDate" message-id="getBeginDate" />
						</div>
						<span id="getBeginDate" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group" ng-show="job.type == 02 || job.type == 01">
					<label class="control-label">{{tlogInfo.log_end_time}}<span class="required">*</span></label>
					<div class="controls">
						<div class="input-append date form_date" id="datetimepicker4">
							<input size="16" type="text" value="" readonly date-picker
								format="yyyy-MM-dd" valid-method="watch"
								ng-model="job.getEndDate" name="getEndDate"
								ng-disabled="disabled" message-id="getEndDate" />
						</div>
						<span id="getEndDate" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group" ng-show="!disabled">
					<div class="controls">
						<button class="btn  blue" type="button" ng-click="showTermSeq()">
							<i class="icon-plus"></i>{{tlogInfo.add_terminal}}
						</button>
						<button class="btn  blue" type="button"
							ng-click="openSelectExcel()">
							<i class="icon-upload"></i>{{tlogInfo.EXCEL_import}}
						</button>
						<button class="btn  blue" type="button" ng-click="deleteTerm()">
							<i class="icon-remove"></i>{{tlogInfo.delete}}
						</button>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{tlogInfo.terminal_list}}<span class="required">*</span></label>
					<div class="controls">
						<select class="span6" id="tout_acct_list" name="tout_acct_list"
							multiple="multiple">
						</select>
					</div>
				</div>
				<div class="control-group" ng-show="errorLength != 0">
					<label class="control-label">{{tlogInfo.import_fial_list}}<span class="required"></span></label>
					<div class="controls">
						<select class="span6" id="tout_error_list" name="tout_error_list"
							multiple="multiple">
						</select>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<div ng-show="!disabled" form-foot
	goback="$state.go('home.monitorlog.list')" submit="form.submit(Form)"
	reset="form.reset(Form)"></div>