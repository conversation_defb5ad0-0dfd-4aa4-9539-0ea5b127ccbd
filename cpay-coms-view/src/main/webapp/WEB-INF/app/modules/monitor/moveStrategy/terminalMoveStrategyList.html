<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <div class="input-append input-group"  ng-click="openOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="所属机构">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
                <button type="button" class="btn" ng-click="reset()">重置</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>查询</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="button" type="button" ng-click="$state.go('home.terminalMoveStrategy.add')" has-permission="terminalMoveStrategy_add">
                    <i class="icon-plus"></i>新增
                </button>
                
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
