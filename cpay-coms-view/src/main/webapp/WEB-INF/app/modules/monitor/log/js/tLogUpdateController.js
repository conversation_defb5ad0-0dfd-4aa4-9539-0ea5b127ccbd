'use strict';
define(["modules/terminal/factory/js/factoryService"], function () {
    return function ($scope,baseUrl,Upload, tLogService, BaseApi, $state,toolsService, 
    		dialogService,factoryService, $stateParams,formService,gridService,$http) {
    	$http.get("modules/monitor/log/i18n/en/tlogList.json").success(function(dataLan) {
        $scope.tlogInfo = dataLan.tlogInfo;
        
    	$scope.$parent.thirdTitle = $scope.tlogInfo.modify_task;
        $scope.readOnly = true;
        var parent = $scope;
        $scope.errorLength = 0;
        BaseApi.get("/terminalType", {page:1,rows:20}, function (data) {
	       $scope.termTypeList = data.rows;
	   	});
	   	BaseApi.get("/factory",{page:1,rows:20}, function (data) {
	   		$scope.factoryList = data.rows;
	   	});
	   	BaseApi.query("/dict/type/log_getType",{}, function (data) {
 		 	$scope.fileTypeList = data;
 	    });
        $scope.changeFactory = function (factoryId) {
            if(factoryId!=null&&factoryId!=""){
                var param  = {"factoryId":factoryId};
                BaseApi.query("/terminalType/activateType/"+factoryId,{}, function (data) {
                    $scope.termTypeList = data;
                });
            }
            $scope.collectionInit();
        };
        // 重置终端集合
        $scope.collectionInit = function(){
            $("#tout_acct_list option").remove();
            $("#tout_error_list option").remove();
            $scope.errorLength = 0;
        };
        tLogService.getJob($stateParams.id,function(data){
            $scope.job = data;
            if($scope.job.type == 2){
            	$scope.job.fileUrl = $scope.job.appCode;
            }
            $scope.job.releaseTime=toolsService.getFullDate(data.releaseTime);
        	$scope.job.validDate=toolsService.getFullDate(data.validDate);
        	$scope.job.getBeginDate=toolsService.getDayDate(data.getBeginDate);
        	$scope.job.getEndDate=toolsService.getDayDate(data.getEndDate);
            if(data.collection){
	            var collections = data.collection.split(",");
	            var selectObj = angular.element("#tout_acct_list");
	            for(var i = 0;i < collections.length;i++) {
	        		selectObj.append("<option value='"+collections[i]+"'>"
	                    		+collections[i]+"</option>");
	        	}
        	}
        });
        $scope.openLogSet = function(){
            dialogService.openDialog({
                template: "modules/monitor/log/showLogSetInfo.html",
                width: '60%',
                controller: function ($scope,dialogService,$timeout) {
             	   $timeout(function(){
             		  gridService.initTable({
             				url : "/logSet",
             				scope : $scope,
             				operator:false,
             				singleSelect:true,
             				columns : [ {
             					field : 'state',
             					checkbox : 'true'
             				}, {
             					field : 'appName',
             					title : 'application name'
             				}, {
             					field : 'appCode',
             					title : 'packages name'
             				}, {
             					field : 'logUrl',
             					title : 'Log storage path'
             				} ]
             			});
             	    },100)
             	   $scope.ok = function () {
              		    var rows= $("#table").bootstrapTable("getSelections");
              		     if (rows.length == 0) {
                            dialogService.alertInfo("info", "Please select record！");
                            return;
                        }else{
                        	if(parent.job.type=='01'){
                        		parent.job.appCode=rows[0].appCode;
                        	}else if(parent.job.type=='02'){
                        		parent.job.fileUrl=rows[0].logUrl;
                        	}
                        }
                       	  $scope.closeThisDialog(0);
                     };
                }
            });
        }
        $scope.form = formService.form(function () {
        	if($scope.job.releaseTime > $scope.job.validDate
            		|| $scope.job.getBeginDate > $scope.job.getEndDate) {
            		dialogService.alertInfo("info", "The start date shall not exceed the end date！");
            		return;
        	}
        	var collections ="";
        	var selectObj = angular.element("#tout_acct_list").get(0);
        	var options = selectObj.options;
        	if(options.length == 0) {
        		dialogService.alertInfo("info", "The terminal set is not empty！");
                return;
        	}
        	for(var i = 0;i < options.length;i++) {
        		collections += options[i].text+",";
        	}
        	if($scope.job.type == '02'){
        		$scope.job.appCode=$scope.job.fileUrl;
            } else if($scope.job.type == "03") {
                $scope.job.appCode = 'null';
            }
        	$scope.job.collection = collections.substring(0,collections.length-1);
            tLogService.updateJob($scope.job, function (data) {
                dialogService.alertInfo("success", data.msg);
                $state.go("home.monitorlog.list");
            });
        });

        $scope.addTerm = function () {
            dialogService.openDialog({
                template: "modules/monitor/log/tLogSubTermEdit.html",
                controller: function ($scope) {
                    $scope.dialogTitle = "Terminal serial number added";
                    $scope.readOnly = false;
                    $scope.appendTerm = function () {
                    	if(!$scope.addSuTerm) {
                    		dialogService.alertInfo("info", "Terminal serial number is not empty！");
                    		return;
                    	}
                    	angular.element("#tout_acct_list").append("<option value='"+$scope.addSuTerm.termSeqId+"'>"
                    		+$scope.addSuTerm.termSeqId+"</option>");
                    	$scope.closeThisDialog(0);
                    };
                },
                closeByDocument: false
            });
        };
        $scope.showTermSeq = function(){
        	var manufacturerId =$scope.job.manufacturerId;
        	var terminalTypeId = $scope.job.terminalTypeId;
        	if(manufacturerId == null || manufacturerId == '' || manufacturerId == undefined){
        		dialogService.alertInfo("info", "Please select the terminal manufacturer first！");
                return;
        	}
        	if(terminalTypeId == null || terminalTypeId == '' || terminalTypeId == undefined){
        		dialogService.alertInfo("info", "Please select the terminal model！");
                return;
        	}
            dialogService.openDialog({
                template: "modules/monitor/log/showTermSeqList.html",
                width: '60%',
                controller: function ($scope,dialogService,$timeout) {
             	   $scope.app={};
             	   $timeout(function(){
             		   gridService.initTable({
             			    url:"/terminal?termMfrId="+manufacturerId+"&terminalTypeId="+terminalTypeId,
                            scope: $scope,
                            operator:false,
                            uniqueId: 'id',
							    columns: [
                              {field: 'state',checkbox: 'true'},
                              {field: "termSeq", title: "Serial No."},
                              {field: "insName", title: "Organization"},
                              {field: "termMfrName", title: "Manufacturer"},
                              {field: "termTypeName", title: "Model"}
                              ]
                        });
             	    },100)
             	    $scope.serachTermInfo = function () {
             		   gridService.search($scope.condition);
                    };
                    $scope.resetTermInfo = function () {
              		   $scope.condition={};
              		   gridService.search();
                    };
                    $scope.openOrganization = function () {
                        organizationService.openOrganization(function (value) {
                            if (!$scope.condition) {
                                $scope.condition = {};
                            }
                            $scope.condition.insId = value.id;
                            $scope.condition.insName = value.name;
                        });
                    };
            	    $scope.ok = function () {
           		    var rows= $("#table").bootstrapTable("getSelections");
           		     if (rows.length == 0) {
                         dialogService.alertInfo("info", "Please select record！");
                         return;
                     }
           		     var ids = "";
                    	 if(angular.element("#tout_acct_list option").size()==0){
                    		 for (var index in rows) {
                    		 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                             		+rows[index].termSeq +"</option>");
                    		 }
                           
                    	 }else{
                    		 for (var index in rows) {
                    		 var j =0;
                    		 angular.element("#tout_acct_list option").each(function () {
                    			 if(rows[index].termSeq==$(this).val()){
                    				 return false;
                    			 }
                    			 j++;
                    	       });
	                    		 if(j==angular.element("#tout_acct_list option").size()){
	                    			 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
	                                  		+rows[index].termSeq +"</option>");
	                    		 }
                    		 }
                    	 }
                    	  
                    	  $scope.closeThisDialog(0);
                    
                  };
                }
            });
        };
        $scope.openSelectExcel = function () {
            var manufacturerId = $scope.job.manufacturerId;
            if (manufacturerId == null || manufacturerId == '' || manufacturerId == undefined) {
                dialogService.alertInfo("info", "Please select the terminal manufacturer first！");
                return;
            }
            var terminalTypeId = $scope.job.terminalTypeId;
            if (terminalTypeId == null || terminalTypeId == '' || terminalTypeId == undefined) {
                dialogService.alertInfo("info", "Please select the terminal model！");
                return;
            }
            for(var i =0,len = $scope.termTypeList.length;i<len;i++){
                if($scope.termTypeList[i].id == terminalTypeId){
                    var termTypeCodes = $scope.termTypeList[i].code;
                    break;
                }
            }
            var params = "?termMfrId=" + manufacturerId + "&termTypeCodes=" + termTypeCodes;
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/driverJobExcel.html",
                controller: function ($scope) {
                    $scope.dialogTitle = "Terminal serial number import";
                    $scope.readOnly = false;
                    $scope.submitFile = function () {
                        if (!$scope.file || !$scope.file.size) {
                            dialogService.alertInfo("success", "Please select file");
                            return;
                        }
                        var name = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
                        if (!(name == "xls" || name == "xlsx")) {
                            dialogService.alertInfo("info", "Please select Excel file");
                            return;
                        }
                        Upload.upload({
                            url: baseUrl + '/uploadjob/importExcel'+ params,
                            data: {file: $scope.file},
                            method: 'POST'
                        }).then(function (resp) {
                            var collections = resp.data.data[0];

                            for (var i = 0,len = collections.length;i<len;i++) {

                                var j = 0;
                                angular.element("#tout_acct_list option").each(function () {
                                    if (collections[i] == $(this).val()) {
                                        return false;
                                    }
                                    j++;
                                });
                                if (j == angular.element("#tout_acct_list option").size()) {
                                    angular.element("#tout_acct_list").append("<option value='" + collections[i] + "'>"
                                        + collections[i] + "</option>");
                                }
                            }
                            var errorCollections = resp.data.data[1];
                            if(errorCollections.length != 0){
                                parent.errorLength = 1;
                            }
                            for (var i = 0,len = errorCollections.length;i<len;i++) {

                                var j = 0;
                                angular.element("#tout_error_list option").each(function () {
                                    if (errorCollections[i] == $(this).val()) {
                                        return false;
                                    }
                                    j++;
                                });
                                if (j == angular.element("#tout_error_list option").size()) {
                                    angular.element("#tout_error_list").append("<option value='" + errorCollections[i] + "'>"
                                        + errorCollections[i] + "</option>");
                                }
                            }

                            dialogService.alertInfo("success", "File uploaded successfully");
                        }, function (resp) {
                            dialogService.alertInfo("warning", "File upload failed");
                        }, function (evt) {
                        });
                        $scope.closeThisDialog(0);
                    };
                    $scope.downModel = function () {
                        var downloadUrl = baseUrl + '/uploadjob/fileDownload';
                        window.open(downloadUrl, '_blank');
                    };
                },
                closeByDocument: false
            });
        };
        $scope.deleteTerm = function () {
        	var oSelect = angular.element("#tout_acct_list");
        	var options = oSelect.option;	
			$("#tout_acct_list option:selected").remove();
        }
    	});
    };
});