'use strict';
define(["modules/terminal/factory/js/factoryService","organizationService"], function () {
	return function ($scope, dynService,factoryService,organizationService, $state, dialogService, gridService,anguloaderService,BaseApi,$http) {
		$http.get("modules/monitor/dyn/i18n/en/dyn.json").success(function(data) {
			$scope.html = data.html;
			$scope.controller = data.controller;
			$scope.$parent.secondTitle = data.title.secondTitle;
			$scope.$parent.firstTitle = data.title.firstTitle;
			$scope.$parent.thirdTitle = "";

			dynService.statistics($scope.condition,function(data){
				$scope.statisticsData = data.data;
			});

			gridService.initTable({
				url: "/dynInf",
				scope: $scope,
				operator:false,
				singleSelect:true,
				columns: [
					{
						field: "termSeq",
						title: $scope.controller.termSeqId
					},
					{
						title: $scope.controller.insName,
						field:"insName"
					},
					{
						title: $scope.controller.group,
						field:"groupName"
					},
					{
						field: "activateStatus",
						title: $scope.controller.activateStatus,
						formatter:function(value){
							if(value == 1){
								value = "Activated";
							}else if(value == 0){
								value = "Non-activated";
							}else if(value == null){
								value = "Non-activated";
							}
							return value;
						}
					},{
						field: "onlineStatus",
						title: $scope.controller.online,
						formatter:function(value){
							if(value == '在线'){
								value = '<img src="app/images/default/greenLight.png"></img>'+"On-line";
							}else if(value == '离线'){
								value = '<img src="app/images/default/redLight.png"></img>'+"Off-line";
							}else if(value == null){
								value = '<img src="app/images/default/redLight.png"></img>'+"Off-line";
							}
							return value;
						}
					},{
						field: "hardwareStatBmp",
						title: "Hardware Status",
						formatter:function(value){
							if(value == '0000000000000000'){
								value = "normal";
							}else if(value == ''){
								value = "normal";
							}else if(value == null){
								value = "normal";
							}else{
								value = "abnormal";
							}
							return value;
						}
					}]
					,
		            operate: function (value, row, index) {
		            	return '<a  href="javascript:;" ng-click="detail(' + index + ')"  title="Detail" >Detail</a>';
		            },
		            operatorLen:150
			});
			$scope.openOrganization = function () {
				organizationService.openOrganization(function (value) {
					if (!$scope.condition) {
						$scope.condition = {};
					}
					$scope.condition.insId = value.id;
					$scope.condition.insName = value.name;
					$scope.condition.terminalGroupId = 0;
					BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
						$scope.terminalGroupList = data;
					});
				});
			};
			$scope.detail = function (index) {
	            var row = gridService.getRow(index);
	            $state.go('home.monitordyn.view', {id: row.termSeq});
	        };
			$scope.search = function () {
				gridService.search($scope.condition);
				dynService.statistics($scope.condition,function(data){
					$scope.statisticsData = data.data;
				});
			};
			$scope.reset = function () {
				$scope.condition = {};
				$scope.terminalGroupList = [];
				$scope.search();
			};
			gridService.setPlaceholder();
		});
	};
});