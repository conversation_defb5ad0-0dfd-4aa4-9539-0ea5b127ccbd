'use strict';
define(["modules/terminal/factory/js/factoryService"], function () {
    return function ($scope, $http, baseUrl, tLogService, BaseApi, toolsService, factoryService, $state, dialogService, gridService, $stateParams) {
        $scope.$parent.thirdTitle = "Executive condition";
        BaseApi.query("/dict/type/task_type", {}, function (data) {
            $scope.taskTypeList = data;
            gridService.initTable({
                url: "/uploadtask?jobId=" + $stateParams.id,
                scope: $scope,
                operator: false,
                columns: [
                    {
                        field: 'state',
                        checkbox: 'true'
                    }, {
                        field: "termSeq",
                        title: "Serial No."
                    }, 
                    {
                        field: "uploadFlag",
                        title: "Status",
                        formatter: function (value) {
                            var type = '';
                            $.each(data, function (n, obj) {
                                if (obj.type == value) {
                                    type = obj.name;
                                }
                            });
                            return type;
                        }
                    }, {
                        field: "getBeginDate",
                        title: "Extract Start Date",
                        formatter: function (value) {
                            return toolsService.getFullDate(value);
                        }
                    }, {
                        field: "getEndDate",
                        title: "Extract End Date",
                        formatter: function (value) {
                            return toolsService.getFullDate(value);
                        }
                    },
                    {
                        field: "releaseTime",
                        title: "Validity Start Date",
                        formatter: function (value) {
                            return toolsService.getFullDate(value);
                        }
                    }, {
                        field: "validDate",
                        title: "Validity End Date ",
                        formatter: function (value) {
                            return toolsService.getFullDate(value);
                        }
                    }]
            });
        });
        $scope.reIssued = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select Serial No.！");
                return;
            }
            for (var i in rows) {
                if (rows[i].uploadFlag == 0) {
                    dialogService.alertInfo("info", "You may not select a task that has been successfully uploaded for cancellation！");
                    return false;
                }
                if (rows[i].uploadFlag == 3) {
                    dialogService.alertInfo("info", "Do not select a successful task for cancellation！");
                    return false;
                }
            }
            dialogService.openConfirm("Make sure you redistribute the selected task？", function () {
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                var param = ids.substr(0, ids.length - 1);
                tLogService.reIssudJob(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Resend success！");
                    gridService.refresh();
                });
            });
        };
        $scope.cancelIssued = function () {
            var rows = gridService.getSelectedRow();
            if (rows.length == 0) {
                dialogService.alertInfo("info", "Please select the issuing terminal！");
                return;
            }
            dialogService.openConfirm("Make sure you want to undistribute the selected job？", function () {
                var ids = "";
                for (var index in rows) {
                    ids = ids + rows[index].id + ",";
                }
                for (var i in rows) {
                    if (rows[i].uploadFlag == 0) {
                        dialogService.alertInfo("info", "You may not select a task that has been successfully uploaded for cancellation！");
                        return false;
                    }
                    if (rows[i].uploadFlag == 3) {
                        dialogService.alertInfo("info", "Do not select a successful task for cancellation！");
                        return false;
                    }
                }
                var param = ids.substr(0, ids.length - 1);
                tLogService.cancelIssudJob(param, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", "Cancel distribution successful！");
                    gridService.refresh();
                });
            });
        };


        $scope.downloadLog = function (index) {
            var rows = gridService.getSelectedRow();
            if (rows.length != 1) {
                dialogService.alertInfo("info", "Please select a task！");
                return;
            }
            var row = rows[0];
            if (row.uploadFlag != 0) {
                dialogService.alertInfo("info", "The file has not been uploaded successfully！");
                return;
            }
            // if (!row.logName){
            //     dialogService.alertInfo("info", "文件名不存在，无法下载！");
            //     return;
            // }
            BaseApi.get("/uploadtask/downloadlog?termSeq="+row.termSeq+"&taskId="+row.id,{},function (res) {
                var downloadUrl = res.data;
                window.open(downloadUrl, '_blank');
            })

        };
        $scope.search = function () {
            $scope.condition = $scope.task;
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.task = {};
            gridService.search();
        };
        $scope.refreshStatus = function () {
            $scope.task = {};
            gridService.search();
        };
    };
});