'use strict';
define(["modules/monitor/pmap/js/positionService"], function () {
    return function ($scope,positionService, $state,dialogService,$timeout, $stateParams) {
    	$scope.$parent.thirdTitle = "终端轨迹";
    	positionService.getDetailPositionList($stateParams.id,function (data) {
			// 地图操作
			if (data.data.length > 0) {
				dialogService.alertInfo("success", data.msg);
				positionService.operBMap(data.data);
			}else {
				dialogService.alertInfo("info", "无匹配数据");
				angular.element("#container").empty();
			}
        });
    };
});