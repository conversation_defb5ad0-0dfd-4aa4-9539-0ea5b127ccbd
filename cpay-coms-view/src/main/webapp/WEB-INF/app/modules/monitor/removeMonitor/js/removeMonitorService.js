'use strict';
define([ 'js/app' ], function(app) {
	app.factory("removeMonitorService", function(BaseApi) {
		var orderedPackageData = {};
		var map,tool;
		return {
			addOrderedWarr : function(data, success) {
				data.orderDate = new Date(data.orderDate);
				BaseApi.post("/orderedWarr/reAddWarr", data, success);
			},
			deleteOrderedWarr : function(ids, success) {
				BaseApi["delete"]("/orderedWarr?ids=" + ids, success);
			},
			getMakerByLogLat:function(map,lnglatXY,judge,ima,data){
                var geocoder = new AMap.Geocoder({
                    radius: 1000,
                    extensions: "all",
                    level:11
                });        
                geocoder.getAddress(lnglatXY, function(status, result) {
                    if (status === 'complete' && result.info === 'OK') {
                    	  var address = result.regeocode.formattedAddress; //返回地址描述
      					  var provinceCheck = result.regeocode.addressComponent.province;
      					  var cityCheck = result.regeocode.addressComponent.city;
      					  var countyCheck = result.regeocode.addressComponent.district;
                    	  var marker = new AMap.Marker({  //加点
                              map: map,
                              icon: new AMap.Icon({
                                  size:new AMap.Size(37,45),//图标大小  
                                  image:ima
                              }),
                              offset: new AMap.Pixel(-13,-38),
                              position: lnglatXY,
                              draggable: judge,
                              cursor: 'move',
                              raiseOnDrag: true
                          });
                          var infoWindow = new AMap.InfoWindow({
                              content: address,
                              offset: {x: 0, y: -30}
                          });
                          marker.on("mouseover", function(e) {
                              infoWindow.open(map, marker.getPosition());
                          });
                          marker.on("mouseout", function(e) {
                              infoWindow.close	(map, marker.getPosition());
                          });
                    
                          new AMap.event.addListener(marker, 'dragend', function(e){
                              	  var geocoder = new AMap.Geocoder({
                                        radius: 1000,
                                        extensions: "all"
                                    });   
                              	  var lat = e.lnglat.lat;
                                  var lng = e.lnglat.lng;
                                  lnglatXY =[lng,lat];
                              	  geocoder.getAddress(lnglatXY, function(status, result) {
                                        if (status === 'complete' && result.info === 'OK') {
                                        		  address = result.regeocode.formattedAddress; //返回地址描述
                                      	  		  provinceCheck = result.regeocode.addressComponent.province;
                                      	  		  cityCheck = result.regeocode.addressComponent.city;
                                      	  		  countyCheck = result.regeocode.addressComponent.district;
                                            	  var lat = e.lnglat.lat;
                                                  var lng = e.lnglat.lng;
                                                  marker.setPosition(new AMap.LngLat(lng,lat));
                                                  infoWindow.close();
                                                   infoWindow = new AMap.InfoWindow({
                                                      content: address,
                                                      offset: {x: 0, y: -30}
                                                  });
                                                  marker.on("mouseover", function(e) {
                                                      infoWindow.open(map, marker.getPosition());
                                                  });
                                                  marker.on("mouseout", function(e) {
                                                      infoWindow.close	(map, marker.getPosition());
                                                  });
                                        }
                                    })
                        	 
                            });
                    }
                }); 
        },
			operBMap : function(data) {
				var lnglatXY = [ data.longitude, data.latitude ];
				var obj = this;
				map = new AMap.Map('container', {
			        resizeEnable: true,
			        zoom: 13,
			        center: lnglatXY
			    });
				var markers = [ {
					icon : 'images/pic_map_equipment.png',
					position : lnglatXY
				} ];
				markers.forEach(function(marker) {
					new AMap.Marker({
						map : map,
						icon : marker.icon,
						position : [ marker.position[0], marker.position[1] ],
						offset : new AMap.Pixel(-12, -36)
					});
				});
				map.plugin(["AMap.ToolBar"],function(){
              	    //加载工具条
              	    tool = new AMap.ToolBar({
              	    //初始化定义配置
              	       direction:true,//隐藏方向导航
              	       ruler:true//隐藏视野级别控制尺
              	    });
              	    map.addControl(tool);
               });
               obj.getMakerByLogLat(map,lnglatXY,false,'images/pic_map_position.png',data);
			},
		}
	});
});