<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <input type="hidden" name="jobId" ng-model="task.jobId">
                <input type="text" class="input-large top-inp" placeholder="Serial No." ng-model="task.termSeq">
                <select ng-model="task.uploadFlag" ng-options="m.type as m.name for m in taskTypeList">
                    <option value="">Upload status</option>
                </select>
                <button type="button" class="btn" ng-click="reset()">Reset</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>Query</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
                <button class="btn-default"  style="width: 120px;" type="button" ng-click="reIssued()" has-permission="uploadjob_reIssued">
                    <i class="icon-ok"></i>Reset Task
                </button>
                <button class="btn-default"   style="width: 120px;" type="button" ng-click="cancelIssued()" has-permission="uploadjob_cancelIssued">
                    <i class="icon-remove"></i>Cancel Task
                </button>
                <button class="btn-default"  style="width: 120px;" type="button" ng-click="downloadLog()" has-permission="uploadjob_downloadLog">
                    <i class="icon-download-alt"></i>Download
                </button>
                <button class="btn-special" style="width:120px;" type="button" ng-click="refreshStatus()">
                    <i class="icon-refresh"></i>Refresh
                </button>
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>