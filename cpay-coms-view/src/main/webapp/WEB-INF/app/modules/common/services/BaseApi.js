'use strict';
define(['js/app', "dialogService", "formService", "formDirective", "myFilter",
    "gridService", "commonService","securityService"], function (app) {
    app.factory("BaseApi", function ($resource, dialogService, baseUrl, securityService) {
        var successCallback = function (data, success) {
            if(data.status == 400){
                alert(data.msg);
                window.location= "login";
                return null;
            }
            if (data.status == 500 || data.status == 300) {
                dialogService.alertInfo("error", data.msg);
            } else {
                if (typeof success == "function") {
                    success(data);
                } else {
                    dialogService.alertInfo("error", "Passing parameters incorrectly!");
                }
            }
        };
        var errorCallback = function (error) {
            if (error && error.statusText) {
                if (error.status.toString().indexOf("40") != -1) {
                    dialogService.alertInfo("error", "Request error!");
                } else if (error.status.toString().indexOf("50") != -1) {
                    dialogService.alertInfo("error", "The server is abnormal!");
                } else {
                    dialogService.alertInfo("error", JSON.stringify(error.statusText));
                }
            } else {
                console.log(error);
                dialogService.alertInfo("error", "Request timed out, please try again！");
            }
        };
        var tout = 10000;//默认超时时间
        return {
            get: function (url, param, success,timeout) {
                if(!timeout){
                    timeout = tout;
                }
                var resource = $resource(baseUrl + url, param, {
                    get:{
                        method: "get",
                        timeout: timeout,
                        headers: securityService.getHeader()
                    }
                });
                resource.get(function (data) {
                    successCallback(data, success);
                }, errorCallback);
            },
            query: function (url, param, success,timeout) {
                if(!timeout){
                    timeout = tout;
                }
                var resource = $resource(baseUrl + url, param, {
                    query: {
                        method: "get",
                        isArray: true,
                        timeout:timeout,
                        headers: securityService.getHeader()
                    }
                });
                resource.query(function (data) {
                    successCallback(data, success);
                }, errorCallback);
            },
            'delete': function (url, success,timeout) {
                if(!timeout){
                    timeout = tout;
                }
                var resource = $resource(baseUrl + url, {}, {
                    "delete": {
                        method: "delete",
                        timeout:timeout,
                        headers: securityService.getHeader()
                    }
                });
                resource['delete'](success, errorCallback);
            },
            post: function (url, param, success,timeout) {
                if(!timeout){
                    timeout = tout;
                }
                var resource = $resource(baseUrl + url, {}, {
                    post: {
                        method: "post",
                        headers: securityService.getHeader(),
                        timeout: timeout
                    }
                });
                resource.post({}, param, function (data) {
                    successCallback(data, success);
                }, errorCallback);
            },
            patch: function (url, data, success,timeout) {
                if(!timeout){
                    timeout = tout;
                }
                var resource = $resource(baseUrl + url, null, {
                    'update': {
                        method: 'patch',
                        timeout: timeout,
                        headers: securityService.getHeader()
                    }
                });
                resource.update(baseUrl + url, data, function (data) {
                    successCallback(data, success);
                }, errorCallback);
            },
            getUser: function () {
               return securityService.getStorage()["user"];
            },
            /*新建cookie。  days为空时,cookie的生存期至浏览器会话结束。days为数字0时,
            建立的是一个失效的cookie,这个cookie会覆盖已经建立过的同名、同path的cookie（如果这个cookie存在）。  */
            setCookie:function (name,value,days){  
              var name = escape(name);  
              var value = escape(value);
              var _expires = "";
              if(days && !isNaN(days)){
              	var expires = new Date();  
                  expires.setTime(expires.getTime() + days * (24 * 60 * 60 * 1000));
                  _expires = ";expires=" + expires.toUTCString(); 
              }
               //path = path == null ? "" : ";path=" + path;   
               document.cookie = name + "=" + value + _expires;  
            },
            //获取cookie值  
            getCookieValue: function (name){  
              var name = escape(name);  
              //读cookie属性，这将返回文档的所有cookie  
              var allcookies = document.cookie;      
              //查找名为name的cookie的开始位置  
               name += "=";  
              var pos = allcookies.indexOf(name);    
              //如果找到了具有该名字的cookie，那么提取并使用它的值  
              if (pos != -1){                       //如果pos值为-1则说明搜索"version="失败  
                var start = pos + name.length;         //cookie值开始的位置  
                var end = allcookies.indexOf(";",start);    //从cookie值开始的位置起搜索第一个";"的位置,即cookie值结尾的位置  
                if (end == -1) end = allcookies.length;    //如果end值为-1说明cookie列表里只有一个cookie  
                var value = allcookies.substring(start,end); //提取cookie的值  
                return unescape(value);              //对它解码     
               }else{
              	 return "";                //搜索失败，返回空字符串 
               }   
            }
        }
    });
});
