define([], function () {
    return {
    	"home.unicomCard":{
    		url:"/unicomCard",
    		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
            controllerUrl: "modules/unicomCard/unicomCard/js/unicomCardController.js"
    		
    	},
    	"home.unicomCard.list": {
             url: "/list",
             cache:false,templateUrl: "modules/unicomCard/unicomCard/unicomCardList.html",
             controllerUrl: "modules/unicomCard/unicomCard/js/unicomCardListController.js"
         },
         "home.unicomCard.add": {
             url: "/add",
             cache:false,templateUrl: "modules/unicomCard/unicomCard/unicomCardUpdate.html",
             controllerUrl: "modules/unicomCard/unicomCard/js/unicomCardAddController.js"
         },
         "home.unicomCard.update": {
        	 url: "/:id",
             cache:false,templateUrl: "modules/unicomCard/unicomCard/unicomCardUpdate.html",
             controllerUrl: "modules/unicomCard/unicomCard/js/unicomCardUpdateController.js"
         },
         "home.unicomCard.view": {
        	 url: "/view/:id",
             cache:false,templateUrl: "modules/unicomCard/unicomCard/unicomCardView.html",
             controllerUrl: "modules/unicomCard/unicomCard/js/unicomCardViewController.js"
         },
    }
});