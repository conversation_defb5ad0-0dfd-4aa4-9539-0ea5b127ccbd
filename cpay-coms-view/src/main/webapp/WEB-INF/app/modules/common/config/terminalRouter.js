define(
		[],
		function() {
			return {
				"home.terminal" : {
					url : "/terminal",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminal/js/terminalController.js"
				},
				"home.terminal.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalList.html",
					controllerUrl : "modules/terminal/terminal/js/terminalListController.js"
				},
				"home.terminal.adQuery" : {
					url : "/adQuery",
					cache : false,
					templateUrl : "modules/terminal/terminalAdvQuery/terminalAdvQueryList.html",
					controllerUrl : "modules/terminal/terminalAdvQuery/js/terminalAdvQueryListController.js"
				},
				"home.terminal.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalUpdate.html",
					controllerUrl : "modules/terminal/terminal/js/terminalUpdateController.js"
				},
				"home.termview" : {
					url : "/terminal/view/:id",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalView.html",
					controllerUrl : "modules/terminal/terminal/js/terminalViewController.js"
				},
				"home.termview.info" : {
					url : "/info",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalInfo.html",
					controllerUrl : "modules/terminal/terminal/js/terminalInfoController.js"
				},
				"home.termview.driverVersion" : {
					url : "/driverVersion",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalDriver.html",
					controllerUrl : "modules/terminal/terminal/js/terminalDriverController.js"
				},
				"home.termview.sysVersion" : {
					url : "/sysVersion",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalSystem.html",
					controllerUrl : "modules/terminal/terminal/js/terminalSystemController.js"
				},
				"home.termview.appView" : {
					url : "/apkInfo",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalApk.html",
					controllerUrl : "modules/terminal/terminal/js/terminalApkInfoController.js"
				},
				"home.termview.basicApp" : {
					url : "/basicApp",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalBasicApp.html",
					controllerUrl : "modules/terminal/terminal/js/terminalBasicAppController.js"
				},
                "home.termview.termFlowCountDay" : {
                    url : "/termFlow",
                    cache : false,
                    templateUrl : "modules/terminal/terminal/terminalFlowCurMonth.html",
                    controllerUrl : "modules/terminal/terminal/js/terminalFlowController.js"
                },
                "home.termview.switchrecord" : {
                    url : "/switchrecord",
                    cache : false,
                    templateUrl : "modules/terminal/terminal/switchrecord.html",
                    controllerUrl : "modules/terminal/terminal/js/switchrecordController.js"
                },
				"home.terminal.upload" : {
					url : "",
					cache : false,
					templateUrl : "modules/terminal/terminal/terminalUpload.html",
					controllerUrl : "modules/terminal/terminal/js/terminalUploadController.js"
				},
				"home.factory" : {
					url : "/factory",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/factory/js/factoryController.js"
				},
				"home.factory.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/factory/factoryList.html",
					controllerUrl : "modules/terminal/factory/js/factoryListController.js"
				},
				"home.factory.add" : {
					url : "/add",
					cache : false,
					templateUrl : "modules/terminal/factory/factoryUpdate.html",
					controllerUrl : "modules/terminal/factory/js/factoryAddController.js"
				},
				"home.factory.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/factory/factoryUpdate.html",
					controllerUrl : "modules/terminal/factory/js/factoryUpdateController.js"
				},
				"home.factory.view" : {
					url : "/view/:id",
					cache : false,
					templateUrl : "modules/terminal/factory/factoryView.html",
					controllerUrl : "modules/terminal/factory/js/factoryViewController.js"
				},
				"home.terminalType" : {
					url : "/terminalType",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalType/js/terminalTypeController.js"

				},
				"home.terminalType.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalType/terminalTypeList.html",
					controllerUrl : "modules/terminal/terminalType/js/terminalTypeListController.js"
				},
				"home.terminalType.add" : {
					url : "/add",
					cache : false,
					templateUrl : "modules/terminal/terminalType/terminalTypeUpdate.html",
					controllerUrl : "modules/terminal/terminalType/js/terminalTypeAddController.js"
				},
				"home.terminalType.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalType/terminalTypeUpdate.html",
					controllerUrl : "modules/terminal/terminalType/js/terminalTypeUpdateController.js"
				},
				/*"home.terminalType.alarmSet" : {
					url : "/alarmSet/:id/:level",
					cache : false,
					templateUrl : "modules/terminal/terminalType/terminalTypeAlarmSet.html",
					controllerUrl : "modules/terminal/terminalType/js/terminalTypeAlarmSetController.js"
				},*/
				"home.terminalParam" : {
					url : "/terminalParam",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalParam/js/terminalParamController.js"

				},
				"home.terminalParam.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalParam/terminalParamList.html",
					controllerUrl : "modules/terminal/terminalParam/js/terminalParamListController.js"
				},
				"home.terminalParam.add" : {
					url : "/add",
					cache : false,
					templateUrl : "modules/terminal/terminalParam/terminalParamUpdate.html",
					controllerUrl : "modules/terminal/terminalParam/js/terminalParamAddController.js"
				},
				"home.terminalParam.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalParam/terminalParamUpdate.html",
					controllerUrl : "modules/terminal/terminalParam/js/terminalParamUpdateController.js"
				},
				"home.terminalParam.copy" : {
					url : "/copy/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalParam/terminalParamUpdate.html",
					controllerUrl : "modules/terminal/terminalParam/js/temrinalParamCopyController.js"
				},
				"home.terminalParam.view" : {
					url : "/view/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalParam/terminalParamView.html",
					controllerUrl : "modules/terminal/terminalParam/js/terminalParamViewController.js"
				},
				"home.terminalGroup" : {
					url : "/terminalGroup",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalGroup/js/terminalGroupController.js"

				},
				"home.terminalGroup.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalGroup/terminalGroupList.html",
					controllerUrl : "modules/terminal/terminalGroup/js/terminalGroupListController.js"
				},
				"home.terminalGroup.add" : {
					url : "/add",
					cache : false,
					templateUrl : "modules/terminal/terminalGroup/terminalGroupUpdate.html",
					controllerUrl : "modules/terminal/terminalGroup/js/terminalGroupAddController.js"
				},
				"home.terminalGroup.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalGroup/terminalGroupUpdate.html",
					controllerUrl : "modules/terminal/terminalGroup/js/terminalGroupUpdateController.js"
				},
				"home.terminalGroupSet" : {
					url : "/terminalGroupSet/tree",
					cache : false,
					templateUrl : "modules/terminal/terminalGroupSet/menuTree.html",
					// cache:false,templateUrl: "modules/common/tpl/tab.html",
					controllerUrl : "modules/terminal/terminalGroupSet/js/menuTreeController.js"
				},
				"home.terminalGroupSet.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalGroupSet/terminalGroupSetList.html",
					controllerUrl : "modules/terminal/terminalGroupSet/js/terminalGroupSetListController.js"
				},
				"home.terminalGroupSet.list.group" : {
					url : "/group/{groupId}",
					cache : false,
					templateUrl : "modules/terminal/terminalGroupSet/terminalGroupSetGrid.html",
					controllerUrl : "modules/terminal/terminalGroupSet/js/terminalGroupSetListController.js"
				},
				"home.terminalGroupSet.list.nogroup" : {
					url : "/group/{groupId}",
					cache : false,
					templateUrl : "modules/terminal/terminalGroupSet/terminalGroupSetGrid2.html",
					controllerUrl : "modules/terminal/terminalGroupSet/js/terminalGroupSetListController.js"
				},
				"home.payTerminal" : {
					url : "/payTerminal",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/payTerminal/js/payTerminalController.js"
				},
				"home.payTerminal.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/payTerminal/payTerminalList.html",
					controllerUrl : "modules/terminal/payTerminal/js/payTerminalListController.js"
				},
				"home.payTerminal.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/payTerminal/payTerminalUpdate.html",
					controllerUrl : "modules/terminal/payTerminal/js/payTerminalController.js"
				},
				"home.payTerminalHistory" : {
					url : "/payTerminalHistory",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/payTerminalHistory/js/payTerminalHistoryController.js"
				},
				"home.payTerminalHistory.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/payTerminalHistory/payTerminalHistoryList.html",
					controllerUrl : "modules/terminal/payTerminalHistory/js/payTerminalHistoryListController.js"
				},
				"home.payTerminalHistory.update" : {
					url : "/:id",
					cache : false,
					templateUrl : "modules/terminal/payTerminalHistory/payTerminalHistoryUpdate.html",
					controllerUrl : "modules/terminal/payTerminalHistory/js/payTerminalHistoryController.js"
				},
				"home.terminalSystemInfo" : {
					url : "/terminalSystemInfo",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalSystemInfo/js/terminalSystemInfoController.js"
				},
				"home.terminalSystemInfo.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalSystemInfo/terminalSystemInfoList.html",
					controllerUrl : "modules/terminal/terminalSystemInfo/js/terminalSystemInfoListController.js"
				},
				"home.terminalAppInfo" : {
					url : "/terminalAppInfo",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalAppInfo/js/terminalAppInfoController.js"
				},
				"home.terminalAppInfo.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalAppInfo/terminalAppInfoList.html",
					controllerUrl : "modules/terminal/terminalAppInfo/js/terminalAppInfoListController.js"
				},
				"home.terminalPayMethod" : {
					url : "/terminalPayMethod",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalPayMethod/js/terminalPayMethodController.js"
				},
				"home.terminalPayMethod.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalPayMethod/temirnalPayMethodList.html",
					controllerUrl : "modules/terminal/terminalPayMethod/js/terminalPayMethodListController.js"
				},
				"home.terminalPayMethod.add" : {
					url : "/add",
					cache : false,
					templateUrl : "modules/terminal/terminalPayMethod/terminalPayMethodUpdate.html",
					controllerUrl : "modules/terminal/terminalPayMethod/js/terminalPayMethodAddController.js"
				},
				"home.terminalPayMethod.edit" : {
					url : "/edit/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalPayMethod/terminalPayMethodUpdate.html",
					controllerUrl : "modules/terminal/terminalPayMethod/js/terminalPayMethodUpdateController.js"
				},
				"home.terminalPayMethod.detail" : {
					url : "/taskList/:id",
					cache : false,
					templateUrl : "modules/terminal/terminalPayMethod/terminalPayMethodDetail.html",
					controllerUrl : "modules/terminal/terminalPayMethod/js/terminalPayMethodDetailController.js"
				},
				"home.terminalActAudit" : {
					url : "/merchantAudit",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/merchant/merchantAudit/js/merchantAuditController.js"
				},
				"home.terminalActAudit.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/merchant/merchantAudit/merchantAuditList.html",
					controllerUrl : "modules/merchant/merchantAudit/js/merchantAuditListController.js"
				},
				"home.termAdvQuery" : {
					url : "/termAdvQuery",
					cache : false,
					templateUrl : "modules/common/tpl/breadcrumb.html",
					controllerUrl : "modules/terminal/terminalAdvQuery/js/terminalAdvQueryController.js"
				},
				"home.termAdvQuery.list" : {
					url : "/list",
					cache : false,
					templateUrl : "modules/terminal/terminalAdvQuery/terminalAdvQueryList.html",
					controllerUrl : "modules/terminal/terminalAdvQuery/js/terminalAdvQueryListController.js"
				},
				"home.terminalElectricity":{
		     		  url:"/terminalElectricity",
		     		  cache:false,
		     		  templateUrl: "modules/common/tpl/breadcrumb.html",
		            controllerUrl: "modules/terminal/terminalElectricity/js/terminalElectricityController.js"
		     	},
		     	"home.terminalElectricity.list": {
		              url: "/list",
		              cache:false,
		              templateUrl: "modules/terminal/terminalElectricity/terminalElectricityList.html",
		              controllerUrl: "modules/terminal/terminalElectricity/js/terminalElectricityListController.js"
		        }, 
		        "home.terminalSwitch":{
		    		url:"/terminalSwitch",
		    		cache:false,
		    		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
		            controllerUrl: "modules/terminal/terminalSwitch/js/terminalSwitchController.js"
		     	 },
		         "home.terminalSwitch.list": {
		            url: "/list",
		            cache:false,
		            templateUrl: "modules/terminal/terminalSwitch/terminalSwithList.html",
		            controllerUrl: "modules/terminal/terminalSwitch/js/terminalSwitchListController.js"
		        },
		         "home.terminalSwitch.task": {
		       	 url: "/taskList/:id",
		         cache:false,templateUrl: "modules/terminal/terminalSwitch/switchTask.html",
		         controllerUrl: "modules/terminal/terminalSwitch/js/switchTaskController.js"
		         },
		         "home.terminalSwitch.restartadd": {
		             url: "/add",
		             cache:false,templateUrl: "modules/terminal/terminalSwitch/terminalSwitchUpdate.html",
		             controllerUrl: "modules/terminal/terminalSwitch/js/terminalSwitchAddController.js"
		         },
		         "home.terminalSwitch.offadd": {
		             url: "off/add",
		             cache:false,templateUrl: "modules/terminal/terminalSwitch/terminalSwitchOffUpdate.html",
		             controllerUrl: "modules/terminal/terminalSwitch/js/terminalSwitchOffAddController.js"
		         },
		         "home.terminalSwitch.update": {
		        	 url: "/:id",
		             cache:false,templateUrl: "modules/terminal/terminalSwitch/terminalSwitchUpdate.html",
		             controllerUrl: "modules/terminal/terminalSwitch/js/terminalSwitchUpdateController.js"
		         },
		         "home.terminalCert":{
		      		  url:"/terminalCert",
		       		  cache:false,
		       		  templateUrl: "modules/common/tpl/breadcrumb.html",
		              controllerUrl: "modules/terminal/terminalCert/js/terminalCertController.js"
		       	  	},
		       	  	"home.terminalCert.list": {
		                url: "/list",
		                cache:false,
		                templateUrl: "modules/terminal/terminalCert/terminalCertList.html",
		                controllerUrl: "modules/terminal/terminalCert/js/terminalCertListController.js"
		       	  	},
		       	  	"home.terminalCert.add": {
		       	  		url: "/add",
		       	  		cache:false,
		       	  		templateUrl: "modules/terminal/terminalCert/terminalCertAdd.html",
		       	  		controllerUrl: "modules/terminal/terminalCert/js/terminalCertAddController.js"
		    	  	},

		    	  	"home.terminalCert.update": {
		       	  		url: "/update/:id",
		       	  		cache:false,
		       	  		templateUrl: "modules/terminal/terminalCert/terminalCertAdd.html",
		       	  		controllerUrl: "modules/terminal/terminalCert/js/terminalCertUpdateController.js"
		    	  	},  
		    		
		    		"home.terminalCertPush":{
		      		url:"/terminalCertPush",
		      		cache:false,
		      		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
		            controllerUrl: "modules/terminal/terminalCertPush/js/terminalCertPushController.js"
		       	   },
		      	    "home.terminalCertPush.list": {
		     	  		url: "/list",
		     	  		cache:false,
		     	  		templateUrl: "modules/terminal/terminalCertPush/terminalCertPush.html",
		     	  		controllerUrl: "modules/terminal/terminalCertPush/js/terminalCertPushListController.js"
		    	  	},
		    	  	"home.terminalCertPush.update": {
		           	 url: "/:id",
		                cache:false,templateUrl: "modules/terminal/terminalCertPush/terminalCertPushUpdate.html",
		                controllerUrl: "modules/terminal/terminalCertPush/js/terminalCertPushUpdateController.js"
		            }, 
		            "home.terminalCertPush.task": {
		              	 url: "/taskList/:id",
		                 cache:false,templateUrl: "modules/terminal/terminalCertPush/CertPushTask.html",
		                 controllerUrl: "modules/terminal/terminalCertPush/js/CertPushTaskController.js"
		                 },
		            "home.terminalCertPush.Addpush": {
		            	  	url: "/add/push",
		            	  	cache:false,
		            	  	templateUrl: "modules/terminal/terminalCertPush/terminalCertPushAdd.html",
		            	  	controllerUrl: "modules/terminal/terminalCertPush/js/terminalCertPushAddController.js"
		         	},
		         	
		         	"home.terminalMoveSwitch":{
			      		url:"/terminalMoveSwitch",
			      		cache:false,
			      		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
			            controllerUrl: "modules/terminal/terminalMoveSwitch/js/terminalMoveSwitchController.js"
			       	   },	
	         	    "home.terminalMoveSwitch.list": {
		     	  		url: "/list",
		     	  		cache:false,
		     	  		templateUrl: "modules/terminal/terminalMoveSwitch/terminalMoveSwitchList.html",
		     	  		controllerUrl: "modules/terminal/terminalMoveSwitch/js/terminalMoveSwitchListController.js"
		    	  	},
		           "home.terminalMoveSwitch.add": {
		            	  	url: "/add",
		            	  	cache:false,
		            	  	templateUrl: "modules/terminal/terminalMoveSwitch/terminalMoveSwitchAdd.html",
		            	  	controllerUrl: "modules/terminal/terminalMoveSwitch/js/terminalMoveSwitchAddController.js"
		         	},
		         	"home.terminalMoveSwitch.update": {
			           	 url: "/:id",
			                cache:false,templateUrl: "modules/terminal/terminalMoveSwitch/terminalMoveSwitchUpdate.html",
			                controllerUrl: "modules/terminal/terminalMoveSwitch/js/terminalMoveSwitchUpdateController.js"
			        }, 
			        "home.terminalWarehouse":{
			      		url:"/terminalWarehouse",
			      		cache:false,
			      		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
			            controllerUrl: "modules/terminal/terminalWarehouse/js/terminalWarehouseController.js"
			       	   },	
	         	    "home.terminalWarehouse.list": {
		     	  		url: "/list",
		     	  		cache:false,
		     	  		templateUrl: "modules/terminal/terminalWarehouse/terminalWarehouseList.html",
		     	  		controllerUrl: "modules/terminal/terminalWarehouse/js/terminalWarehouseListController.js"
		    	  	},
		           "home.terminalWarehouse.uploadByTerm": {
		            	url: "/add",
		            	cache:false,
		            	templateUrl: "modules/terminal/terminalWarehouse/terminalWarehouseAdd.html",
		            	controllerUrl: "modules/terminal/terminalWarehouse/js/terminalWarehouseAddController.js"
		         	},
		         	"home.terminalWarehouse.scanRK":{
		         		url: "/scanRK",
	            	  	cache:false,
	            	  	templateUrl: "modules/terminal/terminalWarehouse/terminalWarehouseScanRK.html",
	            	  	controllerUrl: "modules/terminal/terminalWarehouse/js/terminalWarehouseScanRKController.js"
		         	},
		         	"home.terminalWarehouse.update": {
			           	 url: "/:id",
			             cache:false,templateUrl: "modules/terminal/terminalWarehouse/terminalWarehouseUpdate.html",
			             controllerUrl: "modules/terminal/terminalWarehouse/js/terminalWarehouseUpdateController.js"
			        },
			        "home.termChangeMer":{
			      		url:"/termChangeMer",
			      		cache:false,
			      		cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
			            controllerUrl: "modules/terminal/termChangeMer/js/termChangeMerController.js"
			       	   },	
	         	    "home.termChangeMer.list": {
		     	  		url: "/list",
		     	  		cache:false,
		     	  		templateUrl: "modules/terminal/termChangeMer/termChangeMerList.html",
		     	  		controllerUrl: "modules/terminal/termChangeMer/js/termChangeMerListController.js"
		    	  	},
				"home.terminalCardIssueParam":{
					url:"/terminalCardIssueParam",
					cache:false,
					cache:false,templateUrl: "modules/common/tpl/breadcrumb.html",
					controllerUrl: "modules/terminal/terminalCardIssueParam/js/terminalCardIssueParamController.js"
				},
				"home.terminalCardIssueParam.list":{
					url: "/list",
					cache:false,
					templateUrl: "modules/terminal/terminalCardIssueParam/terminalCardIssueParamList.html",
					controllerUrl: "modules/terminal/terminalCardIssueParam/js/terminalCardIssueParamListController.js"
				},
				"home.terminalCardIssueParam.add":{
					url: "/add",
					cache:false,
					templateUrl: "modules/terminal/terminalCardIssueParam/terminalCardIssueParamUpdate.html",
					controllerUrl: "modules/terminal/terminalCardIssueParam/js/terminalCardIssueParamAddController.js"
				},
				"home.terminalCardIssueParam.update":{
					url: "/update/:id",
					cache:false,
					templateUrl: "modules/terminal/terminalCardIssueParam/terminalCardIssueParamUpdate.html",
					controllerUrl: "modules/terminal/terminalCardIssueParam/js/terminalCardIssueParamUpdateController.js"
				},
				"home.terminalCardIssueParam.copy":{
					url: "/copy/:id",
					cache:false,
					templateUrl: "modules/terminal/terminalCardIssueParam/terminalCardIssueParamUpdate.html",
					controllerUrl: "modules/terminal/terminalCardIssueParam/js/terminalCardIssueParamCopyController.js"
				},
		         	  	
			};
		});
