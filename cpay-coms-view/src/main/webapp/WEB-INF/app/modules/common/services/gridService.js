'use strict';
define(['js/app'], function (app) {
    app.factory("gridService",
        function ($compile, dialogService, baseUrl,
                  securityService,BaseApi,$http,toolsService) {
            var pageSize = 10;
            var getId = function (id) {
                if (id) {
                    return id;
                } else {
                    return "#table";
                }
            };
            var getContent = function (tableId) {
                var content = ".content-main";
                if ($(tableId).parents(".ngdialog-content").length > 0) {
                    content = ".ngdialog-content";
                }
                return content;
            };
            var isPlaceholder = function () {
                var input = document.createElement('input');
                return 'placeholder' in input;
            };
            return {
                condition: {
                    page: 1,
                    name:'',
                    info:''
                },

                initTable: function (options) {
                    options = $.extend({
                        rowStyle: function (row, index) {
                            return {
                                css: {
                                    "padding-left": "10px",
                                    "padding-right": "10px"
                                }
                            };
                        }
                    },options);
                    var obj = this;

                    var getOperate = function (options, index, row) {
                        var el = '';
                        var detaileEl = '<a  href="javascript:;" ng-click="detail(' + index + ')" title="detail" ' + '> '+"detail" +'</a>';
                        var updateDetail = el + ' <a  href="javascript:;" ng-click="updateRow(' + index + ')"   has-permission="' + options.update + '" title="update">'+"update" +'</a> ';
                        var deletesEl = el + '<a  href="javascript:;" class="remove" ng-click="deleteRow(' + index + ')"  has-permission="' + options.deletes + '" title="delete">'+"delete" +'</a>';
                        if (options.detail) {
                            el = el + detaileEl;
                        }
                        if (options.update) {
                            el = el + updateDetail;
                        }
                        if (options.deletes) {
                            el = el + deletesEl;
                        }
                        if (el == "") {
                            el = detaileEl + updateDetail + deletesEl;
                        }
                        return el;
                    };
                    var successCallback = function (data) {
                        if (data.status == 400) {
                            alert(data.msg);
                            window.location = "login.html";
                            return;
                        }
                        if (data && (data.status == 300 || data.status == 405 || data.status == 500)) {
                            dialogService.alertInfo("error",
                                data.msg);
                            return {};
                        } else {
                            return data;
                        }
                    };
                    var judgeCheck = function (tableId) {
                        var tableId = getId(tableId);
                        var headCheck = $(tableId + " thead .bs-checkbox");
                        var checkbox = $(tableId + " th.bs-checkbox input");
                        if (checkbox.attr("checked") == "checked") {
                            headCheck.addClass("bootstrap-table-selectedAll");
                        } else {
                            headCheck.removeClass("bootstrap-table-selectedAll");
                        }
                    };
                    var bindEvent = function (tableId) {
                        var content = getContent(tableId);
                        if ($(content + " .pagination-footer .pagination-detail").length == 0) {
                            $(content + " .pagination-pager").before($(content + " .fixed-table-pagination .pagination-detail").clone());
                            $(content + " .pagination-detail ul.dropdown-menu>li").click(function (e) {
                                $(content + " ul.dropdown-menu>li.active").removeClass("active");
                                $(e.target).parent().addClass("active");
                                var pageTotal = $(e.target).text();
                                pageSize = pageTotal;
                                $(tableId).bootstrapTable('selectPage', 1);
                                obj.condition.page = 1;
                                $(content + " .page-size").text(pageTotal);
                            });
                        }
                    };
                    var goPage = function (page, tableId) {
                        if (!tableId) {
                            tableId = "#table";
                        }
                        $(tableId).bootstrapTable('selectPage', page);
                        obj.condition.page = page;
                    };
                    var initPageEvent = function (tableId) {
                        var content = getContent(tableId);
                        $(tableId).after('<div class="footer-container"><div class="pagination-footer"> ' +
                            '<div class="pagination-pager"><div class="pagination-first"></div>' +
                            '<div class="pagination-pre"></div>' +
                            '<div class="pagination-go">'+"the  "+'      <input style="width: 45px;">         '+"page"+'</div>' +
                            '<div class="pagination-total">'+"total 0 page"+'</div>' +
                            '<div class="pagination-next"></div>' +
                            '<div class="pagination-last"></div></div>' +
                            '</div></div>');
                        $(content + " .pagination-first").click(function () {
                            if ($(content + " .pagination-first").hasClass("disabled")) {
                                return;
                            }
                            goPage(1, tableId);
                        });
                        $(content + " .pagination-pre").click(function () {
                            if ($(content + " .pagination-pre").hasClass("disabled")) {
                                return;
                            }
                            $(tableId).bootstrapTable('prevPage');
                        });
                        $(content + " .pagination-next").click(function () {
                            if ($(content + " .pagination-next").hasClass("disabled")) {
                                return;
                            }
                            $(tableId).bootstrapTable('nextPage');
                        });
                        $(content + " .pagination-last").click(function () {
                            if ($(content + " .pagination-last").hasClass("disabled")) {
                                return;
                            }
                            var total = $(content + " .pagination-total").text();
                            total = Number(total.replace(/[^0-9]/g, ''));
                            goPage(total, tableId);
                        });
                        $(content + " .pagination-go input").on("keypress", function (e) {
                            if (e.which == 13) {
                                var total = $(content + " .pagination-total").text();
                                total = parseInt(total.replace("共", "").replace("页", ""));
                                var page = parseInt($(content + " .pagination-go input").val());
                                if (isNaN(page) || !page || page < 0 || page > total) {
                                    $(tableId).bootstrapTable("refresh");
                                } else
                                    goPage(page, tableId);
                            }
                        });
                    };
                    var getId = function (id) {
                        if (id) {
                            return id;
                        } else {
                            return "#table";
                        }
                    };
                    var getContent = function (tableId) {
                        var content = ".content-main";
                        if ($(tableId).parents(".ngdialog-content").length > 0) {
                            content = ".ngdialog-content";
                        }
                        return content;
                    };
                    var judegBtn = function (content) {
                        var page = parseInt($(content + " .pagination-go input").val());
                        var total = $(content + " .pagination-total").text();
                        total = parseInt(total.replace("共", "").replace("页", ""));
                        if (page == 1) {
                            $(content + " .pagination-first").addClass("disabled");
                            $(content + " .pagination-pre").addClass("disabled");
                        } else {
                            $(content + " .pagination-first").removeClass("disabled");
                            $(content + " .pagination-pre").removeClass("disabled");
                        }
                        if (page >= total) {
                            $(content + "  .pagination-next").addClass("disabled");
                            $(content + "  .pagination-last").addClass("disabled");
                        } else {
                            $(content + "  .pagination-next").removeClass("disabled");
                            $(content + "  .pagination-last").removeClass("disabled");
                        }
                    };
                    var isPlaceholder = function () {
                        var input = document.createElement('input');
                        return 'placeholder' in input;
                    };
                    if (!options.singleSelect) {
                        options.singleSelect = false;
                    }
                    var ourl = options.url;
                    if(ourl.indexOf('/') == 0){
                        ourl = ourl.substr(1);
                    }
                    if(obj.condition.name == ourl){
                        //关闭请求时保存查询条件
                        /* if(obj.condition.info && obj.condition.info != ''){
                             options.scope.condition = JSON.parse(obj.condition.info);
                         }*/
                    }else {
                        obj.condition.name = ourl;
                        obj.condition.page = 1;
                    }


                    var tools = "#tools";
                    if (options.id) {
                        tools = "";
                    }
                    var tableId = getId(options.id);
                    if (options.pagination == undefined || options.pagination) {
                        initPageEvent(tableId);
                    }
                    var content = getContent(tableId);
                    if (options.operator == undefined || options.operator || options.operate) {
                        var width = 'auto';
                        if (options.operatorLen) {
                            width = options.operatorLen;
                        }

                        var index = 0;
                        if (options.columns[0].checkbox) {
                            index = 1;
                        }
                        options.columns.splice(index, 0, {
                            title: "Operate",
                            width: width,
                            class: 'th-operator',
                            field: 'th-operator',
                            formatter: function (value, row,
                                                 index) {
                                var operateEl = "";
                                if (options.operator == undefined || options.operator != false) {
                                    operateEl = getOperate(options, index);
                                }
                                if (options.operate && typeof options.operate == 'function') {
                                    var addEl = options.operate(value, row, index);
                                    operateEl = addEl + operateEl;
                                }
                                return operateEl;
                            },
                            hides: function (value, row,index) {
                                alert("3333");
                                options.columns[i].hide();
                            },
                            cellStyle: function (value, row, index, field) {
                                return {
                                    classes: 'table-operator'
                                }
                            }
                        });
                    }
                    var columns = [];
                    for(var i=0;i<options.columns.length;i++){
                        if(!options.columns[i].hide){
                            var o = options.columns[i];
                            delete o.hide;
                            columns.push(o);
                        }
                    }
                    $(tableId).bootstrapTable(
                        {
                            url: baseUrl + options.url,
                            responseHandler: function (data) {
                                var res = successCallback(data);
                                var pageCount = 0;
                                if (res.total && res.total >= 0) {
                                    $(content + " .pagination-footer .pagination-info").html("total " + res.total + " record");
                                    var pageTotal = $(content + " .pagination-footer .page-size").text();
                                    if (!isNaN(parseInt(pageTotal))) {
                                        pageTotal = parseInt(pageTotal);
                                    } else {
                                        pageTotal = pageSize;
                                    }
                                    pageCount = Math.ceil(res.total / pageTotal);
                                } else {
                                    $(content + " .pagination-footer .pagination-info").html("total 0 record");
                                }
                                $(content + " .pagination-total").text("total " + pageCount + "page");
                                if (res && res.rows && res.rows.length == 0 && this.pageNumber > 0) {
                                    $(tableId).bootstrapTable('selectPage', this.pageNumber - 1);
                                    obj.condition.page = this.pageNumber - 1;
                                }
                                if (!res) {
                                    res = [];
                                }
                                judegBtn(content);
                                return res;
                            },
                            queryParams: function (params) {
                                var page = params.offset / params.limit + 1;
                                if (!options.scope.condition) {
                                    options.scope.condition = {};
                                }
                                options.scope.condition.page = page;
                                var pageTotal = $(content + " .pagination-footer ul.dropdown-menu>li.active").text();
                                if (pageTotal != null && pageTotal != undefined && pageTotal != "") {
                                    pageSize = pageTotal;
                                }
                                options.scope.condition.rows = pageSize;
                                $(content + " .pagination-go input").val(page);
                                judegBtn(content);
                                return options.scope.condition;
                            },
                            sidePagination: 'server',
                            paginationDetailHAlign: 'left',
                            pagination: true,
                            cache: false,
                            onlyInfoPagination: false,
                            pageNumber: obj.condition.page,
                            pageSize: pageSize,
                            strip: true,
                            clickToSelect: true,
                            classes: "table table-hover",
                            singleSelect: options.singleSelect,
//                                rowStyle: function (row, index) {
//                                    return {
//                                        css: {
//                                            "padding-left": "10px",
//                                            "padding-right": "10px"
//                                        }
//                                    };
//                                },
                            rowStyle:options.rowStyle,
                            paginationPreText: '<',
                            paginationNextText: '>',
                            method: "get",
                            ajaxOptions: {
                                headers: securityService.getHeader(),
                                timeout: 10000
                            },
                            pageList: [5, 10, 15, 25, 50, 75, 100, 300],
                            columns: columns,
                            onLoadSuccess: function (data) {
                                bindEvent(tableId);
                                var tabEl = angular.element(tableId);
                                var checkbox = $(tableId + " th.bs-checkbox input");
                                $compile(tabEl)(options.scope);
                                checkbox.bind('click', function () {
                                    judgeCheck(tableId);
                                });
                                if (data && data.total>0&&$(".table-operator").children().length == 0) {
                                    $(tableId).bootstrapTable('hideColumn', "th-operator");
                                }
                                if(options.onLoadSuccess){
                                    options.onLoadSuccess();
                                }
                                $(tableId).basictable({'field':options.columns});
                                var fn =$(tableId).data("basictablefn");
                                fn();
                                //调整表格滚动条位置
                                /*    if($(window).width()>979 && !toolsService.hasTouch()){
                                        var h = $(window).height()- $(tableId).offset().top-55;
                                        if($(tableId).height()>h){
                                           $(tableId).closest('.fixed-table-body').css('height',h+'px'); 
                                        }

                                    }
                                    $(window).resize(function(){
                                        $(tableId).closest('.fixed-table-body').css('height','auto'); 
                                        if($(window).width()>979 && !toolsService.hasTouch()){
                                            var h = $(window).height()- $(tableId).offset().top-55;
                                            if($(tableId).height()>h){
                                               $(tableId).closest('.fixed-table-body').css('height',h+'px'); 
                                            }

                                        }
                                    });*/

                            },
                            onPageChange: function (number, size){
                                var headCheck = $(content + " .bootstrap-table thead .bs-checkbox");
                                headCheck.removeClass(content + " bootstrap-table-selectedAll");
                                var pageTotal = $(content + " .pagination-footer ul.dropdown-menu>li.active").text();
                                if (pageTotal) {
                                    this.pageSize = pageTotal;
                                }
                                pageSize = this.pageSize;
                                obj.condition.page =number;
                            },
                            onUncheck: function () {
                                judgeCheck(tableId);
                            },
                            onCheck: function () {
                                judgeCheck(tableId);
                            },
                            onClickRow: function (row, $element, field) {
                                if ($.trim($element.context.className) != "bs-checkbox") {
                                    $(tableId).bootstrapTable("uncheckAll");
                                }
                            }
                        });


                },
                getRow: function (index, id) {
                    var row = $(getId(id)).bootstrapTable(
                        'getData');
                    return row[index];
                },
                refresh: function (id) {
                    $(getId(id)).bootstrapTable('refresh', {
                        silent: true
                    });
                },
                search: function (condition, id) {
                    var tableId = getId(id);
                    var content = getContent(tableId);
                    $(content + " .pagination-go input").val(1);
                    $(tableId).bootstrapTable('selectPage', 1);
                    this.condition.page = 1;
                    this.condition.info = JSON.stringify(condition);

                },
                getSelectedRow: function (id) {
                    return $(getId(id)).bootstrapTable("getSelections");
                },
                setPlaceholder: function () {
                    var isPlaceholder = function () {
                        var input = document.createElement('input');
                        return 'placeholder' in input;
                    };
                    if (!isPlaceholder()) {
                        $("input").not("input[type='password']").each(//把input绑定事件 排除password框
                            function () {
                                if (typeof $(this).attr("placeholder") != 'undefined'
                                    && $(this).attr("placeholder") != "") {
                                    var divO = $('<div></div>').insertBefore($(this));
                                    divO.append('<div class="placeholder"></div>');
                                    var o = $(this).clone(true).appendTo(divO);
                                    divO.css({
                                        display: 'inline-block',
                                        position: 'relative',
                                        verticalAlign: 'middle'
                                    });
                                    divO.find('.placeholder').css({
                                        color: '#acafb7',
                                        position: 'absolute',
                                        top: '0',
                                        left: '7px',
                                        zIndex: '2',
                                        display: 'none',
                                        lineHeight: '30px'
                                    })
                                    divO.find('.placeholder').html($(this).attr("placeholder"));
                                    $(this).remove();
                                    if (o.val() == '') {
                                        divO.find('.placeholder').show();
                                    }
                                    divO.find('.placeholder').on('click', function () {
                                        $(this).hide();
                                    });
                                    o.focus(function () {
                                        $(this).prev().hide()
                                    });
                                    o.blur(function () {
                                        if ($(this).val() == "") $(this).prev().show();
                                    });
                                }
                            });


                    }
                }
            };
        });
});
