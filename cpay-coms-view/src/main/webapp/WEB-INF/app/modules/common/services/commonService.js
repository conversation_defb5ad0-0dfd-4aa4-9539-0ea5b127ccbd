'use strict';
define(['js/app'], function (app) {
    app.factory("commonService", function (BaseApi, $rootScope) {
        var permissions ="";
        return {
            //获取数字字典下拉框：
            getDictList: function (data, success) {
                BaseApi.query("/dict/type/", data, success);
            },
            //获取省市县地区下拉框
            getAreaList: function (data, success) {
                BaseApi.query("/area/list", data, success);
            },
            setPerssion: function (permission) {
                permissions = permission;
            },
            getPerssion: function () {
                return permissions;
            },
            hasPermission: function (permission) {
                if (permissions.indexOf(permission) == -1) {
                    return false;
                }
                return true;
            }
        };
    });
});