'use strict';
define([], function() {
	return function($scope, organizationService, terminalWarehouseService,
			$state, gridService, dialogService, formService, BaseApi, $http) {
		var language = BaseApi.getCookieValue("userLanguage");
		BaseApi.query("/factory/type", {}, function(data) {
			$scope.factoryList = data;
		});
		$scope.$parent.thirdTitle = "扫码入库";
		$scope.terminalWarehouse = {};
		$scope.changeFactory = function(factoryId) {
			if (factoryId != null && factoryId != "") {
				var param = {
					"factoryId" : factoryId
				};
				BaseApi.query("/terminalType/type/" + factoryId, {}, function(
						data) {
					$scope.termTypeList = data;
				});
			} else {
				$scope.termTypeList = [];
			}
		};
		$scope.form = formService.form(function() {
			if($scope.terminalWarehouse.termSeq == null || $scope.terminalWarehouse.termSeq == ""){
				dialogService.alertInfo("error", "请填入终端信息");
				return ;
			}
			terminalWarehouseService.scanRK($scope.terminalWarehouse, function(data) {
				if (data.status != 200) {
					dialogService.alertInfo("error", data.msg);
					return false;
				}else{
					dialogService.alertInfo("success", "入库成功");
					$scope.terminalWarehouse.termSeq="";
				}
				//$state.go("home.terminalWarehouse.list");
			});
		});
		$scope.openInsDialog = function() {
			organizationService.openOrganization(function(value) {
				if (!$scope.terminalWarehouse) {
					$scope.terminalWarehouse = {};
				}
				$scope.terminalWarehouse.insId = value.id;
				$scope.terminalWarehouse.insName = value.name;
			});
		};
        $scope.clearTerm = function () {
        	 $scope.terminalWarehouse.termSeq="";
        }
	};
});