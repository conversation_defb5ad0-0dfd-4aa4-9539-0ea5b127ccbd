<style>
.titlediv {
	width: 100%;
	background: #cadaec;
	position: relative;
	margin-top: 10px;
	padding: 1px;
}

.titleh1 {
	font: 110% Arial, Helvetica, sans-serif;
	text-align: center;
	font-weight: bolder;
	display: block;
	color: #535353
}

.titleleft {
	float: left;
	width: 55%;
	background: #Bfcfe1;
	position: relative;
}

.titleright {
	float: left;
	width: 45%;
	background: #Bfcfe1;
	position: relative;
}

.titleh1left {
	font: 110% Arial, Helvetica, sans-serif;
	text-align: right;
	font-weight: bolder;
	display: block;
	color: #535353
}
</style>
<div class="container-fluid">
	<div class="row-fluid">
	
        <div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class='titlediv'>
					<h1 class='titleh1'>【机构信息】</h1>
					<span
						style='position: absolute; right: 10px; top: 8px; color: #666666; cursor: pointer;'
						ng-click="showOrHide(0)"><img src='{{baseinfospan}}'></span>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">所属机构<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="insName" ng-model="terminalParam.insName" readonly 
							           ng-disabled="disabled" class="orginput dropdown input-float"
							           validator="required" message-id="insId" style="width:220px;"/>
							    <button class="choice btn" type="button"
									 ng-show="update==1" ng-click="openInsDialog()">选择</button>
						    <span id="insId" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;">所属组</td>
							<td style="width: 38%;" >
		                        <select name="terminalGroupId" ng-model="terminalParam.terminalGroupId"
									ng-readonly="readOnly" 
									ng-options="m.id as m.name for m in terminalGroupList"
									message-id="terminalGroupId" 
									style="width:300px;" ng-readonly="readOnly" disabled="disabled">
				                </select>
				                <span id="terminalGroupId" class="help-inline"></span>							
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1'>【基础地址】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">心跳时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="htIntvl" ng-model="terminalParam.htIntvl" ng-disabled="disabled" 
								validator="required,positiveInteger" message-id="htIntvl" maxlength="4" 
								placeholder="{{terminalParamAdd.maxlength1}}"/>
								<span id="htIntvl" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;">文件传输重试次数<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
	                        <input type="text" name="reDownNum" ng-model="terminalParam.reDownNum"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="reDownNum" maxlength="1" 
								placeholder="{{terminalParamAdd.maxlength11}}"/>
							<span id="reDownNum" class="help-inline"></span>
	                    </div>
							</td>
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">文件传输超时时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text"
								name="fileTraTimeout" ng-model="terminalParam.fileTraTimeout"
								maxlength="2" placeholder="{{terminalParamAdd.maxlength12}}" 
								ng-disabled="disabled"
								validator="required" message-id="fileTraTimeout">
								<span id="fileTraTimeout" class="help-inline"> </span>
							</td>
							<td style="width: 12%; text-align: right;">信息上送间隔时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
	                        <input type="text" name="upInfoIntvl" ng-model="terminalParam.upInfoIntvl" 
	                        	ng-disabled="disabled" validator="required,positiveInteger"
								message-id="upInfoIntvl" maxlength="2" 
								placeholder="{{terminalParamAdd.maxlength12}}"/>
								<span id="upInfoIntvl" class="help-inline"></span>
							</td>
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">流量上送间隔时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
								<input type="text" name="upFlowIntvl" ng-model="terminalParam.upFlowIntvl" 
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="upFlowIntvl" maxlength="2" 
								placeholder="{{terminalParamAdd.maxlength4}}"/>
								<span id="upFlowIntvl" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;">定位上送间隔时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="locationIntvl" ng-model="terminalParam.locationIntvl"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="locationIntvl" maxlength="2" 
								placeholder="{{terminalParamAdd.maxlength4}}"/>
								<span id="locationIntvl" class="help-inline"></span>
							</td>
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">广告轮询时间(分)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="adQueryIntvl" 
								ng-model="terminalParam.adQueryIntvl" ng-disabled="disabled" 
								validator="required,positiveInteger"
								message-id="adQueryIntvl" maxlength="2" 
								placeholder="{{terminalParamAdd.maxlength13}}"/>
								<span id="adQueryIntvl" class="help-inline"></span>
							</td>	
							<td style="width: 12%; text-align: right;">系统启动项</td>
							<td style="width: 38%;">
							<input type="text" name="systemStartUp" ng-model="terminalParam.systemStartUp"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="systemStartUp" maxlength="200" 
								placeholder=""/>
								<span id="systemStartUp" class="help-inline"></span>
							</td>	
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">设备故障标识<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="alarmInfo" ng-model="terminalParam.alarmInfo"
								ng-disabled="disabled"
								message-id="alarmInfo" style="width: 200px;" readonly="readonly"/>
							<input type="text" name="alarmInfoCode" ng-model="terminalParam.alarmInfoCode" ng-hide="true"/>
							<button class="button" type="button"
								ng-click="checkExceptionInfo()" ng-disabled="disabled">
								<i class="icon-large"></i>选择</button>
							<span id="alarmInfo" class="help-inline"></span>
							<td style="width: 12%; text-align: right;">电子围挡半径(米)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="eleFenceRadius" ng-model="terminalParam.eleFenceRadius"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="eleFenceRadius" maxlength="6" 
								placeholder="{{terminalParamAdd.maxlength5}}"//>
								<span id="eleFenceRadius" class="help-inline"></span>
							</td>
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">运维口令<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="mapPswd" ng-model="terminalParam.mapPswd"
							ng-disabled="disabled" validator="required,number"
							message-id="mapPswd" maxlength="6" placeholder="{{terminalParamAdd.maxlength5}}"
							/><span id="mapPswd" class="help-inline"></span>
							</td>		
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1'>【更新设置】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">系统更新电量阀值(%)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
								<input type="text" name="systemUpdateRequiredPower" ng-model="terminalParam.systemUpdateRequiredPower"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="systemUpdateRequiredPower" maxlength="2" placeholder="{{terminalParamAdd.maxlength3}}"/>
							<span id="systemUpdateRequiredPower" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;">应用更新电量阀值(%)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="appUpdateRequiredPower" 
								ng-model="terminalParam.appUpdateRequiredPower"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="appUpdateRequiredPower" maxlength="2" 
								placeholder="{{terminalParamAdd.maxlength3}}"/>
								<span id="appUpdateRequiredPower" class="help-inline"></span>
							</td>
						</tr>
						<tr>
							<td style="width: 12%; text-align: right;">系统更新闲置时间(秒)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="sysUpRequiresNoOperTime" 
								ng-model="terminalParam.sysUpRequiresNoOperTime"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="sysUpRequiresNoOperTime" maxlength="3" 
								placeholder="{{terminalParamAdd.maxlength2}}"/>
								<span id="sysUpRequiresNoOperTime" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;">应用更新闲置时间(秒)<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="appUpRequiresNoOperTime" 
								ng-model="terminalParam.appUpRequiresNoOperTime"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="appUpRequiresNoOperTime" maxlength="3" 
								placeholder="{{terminalParamAdd.maxlength2}}"/>
								<span id="appUpRequiresNoOperTime" class="help-inline"></span>
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1'>【TMS地址】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">域名/IP+端口<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="tmsDomainName" ng-model="terminalParam.tmsDomainName"
								ng-disabled="disabled" validator="required"
								message-id="tmsDomainName" maxlength="50" 
								placeholder=""/>
								<span id="tmsDomainName" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;"><span
								style='color: #e02222'></span></td>
							<td style="width: 38%;">
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1'>【AMS地址】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">域名/IP+端口<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="amsDomainName" ng-model="terminalParam.amsDomainName"
								ng-disabled="disabled" validator="required"
								message-id="amsDomainName" maxlength="50" 
								placeholder=""/>
								<span id="amsDomainName" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;"><span
								style='color: #e02222'></span></td>
							<td style="width: 38%;">
							</td>
						</tr>
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1'>【CAS地址】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 12%; text-align: right;">域名/IP+端口<span
								style='color: #e02222'>*</span></td>
							<td style="width: 38%;">
							<input type="text" name="casDomainName" ng-model="terminalParam.casDomainName"
								ng-disabled="disabled" validator="required"
								message-id="casDomainName" maxlength="50" 
								placeholder=""/>
								<span id="casDomainName" class="help-inline"></span>
							</td>
							<td style="width: 12%; text-align: right;"><span
								style='color: #e02222'></span></td>
							<td style="width: 38%;">
							</td>
						</tr>
					</table>
				</div>
			</form>
		</div>
	</div>
</div>
 