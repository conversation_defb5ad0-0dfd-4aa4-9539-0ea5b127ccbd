'use strict';
define(function () {
    return function ($scope,$state,dialogService,toolsService, gridService,terminalPayMethodService,$stateParams,BaseApi) {
        $scope.$parent.thirdTitle = "";
        gridService.initTable({
            url: "/terminalPayMethod/taskList/"+$stateParams.id,
            scope: $scope,
            operator:false,
            columns: [{
                field: 'state',
                checkbox: 'true'	
            }, 
            {
                field: 'termSeq',
                title: '终端序列号'
            }, {
                field: 'sendPayName',
                title: '需要切换支付渠道'
            },
            {
                field: 'receivePayName',
                title: '终端返回支付渠道',
            },
	        {
	            field: 'batchNumber',
	            title: '批次号'
	        },
	        {
	            field: 'receiveTime',
	            title: '返回时间',
	            formatter: function (value) {
                	return toolsService.getFullDate(value);
                }
	        },
	        {
	            field: 'changeStatus',
	            title: '状态',
	            width:150,
	            formatter : function(value) {
					if (value==1) {
						return '<span>切换成功</span>';
					}else if(value==0){
						return '<span>切换失败</span>';
					}else if(value==2){
						return '<span>等待切换</span>';
					}
			    } 
	        }]
        });
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        BaseApi.query("/dict/type/changeStatus",{}, function (data) {
   		 	$scope.changeStatusList = data;
   	 	});
        $scope.refreshStatus = function () {
            $scope.condition = {};
            gridService.search();
        };
    };
});