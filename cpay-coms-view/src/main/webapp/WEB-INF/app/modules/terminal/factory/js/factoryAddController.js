'use strict';
define([], function () {
    return function ($scope,baseUrl,securityService, factoryService, $state,Upload, dialogService,formService,$http,BaseApi) {
        $scope.$parent.thirdTitle = "Add Vendor";
        $http.get("modules/terminal/factory/i18n/en/factoryList.json").success(function(dataLan) {
        $scope.addList = dataLan.addList
        $scope.factory = {};
        $scope.readOnly = false;
        $scope.form = formService.form(function(){

        	factoryService.addFactory($scope.factory, function (data) {
    		
       		 if(data.status == 300){
    			 dialogService.alertInfo("error", data.msg);
    			 return false;
    			 
    		 }else{
    			 dialogService.alertInfo("success", $scope.addList.successMsg);
    		 }
                $state.go("home.factory.list");
            });
        });
        });
    };
});