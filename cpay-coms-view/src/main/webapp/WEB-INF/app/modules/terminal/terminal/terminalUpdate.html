<div class="container-fluid" id="breadcrumb-scroll">
    <div class="row-fluid">
        <div class="nav-breadcrumb">
            <div class="breadcrumb-icon"
                 style="background-image: url(images/default/menu/pic_menu_setting_grey.png)"></div>
            <ul class="breadcrumb">
                <li><span class="navigation_home ng-binding">Terminal management</span>
                    <i class="icon-angle-right"></i></li>
                <li><a ng-class="{active:!thirdTitle}" class="active"
                       ui-sref="home.terminal.list">Terminal info management</a></li>
                <li class="active ng-binding""><i class="icon-angle-right"
                                                  ng-hide" ng-show="thirdTitle"></i>{{thirdTitle}}</li>
            </ul>
        </div>
        <div class="hr"></div>
    </div>
</div>
<div class="modal-body">
    <form name="Form" class="form-horizontal">
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.termSeq}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.termSeq"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.termMfrName}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.termMfrName"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.termTypeName}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.termTypeName"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.insName}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.insName"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.groupName}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.groupName"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.timeZone}} </label>
            <div class="controls" style="margin-left:90px">
                <input type="text" ng-model="terminal.timeZone"  readonly="readonly"/>
            </div>
        </div>
        <div class="control-group">
            <label class="control-label" style="width: 86px;">{{terminalListJsLan.remark}} <span class="required">*</span></label>
            <div class="controls" style="margin-left:90px">
                <input type="text" name="remark" ng-model="terminal.remark"  initial-validity="false"
                      maxlength="80" message-id="remark"/>
                <span id="remark" class="help-inline"> </span>
            </div>
        </div>
        <!--<div class="control-group">
            <label class="control-label" style="width: 86px;">终端联系人<span class="required">*</span></label>
            <div class="controls" style="margin-left:90px">
                <input type="text" name="linkMan" ng-model="terminal.linkMan"  initial-validity="false"
                       validator="required" maxlength="15" message-id="linkMan"/>
                <span id="linkMan" class="help-inline"> </span>
            </div>
        </div>
        <div class="control-group">
        <label class="control-label" style="width: 86px;">联系人电话<span class="required">*</span></label>
        <div class="controls" style="margin-left:90px">
            <input type="text" name="linkPhone" ng-model="terminal.linkPhone"  initial-validity="false"
                   validator="required,telephone" maxlength="11" message-id="linkPhone"/>
            <span id="linkPhone" class="help-inline"> </span>
        </div>
       </div>
        <div class="control-group">
             <label class="control-label" style="width: 86px;">出厂时间<span class="required">*</span></label>
             <div class="input-append date form_datetime" data-date="2016-01-01T15:25:00Z" style="margin-left:10px">
			       <input size="10" type="text" value="" readonly class="top-inp" ng-disabled="disabled" date-picker format="yyyy-MM-dd" valid-method="watch"
                            ng-model="terminal.productionTime" name="productionTime"  validator="required" message-id="productionTime" />
        <span id="productionTime" class="help-inline"></span>
        </div>
        </div>-->
    </form>
</div>
<div ng-show="true" form-foot goback="$state.go('home.terminal.list')"
     submit="form.submit(Form)"
     reset="form.reset(Form)">
</div>