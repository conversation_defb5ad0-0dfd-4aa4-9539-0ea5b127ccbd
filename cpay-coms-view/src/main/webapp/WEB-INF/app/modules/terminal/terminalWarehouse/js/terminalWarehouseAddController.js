'use strict';
define([], function() {
	return function($scope, organizationService, terminalWarehouseService,
			$state, gridService, dialogService, formService, BaseApi, $http) {
		var language = BaseApi.getCookieValue("userLanguage");
		BaseApi.query("/factory/type", {}, function(data) {
			$scope.factoryList = data;
		});
		$scope.$parent.thirdTitle = "按号段入库";
		$scope.terminalWarehouse = {};
		$scope.changeFactory = function(factoryId) {
			if (factoryId != null && factoryId != "") {
				var param = {
					"factoryId" : factoryId
				};
				BaseApi.query("/terminalType/type/" + factoryId, {}, function(
						data) {
					$scope.termTypeList = data;
				});
			} else {
				$scope.termTypeList = [];
			}
		};
		BaseApi.query("/dict/type/term_header",{}, function (data) {
   		 	$scope.termHeaderList = data;
   	 	});
		BaseApi.query("/dict/type/term_length",{}, function (data) {
   		 	$scope.termLengthList = data;
   	 	});
		$scope.form = formService.form(function() {
			terminalWarehouseService.add($scope.terminalWarehouse, function(data) {
				if (data.status == 300) {
					dialogService.alertInfo("error", data.msg);
					return false;
				}
				dialogService.alertInfo("success", "操作成功,数据会陆续添加");
				$state.go("home.terminalWarehouse.list");
			});
		});
		$scope.openInsDialog = function() {
			organizationService.openOrganization(function(value) {
				if (!$scope.terminalParam) {
					$scope.terminalParam = {};
				}
				$scope.terminalWarehouse.insId = value.id;
				$scope.terminalWarehouse.insName = value.name;
			});
		};
	};
});