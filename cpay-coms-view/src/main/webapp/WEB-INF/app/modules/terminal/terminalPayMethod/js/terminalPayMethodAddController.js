'use strict';
define(function () {
    return function ($scope, terminalPayMethodService,Upload,terminalTypeService,factoryService, $state, dialogService,formService,BaseApi,organizationService,terminalGroupService,gridService) {
        $scope.$parent.thirdTitle = "Payment channel switching added";
        $scope.readOnly = false;
        var parentScope =$scope ;
        var domain = window.location.href.split("#")[0];
        var baseUrl=domain.substr(0,domain.length-6);
        BaseApi.query("/dict/type/messionType",{}, function (data) {
       	
 		 	$scope.releaseTypeList = data;
 	 	});
        BaseApi.query("/dict/type/payMethod",{}, function (data) {
           	
 		 	$scope.payTypeList = data;
 	 	});
        $scope.form = formService.form(function(){
        	 if($scope.terminalPayMethod.releaseType==1){
        		 if($scope.terminalPayMethod.insName == "" || $scope.terminalPayMethod.insName== null){
        			 dialogService.alertInfo("error", "Please select an organization name");
        			 return ;
        		 }
        	 }
        	 if($scope.terminalPayMethod.releaseType==2){
             	var collections ="";
            	var selectObj = angular.element("#tout_acct_list").get(0);
            	var options = selectObj.options;
            	if(options.length == 0) {
            		dialogService.alertInfo("info", "Terminal collection cannot be empty！");
                    return;
            	}
            	for(var i = 0;i < options.length;i++) {
            		collections += options[i].text+",";
            	}
            	$scope.terminalPayMethod.termCollection = collections.substring(0,collections.length-1);
        	 }
        	terminalPayMethodService.addTerminalPayMethod($scope.terminalPayMethod, function (data) {
                dialogService.alertInfo("success", "Successfully Added!");
                $state.go("home.terminalPayMethod.list");
            });
        });
        $scope.cleanTermSeqCollection = function(){
        	$scope.terminalPayMethod.termCollection = "";
        }
        $scope.openSelectExcel = function () {
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/driverJobExcel.html",
                controller: function ($scope) {
                    $scope.dialogTitle = "Terminal serial number import";
                    $scope.readOnly = false;
                    $scope.submitFile = function() {
			            if (!$scope.file||!$scope.file.size) {
			            	dialogService.alertInfo("success", "Please select a file");
			            	return;
			            }
			            if ($scope.file.name.indexOf(".xls") == -1 && $scope.file.name.indexOf(".xlsx") == -1) {
			            	dialogService.alertInfo("success", "Please select an Excel file");
			            	return;
			            }
			            Upload.upload({
			                url:  baseUrl+'/uploadjob/importFile',
			                data: {file: $scope.file},
			                method: 'POST'
			            }).then(function (resp) {
                    		var collections = resp.data.data;
                    		var selectObj = angular.element("#tout_acct_list");
				            for(var i = 0;i < collections.length;i++) {
				        		selectObj.append("<option value='"+collections[i]+"'>"
				                    		+collections[i]+"</option>");
				        	}
			            	dialogService.alertInfo("success", "Successfully uploading files");
			            }, function (resp) {
			            	dialogService.alertInfo("warning", "Failed to upload file");
			            }, function (evt) {
			            });
			            $scope.closeThisDialog(0);
			        };
			        $scope.downModel =  function () {
			        	var downloadUrl=baseUrl+'/uploadjob/fileDownload';
			            window.open(downloadUrl,'_blank');
			        };
                },
                closeByDocument: false
            });
        };
        $scope.showTermSeq = function(){
            dialogService.openDialog({
                template: "modules/taskScheduling/driverJob/showTermSeqList.html",
                width: '60%',
                controller: function ($scope,dialogService,$timeout) {
             	   $scope.app={};
             	   $timeout(function(){
             		   gridService.initTable({
             			    url:"/terminal?termStatus=1&merchantStatus=1",
                            scope: $scope,
                            operator:false,
                            uniqueId: 'id',
							    columns: [
                              {field: 'state',checkbox: 'true'},
                              {field: "termSeq", title: "Terminal serial number"},
                              {field: "insName", title: "Organization"},
                              {field: "termMfrName", title: "Terminal manufacturer"},
                              {field: "termTypeName", title: "Terminal type"},
                              {field: "merchantName", title: "Business Name"}
                           ]
                        });
             	    },100)
             	    $scope.serachTermInfo = function () {
             		   gridService.search($scope.condition);
                    };
                    $scope.resetTermInfo = function () {
              		   $scope.condition={};
              		   gridService.search();
                    };
                    $scope.openOrganization = function () {
                        organizationService.openOrganization(function (value) {
                            if (!$scope.condition) {
                                $scope.condition = {};
                            }
                            $scope.condition.insId = value.id;
                            $scope.condition.insName = value.name;
                        });
                    };
            	    $scope.ok = function () {
           		    var rows= $("#table").bootstrapTable("getSelections");
           		     if (rows.length == 0) {
                         dialogService.alertInfo("info", "Please select a record！");
                         return;
                     }
           		  var ids = "";
             	 if(angular.element("#tout_acct_list option").size()==0){
             		 for (var index in rows) {
             		 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                      		+rows[index].termSeq +"</option>");
             		 }
                    
             	 }else{
             		 for (var index in rows) {
             		 var j =0;
             		 angular.element("#tout_acct_list option").each(function () {
             			 if(rows[index].termSeq==$(this).val()){
             				 return false;
             			 }
             			 j++;
             	       });
                 		 if(j==angular.element("#tout_acct_list option").size()){
                 			 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                               		+rows[index].termSeq +"</option>");
                 		 }
             		 }
             	 }
             	  
             	  $scope.closeThisDialog(0);
             
           };
                }
            });
        }
        BaseApi.query("/terminalGroup/selectGroup",{}, function (data) {
   		 	$scope.terminalGroupList = data;
   	 	});
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.terminalPayMethod) {
                    $scope.terminalPayMethod = {};
                }
                $scope.terminalPayMethod.insId = value.id;
                $scope.terminalPayMethod.insName = value.name;
//                BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
//    	   		 	$scope.terminalGroupList = data;
//    	   	 	});
            });
        };
        $scope.deleteTerm = function () {
        	var oSelect = angular.element("#tout_acct_list");
        	var options = oSelect.option;
        	var termSeqs = $("#tout_acct_list option:selected").val();
         	if(termSeqs == undefined || termSeqs == null || termSeqs == ""){
         		dialogService.alertInfo("success", "Please select at least one record！");
         		return ;
         	}
			$("#tout_acct_list option:selected").remove();
        }
        $scope.clearTerm = function () {
        	var selectObj = angular.element("#tout_acct_list").get(0);
         	var options = selectObj.options;
         	if(options.length == 0) {
         		dialogService.alertInfo("success", "There is currently no terminal information that can be emptied！");
         		return ;
         	}
       	 	 dialogService.openConfirm("Are you sure you want to clear the data of the current terminal collection?", function () {
       		 $("#tout_acct_list option").remove();
       		 dialogService.closeConfirm();
       	 	 });
        }
    };
});