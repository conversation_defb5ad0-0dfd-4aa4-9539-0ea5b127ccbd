'use strict';
define(function () {
    return function ($scope, factoryService, $state,dialogService, gridService,$http,BaseApi) {
        $scope.$parent.thirdTitle = "";
    	$http.get("modules/terminal/factory/i18n/en/factoryList.json").success(function(data) {
        $scope.factory = data.list;
        $scope.deleteRow = data.deleteRow;
        var confirmMsg1 = $scope.deleteRow.confirmMsg1;
        var successMsg = $scope.deleteRow.successMsg;
        gridService.initTable({
            url: "/factory",
            scope: $scope,
            detail:false,
            update:'factory_edit',
            deletes:'factory_del',
            columns: [
            {
                field: 'code',
                title: $scope.factory.code
            }, {
                field: 'name',
                title: $scope.factory.name
            },{
            	field:'tusnHeader',
            	title: $scope.factory.tusnHeader
            },
            {
                field: 'download',
                title: $scope.factory.pubKeyDownload,
                formatter: function (value, row, index) {
                	var el = '<a  href="javascript:;" ng-click=download('+row.id+')  title="download" has-permission="terminal_contactBinding"> download </a>';
    	        	return el
                }
                
            }],
            
        });
        $scope.download=function(id){
            var domain = window.location.href.split("#")[0];
            var baseUrl=domain.substr(0,domain.lastIndexOf("/"));
            var downloadUrl = "";

            downloadUrl=baseUrl+'/factory/downloadPublicKey/'+id;
            var iframe = document.createElement("iframe");
            document.body.appendChild(iframe);
            iframe.src = downloadUrl;
            iframe.style.display = "none";
        }
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            console.log($scope.deleteRow.confirmMsg1);
            dialogService.openConfirm(confirmMsg1+" ”"+ row.name +"“ "+ "？", function (confirm) {
                factoryService.deleteFactory(row.id, function (data) {
                	if(data.status !=200){
                		dialogService.closeConfirm();
                        dialogService.alertInfo("error", data.msg);
                        return ;
                	}
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", successMsg);
                    gridService.refresh();
                });
            });
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.factory.update', {id: row.id});
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.detail = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.factory.view', {id: row.id});
        };
        gridService.setPlaceholder();
    	});
    };
});