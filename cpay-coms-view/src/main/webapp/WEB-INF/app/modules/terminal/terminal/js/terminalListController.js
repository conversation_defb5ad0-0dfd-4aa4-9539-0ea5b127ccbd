'use strict';
define([], function () {
    return function ($scope, terminalService, $state, organizationService, dialogService, gridService,BaseApi,Upload,$http,baseUrl,securityService) {
    	$http.get("modules/terminal/terminal/i18n/en/terminalList.json").success(function(data) {
    	$scope.$parent.thirdTitle="";
    	$scope.terminalListJsLan = data.terminalList;
    	$scope.terminalListHtmLan = data.terminalList;
    	$scope.terminalUploadHtmLan = data.upload;
    	$scope.terminalExportLan = data.exportData;
        $scope.dccList = [{type: "Y", name: "YES"},{type: "N", name: "NO"}]
        BaseApi.get("/dict/types/cup_conn_mode,buss_type", {}, function (data) {
            $scope.cupConnModeList = data.data1;
            $scope.bussTypeList = data.data2;
        });
        BaseApi.query("/dict/type/terminal_type", {}, function (data) {
    	gridService.initTable({
                url: "/terminal",
                scope: $scope,
                detail:true,
                update:true,
                columns: [
                    {field: "termSeq", title: $scope.terminalListJsLan.termSeq},
                    {field: "termMfrName", title: $scope.terminalListJsLan.termMfrName},
                    {field: "termTypeName", title: $scope.terminalListJsLan.termTypeName},
                    {field: "insName", title: $scope.terminalListJsLan.insName},
                    {field: "groupName", title: $scope.terminalListJsLan.groupName},
                    {field: "timeZone", title: $scope.terminalListJsLan.timeZone},
                    {field: "remark", title: $scope.terminalListJsLan.remark},
                    {field: "onlineStatus", title: $scope.terminalListJsLan.onlineStatus,
                        formatter:function(value){
							if(value === '0'){
								value = '<img src="app/images/default/greenLight.png"></img>'+"Online";
							}else if(value === '1'){
								value = '<img src="app/images/default/redLight.png"></img>'+"Offline";
							}else if(value == null){
								value = '<img src="app/images/default/redLight.png"></img>'+"Offline";
							}
							return value;
						}},
                    ]
            	});
    		});
            BaseApi.query("/factory/type",{}, function (data) {
       		 	$scope.factoryList = data;
       	 	});
            $scope.adQuery = function(){
        		$state.go('home.termAdvQuery.list');
        	}
            BaseApi.query("/dict/type/terminal_type", {}, function (data) {
                $scope.termTypeListTwo = data;
            });
        	$scope.changeFactory = function (factoryId) {
        		if(factoryId!=null&&factoryId!=""){
        			var param  = {"factoryId":factoryId};
        	        BaseApi.query("/terminalType/type/"+factoryId,{}, function (data) {
        	   		 	$scope.termTypeList = data;
        	   	 	});
        		}
        		else{
        			$scope.termTypeList=[];
        		}
            };
            $scope.openOrganization = function () {
                organizationService.openOrganization(function (value) {
                    if (!$scope.condition) {
                        $scope.condition = {};
                    }
                    $scope.condition.insId = value.id;
                    $scope.condition.insName = value.name;
                    $scope.condition.terminalGroupId = null;
                    BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
               		 	$scope.terminalGroupList = data;
               	 	});
                });
            };
            $scope.detail = function (index) {
                var row = gridService.getRow(index);
                $state.go('home.termview.info', {id: row.id});
            };
            $scope.updateRow = function (index) {
                var row = gridService.getRow(index);
                $state.go('home.terminal.update', {id: row.id});
            };
            $scope.search = function () {
            	if($scope.condition.terminalGroupId == null || 
            			$scope.condition.terminalGroupId == 'null'){
            		$scope.condition.terminalGroupId = 0;
            	}
            	gridService.search($scope.condition);
            };
            $scope.reset = function () {
                $scope.condition = {};
                $scope.terminalGroupList = [];
                $scope.termTypeList = [];
                gridService.search();
            };
        $scope.upload = function () {
            dialogService.openDialog({
                template: "modules/terminal/terminal/terminalUpload.html",
                width: '55%',
                controller: function ($scope, terminalService, formService) {
                    $scope.readOnly = false;
                    $scope.terminalUploadHtmLan = data.upload;
                    $scope.termMerDownload = function(){
                        var downloadUrl=baseUrl+'/terminal/termMerDownload';
                        window.open(downloadUrl,'_blank');
                    },

                        $scope.fileSelected = function(){
                            if(!$scope.file){
                                return;
                            }
                            var fileType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
                            if(fileType != 'xls' && fileType != 'xlsx'){
                                dialogService.alertInfo("error", $scope.terminalUploadHtmLan.resultMsgError1);
                                $scope.file = null;
                                return;
                            }
                        },
                        $scope.import = function(){
                            $scope.anguloader.show = false;
                            $scope.doingClose = true;
                            if($scope.file ==  null ){
                            	dialogService.alertInfo("error", $scope.terminalUploadHtmLan.resultMsgError1);
                                $scope.file = null;
                                return ;
                            }
                            var picType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
                            if(picType != 'xlsx' && picType != 'xls'){
                            	dialogService.alertInfo("error", $scope.terminalUploadHtmLan.resultMsgError1);
                                $scope.file = null;
                                return;
                            }
                            var url = baseUrl+'/terminal/uploadTermMerFile'
                            $scope.$watch('anguloader.show+doingClose',function(){
                                if(!$scope.anguloader.show && !$scope.doingClose){
                                    $scope.anguloader.show = true;
                                }
                            });
                            Upload.upload({
                                url:  url,
                                data: {file: $scope.file},
                                method: 'POST',
                                headers: securityService.getHeader()
                            }).then(function (resp) {
                                $scope.list = "";
                                for(var i = 0; i < resp.data.data.length;i++){
                                    $scope.list = $scope.list + resp.data.data[i];
                                }
                                $scope.anguloader.show = false;
                                $scope.doingClose = true;
                                if($scope.list == null || $scope.list == ""){
                                	dialogService.alertInfo("error", $scope.terminalUploadHtmLan.resultMsgError2);
                                    return "";
                                }else{
                                    //dialogService.alertInfo("success", "导入终端数据成功");
                                }
                            }, function (resp) {
                            	dialogService.alertInfo("error", $scope.terminalUploadHtmLan.resultMsgError2);
                            });
                        }

                },
                closeByDocument: false
            });
        };
              $scope.exportExcel=function() {
                  dialogService.openConfirm($scope.terminalExportLan.comfirmMsg,
                  function(){
                      dialogService.closeConfirm();
                      dialogService.alertInfo("success", "Exporting now, please wait a minute...");
            	  var domain = window.location.href.split("#")[0];
                  var baseUrl=domain.substr(0,domain.lastIndexOf("/"));
                  var downloadUrl = "";
                  var termSeq = $scope.condition.termSeq;
                  var termNo = $scope.condition.termNo;
                  var payMerchantNo = $scope.condition.payMerchantNo;
                  var insId = $scope.condition.insId;
                  var termMfrId = $scope.condition.termMfrId;
                  var terminalGroupId = $scope.condition.terminalGroupId;
                  var termTypeCode = $scope.condition.termTypeCode;
                  var dccSupFlag = $scope.condition.dccSupFlag;
                  var cupConnMode = $scope.condition.cupConnMode;
                  var bussType = $scope.condition.bussType;
                  var remark = $scope.condition.remark;
                  var activateType = $scope.condition.activateType;
                  if(termSeq == undefined){
                	  termSeq = "";
                  }
                  if(insId == undefined){
                	  insId = "";
                  }
                  if(termMfrId == undefined){
                	  termMfrId = "";
                  }
                  if(terminalGroupId == undefined){
                	  terminalGroupId = "";
                  }
                  if(termTypeCode == undefined){
                	  termTypeCode = "";
                  }
                  if(termNo == undefined){
                      termNo = "";
                  }
                  if(payMerchantNo == undefined){
                      payMerchantNo = "";
                  }
                  if(dccSupFlag == undefined){
                      dccSupFlag = "";
                  }
                  if(cupConnMode == undefined){
                      cupConnMode = "";
                  }
                  if(bussType == undefined){
                      bussType = "";
                  }
                  if(activateType == undefined){
                      activateType = "";
                  }
                  if(remark == undefined){
                      remark = "";
                  }
                  var str="termSeq="+termSeq+"&insId="+insId+"&termMfrId="+termMfrId+"&terminalGroupId="+
                      terminalGroupId+"&termTypeCode="+termTypeCode+"&termNo="+termNo+"&payMerchantNo="+payMerchantNo+"&remark="+remark
                      +"&dccSupFlag="+dccSupFlag+"&activateType="+activateType+"&cupConnMode="+cupConnMode+"&bussType="+bussType;
                  downloadUrl=baseUrl+'/excelExport/exportTermInfo/'+str;
                  var iframe = document.createElement("iframe");
                  document.body.appendChild(iframe);  
                  iframe.src = downloadUrl;  
                  iframe.style.display = "none";
              })};
              $scope.changeLanguage = function (langKey) {
     	         $translate.use(langKey);
     	    }; 
              gridService.setPlaceholder();
//    	});
    	});
    };
});