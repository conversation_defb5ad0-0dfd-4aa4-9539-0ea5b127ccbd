<div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div class="info-container">
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name">{{terminalSysLan.osVer}}</td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.osVer"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.safeModVer}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.safeModVer"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.tmsSDK}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.tmsSDK"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.paySDK}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.paySDK"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.tmsAppVersion}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.tmsAppVersion"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double" style="border-bottom-left-radius: 0;border-bottom-right-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.blueTooth}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.blueTooth"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.androidVer}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.androidVer"></span>
                        </td>
                    </tr>


                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.commParaVersion}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.commParaVersion"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.emvVer}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.emvVer"></span>
                        </td>
                    </tr>
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.tmsAppVersionOutSide}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.tmsAppVersionOutSide"></span>
                        </td>
                    </tr>

                </table>

                <table class="info-table" style="max-width: 1060px;clear: both;width: calc(100% - 1px);border-top: 0;border-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name"> {{terminalSysLan.payApp}} </td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.payApp"></span>
                        </td>
                    </tr>
                </table>
                <table class="info-table info-table-short double" style="border-top: 0;border-left-width:1px;border-radius: 0;">
                    <tr class="info-table-row">
                        <td class="info-table-name">{{terminalSysLan.networkType}}</td>

                        <td class="info-table-value">
                            <span ng-bind="terminalSys.networkType"></span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>


