{"list": {"termSeq": "Serial No.", "termNo": "Terminal No.", "payMerchantNo": "Merchant No.", "activateType": "Terminal Type", "insName": "Orginzation", "groupName": "Group", "termMfrName": "Manufacturer", "termTypeName": "Model", "dccSupFlag": "Dcc Flag", "cupConnMode": "UnionPay Model", "bussType": "Business Type", "confirmMsg": "Confirm to export terminal information?", "exportMsg": "Exporting now, please wait a minute", "many": "Current query conditions are too many", "reset": "Reset", "query": "Query", "add": "Add"}, "html": {"termSeq": "Serial No.", "termNo": "Terminal No.", "merchantNo": "Merchant No.", "activateType": "Terminal Type", "insName": "Orginzation", "groupName": "Group", "termMfrName": "Manufacturer", "termTypeName": "Model", "dccSupFlag": "Dcc Flag", "cupConnMode": "UnionPay Model", "bussType": "Business Type", "appName": "App Name", "appCode": "App Code", "version": "Version", "innerVersion": "InnerVersion", "osVer": "Os Version", "safeModVer": "safe Mod Version", "androidVer": "Android Version", "tmsSdk": "Tms SDK", "paySdk": "Pay SDK", "payAppCode": "Pay App Code", "payAppVersion": "Pay App Version", "equal": "Equal", "notEqual": "Not Equal", "match": "Match", "download": "Export", "add": "Add", "reset": "Reset", "query": "Query", "basic": "Basic"}}