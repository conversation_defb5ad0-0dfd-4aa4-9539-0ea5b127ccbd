'use strict';
define(['js/app'], function (app) {
    app.factory("terminalService", function (BaseApi) {
        var termInftData = {};
        var address ="";
        var map,tool;
        return {
        	getTermInfList: function (data, success) {
                BaseApi.get("/terminal", data, success);
            },
            setTermInftDataData: function (termInft) {
            	termInftData = termInft;
            },
            getTermInftDataData: function () {
                return termInftData;
            },
            getTerminalView: function (id, success) {
                BaseApi.get("/terminal/:id", {
                	id: id
                }, success);
            },
            uploadFile: function (data, success) {
            	var file ={
            		name:data.name	
            	}
                BaseApi.post("/terminal/upload", file, success);
            },
            reContactBinding: function (id,success) {
                BaseApi["delete"]("/terminal/reContactBinding?id=" + id, success);
            },
            getOtpPwd:function(termSeq,success){
            	 BaseApi.get("/terminal/getOtpPwd/:termSeq", {
                 	termSeq: termSeq
                 }, success);
            },
            getSystemView : function(id,success){
            	 BaseApi.get("/terminal/sysDetail/:id", {
                  	id:id
                }, success);
            },
            basicApp:function(id,success){
	           	 BaseApi.get("/terminal/sysDetail/:id", {
	               	id:id
	             }, success);
            },
         	updateTerminal: function (data, success) {
         		BaseApi.patch("/terminal", data, success);
         	},
         	testAuto:function(data,success){
         		BaseApi.patch("/terminal/testAuto", data, success);
         	},
         	queryMerchantInfo : function(id,success){
         		BaseApi.get("/merchant/queryMerchantInfo/:id", {
                 	id:id
               }, success);
           },
         	getTerminalCurrentPosition:function(id,success){
         		BaseApi.get("/terminal/getTerminalCurrentPosition/:id", {
                  	id:id
                }, success);
           },
           getTermPosiByTermSeq:function(termSeq,success){
        		BaseApi.get("/terminal/getTermPosiByTermSeq/:termSeq", {
        			termSeq:termSeq
               }, success);
           },
           getTermPositionByTermSeq:function(termSeq,success){
       		BaseApi.get("/terminal/getTermPositionByTermSeq/:termSeq", {
       			termSeq:termSeq
              }, success);
           },
           getCircleByTermSeq:function(termSeq,success){
       		BaseApi.get("/terminal/getCircleByTermSeq/:termSeq", {
       			termSeq:termSeq
              }, success);
          },
           getCheckTerminalPosition:function(termSeq,success){
        		BaseApi.query("/terminal/getCheckTerminalPosition/:termSeq", {
        			termSeq:termSeq
               }, success);
           },
           getActivatePosition:function(id,success){
        		BaseApi.get("/terminal/getTerminalActPosition/:id", {
                 	id:id
               }, success);
           },
           //根据经纬度获取标记点
            getMakerByLogLat:function(map,lnglatXY,judge,ima,data){
                    var geocoder = new AMap.Geocoder({
                        radius: 1000,
                        extensions: "all",
                        level:11
                    });        
                    geocoder.getAddress(lnglatXY, function(status, result) {
                        if (status === 'complete' && result.info === 'OK') {
                        	  var address = result.regeocode.formattedAddress; //返回地址描述
          					  var provinceCheck = result.regeocode.addressComponent.province;
          					  var cityCheck = result.regeocode.addressComponent.city;
          					  var countyCheck = result.regeocode.addressComponent.district;
                        	  var marker = new AMap.Marker({  //加点
                                  map: map,
                                  icon: new AMap.Icon({
                                      size:new AMap.Size(37,45),//图标大小  
                                      image:ima
                                  }),
                                  offset: new AMap.Pixel(-13,-38),
                                  position: lnglatXY,
                                  draggable: judge,
                                  cursor: 'move',
                                  raiseOnDrag: true
                              });
                              var infoWindow = new AMap.InfoWindow({
                                  content: address,
                                  offset: {x: 0, y: -30}
                              });
                              marker.on("mouseover", function(e) {
                                  infoWindow.open(map, marker.getPosition());
                              });
                              marker.on("mouseout", function(e) {
                                  infoWindow.close	(map, marker.getPosition());
                              });
                        
                              new AMap.event.addListener(marker, 'dragend', function(e){
                                  	  var geocoder = new AMap.Geocoder({
                                            radius: 1000,
                                            extensions: "all"
                                        });   
                                  	  var lat = e.lnglat.lat;
                                      var lng = e.lnglat.lng;
                                      lnglatXY =[lng,lat];
                                  	  geocoder.getAddress(lnglatXY, function(status, result) {
                                            if (status === 'complete' && result.info === 'OK') {
                                            		  address = result.regeocode.formattedAddress; //返回地址描述
                                          	  		  provinceCheck = result.regeocode.addressComponent.province;
                                          	  		  cityCheck = result.regeocode.addressComponent.city;
                                          	  		  countyCheck = result.regeocode.addressComponent.district;
                                                	  var lat = e.lnglat.lat;
                                                      var lng = e.lnglat.lng;
                                                      marker.setPosition(new AMap.LngLat(lng,lat));
                                                      infoWindow.close();
                                                       infoWindow = new AMap.InfoWindow({
                                                          content: address,
                                                          offset: {x: 0, y: -30}
                                                      });
                                                      marker.on("mouseover", function(e) {
                                                          infoWindow.open(map, marker.getPosition());
                                                      });
                                                      marker.on("mouseout", function(e) {
                                                          infoWindow.close	(map, marker.getPosition());
                                                      });
                                            }
                                        })
                            	 
                                });
                        }
                    }); 
            },
            operBMap: function (data) {
            	   var lnglatXY =[data.longitude,data.latitude];
            	   var obj = this;
                   var geocoder = new AMap.Geocoder({
                       radius: 1000, //范围，默认：500
                       level:11
                   });
	               geocoder.getAddress(lnglatXY, function(status, result) {
                       if (status === 'complete' && result.info === 'OK') {
                     	  map = new AMap.Map('container', {
                   	  		  isHotspot: true,
                   	  		  resizeEnable: true
                   	  	  });
                     	  map.plugin(["AMap.ToolBar"],function(){
                      	        //加载工具条
                      	        tool = new AMap.ToolBar({
                      	            //初始化定义配置
                      	            direction:true,//隐藏方向导航
                      	            ruler:true//隐藏视野级别控制尺
                      	        });
                      	        map.addControl(tool);
                      	  });
                     	  map.setZoomAndCenter(14, [data.longitude,data.latitude]);
                          obj.getMakerByLogLat(map,lnglatXY,false,'images/pic_map_position.png',data);
                       }
                   })
            },
            getAddress: function (data,success) {
         	    var lnglatXY =[data.longitude,data.latitude];
         	    var obj = this;
                var geocoder = new AMap.Geocoder({
                    radius: 1000, //范围，默认：500
                    level:11
                });
	            geocoder.getAddress(lnglatXY, function(status, result) {
                    if (status === 'complete' && result.info === 'OK') {
                  	  	var map = new AMap.Map('container', {
                	  		  isHotspot: true,
                	  		  resizeEnable: true
                	  	  });
                  	address = result.regeocode.formattedAddress; //返回地址描述
                    }
                })
                
            },
        };
    });
});