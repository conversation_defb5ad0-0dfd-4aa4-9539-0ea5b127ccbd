'use strict';
define(function() {
	return function($scope, terminalService, $state, dialogService,
			gridService, $stateParams, $http, BaseApi,$timeout,toolsService) {
		$scope.$parent.thirdTitle="System info";
		$scope.terminalSysLan = {};
		$http.get("modules/terminal/terminal/i18n/en/terminalSystem.json").success(function(dataLan) {
		$scope.terminalSysLan = dataLan.sysInfo;
		terminalService.getSystemView($stateParams.id, function (data) {
            $scope.terminalSys = data;
            if(data.payAppCode == undefined || data.payAppCode ==""){
                return;
            }
            if(data.networkType == "1"){
                $scope.terminalSys.networkType = "Public";
            }else if(data.networkType == "2"){
                $scope.terminalSys.networkType = "Private";
            }else{
                $scope.terminalSys.networkType = "Unknow";
            }
            $scope.terminalSys.payApp = data.payAppCode+"("+data.payAppName+") "
                +data.payAppVersionOutSide +"("+data.payAppVersion+") ";
        });
		});
	};
});