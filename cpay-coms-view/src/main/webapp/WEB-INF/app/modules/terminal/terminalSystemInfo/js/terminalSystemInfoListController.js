'use strict';
define(function () {
    return function ($scope, terminalSystemInfoService, toolsService, $state, dialogService, gridService, $http, BaseApi) {
        $scope.$parent.thirdTitle = "";
        $http.get("modules/terminal/terminalSystemInfo/i18n/en/terminalSystemInfoList.json").success(function (dataLan) {
            $scope.sysInfo = dataLan.sysInfo;
            gridService.initTable({
                url: "/terminalSystemInfo",
                scope: $scope,
                operator: false,
                columns: [{
                    field: 'state',
                    checkbox: 'true'
                },
                    {
                        field: 'termSeq',
                        title: $scope.sysInfo.termSeq
                    },
                    {
                        field: 'osVer',
                        title: $scope.sysInfo.osVer
                    },
                    {
                        field: 'safeModVer',
                        title: $scope.sysInfo.safeModVer
                    },
                    {
                        field: 'androidVer',
                        title: $scope.sysInfo.androidVer
                    },
                    {
                        field: 'tmsSDK',
                        title: $scope.sysInfo.tmsSDK
                    },
                    {
                        field: 'paySDK',
                        title: $scope.sysInfo.paySDK
                    },
                    {
                        field: 'emvVer',
                        title: $scope.sysInfo.emvVer
                    },
                    {
                        field: 'payAppCode',
                        title: $scope.sysInfo.payAppCode
                    },
                    {
                        field: 'payAppName',
                        title: $scope.sysInfo.payAppName
                    },
                    {
                        field: 'payAppVersion',
                        title: $scope.sysInfo.payAppVersion
                    },
                    {
                        field: 'payAppVersionOutSide',
                        title: $scope.sysInfo.payAppVersionOutSide
                    },
                    {
                        field: 'tmsAppVersion',
                        title: $scope.sysInfo.tmsAppVersion
                    },
                    {
                        field: 'tmsAppVersionOutSide',
                        title: $scope.sysInfo.tmsAppVersionOutSide
                    },
                    {
                        field: 'networkType',
                        title: $scope.sysInfo.networkType
                    },
                    {
                        field: 'updateTime',
                        title: $scope.sysInfo.modifyTime,
                        formatter: function (value) {
                            return toolsService.getFormatTime(value);
                        },
                        width: 180
                    }
                ]
            });
            $scope.search = function () {
                gridService.search($scope.condition);
            };
            $scope.reset = function () {
                $scope.condition = {};
                gridService.search();
            };
            $scope.exportExcel = function () {
                dialogService.openConfirm($scope.sysInfo.confirmMsg1, function () {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", $scope.sysInfo.confirmMsg2);
                    var termSeq = $scope.condition.termSeq;
                    if (termSeq == undefined || termSeq == null) {
                        termSeq = "";
                    }
                    var osVer = $scope.condition.osVer;
                    if (osVer == undefined || osVer == null) {
                        osVer = "";
                    }
                    var safeModVer = $scope.condition.safeModVer;
                    if (safeModVer == undefined || safeModVer == null) {
                        safeModVer = "";
                    }
                    var androidVer = $scope.condition.androidVer;
                    if (androidVer == undefined || androidVer == null) {
                        androidVer = "";
                    }
                    var tmsSDK = $scope.condition.tmsSDK;
                    if (tmsSDK == undefined || tmsSDK == null) {
                        tmsSDK = "";
                    }
                    var paySDK = $scope.condition.paySDK;
                    if (paySDK == undefined || androidVer == null) {
                        paySDK = "";
                    }
                    var emvVer = $scope.condition.emvVer;
                    if (emvVer == undefined || emvVer == null) {
                        emvVer = "";
                    }
                    var payAppCode = $scope.condition.payAppCode;
                    if (payAppCode == undefined || payAppCode == null) {
                        payAppCode = "";
                    }
                    var payAppVersion = $scope.condition.payAppVersion;
                    if (payAppVersion == undefined || payAppVersion == null) {
                        payAppVersion = "";
                    }
                    var tmsAppVersion = $scope.condition.tmsAppVersion;
                    if (tmsAppVersion == undefined || tmsAppVersion == null) {
                        tmsAppVersion = "";
                    }
                    var networkType = $scope.condition.networkType;
                    if (networkType == undefined || networkType == null) {
                        networkType = "";
                    }

                    var str = "termSeq=" + termSeq + "&osVer=" + osVer + "&androidVer=" + androidVer + "&safeModVer=" + safeModVer
                        + "&tmsSDK=" + tmsSDK + "&paySDK=" + paySDK + "&emvVer=" + emvVer + "&payAppCode="
                        + payAppCode + "&payAppVersion=" + payAppVersion + "&tmsAppVersion=" + tmsAppVersion + "&networkType=" + networkType;
                    str = str.replace(/\./g, "^");
                    var domain = window.location.href.split("#")[0];
                    var baseUrl = domain.substr(0, domain.lastIndexOf("/"));
                    var downloadUrl = baseUrl + '/excelExport/exportTerminalSystemInfo/' + str;
                    var iframe = document.createElement("iframe");
                    document.body.appendChild(iframe);
                    iframe.src = downloadUrl;
                    iframe.style.display = "none";
                })
            };
            gridService.setPlaceholder();
        });
    };
});