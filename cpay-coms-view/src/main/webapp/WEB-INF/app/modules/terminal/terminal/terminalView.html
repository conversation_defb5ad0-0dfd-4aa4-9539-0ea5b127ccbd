<div class="container-fluid" id="breadcrumb-scroll">
	<div class="row-fluid">
		<div class="nav-breadcrumb">
			<div class="breadcrumb-icon"
				style="background-image: url(images/default/menu/pic_menu_setting_grey.png)"></div>
			<ul class="breadcrumb">
				<li><span class="navigation_home ng-binding">Terminal management</span>
					<i class="icon-angle-right"></i></li>
				<li><a ng-class="{active:!thirdTitle}" class="active"
					ui-sref="home.terminal.list">Terminal info management</a></li>
				<li class="active ng-binding""><i class="icon-angle-right"
					ng-hide" ng-show="thirdTitle"></i>{{thirdTitle}}</li>
			</ul>
		</div>
		<div class="hr"></div>
	</div>
</div>
<div class="content-scroller">
	<div class="content-main">
		<div class="container-fluid">
			<div class="row-fluid">
				<div class="span12">
					<ul class="nav nav-tab-head" style="margin-left: 20px;">
						<li ng-class="{active:$state.is('home.termview.info')}"><a
							ng-click="terminalView();" style="cursor: pointer;">basic info</a>
						</li>
						<li ng-class="{active:$state.is('home.termview.sysVersion')}">
							<a ng-click="systemVersion();" style="cursor: pointer;">system info</a>
						</li>
						<li ng-class="{active:$state.is('home.termview.basicApp')}">
							<a ng-click="basicApp();" style="cursor: pointer;">app info</a>
						</li>
						<!--<li ng-class="{active:$state.is('home.termview.appView')}"><a
							ng-click="appView();" style="cursor: pointer;">普通应用</a>
						</li>-->
						<!-- <li ng-class="{active:$state.is('home.termview.termFlowCountDay')}"><a
								ng-click="flowCurMonth();" style="cursor: pointer;">当月流量</a>
						</li> -->
						<li ng-class="{active:$state.is('home.termview.switchrecord')}">
							<a ng-click="switchrecord();" style="cursor: pointer;">switch record</a>
						</li>
					</ul>
				</div>
			</div>
			<div ui-view class="nav-tab-contetnt"></div>
		</div>
	</div>
</div>




