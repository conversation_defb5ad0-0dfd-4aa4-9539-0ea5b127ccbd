'use strict';
define(function () {
    return function ($scope, terminalCertPushService,$state,$stateParams, dialogService, formService,BaseApi,factoryService,organizationService,terminalGroupService,gridService) {
    	 $scope.$parent.thirdTitle = "任务修改";
    	 var parentScope =$scope ;
    	 terminalCertPushService.selectByPrimaryKey($stateParams.id, function (data) {
             $scope.driverJob = data;
             
                     
            if(data.collection){
	            var collections = data.collection.split(",");
	            var selectObj = angular.element("#tout_acct_list");
	            for(var i = 0;i < collections.length;i++) {
	        		selectObj.append("<option value='"+collections[i]+"'>"
	                    		+collections[i]+"</option>");
	        	}
        	}
            
         });
         BaseApi.query("/driverJob/app",{}, function (data) {
         	
   		 	$scope.driverList = data;
   	 	});
        
 	   
        BaseApi.query("/dict/type/messionType",{}, function (data) {
        	
  		 	$scope.releaseTypeList = data;
  	 	});
   
         $scope.cleanTermSeqCollection = function(){
         	$scope.driverJob.collection = "";
         }
         $scope.showTermSeq = function(){
         	
             dialogService.openDialog({
                 template: "modules/taskScheduling/driverJob/showTermSeqList.html",
                 width: '60%',
                 controller: function ($scope,dialogService,toolsService,BaseApi,$timeout) {
               	   $scope.app={};
               	   $scope.condition={};
               	   $scope.condition.insId = parentScope.insId;
               	   $scope.serachTermInfo = function () {
               	   $scope.condition.termTypeCodes = parentScope.termTypeCodes;
             	   gridService.search($scope.condition);
                    };
              	   $timeout(function(){
              		   gridService.initTable({
              			   	 url:"/terminal?termStatus=1&merchantStatus=1",
                             scope: $scope,
                             operator:false,
                             uniqueId: 'id',
 							    columns: [
                               {field: 'state',checkbox: 'true'},
                               {field: "termSeq", title: "终端序列号"},
                               {field: "insName", title: "机构"},
                               {field: "termMfrName", title: "终端厂商"},
                               {field: "termTypeName", title: "终端型号"},
                               {field: "merchantName", title: "商户名称"}
                            ]
                         });
              	    },100)
              	    $scope.serachTermInfo = function () {
              		   gridService.search($scope.condition);
                     };
                     $scope.resetTermInfo = function () {
               		   $scope.condition={};
               		   gridService.search();
                     };
                     $scope.openOrganization = function () {
                         organizationService.openOrganization(function (value) {
                             if (!$scope.condition) {
                                 $scope.condition = {};
                             }
                             $scope.condition.insId = value.id;
                             $scope.condition.insName = value.name;
                         });
                     };
                     $scope.ok = function () {
                		    var rows= $("#table").bootstrapTable("getSelections");
                		     if (rows.length == 0) {
                              dialogService.alertInfo("info", "请选择记录！");
                              return;
                          }
                		  var ids = "";
                  	 if(angular.element("#tout_acct_list option").size()==0){
                  		 for (var index in rows) {
                  		 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                           		+rows[index].termSeq +"</option>");
                  		 }
                         
                  	 }else{
                  		 for (var index in rows) {
                  		 var j =0;
                  		 angular.element("#tout_acct_list option").each(function () {
                  			 if(rows[index].termSeq==$(this).val()){
                  				 return false;
                  			 }
                  			 j++;
                  	       });
                      		 if(j==angular.element("#tout_acct_list option").size()){
                      			 angular.element("#tout_acct_list").append("<option value='"+rows[index].termSeq +"'>"
                                    		+rows[index].termSeq +"</option>");
                      		 }
                  		 }
                  	 }
                  	  
                  	  $scope.closeThisDialog(0);
                  
                };
                 }
             });
         }
         
         
         
         $scope.openInsDialog = function () {
             organizationService.openOrganization(function (value) {
                 if (!$scope.driverJob) {
                     $scope.driverJob = {};
                 }
                 $scope.driverJob.insId = value.id;
                 $scope.driverJob.insName = value.name;
                 BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
     	   		 	$scope.terminalGroupList = data;
     	   	 	});
             });
         };
         $scope.form = formService.form(function () {
        	 if($scope.driverJob.releaseTime > $scope.driverJob.validDate) {
	        		dialogService.alertInfo("info", "起始日期不能大于截至日期");
	        		return;
	        	}
	    	 if($scope.driverJob.releaseType==1){
	    		 if($scope.driverJob.insName == "" || $scope.driverJob.insName== null){
	    			 dialogService.alertInfo("error", "请选择机构名称");
	    			 return ;
	    		 }
	    	 }
	    	 if($scope.driverJob.releaseType==2){
	         	var collections ="";
	        	var selectObj = angular.element("#tout_acct_list").get(0);
	        	var options = selectObj.options;
	        	if(options.length == 0) {
	        		dialogService.alertInfo("info", "终端集合不能为空！");
	                return;
	        	}
	        	for(var i = 0;i < options.length;i++) {
	        		collections += options[i].text+",";
	        	}
	        	$scope.driverJob.collection = collections.substring(0,collections.length-1);
	    	 }
	    	 terminalCertPushService.updateDriverJob($scope.driverJob, function () {
                 dialogService.alertInfo("success", "The driver task was successfully modified!！");
                 $state.go("home.terminalCertPush.list");
             });
         });
         $scope.deleteTerm = function () {
         	var oSelect = angular.element("#tout_acct_list");
         	var options = oSelect.option;
         	var termSeqs = $("#tout_acct_list option:selected").val();
         	if(termSeqs == undefined || termSeqs == null || termSeqs == ""){
         		dialogService.alertInfo("success", "Please select at least one record!");
         		return ;
         	}
 			$("#tout_acct_list option:selected").remove();
         }
         $scope.clearTerm = function () {
        	 	var selectObj = angular.element("#tout_acct_list").get(0);
	         	var options = selectObj.options;
	         	if(options.length == 0) {
	         		dialogService.alertInfo("success", "There is currently no terminal information that can be emptied!");
          		return ;
	         }
        	 dialogService.openConfirm("Are you sure you want to clear the data of the current terminal set?", function () {
        		 $("#tout_acct_list option").remove();
        		 dialogService.closeConfirm();
        	 });
         } 
    }
});