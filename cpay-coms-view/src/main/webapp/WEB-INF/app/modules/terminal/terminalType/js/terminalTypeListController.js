'use strict';
define(function () {
    return function ($scope, terminalTypeService, $state,dialogService, gridService,$http,BaseApi) {
        $scope.$parent.thirdTitle = "";
        $http.get("modules/terminal/terminalType/i18n/en/terminalTypeList.json").success(function(dataLan) {
        $scope.terminalType = dataLan.list;
        //厂商列表
        BaseApi.query("/factory/type", {}, function (data) {
            $scope.factoryList = data;
        });
        gridService.initTable({
            url: "/terminalType",
            scope: $scope,
            update:'terminaltype_edit',
            deletes:'terminaltype_del',
            singleSelect:true,
            columns: [
            {
                field: 'code',
                title: $scope.terminalType.code
            }, {
                field: 'name',
                title: $scope.terminalType.name
            }, {
                field: 'termMfrName',
                title: $scope.terminalType.termMfrName
            }, {
                field: 'termType',
                title: $scope.terminalType.isStart,
                formatter: function (value) {
                    if (value == 1) {
                        return "running";
                    }else if (value == 2){
                        return "not running";
                    }
                }
            }/*,

            {
                field: 'remark',
                title: "备注"
            }*/]
        });
        $scope.deleteRow = function (index) {
            var row = gridService.getRow(index);
            dialogService.openConfirm(dataLan.deleteRow.confirmMsg+"“" + row.code + "”？", function (confirm) {
            	terminalTypeService.deleteTerminalType(row.id, function (data) {
                    dialogService.closeConfirm();
                    dialogService.alertInfo("success", dataLan.deleteRow.successMsg);
                    gridService.refresh();
                });
            });
        };
        $scope.search = function () {
            gridService.search($scope.condition);
        };
        $scope.updateRow = function (index) {
            var row = gridService.getRow(index);
            $state.go('home.terminalType.update', {id: row.id});
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        });
        gridService.setPlaceholder();
    }
    
});