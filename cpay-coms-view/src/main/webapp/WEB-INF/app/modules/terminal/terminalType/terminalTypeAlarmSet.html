<!-- <div class="container-fluid">
    <div class="row-fluid">
        <div class="span12">
            <div>
                {{alarmHtml.chooseRank}}：
            <select style="width: 150px;height: 35px;" id="rank" ng-change="changeRank(x);" ng-model="x">
                <option value="1" selected="selected">{{alarmHtml.oneAlarm}}</option>
                <option value="2">{{alarmHtml.twoAlarm}}</option>
                <option value="3">{{alarmHtml.threeAlarm}}</option>
            </select>
             <button class="button-default" type="button" ng-click="setAlarmRank();">
                    <i class="icon-edit"></i>{{alarmHtml.SetAlarmLevel}}
                </button>
            </div>
            <div id="tools">
               <button class="button-default" type="button" ng-click="selectRows();">
                    <i class="icon-ok"></i>{{alarmHtml.configureAlarmInfo}}
                </button>
            </div>
        </div>
    </div>
    <div  class="row-fluid">
        <div class="span6">
            <table id="table1"></table>
        </div>
        <div class="span6">
            <table id="table2"></table>
        </div>
    </div>
</div>

</div>
 -->