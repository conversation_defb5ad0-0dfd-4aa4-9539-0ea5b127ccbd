{"terminalList": {"termSeq": "Serial No.", "termNo": "Terminal No.", "merchantNo": "Merchant No.", "insName": "Organization", "groupName": "Group", "termMfrName": "Manufacturer", "termTypeName": "Model", "type": "Terminal Type", "merchantName": "Merchant", "dccSupFlag": "Dcc Flag.", "cupConnMode": "Activation Code", "Unbundling": "Unbind", "Rebind": "Bind", "remark": "Remark", "timeZone": "Time Zone", "onlineStatus": "Status"}, "exportData": {"comfirmMsg": "Confirm that terminal information is to be exported?", "waitingMsg": "Exporting now, please wait a minute"}, "editData": {"successMsg": "Successful modification"}, "upload": {"dialogTitle": "Import Terminal Data", "resultMsgError1": "Please upload EXCEL file!", "resultMsgError2": "The imported file data is empty or please select a compatible file to import!", "resultMsgSuccess": "Import terminal data successfully!", "lable": "EXCEL File", "close": "Close", "uploadType": "Imported Data Type", "termSeq": "Serial No.", "TModel": "Model", "choose": "<PERSON><PERSON>", "upload": "Import"}, "list": {"termMfrName": "Manufacturer", "termTypeName": "Model", "choose": "<PERSON><PERSON>", "reset": "Reset", "query": "Search", "import": "Import", "Export": "Export", "adHQuery": "Return advanced query"}}