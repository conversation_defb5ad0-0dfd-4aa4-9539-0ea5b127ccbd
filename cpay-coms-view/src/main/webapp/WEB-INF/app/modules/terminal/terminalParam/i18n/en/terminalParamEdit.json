{"insInfoList": {"insInfo": "Organization info", "insName": "Organization name", "groupName": "Group name", "choose": "<PERSON><PERSON>"}, "baseInfoList": {"baseInfo": "Basic information", "insInfo": "Organization Info", "successMsg": "Success", "insName": "Organization name", "htIntvl": "Heartbeat interval time (second)", "htIntvlRetry": "Heart rate reconnection number", "upTimeout": "Upload Timeout time limit (second)", "reDownIntvl": "File re-download interval (minutes)", "upFlowIntvl": "Flow up time interval (minutes)", "upInfoIntvl": "Information upload interval (minutes)", "opePswd": "Operation pwd", "mapPswd": "Manage pwd", "broInval": "Broadcast interval time (second)", "FdInval": "File download interval(second)", "isWifi": "Communicate only with WIFI", "maxlength1": "Maximum input is 9 only", "maxlength2": "Maximum input is 99 only", "maxlength3": "Maximum input is 999 only", "maxlength4": "Input range should be between 10 and 9999", "maxlength5": "Input range should be between 600 and 99999", "maxlength6": "Enter only 8 digits", "maxlength7": "Maximum input 10 bits", "fileReDownNum": "Number of file downloads retries"}, "updateList": {"upSet": "Update Setting", "sysUpPower": "System updating power threshold(%)", "appUpPower": "App updating power threshold(%)", "sysWait": "System update idle time (seconds)", "appWait": "App update idle time (seconds)"}, "serverList": {"serverCon": "Server address configuration", "addUp": "Address modification switch", "tmsAdd1": "Tms server address", "tmsAdd2": "Tms server backups address  1", "tmsAdd3": "Tms server backups address  2", "appAdd": "App server address", "iotActivateUrl": "Iot Activate Url", "lockTerminalImgUrl": "Lock Terminal Img Url"}, "appList": {"appConf": "Application configuration", "gsh": "Format", "gshMsg": "The formatted line represents an application package name", "installWhiteListQ7": "Application white list", "format": "Separate each application package using commas"}, "copy": {"msg1": "Fill in at least one application to upload", "msg2": "Application information cannot be null", "msg3": "Application information cannot contain blank space", "copyMsg_success": "Add success!"}, "add": {"msg1": "Fill in at least one application to upload", "msg2": "Application information cannot be null", "msg3": "Application information cannot contain blank space", "addMsg_success": "Add success!"}, "edit": {"msg1": "Fill in at least one application to upload", "msg2": "Application information cannot be null", "msg3": "Application information cannot contain blank space", "editMsg_success": "Update success!"}, "serverQ7List": {"title": "Server configuration Only For Q7", "sysUpRequiresDisplay": "Always display", "sysUpRequiresVolume": "Standby strategy", "sysUpRequiresVolumeValue": "Volume level", "maxlength1": "The range of input is limited to 0 to 15"}}