<style>
	.titlediv {
		width: 100%;
		background: #ffffff;
		position: relative;
		margin-top: 10px;
		padding: 1px;
	}

	.titleh1 {
		font: 110% Arial, Helvetica, sans-serif;
		text-align: center;
		font-weight: bolder;
		display: block;
		color: #535353
	}
	.info-piece{
		margin-top: 10px;
		display: inline-block;
		margin-right: 20px;
	}
	.info-text-cont{
		display: inline-block;
		width: 200px;
	}
	.info-text-cont span{
		line-height: 30px;
		font-size: 14px;
		padding-left: 7px;
	}
	.info-text-cont input{
		width: 160px;
	}
	.info-piece a:hover{
		text-decoration: none;
	}
</style>
<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{insInfoList.insInfo}}】</h1>
					<span
						style='position: absolute; right: 10px; top: 8px; color: #666666; cursor: pointer;'
						ng-click="showOrHide(0)"><img src='{{baseinfospan}}'></span>
				</div>
				<div>
					<table class="table table-hovers">
						<tr>
							<td style="width: 15%; text-align: right;">{{insInfoList.insName}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="text" name="insName"
								ng-model="terminalParam.insName" readonly ng-disabled="disabled"
								validator="required" message-id="insId" style="width: 230px;" />
								<button class="button" type="button" ng-click="openInsDialog()"
									ng-disabled="disabled" ng-show="update==1">
									<i class="icon-large"></i>{{insInfoList.choose}}
								</button> <span id="insId" class="help-inline"></span></td>
							<td style="width: 15%; text-align: right;">{{insInfoList.groupName}}</td>
							<td style="width: 30%;"><select name="terminalGroupId"
								ng-model="terminalParam.terminalGroupId" ng-readonly="readOnly"
								ng-options="m.id as m.name for m in terminalGroupList"
								message-id="terminalGroupId" ng-disabled="update==2"
								style="width: 300px;">
									<option value="">{{insInfoList.groupName}}</option>
							</select> <span id="terminalGroupId" class="help-inline"></span></td>
						</tr>
						<!--<tr>
							<td style="width: 15%; text-align: right;border:0px">网络类别<span
								style='color: #e02222;border:0px'>*</span></td>
							<td style="width: 30%;border:0px"><select name="networkType"
								ng-model="terminalParam.networkType" ng-readonly="readOnly"
								ng-options="m.id as m.name for m in netWorkTypeList"
								message-id="networkType" ng-change="changeNetworkType(terminalParam.networkType)"
								style="width: 300px;border:0px">
									<option value="">请选择网络类别</option>
							</select> <span id="networkType" class="help-inline"></span></td>
						</tr>-->
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{baseInfoList.baseInfo}}】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 15%; text-align: right;">{{baseInfoList.htIntvl}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="number" name="htIntvl"
								ng-model="terminalParam.htIntvl" ng-disabled="disabled"
								validator="required,positiveInteger" message-id="htIntvl" min="600" max="99999" maxlength="5" placeholder="{{baseInfoList.maxlength5}}" /> <span id="htIntvl"
								class="help-inline"></span></td>
							<td style="width: 15%; text-align: right;">{{baseInfoList.htIntvlRetry}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="text" name="reHtIntvl"
								ng-model="terminalParam.reHtIntvl" ng-disabled="disabled"
								validator="required,positiveInteger" message-id="reHtIntvl"
								maxlength="1" placeholder="{{baseInfoList.maxlength1}}" /> 
								<span id="reHtIntvl" class="help-inline"></span></td>

						</tr>
						<tr style="border: 0px;">
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.upTimeout}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="fileTraTimeout" ng-model="terminalParam.fileTraTimeout"
								maxlength="3" placeholder="{{baseInfoList.maxlength3}}" ng-disabled="disabled"
								validator="required,positiveInteger" message-id="fileTraTimeout"> <span
								id="fileTraTimeout" class="help-inline"> </span></td>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.fileReDownNum}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="reDownNum" ng-model="terminalParam.reDownNum"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="reDownNum" maxlength="1" placeholder="{{baseInfoList.maxlength1}}" /> <span
								id="reDownNum" class="help-inline"></span></td>

						</tr>
<!--						<tr>-->
<!--							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.upFlowIntvl}}<span-->
<!--								style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%; border: 0px;"><input type="text"-->
<!--								name="upFlowIntvl" ng-model="terminalParam.upFlowIntvl"-->
<!--								ng-disabled="disabled" validator="required,positiveInteger"-->
<!--								message-id="upFlowIntvl" maxlength="4" placeholder="{{baseInfoList.maxlength4}}" />-->
<!--								<span id="upFlowIntvl" class="help-inline"></span></td>-->
<!--							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.upInfoIntvl}}<span-->
<!--								style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%; border: 0px;"><input type="text"-->
<!--								name="upInfoIntvl" ng-model="terminalParam.upInfoIntvl"-->
<!--								ng-disabled="disabled" validator="required,positiveInteger"-->
<!--								message-id="upInfoIntvl" maxlength="4" placeholder="{{baseInfoList.maxlength4}}" />-->
<!--								<span id="upInfoIntvl" class="help-inline"></span></td>-->

<!--						</tr>-->
						<tr>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.opePswd}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="mapPswd" ng-model="terminalParam.mapPswd"
								ng-disabled="disabled" validator="required,number"
								message-id="mapPswd" minlength="8" maxlength="8" placeholder="{{baseInfoList.maxlength6}}" /><span
								id="mapPswd" class="help-inline"></span></td>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.mapPswd}}
								<span style='color: #e02222'>*</span>
							</td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="managePwd" ng-model="terminalParam.managePwd"
								validator="required" ng-disabled="disabled"
								message-id="managePwd" maxlength="10" placeholder="{{baseInfoList.maxlength7}}" />
								<span id="managePwd" class="help-inline"></span></td>

						</tr>
						<tr>

							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.broInval}}<span style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="broadcastTime" ng-model="terminalParam.broadcastTime"
								ng-disabled="disabled" message-id="broadcastTime" maxlength="3"
								placeholder="{{baseInfoList.maxlength3}}" validator="required,positiveInteger"/> <span id="broadcastTime" class="help-inline"></span></td>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.FdInval}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text" name="fileDownloadHtval"
														   ng-model="terminalParam.fileDownloadHtval"
														   ng-disabled="disabled" message-id="fileDownloadHtval"
														   maxlength="2" placeholder="{{baseInfoList.maxlength2}}" validator="required,number"/>
								<span id="fileDownloadHtval" class="help-inline"></span></td>
						</tr>
						<tr>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.upInfoIntvl}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="number"
																		name="upInfoIntvl" ng-model="terminalParam.upInfoIntvl"
																		ng-disabled="disabled" validator="required,positiveInteger" min = "10" max="99999"
																		message-id="upInfoIntvl" maxlength="4" placeholder="{{baseInfoList.maxlength4}}" />
								<span id="upInfoIntvl" class="help-inline"></span></td>
							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.isWifi}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><label> <input
									name="switch-field-1"
									class="ace ace-switch" type="checkbox"
									ng-model="terminalParam.isWifi"> <span class="lbl"></span></label></td>
						</tr>
<!--						<tr>-->
<!--							<td style="width: 15%; text-align: right; border: 0px;">{{baseInfoList.DownloadUnderWifi}}<span-->
<!--									style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%; border: 0px;"><label> <input-->
<!--									name="isWifiDownload" class="ace ace-switch" type="checkbox"-->
<!--									ng-model="terminalParam.isWifiDownload"> <span class="lbl"></span></label></td>-->

<!--							<td style="width: 15%; text-align: right; border: 0px;"></td>-->
<!--							<td style="width: 30%; border: 0px;"></td>-->
<!--						</tr>-->
					</table>
				</div>
				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{updateList.upSet}}】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 15%; text-align: right;">{{updateList.sysUpPower}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="text"
								name="systemUpdateRequiredPower"
								ng-model="terminalParam.systemUpdateRequiredPower"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="systemUpdateRequiredPower" maxlength="2"
								placeholder="{{baseInfoList.maxlength2}}" /> <span id="systemUpdateRequiredPower"
								class="help-inline"></span></td>
							<td style="width: 15%; text-align: right;">{{updateList.appUpPower}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="text"
								name="appUpdateRequiredPower"
								ng-model="terminalParam.appUpdateRequiredPower"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="appUpdateRequiredPower" maxlength="2"
								placeholder="{{baseInfoList.maxlength2}}" /> <span id="appUpdateRequiredPower"
								class="help-inline"></span></td>
						</tr>
						<tr>
							<td style="width: 15%; text-align: right; border: 0px;">{{updateList.sysWait}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="sysUpRequiresNoOperTime"
								ng-model="terminalParam.sysUpRequiresNoOperTime"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="sysUpRequiresNoOperTime" maxlength="3"
								placeholder="{{baseInfoList.maxlength2}}" /> <span id="sysUpRequiresNoOperTime"
								class="help-inline"></span></td>
							<td style="width: 15%; text-align: right; border: 0px;">{{updateList.appWait}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%; border: 0px;"><input type="text"
								name="appUpRequiresNoOperTime"
								ng-model="terminalParam.appUpRequiresNoOperTime"
								ng-disabled="disabled" validator="required,positiveInteger"
								message-id="appUpRequiresNoOperTime" maxlength="3"
								placeholder="{{baseInfoList.maxlength3}}" /> <span id="appUpRequiresNoOperTime"
								class="help-inline"></span></td>
						</tr>
					</table>
				</div>

<!--				<div class='titlediv'>-->
<!--					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{locationList.locationConfig}}】</h1>-->
<!--				</div>-->
<!--				<div>-->
<!--					<table class="table table-hover">-->
<!--						<tr>-->
<!--							<td style="width: 15%; text-align: right;">{{locationList.enableLocation}}<span-->
<!--									style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%;"><label><input-->
<!--									name="enableLocation" class="ace ace-switch" type="checkbox"-->
<!--									ng-model="terminalParam.enableLocation"> <span class="lbl"></span></label></td>-->

<!--							<td style="width: 15%; text-align: right;">{{locationList.locatingInterval}}<span-->
<!--									style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%;">-->
<!--								<input type="text" name="locatingInterval"-->
<!--									   ng-model="terminalParam.locatingInterval"-->
<!--									   ng-readonly="!terminalParam.enableLocation"-->
<!--									   ng-disabled="!terminalParam.enableLocation"-->
<!--									   placeholder="{{baseInfoList.maxlength2}}">-->
<!--							</td>-->
<!--						</tr>-->
<!--					</table>-->
<!--				</div>-->

				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{serverList.serverCon}}】</h1>
				</div>
				<div>
					<table class="table table-hover" >
						<tr>
							<td style="width: 15%; text-align: right;">{{serverList.addUp}}<span
								style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><label> <input
									name="switch-field-1" class="ace ace-switch" type="checkbox"
									ng-model="urlReadOnly"> <span class="lbl"></span></label></td>

							<td style="width: 15%; text-align: right;"></td>
							<td style="width: 30%;"></td>
						</tr>
						<tr>
							<td style="width: 15%; text-align: right;border: 0px;">{{serverList.tmsAdd1}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;border: 0px;"><input type="text"
								   ng-readonly="!urlReadOnly" name="tmsDomainName"
								   ng-model="terminalParam.tmsDomainName" ng-disabled="disabled"
								   validator="required" message-id="tmsDomainName" maxlength="100"
								   placeholder="" />
							<!--<td style="width: 15%; text-align: right;">【{{serverList.tmsAdd2}}】<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;"><input type="text"
								ng-readonly="!urlReadOnly" name="tmsDomainNameBakFirst"
								ng-model="terminalParam.tmsDomainNameBakFirst" ng-disabled="disabled"
								validator="required" message-id="tmsDomainNameBakFirst" maxlength="50"
								placeholder="" /> <span id="tmsDomainNameBakFirst" class="help-inline"></span></td>
-->
							<td style="width: 15%; text-align: right;border: 0px;">{{serverList.appAdd}}<span
									style='color: #e02222'>*</span></td>
							<td style="width: 30%;border: 0px;"><input type="text"
							   ng-readonly="!urlReadOnly" name="amsDomainName"
							   ng-model="terminalParam.amsDomainName" ng-disabled="disabled"
							   validator="required" message-id="amsDomainName" maxlength="100"
							   placeholder="" /> <span id="amsDomainName" class="help-inline"></span></td>
						</tr>
<!--						<tr>-->
<!--							<td style="width: 15%; text-align: right;">【{{serverList.iotActivateUrl}}】<span-->
<!--									style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%;"><input type="text"-->
<!--														   ng-readonly="!urlReadOnly" name="iotActivateUrl"-->
<!--														   ng-model="terminalParam.iotActivateUrl" ng-disabled="disabled"-->
<!--														   validator="required" message-id="iotActivateUrl" maxlength="100"-->
<!--														   placeholder="" /> <span id="iotActivateUrl" class="help-inline"></span></td>-->


<!--							<td style="width: 15%; text-align: right;">【{{serverList.lockTerminalImgUrl}}】<span-->
<!--									style='color: #e02222'>*</span></td>-->
<!--							<td style="width: 30%;"><input type="text"-->
<!--														   ng-readonly="!urlReadOnly" name="lockTerminalImgUrl"-->
<!--														   ng-model="terminalParam.lockTerminalImgUrl" ng-disabled="disabled"-->
<!--														   validator="required" message-id="lockTerminalImgUrl" maxlength="100"-->
<!--														   placeholder="" /> <span id="lockTerminalImgUrl" class="help-inline"></span></td>-->
<!--						</tr>-->
					</table>
				</div>


				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{serverQ7List.title}}】</h1>
				</div>
				<div>
					<table class="table table-hover" >
						<tr >
							<td style="width: 15%; text-align: right;">{{serverQ7List.sysUpRequiresDisplay}}</td>
							<td style="width: 30%;"><label> <input
									ng-init="terminalParam.sysUpRequiresDisplay=true"
									name="switch-field-1" class="ace ace-switch" type="checkbox"
									ng-model="terminalParam.sysUpRequiresDisplay"> <span class="lbl"></span></label></td>
							<td style="width: 15%; text-align: right;"></td>
							<td style="width: 30%;"></td>
						</tr>

						<tr >
							<td style="width: 15%;text-align: right; border: 0px;">{{serverQ7List.sysUpRequiresVolume}}</td>
							<td style="width: 30%; border: 0px;"><label>
								<select name="sysUpRequiresVolume"
										ng-init="terminalParam.sysUpRequiresVolume='forbidden'"
										ng-model="terminalParam.sysUpRequiresVolume"
										ng-disabled="disabled" message-id="sysUpRequiresVolume">
									<option value="standard">Standard</option>
									<option value="forbidden">Forbidden</option>
									<option value="customization">Customization</option>
								</select>
								<span class="lbl"></span></label>
							</td>


							<td style="width: 15%; text-align: right; border: 0px;">{{serverQ7List.sysUpRequiresVolumeValue}}</td>
							<td style="width: 30%; border: 0px;"><input type="number" name="sysUpRequiresVolumeValue"
														   min="0" max="15"
														   ng-model="terminalParam.sysUpRequiresVolumeValue" ng-disabled="disabled"
														   message-id="sysUpRequiresVolumeValue"
														   placeholder="{{serverQ7List.maxlength1}}" />

						</tr>
						<tr>
							<td style="width: 15%; text-align: right; border: 0px; vertical-align:top;line-height:30px;" rowspan="4">{{appList.installWhiteListQ7}}
							</td>
						</tr>
						<tr>
							<td style="width: 30%; border: 0px;" rowspan="4">
								<textarea placeholder="{{appList.format}}" name="installWhiteListQ7" message-id="installWhiteListQ7" ng-model="installWhiteListQ7" style="width: 50%;height: 200px">

								</textarea>
								<button ng-click="whiteListFormat(installWhiteListQ7)">{{appList.gsh}}</button>
							</td>
						</tr>
					</table>
				</div>


				<div class='titlediv'>
					<h1 class='titleh1' style="font-size: 14px; font-weight: bold;">【{{appList.appConf}}】</h1>
				</div>
				<div>
					<table class="table table-hover">
						<tr>
							<td style="width: 15%; text-align: right;vertical-align:top;line-height:30px;" rowspan="4">{{appList.appConf}}<span
									style='color: #e02222'>*</span></td>
						</tr>
						<tr>
							<td style="width: 75%;" rowspan="4">
								<textarea name="infoList" message-id="infoList" ng-model="infoList" style="width: 50%;height: 200px">

								</textarea>
								<button ng-click="infoFormat(infoList)">{{appList.gsh}}</button>
								<span>({{appList.gshMsg}})</span>
							</td>
						</tr>
					</table>
				</div>
			</form>
		</div>
	</div>
</div>
<div ng-show="!disabled" form-foot
	goback="$state.go('home.terminalParam.list')"
	submit="form.submit(Form)" reset="form.reset(Form)"></div>