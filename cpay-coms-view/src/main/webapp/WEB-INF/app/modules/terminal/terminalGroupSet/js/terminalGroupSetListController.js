'use strict';
define(["../js/terminalGroupSetService"], function () {
    return function ($scope, terminalGroupSetService, $state,BaseApi, dialogService, $stateParams, gridService,$http,organizationService,baseUrl,Upload,securityService,anguloaderService) {
    	$http.get("modules/terminal/terminalGroupSet/i18n/en/terminalGroupSetList.json").success(function(dataLan) {
    	$scope.list = dataLan.list;
    	$scope.grid1 = dataLan.grid1;
    	$scope.grid2 = dataLan.grid2;
    	var upData = dataLan.uploadData;
    	BaseApi.query("/dict/type/terminal_type", {}, function (data) {
    	var url = "/terminal/";
    	if($state.is('home.terminalGroupSet.list.group')){
    		url= url+"group/"+$stateParams.groupId;
    	}else if($state.is('home.terminalGroupSet.list.nogroup')){
    		url= url+"nogroup/"+$stateParams.groupId;
    	}
        gridService.initTable({
            url: url,
            operator:false,
            scope:$scope,
            columns: 
            	[
            	 {field: 'state',checkbox: 'true'},
            	 {field: "termSeq", title: $scope.list.termSeq, width: 120},
            	 {field: "termMfrName", title: $scope.list.termMfrName, width: 120},
            	 {field: "termNo", title: $scope.list.termNo, width: 120},
            	 {field: "payMerchantNo", title: $scope.list.payMerchantNo, width: 120},
            	 {field: "termTypeName", title: $scope.list.termTypeName, width: 120},
            	 {field: "activateType", title: $scope.list.activateType,
            		 formatter: function (value) {
                         var type = '';
                         $.each(data, function (n, obj) {
                             if (obj.type == value) {
                                 type = obj.name;
                             }
                         });
                         return type;
                     }
            	 },
            	 {field: "insName", title: $scope.list.insName , width: 120},
            	 {field: "groupName", title: $scope.list.groupName, width: 220},
                 {field: "merchantName", title: $scope.list.merchantName, width: 120}
                ]
        	});
    	});
        $scope.search = function () {
        	gridService.search($scope.condition);
        };
        $scope.reset = function () {
            $scope.condition = {};
            gridService.search();
        };
        $scope.queryGroup = function(success){
        	if($stateParams.groupId == null || $stateParams.groupId == ''){
        		dialogService.alertInfo("info",$scope.list.selectLeft);
        		return false;
        	}
        	if($scope.Title == "Already grouped" || $scope.Title == "已分组"){
        		dialogService.alertInfo("info", $scope.list.selectLeft);
        		return false;
        	}
        	$state.go('home.terminalGroupSet.list.group', {groupId:$stateParams.groupId});
         
        },
        BaseApi.query("/dict/type/terminal_type", {}, function (data) {
            $scope.termTypeListTwo = data;
        });
        $scope.queryNoGroup = function(success){
        	if($stateParams.groupId == null || $stateParams.groupId == ''){
        		dialogService.alertInfo("info", $scope.list.selectLeft);
        		return false;
        	}
        	$state.go('home.terminalGroupSet.list.nogroup', {groupId:$stateParams.groupId});
        },
        $scope.openOrganization = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
            });
        };
        $scope.openLimitOrganization = function () {
            organizationService.openLimitOrganization(function (value) {
                if (!$scope.condition) {
                    $scope.condition = {};
                }
                $scope.condition.insId = value.id;
                $scope.condition.insName = value.name;
            },null,$stateParams.groupId);
        };
        $scope.upload = function () {
        	dialogService.openDialog({
                template: "modules/terminal/terminalGroupSet/terminalUpload.html",
                width: '55%',
                controller: function ($scope, formService) {
                	$scope.type = 1;
                    $scope.readOnly = false;
                    $scope.upData = upData;
                    //$scope.list = "导入结果";
                    $scope.downTermSeqModel = function(){
    	        		var downloadUrl=baseUrl+'/terminal/termSeqlModelDownload';
    	                window.open(downloadUrl,'_blank');
    	        	},
    	        	$scope.downTermNoModel = function(){
    	        		var downloadUrl=baseUrl+'/terminal/termNoModelDownload';
    	                window.open(downloadUrl,'_blank');
    	        	},
    	        	$scope.downMerchantNoModel = function(){
    	        		var downloadUrl=baseUrl+'/terminal/merchantNoModelDownload';
    	                window.open(downloadUrl,'_blank');
    	        	},
    	        	$scope.fileSelected = function(){
    	        		if(!$scope.file){
    	        			return;
    	        		}
    	        		var fileType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
        	            if(fileType != 'xls' && fileType != 'xlsx'){
        	        		dialogService.alertInfo("error", $scope.list.uploadExcel);
        	        		$scope.file = null;
        	        		return;
        	        	}
    	        	},
                    $scope.import = function(){
    	        		$scope.anguloader.show = false;
                    	$scope.doingClose = true;
                    	if($scope.file ==  null ){
                    		dialogService.alertInfo("error", $scope.list.uploadExcel);
        	        		$scope.file = null;
        	        		return ;
        	        	}
        	            /*if ($scope.file.type.indexOf('excel') < 0) {
        	                dialogService.alertInfo("error", "请上传excel文件");
        	                return;
        	            }*/
        	            var picType = $scope.file.name.substring($scope.file.name.lastIndexOf('.') + 1);
        	        	if(picType != 'xlsx' && picType != 'xls'){
        	        		dialogService.alertInfo("error", $scope.list.uploadExcel);
        	        		$scope.file = null;
        	        		return;
        	        	}
        	        	var url = "";
        	        	if($scope.type == 1){
        	        		url = baseUrl+'/terminalGroupSet/uploadTermSeq'
        	        	}else if($scope.type == 2){
        	        		url = baseUrl+'/terminalGroupSet/uploadTermNo'
        	        	}else if($scope.type == 3){
        	        		url = baseUrl+'/terminalGroupSet/uploadMerchantNo'
        	        	}
        	        	$scope.$watch('anguloader.show+doingClose',function(){
        	        		if(!$scope.anguloader.show && !$scope.doingClose){
        	            		$scope.anguloader.show = true;
        	            	}
        	            });
                        Upload.upload({
                            url:  url,
                            data: {file: $scope.file},
                            method: 'POST',
                            headers: securityService.getHeader()
                        }).then(function (resp) {
                            if(resp.data.status != 200){
                                dialogService.alertInfo("success",resp.data.msg );
                                return "";
                            }
                        	$scope.list = "";
                        	for(var i = 0; i < resp.data.data.length;i++){
                        		$scope.list = $scope.list + resp.data.data[i];
                        	}
                        	$scope.anguloader.show = false;
                        	$scope.doingClose = true;
                        	if($scope.list == null || $scope.list == ""){
                        		dialogService.alertInfo("success", $scope.list.uploadFail);
                        		return "";
                        	}else{
                        		//dialogService.alertInfo("success", "导入终端数据成功");
                        	}
                        }, function (resp) {
                        	dialogService.alertInfo("warning", $scope.list.uploadFail);
                        });
                    }
                                            
                },
                closeByDocument: false
            });
          };
        $scope.checkRowsJZ = function(){
              var rows = gridService.getSelectedRow();
               if (rows.length == 0) {
                     dialogService.alertInfo("info", $scope.list.chooseOne);
                     return;
               }
               dialogService.openConfirm($scope.list.confirmRemove, function () {
                     var ids = "";
                     for (var index in rows) {
                         ids = ids + rows[index].id + ",";
                     }
                     var param = ids+$stateParams.groupId;
                     terminalGroupSetService.groupJZ(param, function (data) {
                         dialogService.closeConfirm();
                         dialogService.alertInfo("success", $scope.list.successMsg);
                         gridService.refresh();
                     });
                     
                 });
             };
             $scope.checkRowsGZ = function(success){
        	 var rows = gridService.getSelectedRow();
             if (rows.length == 0) {
                   dialogService.alertInfo("info", $scope.list.chooseOne);
                   return;
             }
             dialogService.openConfirm($scope.list.group, function () {
                   var ids = "";
                   for (var index in rows) {
                       ids = ids + rows[index].id + ",";
                   }
                   var param = ids+$stateParams.groupId;
                   terminalGroupSetService.groupGZ(param, function (data) {
                       dialogService.closeConfirm();
                       dialogService.alertInfo("success",  $scope.list.successMsg);
                       gridService.refresh();
                   });
               });
          };
    	});
   };
});