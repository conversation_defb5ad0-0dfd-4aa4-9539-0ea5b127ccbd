<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
            	<input type="text" class=" top-inp" placeholder="{{terminalListHtmLan.termSeq}}" ng-model="condition.termSeq" id="termSeq">
                <div class="input-append input-group"  ng-click="openOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="{{terminalListHtmLan.insName}}" id="insName">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
                <select ng-options="m.id as m.name for m in terminalGroupList" ng-model="condition.terminalGroupId" class="input-big" >
                    <option value="">{{terminalListHtmLan.groupName}}</option>
                 </select>
                 <select ng-options="m.id as m.name for m in factoryList" ng-model="condition.termMfrId" ng-change="changeFactory(condition.termMfrId)" class="input-big" >
                    <option value="">{{terminalListHtmLan.termMfrName}}</option>
                 </select>
                <select ng-options="m.code as m.name for m in termTypeList" ng-model="condition.termTypeCode" class="input-big" >
                    <option value="">{{terminalListHtmLan.termTypeName}}</option>
                 </select>
                <input type="text" class=" top-inp" placeholder="{{terminalListHtmLan.remark}}" ng-model="condition.remark" id="remark">
                <button type="button" class="btn" ng-click="reset()">Reset</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>Query</button>
                <button class="button" type="button" ng-click="upload()" has-permission="terminalInfo_import">
                    <i class="icon-upload"></i>Import
                </button>
                <button class="button" type="button" ng-click="exportExcel()" has-permission="terminal_export">
                    <i class="icon-download"></i>Export
                </button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
        	<div id="tools">
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>