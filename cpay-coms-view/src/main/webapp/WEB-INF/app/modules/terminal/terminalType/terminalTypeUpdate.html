<div class="container-fluid">
	<div class="row-fluid">
		<div class="span12">
			<form name="Form" class="form-horizontal" id="editform">
				<div class="control-group">
					<label class="control-label">{{addType.code}}<span
						class="required">*</span></label>
					<div class="controls">
						<input type="text" name="code" maxlength=20
							ng-model="terminalType.code" class="span6" ng-readonly="readOnly"
							ng-disabled="disabled" validator="required,maxlength=20"
							message-id="code" /> <span id="code" class="help-inline">
						</span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{addType.name}}<span
						class="required">*</span></label>
					<div class="controls">
						<input type="text" name="name" maxlength=25
							ng-model="terminalType.name" class="span6" ng-disabled="disabled"
							validator="required" message-id="name" /> <span id="name"
							class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{addType.termMfrName}}<span
						class="required">*</span></label>
					<div class="controls">
						<select class="form-control" name="factoryList"
							ng-model="terminalType.termMfrId" ng-readonly="readOnly"
							ng-disabled="readOnly"
							ng-options="m.id as m.name for m in factoryList"
							validator="required" message-id="termMfrId">
							<option value="">{{addType.chooseVendor}}</option>
						</select> <span id="termMfrId" class="help-inline"></span>
					</div>
				</div>
				<div class="control-group">
					<label class="control-label">{{addType.isStart}}<span
						class="required">*</span></label>
					<div class="controls" >
						<label class="radio line">
							<input type="radio" name="termType" ng-model="terminalType.termType"
								   class="ace" message-id="termType" value="1"
							/><span class="lbl">YES</span>
						</label>
						<label class="radio line">
							<input type="radio" name="termType" ng-model="terminalType.termType"
								    class="ace" message-id="termType" value="2"
							/><span class="lbl">NO</span>
						</label>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<div ng-show="!disabled" form-foot
	goback="$state.go('home.terminalType.list')" submit="form.submit(Form)"
	reset="form.reset(Form)"></div>


