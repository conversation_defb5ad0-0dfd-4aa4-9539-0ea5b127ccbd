'use strict';
define(function () {
    return function ($scope, terminalService, $state, dialogService, gridService,formService,$stateParams,BaseApi,$http) {
        $scope.terminal = {};
        $scope.terminalBasic = {};
        $scope.$parent.thirdTitle="Basic info";
        $http.get("modules/terminal/terminal/i18n/en/terminalList.json").success(function(data) {

            $scope.terminalListJsLan = data.terminalList;
            $scope.editDataMsg = data.editData;

            terminalService.getTerminalView($stateParams.id, function (data) {
                $scope.terminal = data;
            });
            $scope.form = formService.form(function () {
                terminalService.updateTerminal($scope.terminal, function (data) {
                    if(data.status == 300){
                        dialogService.alertInfo("error", data.msg);
                        return false;
                    }

                    dialogService.alertInfo("success", $scope.editDataMsg.successMsg);
                    $state.go("home.terminal.list");
                });
            });
        });
    };
});
