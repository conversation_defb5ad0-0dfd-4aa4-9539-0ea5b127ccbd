'use strict';
define(["../js/terminalService.js"],function () {
    return function ($scope, terminalService, $state,$stateParams) {
    	$scope.$parent.thirdTitle = "Terminal Detail";
        $scope.readOnly = false;
        $scope.terminalView = function() {
        	 $state.go('home.termview.info', {id:$stateParams.id});
        };
        $scope.driverVersion = function() {
        	$state.go('home.termview.driverVersion', {id:$stateParams.id});
        };
        $scope.systemVersion = function() {
        	$state.go('home.termview.sysVersion', {id:$stateParams.id});
        };
       /* $scope.appView = function() {
        	$state.go('home.termview.appView', {id:$stateParams.id});
        };*/
        $scope.basicApp = function() {
        	$state.go('home.termview.basicApp', {id:$stateParams.id});
        };
        $scope.flowCurMonth = function() {
            $state.go('home.termview.termFlowCountDay', {id:$stateParams.id});
        };
        $scope.switchrecord = function() {
            $state.go('home.termview.switchrecord', {id:$stateParams.id});
        };
    };
});