<div class="modal-header">
	<!--<button type="button" ng-click="cancel()" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>-->
	<h3 class="modal-title">{{upData.import}}</h3>
</div>
<form name="Form" class="form-horizontal">
	<div class="control-group">
		<label class="control-label" style="width: 90px; margin-left: 30px;">{{upData.modelDownload}}</label>
		<div class="controls">
			<label class="radio line margin-normal">
				<button class="btn green" type="button" ng-click="downTermSeqModel()"><i class="icon-download-alt"></i>{{upData.byTermSeq}}</button>
			</label>
			<label class="radio line margin-normal">
				<button class="btn green" type="button" ng-click="downTermNoModel()"><i class="icon-download-alt"></i>{{upData.byTermNo}}</button>
			</label>
			<label class="radio line margin-normal">
				<button class="btn green" type="button" ng-click="downMerchantNoModel()"><i class="icon-download-alt"></i>{{upData.merchantNo}}</button>
			</label>
		</div>
	</div>
	<div class="control-group">
		<label class="control-label" style="width: 90px; margin-left: 30px;margin-top:10px;">{{upData.importType}}</label>
		<div class="controls">
			<label class="radio line margin-normal"> <input type="radio"
				name="type" ng-model="type"
				validator="required" class="ace" message-id="type" value="1"/><span class="lbl">{{upData.byTermSeq}}</span>
			</label> 
			<label class="radio line margin-normal"> <input type="radio"
				name="type" ng-model="type"
				validator="required" class="ace" message-id="type" value="2"/><span class="lbl">{{upData.byTermNo}}</span>
			</label>
			<label class="radio line margin-normal"> <input type="radio"
				name="type" ng-model="type"
				validator="required" class="ace" message-id="type" value="3"/><span class="lbl">{{upData.merchantNo}}</span>
			</label>
		</div>
	</div>

	<div class="control-group">
		<label class="control-label" style="width: 90px; margin-left: 30px;">{{upData.chooseFile}}</label>
		<div class="controls">
			<div class="uneditable-input">
				<i class="icon-file fileupload-exists"></i> <span
					class="fileupload-preview" ng-bind="file.name"></span>
			</div>
			<div class="btn btn-file"  ngf-select="fileSelected()" ng-model="file" name="file">{{upData.choose}}</div>
			<button class="btn blue" type="button" ng-click="import()"><i class="icon-transfer"></i>{{upData.upload}}</button>			
		</div>
		<textarea ng-model="list" rows="10" class="span6" readonly="true"
			style="width: 80%; margin-left: 30px; margin-top: 5px;"></textarea>
	</div>
	<div class="modal-footer">

	</div>
</form>