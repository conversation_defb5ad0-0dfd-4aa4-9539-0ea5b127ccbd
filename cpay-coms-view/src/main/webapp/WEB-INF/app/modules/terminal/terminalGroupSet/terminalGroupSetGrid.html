<style>
    .content-main .footer-container{
        margin-left: 0px;
    }
</style>
     <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline no-margin" name="searchForm" style="margin-top:30px">
                <div class="input-append input-group"  ng-click="openLimitOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="{{grid1.insName}}" id="insName">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
                <input type="text" class=" top-inp" placeholder="{{grid1.termSeq}}" ng-model="condition.termSeq" id="termSeq">
                <input type="text" class=" top-inp" placeholder="{{grid1.termNo}}" ng-model="condition.termNo" id="termNo">
                <input type="text" class=" top-inp" placeholder="{{grid1.merchantNo}}" ng-model="condition.payMerchantNo" id="payMerchantNo">
                <select ng-options="m.type as m.name for m in termTypeListTwo" ng-model="condition.activateType" class="input-big" >
                    <option value="">{{grid1.termType}}</option>
                 </select>
                <button type="button" class="btn" ng-click="reset()">{{grid1.reset}}</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>{{grid1.query}}</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
        	<div id="tools">
                  <button class="btn-special" type="button" ng-click="checkRowsJZ()" has-permission="groupjz" style="width: 120px;">
                    <i class="icon-ok"></i>{{grid1.BatchSolutionGroup}}      
                </button>
            </div>
            <table id="table"></table>
        </div>
    </div>