{"list": {"code": "Model No.", "name": "Model Name", "termMfrName": "Manufacturer", "isStart": "Is enabled?", "reset": "Reset", "query": "Query", "add": "Add"}, "deleteRow": {"confirmMsg": "Confirm to delete current terminal model？", "successMsg": "Delete success!"}, "alarmSet": {"msg1": "Please select a record!", "msg2": "Select only one record!"}, "addList": {"code": "Model No.", "name": "Model Name", "termMfrName": "Manufacturer", "termType": "Terminal Type", "remark": "Remark", "alarm": "SoftWare Alarm", "chooseVendor": "Choose Manufacturer", "chooseType": "Choose Type", "thirdTitle": "Add Model", "isStart": "Is enabled?", "successMsg": "Add success!"}, "editList": {"code": "Model No.", "name": "Model Name", "termMfrName": "Manufacturer", "termType": "Terminal Type", "remark": "Remark", "chooseVendor": "Choose Manufacturer", "chooseType": "Choose Type", "thirdTitle": "Edit Model", "successMsg": "Edit success!"}}