<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
                <div class="input-append date form_datetime">
                    <input size="16" type="text" placeholder="起始日期" value="" readonly  date-picker format="yyyy-MM-dd"
                           ng-model="condition.startTime" name="startTime"  validator="required" message-id="startTime" />
                </div>
                <div class="input-append date form_datetime">
                    <input size="16" type="text" placeholder="结束日期" value="" readonly date-picker format="yyyy-MM-dd"
                           ng-model="condition.endTime" name="endTime" ng-disabled="disabled" validator="required" message-id="endTime" />
                </div>
                <input type="number" class=" top-inp" min="0"  placeholder="最小流量" ng-model="condition.minFlow">
                <input type="number" class=" top-inp" min="0"  placeholder="最大流量" ng-model="condition.maxFlow">

                <button type="button" class="btn" ng-click="reset()">重置</button>
                <button type="submit" ng-click="search()"><i class="icon-search"></i>查询</button>
                <button type="button" class="button" ng-click="exportExcel()" has-permission="export_flow"><i class="icon-download"></i>流量信息导出</button>
            </form>
        </div>
        <div>
            <span>总计(移动流量): {{flowAll||0}} MB</span>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
