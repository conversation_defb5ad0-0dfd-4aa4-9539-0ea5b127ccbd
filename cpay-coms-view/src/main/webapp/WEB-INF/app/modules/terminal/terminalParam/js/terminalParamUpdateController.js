'use strict';
define([], function () {
    return function ($scope, terminalParamService, $state, $stateParams, gridService, dialogService, formService, organizationService, terminalGroupService, BaseApi, $http) {
    	$http.get("modules/terminal/terminalParam/i18n/en/terminalParamEdit.json").success(function(dataLan) {
        $scope.$parent.thirdTitle = "Add parameter";
        $scope.insInfoList = dataLan.insInfoList;
        $scope.baseInfoList = dataLan.baseInfoList;
        $scope.updateList = dataLan.updateList;
        $scope.serverList = dataLan.serverList;
        $scope.locationList = dataLan.locationList;
        $scope.appList = dataLan.appList;
        $scope.serverQ7List = dataLan.serverQ7List;
        $scope.edit = dataLan.edit;
        var parentScope = $scope;
        $scope.$parent.thirdTitle = "Update parameter";
        $scope.showUrlReadOnly = true;
        $scope.urlReadOnly = false;
        $scope.infoList = [];
        $scope.installWhiteListQ7 = [];

        $scope.editInfo = function (info) {
            info.edit = true;
        }
        $scope.saveInfo = function (info) {
            info.edit = false;
        }
        $scope.removeInfo = function (info) {
            $scope.infoList.splice($scope.infoList.indexOf(info), 1);
        }
        $scope.addInfo = function () {
            $scope.infoList.unshift({text: '', edit: true});
        }
        $scope.update = 2;
        terminalParamService.selectByPrimaryKey($stateParams.id, function (data) {
            $scope.terminalParam = data;
            $scope.terminalParam.htIntvl = parseInt(data.htIntvl);
            $scope.terminalParam.upInfoIntvl = parseInt(data.upInfoIntvl);
            $scope.detail = $scope.terminalParam.alarmInfoCode;
            if ($scope.terminalParam.appPackage == null) {
                $scope.terminalParam.appPackage = "";
            }
            if ($scope.terminalParam.isWifi == "T") {
                $scope.terminalParam.isWifi = true;
            }
            if ($scope.terminalParam.isWifi == "F" || $scope.terminalParam.isWifi == "null") {
                $scope.terminalParam.isWifi = false;
            }
            if ($scope.terminalParam.isWifiDownload == "T") {
                $scope.terminalParam.isWifiDownload = true;
            }
            if ($scope.terminalParam.isWifiDownload == "F" || $scope.terminalParam.isWifiDownload == "null") {
                $scope.terminalParam.isWifiDownload = false;
            }

            if (parseInt($scope.terminalParam.sysUpRequiresDisplay) == 0) {
                $scope.terminalParam.sysUpRequiresDisplay = true;
            }
            if (parseInt($scope.terminalParam.sysUpRequiresDisplay) == 1) {
                $scope.terminalParam.sysUpRequiresDisplay = false;
            }
            BaseApi.query("/terminalGroup/selectGroupByInsId/" + $scope.terminalParam.insId, {}, function (data) {
                $scope.terminalGroupList = data;
            });

            $scope.infoFormat($scope.terminalParam.alarmInfoCode);
            $scope.whiteListFormat($scope.terminalParam.installWhiteListQ7);
        });
        BaseApi.query("/dict/type/upload_app_info", {}, function (data) {
            $scope.alarmInfoList = data;
        });
        $scope.infoFormat = function(data){
            $scope.infoList = "";
            if(data == "" || data == undefined){
                return
            }
            var alarmInfoList = data.split(/[^\w.]/);
            var k = "";
            for (k in alarmInfoList) {
                if(alarmInfoList[k] != ""){
                    $scope.infoList =$scope.infoList + alarmInfoList[k] + '\n';
                }

            }
            $scope.infoList = $scope.infoList.substring(0, $scope.infoList.length - 1)
        }

        $scope.whiteListFormat = function(data){
            $scope.installWhiteListQ7 = "";
            if (data === "" || data === undefined) {
                return;
            }
            const infoList = data.split(',');
            let k = "";
            for (k in infoList) {
                if (infoList[k] !== "") {
                    $scope.installWhiteListQ7 = $scope.installWhiteListQ7 + infoList[k] + '\n';
                }
            }
            $scope.installWhiteListQ7 = $scope.installWhiteListQ7.substring(0, $scope.installWhiteListQ7.length - 1)
        }

        $scope.form = formService.form(function () {
            $scope.infoFormat($scope.infoList);
            var obj = $scope.infoList.split('\n');
            var k = "";
            var ids = "";
            if (obj.length == 0) {
                dialogService.alertInfo("error", $scope.edit.msg1);
                return;
            }
            for (k in obj) {
                if (obj[k] == "") {
                    dialogService.alertInfo("error", $scope.edit.msg1);
                    return;
                }
                ids += obj[k] + ",";
            }
            $scope.terminalParam.alarmInfoCode = ids.substring(0, ids.length - 1);

            $scope.whiteListFormat($scope.installWhiteListQ7);
            obj = $scope.installWhiteListQ7.split('\n');
            ids = "";
            if (obj.length > 0 && obj[0] !== '') {
                k = "";
                for (k in obj) {
                    if (obj[k].includes(" ") || obj[k] === '') {
                        dialogService.alertInfo("error", $scope.edit.msg3);
                        return;
                    }
                    ids += obj[k] + ",";
                }
            }
            $scope.terminalParam.installWhiteListQ7 = ids.substring(0, ids.length - 1);

            if($scope.terminalParam.isWifi == true){
 	        	$scope.terminalParam.isWifi="T";
 	        }
 	        if($scope.terminalParam.isWifi == false){
 	        	$scope.terminalParam.isWifi="F";
	        }
            if($scope.terminalParam.isWifiDownload == true){
                $scope.terminalParam.isWifiDownload="T";
            }
            if($scope.terminalParam.isWifiDownload == false){
                $scope.terminalParam.isWifiDownload="F";
            }

            if ($scope.terminalParam.sysUpRequiresDisplay) {
                $scope.terminalParam.sysUpRequiresDisplay = "0";
            }
            if (!$scope.terminalParam.sysUpRequiresDisplay) {
                $scope.terminalParam.sysUpRequiresDisplay = "1";
            }
            $scope.terminalParam.tmsDomainNameBakFirst= $scope.terminalParam.tmsDomainName;
            $scope.terminalParam.tmsDomainNameBakSecond= $scope.terminalParam.tmsDomainName;
            terminalParamService.updateTerminalParam($scope.terminalParam, function () {
                dialogService.alertInfo("success", $scope.edit.editMsg_success);
                $state.go("home.terminalParam.list");
            });
        });

        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.terminalParam) {
                    $scope.terminalParam = {};
                }
                $scope.terminalParam.insId = value.id;
                $scope.terminalParam.insName = value.name;
                BaseApi.query("/terminalGroup/selectGroupByInsId/" + value.id, {}, function (data) {
                    $scope.terminalGroupList = data;
                });
            });
        };
    	});
    }
});