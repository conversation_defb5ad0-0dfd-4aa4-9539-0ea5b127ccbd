'use strict';
define([], function () {
    return function ($scope, terminalGroupService, $state, dialogService,formService,organizationService,BaseApi,$http) {
    	$http.get("modules/terminal/terminalParam/i18n/en/terminalGroupEdit.json").success(function(dataLan) {
    	$scope.$parent.thirdTitle = "Add group";
        $scope.readOnly = false;
        $scope.update=1;
        $scope.form = formService.form(function(){
        	terminalGroupService.addterminalGroup($scope.terminalGroup, function (data) {
          		 if(data.status == 300){
        			 dialogService.alertInfo("error", data.msg);
        			 return false;
        		 }
          		 dialogService.alertInfo("success", "新增成功");
    			 $state.go("home.terminalGroup.list");
            });
        });
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.terminalGroup) {
                    $scope.terminalGroup = {};
                }
                $scope.terminalGroup.insId = value.id;
                $scope.terminalGroup.insName = value.name;
            	});
        	};
    	});
    };
});