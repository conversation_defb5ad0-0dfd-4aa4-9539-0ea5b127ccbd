<div class="container-fluid">
	<div class="row-fluid" id="condition-scroll">
		<div class="span12">
			<form class="form-inline" name="searchForm">
				<div class="search-container tags" id="conditions"></div>

			</form>
		</div>
	</div>
	<div class="search-options">
		<select id="condition-name">
			<option selected="" value="t.term_seq">{{html.termSeq}}</option>
			<option selected="" value="t.ins_name">{{html.insName}}</option>
			<option selected="" value="t.group_name">{{html.groupName}}</option>
			<option selected="" value="t.term_type_name">{{html.termTypeName}}</option>
			<option selected="" value="xt.app_name">{{html.appName}}</option>
			<option selected="" value="xt.app_code">{{html.appCode}}</option>
			<option selected="" value="xt.app_version_outside">{{html.version}}</option>
			<option selected="" value="xt.app_version">{{html.innerVersion}}</option>
			<option selected="" value="st.os_ver">{{html.osVer}}</option>
			<option selected="" value="st.safe_mod_ver">{{html.safeModVer}}</option>
			<option selected="" value="st.android_ver">{{html.androidVer}}</option>
			<option selected="" value="st.tms_sdk">{{html.tmsSdk}}</option>
			<option selected="" value="st.pay_sdk">{{html.paySdk}}</option>
			<option selected="" value="st.pay_app_code">{{html.payAppCode}}</option>
			<option selected="" value="st.pay_app_version">{{html.payAppVersion}}</option>
		</select> <select id="condition-condition">
			<option selected="" value="=">{{html.equal}}</option>
			<option value="!=">{{html.notEqual}}</option>
			<option value="like">{{html.match}}</option>
		</select> <input id="condition-value" type="text" />
		<button type="button" class="btn" ng-click="resetCondition()"
			style="margin-bottom: 10px;">{{html.reset}}</button>
		&nbsp;&nbsp;
		<button type="button" ng-click="addCondition()"
			style="margin-bottom: 10px;">
			<i class="icon-plus"></i>{{html.add}}
		</button>
		&nbsp;&nbsp;
		<button type="button" ng-click="search()" style="margin-bottom: 10px;">
			<i class="icon-search"></i>{{html.query}}
		</button>
		&nbsp;&nbsp;
		<button type="button" ng-click="exportExcel()" has-permission="terminalAdvQuery_export"
			style="margin-bottom: 10px;">
			<i class="icon-download"></i>{{html.download}}
		</button>
		&nbsp;&nbsp; <a style="cursor: pointer" ng-click="returnBasicQuery()"><font
			size="3">[ {{html.basic}} ]</font></a>
	</div>
	<div class="row-fluid">
		<div class="span12">
			<table id="table"></table>
		</div>
	</div>
</div>