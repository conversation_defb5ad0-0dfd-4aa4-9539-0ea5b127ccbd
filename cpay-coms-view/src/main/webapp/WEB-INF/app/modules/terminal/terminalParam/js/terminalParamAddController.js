'use strict';
define([], function () {
    return function ($scope,organizationService,terminalParamService, $state,gridService, dialogService,formService,terminalGroupService,BaseApi,$http) {
    	$scope.terminalParam={};
    	$scope.terminalParam.isWifi=false;
    	$http.get("modules/terminal/terminalParam/i18n/en/terminalParamEdit.json").success(function(dataLan) {
    	$scope.$parent.thirdTitle = "Add parameter";
    	$scope.insInfoList = dataLan.insInfoList;
    	$scope.baseInfoList = dataLan.baseInfoList;
    	$scope.updateList = dataLan.updateList;
    	$scope.serverList = dataLan.serverList;
        $scope.locationList = dataLan.locationList;
    	$scope.appList = dataLan.appList;
    	$scope.serverQ7List = dataLan.serverQ7List;
    	$scope.add = dataLan.add;
    	//$scope.readOnly = true;
    	$scope.showUrlReadOnly = false;
    	$scope.urlReadOnly = true;
        $scope.terminalParam.enableLocation = true;
        $scope.update=1;
        //$scope.ALL="全选";

        $scope.infoList = [];

		$scope.editInfo = function(info){
			info.edit = true;
		}
		$scope.saveInfo = function(info){
			info.edit = false;
		}
		$scope.removeInfo = function(info){
			$scope.infoList.splice($scope.infoList.indexOf(info),1);
		}
		$scope.addInfo = function(){
			$scope.infoList.unshift({text:'',edit:true});
		}
        $scope.infoFormat = function(data){
            $scope.infoList = "";
            if(data == "" || data == undefined){
                return
            }
            var alarmInfoList = data.split(/[^\w.]/);
            var k = "";
            for (k in alarmInfoList) {
                if(alarmInfoList[k] != ""){
                    $scope.infoList =$scope.infoList + alarmInfoList[k] + '\n';
                }

            }
            $scope.infoList = $scope.infoList.substring(0, $scope.infoList.length - 1)
        }

        $scope.whiteListFormat = function(data){
            $scope.installWhiteListQ7 = "";
            if (data === "" || data === undefined) {
                return;
            }
            const infoList = data.split(/[^\w.]/);
            let k = "";
            for (k in infoList) {
                if (infoList[k] !== "") {
                    $scope.installWhiteListQ7 = $scope.installWhiteListQ7 + infoList[k] + '\n';
                }
            }
            $scope.installWhiteListQ7 = $scope.installWhiteListQ7.substring(0, $scope.installWhiteListQ7.length - 1)
        }

        var parentScope =$scope;
        $scope.form = formService.form(function(){
            $scope.infoFormat($scope.infoList);
            var obj = $scope.infoList.split('\n');
            var k = "";
            var ids = "";
            if (obj.length == 0) {
                dialogService.alertInfo("error", $scope.add.msg1);
                return;
            }
            for (k in obj) {
                if (obj[k] == "") {
                    dialogService.alertInfo("error", $scope.add.msg2);
                    return;
                }
                ids += obj[k] + ",";
            }
            $scope.terminalParam.alarmInfoCode = ids.substring(0, ids.length - 1);
            if($scope.terminalParam.isWifi == true){
 	        	$scope.terminalParam.isWifi="T";
 	        }
 	        if($scope.terminalParam.isWifi == false){
 	        	$scope.terminalParam.isWifi="F";
	        }
            if($scope.terminalParam.isWifiDownload == true){
                $scope.terminalParam.isWifiDownload="T";
            }
            if($scope.terminalParam.isWifiDownload == false){
                $scope.terminalParam.isWifiDownload="F";
            }

            if ($scope.terminalParam.sysUpRequiresDisplay == true) {
                $scope.terminalParam.sysUpRequiresDisplay = "0";
            }
            if ($scope.terminalParam.sysUpRequiresDisplay == false) {
                $scope.terminalParam.sysUpRequiresDisplay = "1";
            }

            $scope.whiteListFormat($scope.installWhiteListQ7);
            obj = $scope.installWhiteListQ7.split('\n');
            ids = "";
            if (obj.length > 0 && obj[0] !== '') {
                k = "";
                for (k in obj) {
                    if (obj[k].includes(" ") || obj[k] === '') {
                        dialogService.alertInfo("error", $scope.add.msg3);
                        return;
                    }
                    ids += obj[k] + ",";
                }
            }
            $scope.terminalParam.installWhiteListQ7 = ids.substring(0, ids.length - 1);
            $scope.terminalParam.tmsDomainNameBakFirst= $scope.terminalParam.tmsDomainName;
            $scope.terminalParam.tmsDomainNameBakSecond= $scope.terminalParam.tmsDomainName;
        	terminalParamService.addTerminalParam($scope.terminalParam, function (data) {
                dialogService.alertInfo("success", $scope.add.addMsg_success);
                $state.go("home.terminalParam.list");
            });
        });

        BaseApi.query("/dict/type/upload_app_info",{}, function (data) {
		 	$scope.alarmInfoList = data;
        });
        $scope.openInsDialog = function () {
            organizationService.openOrganization(function (value) {
                if (!$scope.terminalParam) {
                    $scope.terminalParam = {};
                }
                $scope.terminalParam.insId = value.id;
                $scope.terminalParam.insName = value.name;
                BaseApi.query("/terminalGroup/selectGroupByInsId/"+value.id,{}, function (data) {
           		 	$scope.terminalGroupList = data;
           	 	});
            });
          };
    	});
    };
});