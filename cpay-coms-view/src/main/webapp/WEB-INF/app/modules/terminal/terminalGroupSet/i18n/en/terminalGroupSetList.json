{"list": {"firstTitle": "Terminal Management", "secondTitle": "Terminal Group management", "Title": "Grouped", "Title2": "Ungrouped", "add": "Add", "clickItemMsg": "Please select terminal group！", "termSeq": "Serial No.", "termMfrName": "Manufacturer", "termNo": "Terminal No.", "payMerchantNo": "Merchant No.", "termTypeName": "Terminal Model", "activateType": "Terminal Type", "insName": "Orgnization", "groupName": "Group", "merchantName": "Merchant Name", "reset": "Reset", "query": "Query", "selectLeft": "Please select the left group first.", "uploadExcel": "Please upload Excel file!", "uploadFail": "Import failed", "chooseOne": "Please choose record", "confirmRemove": "Confirm remove this record?", "successMsg": "Operate success", "group": "Confirm grouping this record?"}, "grid1": {"insName": "Orgnization", "reset": "Reset", "query": "Query", "termSeq": "Serial No.", "termMfrName": "Manufacturer", "termNo": "Terminal No.", "merchantNo": "Merchant No.", "termType": "Terminal Type", "BatchSolutionGroup": "Remove   ", "Add": "Add"}, "grid2": {"insName": "Orgnization", "reset": "Reset", "query": "Query", "termSeq": "Serial No.", "termMfrName": "Manufacturer", "termNo": "Terminal No.", "merchantNo": "Merchant No.", "termType": "Terminal Type", "BatchClassifyGroup": "Grouping   ", "import": "Import"}, "gridData": {"termSeq": "Serial No.", "termMfrName": "Manufacturer", "termTypeName": "Model", "insName": "Organization", "groupName": "Group", "merchantName": "Merchant", "hyName": "Industry"}, "uploadData": {"import": "Import", "modelDownload": "Model Download", "byTermSeq": "Serial No.", "byTermNo": "Terminal No.", "merchantNo": "Merchant No.", "importType": "Import type", "chooseFile": "Choose file", "choose": "<PERSON><PERSON>", "upload": "Upload"}, "queryData": {"termSeqQuery": "Serial No.", "insNameQuery": "Organization", "merchantNameQuery": "Merchant", "hyNameQuery": "Industry"}, "queryGroup": {"queryGroupMsg": "Please select the left group firstly!"}, "queryNoGroup": {"queryNoGroupMsg": "Please select the left group firstly!"}, "checkRowsJZ": {"checkRowsJZMsg1": "Please select a record!", "checkRowsJZMsg2": "Confirm to remove the selected record from the group?", "checkRowsJZMsg3": "Remove success!"}, "checkRowsGZ": {"checkRowsGZMsg1": "Please select a record!", "checkRowsGZMsg2": "Confirm to add the selected record back to the group?", "checkRowsGZMsg3": "Add back successfully!"}}