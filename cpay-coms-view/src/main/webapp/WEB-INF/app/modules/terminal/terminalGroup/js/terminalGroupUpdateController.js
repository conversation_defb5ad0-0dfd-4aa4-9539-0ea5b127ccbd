'use strict';
define([], function () {
    return function ($scope, terminalGroupService, $state,$stateParams, dialogService, formService,organizationService,BaseApi,$http) {
    	 $scope.update=2;
         $scope.$parent.thirdTitle = "终端组别修改";
    	 
    	 terminalGroupService.getterminalGroupById($stateParams.id, function (data) {
             $scope.terminalGroup = data;
         });
         $scope.form = formService.form(function () {
        	 terminalGroupService.updateterminalGroup($scope.terminalGroup, function (data) {
          		 if(data.status == 300){
        			 dialogService.alertInfo("error", data.msg);
        			 return false;
        		 }
                 dialogService.alertInfo("success", "The terminal group information was successfully modified!");
                 $state.go("home.terminalGroup.list");
             });
         });
         $scope.openInsDialog = function () {
             organizationService.openOrganization(function (value) {
                 if (!$scope.terminalGroup) {
                     $scope.terminalGroup = {};
                 }
                 $scope.terminalGroup.insId = value.id;
                 $scope.terminalGroup.insName = value.name;
             });
         };
    }
});