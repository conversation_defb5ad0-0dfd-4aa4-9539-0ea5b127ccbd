<style>
    .content-main .footer-container{
        margin-left: 0px;
    }
</style>
<div class="row-fluid" id="condition-scroll"    >
        <div class="span12">
            <form class="form-inline no-margin" name="searchForm" style="margin-top:30px">
                <div class="input-append input-group"  ng-click="openLimitOrganization()">
                    <input type="text" class=" top-inp" readonly="true" ng-model="condition.insName"
                           placeholder="{{grid2.insName}}" id="insName">
                    <input style="display: none" ng-model="condition.insId">
                    <button class="choice" type="button"></button>
                </div>
                 <input type="text" class=" top-inp" placeholder="{{grid2.termSeq}}" ng-model="condition.termSeq" id="termSeq">
                <input type="text" class=" top-inp" placeholder="{{grid2.termNo}}" ng-model="condition.termNo" id="termNo">
                <input type="text" class=" top-inp" placeholder="{{grid2.merchantNo}}" ng-model="condition.payMerchantNo" id="payMerchantNo">
                <select ng-options="m.type as m.name for m in termTypeListTwo" ng-model="condition.activateType" class="input-big" >
                    <option value="">{{grid2.termType}}</option>
                 </select>
                <button type="button" class="btn" ng-click="reset()">{{grid2.query}}</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>{{grid2.query}}</button>

            </form>
        </div>
</div>
<div class="row-fluid">
        <div class="span12">
        	<div id="tools">
                  <button class="btn-special" type="button" ng-click="checkRowsGZ()" has-permission="groupgz" style="width: 120px;">
                    <i class="icon-ok"></i>{{grid2.BatchClassifyGroup}}
                </button>
                <button class="button" type="button" ng-click="upload()" has-permission="terminal_import">
                    <i class="icon-upload"></i>{{grid2.import}}
                </button>
            </div>
            <table id="table"></table>
        </div>
</div>