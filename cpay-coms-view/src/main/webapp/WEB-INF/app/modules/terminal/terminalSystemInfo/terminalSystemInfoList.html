<div class="container-fluid">
    <div class="row-fluid" id="condition-scroll">
        <div class="span12">
            <form class="form-inline" name="searchForm">
            	<input type="text" class=" top-inp" placeholder="{{sysInfo.termSeq}}" ng-model="condition.termSeq">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.osVer}}" ng-model="condition.osVer">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.safeModVer}}" ng-model="condition.safeModVer">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.androidVer}}" ng-model="condition.androidVer">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.tmsSDK}}" ng-model="condition.tmsSDK">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.paySDK}}" ng-model="condition.paySDK">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.emvVer}}" ng-model="condition.emvVer">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.payAppCode}}" ng-model="condition.payAppCode">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.payAppVersion}}" ng-model="condition.payAppVersion">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.tmsAppVersion}}" ng-model="condition.tmsAppVersion">
                <input type="text" class=" top-inp" placeholder="{{sysInfo.networkType}}" ng-model="condition.networkType">

                <button type="button" class="btn" ng-click="reset()">{{sysInfo.reset}}</button>
                <button type="submit" class="btn blue" ng-click="search()"><i class="icon-search"></i>{{sysInfo.query}}</button>
                <button type="submit" class="btn blue" ng-click="exportExcel()" has-permission="terminalSystemInfo_export"><i class="icon-download"></i>{{sysInfo.export}}</button>
            </form>
        </div>
    </div>
    <div class="row-fluid">
        <div class="span12">
            <div id="tools">
            </div>
            <table id="table"></table>
        </div>
    </div>
</div>
