'use strict';
define(["modules/terminal/factory/js/factoryService"], function () {
    return function ($scope, warrantyService,Upload,BaseApi, $state,$stateParams,factoryService, dialogService, formService) {
        $scope.$parent.thirdTitle = "Package Modified";
        $scope.readOnly = false; 
        warrantyService.getWarranty($stateParams.id, function (data) {
	        $scope.warranty = data;
	        factoryService.getFactoryList(function (data) {
	             $scope.factoryList = data.rows;
	        });
	        BaseApi.query("/terminalType/type/"+data.termFacturerId,{}, function (data) {
	   		 	$scope.termTypeList = data;
	   	 	});
	        if (!$scope.warranty.id) {
	            $state.go("home.warranty.list");
	            return;
	        }
        });
        $scope.changeFactory = function (factoryId) {
    		if(factoryId!=null&&factoryId!=""){
    			var param  = {"factoryId":factoryId};
    	        BaseApi.query("/terminalType/type/"+factoryId,{}, function (data) {
    	   		 	$scope.termTypeList = data;
    	   	 	});
    		}
    		else{
    			$scope.termTypeList=[];
    		}
        };        
        $scope.form = formService.form(function () {
            warrantyService.updateWarranty($scope.warranty, function (data) {
                dialogService.alertInfo("success", "Package modified successfully");
                $state.go("home.warranty.list");
            });
        });
    };
});