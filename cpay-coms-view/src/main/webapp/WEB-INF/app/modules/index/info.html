<div class="container-fluid" id="breadcrumb-scroll">
	<div class="row-fluid">
		<div class="span12">
			<div class="nav-breadcrumb">
				<div class="breadcrumb-icon"
					style="background-image: url(images/default/menu/pic_menu_setting_grey.png)"></div>
				<ul class="breadcrumb">
					<li><a class="navigation_home" ui-sref="home.index">Main</a>
						<i class="icon-angle-right"></i></li>
					<li><span>Information</span></li>
				</ul>
			</div>
		</div>
	</div>
	<div class="hr"></div>
</div>
<div class="content-scroller">
	<div class="content-main">

		<div ng-if="moreData" class="info-content-more">
			<div ng-repeat="info in moreData.rows" class="index-info-content">
				<div style="vertical-align: middle">
					<div class="index-info-text">
						<a ng-href="#/home/<USER>/{{info.id}}" title="{{info.title}}"
							class="index-info-title"> {{info.title}}
							<span ng-show="info.new" class="badge badge-pill badge-important" >new</span><!-- <span
							class="label index-info-notread"
							ng-show="info.statusDetail!='已读'">未读</span>-->
						</a>
					</div>

					<span class="index-info-date"
						style="margin-right: 20px; width: 100px">{{info.createTime
						| datefmt}}</span>
				</div>
				<div class="index-info-main">
					<div class="index-info-main-text">
						<span>{{info.introduction}}</span>
					</div>

					<div class="index-info-main-img" ng-if="info ">
						<div>
							<a ng-repeat="img in info.picList" href="{{ info.ngsPicPath+img }}"
							   class="fresco" data-fresco-group="Information Picture" data-fresco-caption="caption">
								<img src="{{ info.ngsPicPath+img }}"/>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="info-content" ng-if="content">
			<h1>{{title}}</h1>
			<div class="index-info-detail-date">{{createTime|datefmt}}</div>
			<div class="hr"></div>
			<div class="index-info-detail-content">{{content}}</div>

			<!-- <div>
					<img ng-repeat="img in picList" src="{{ ngsPicPath+img }}">
				</div>-->
		</div>
		<div class="info-content" class="htmleaf-container">
			<div class="htmleaf-content bgcolor-13">
				<div id="gallery"
					style="display: none; margin: 0 auto; margin-bottom: 50px; width: 720px;">
				</div>
			</div>
		</div>

		<div class="row-fluid" ng-show="showFlag=='more'">
			<div class="span12">
				<div class="info-footer-container">
					<div class="pagination-footer">
						<div class="pull-left pagination-detail">
							<span class="pagination-info"> total&nbsp;
								{{total}}&nbsp; record </span>
							<!-- <span class="page-list">
								Per page {{rowNum}} record
							</span> -->
						</div>
						<div class="twpagination-pager">
							<ul id="pagination-demo" class="pagination-sm"></ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>