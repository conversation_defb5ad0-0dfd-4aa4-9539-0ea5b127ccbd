'use strict';
define(["lib/echarts/echarts" , "modules/monitor/mmap/js/monitorService"], function (echarts) {
    return function ($scope, monitorService,BaseApi,$http,gridService) {
        $scope.moveCount = 0;
        var user = BaseApi.getUser();
        $scope.indexType = user.indexType;
        /*BaseApi.get("/monitor/getMoveCount", {}, function (result) {
            $scope.moveCount = result.data;
        });*/


        //计算状态监控报表的宽度
        var listWidth = $('.terminal-status-list ul').width();
        var listLength = Math.floor(listWidth/352);
        $scope.listLength = listLength;
        $('.terminal-status-list li').width(listLength?listWidth/listLength - 22:330);

        $(window).resize(function () {
            var listWidth = $('.terminal-status-list ul').width();
            var listLength = Math.floor(listWidth/352);
            $scope.listLength = listLength;
            $('.terminal-status-list li').width(listLength?listWidth/listLength - 22:330);
        });

        //任务进度存情况报表

        /*var dom = document.getElementById("terminal-chart");
        var myChart = echarts.init(dom);*/
        var app = {};
        var option = null;
        var posList = [
            'left', 'right', 'top', 'bottom',
            'inside',
            'insideTop', 'insideLeft', 'insideRight', 'insideBottom',
            'insideTopLeft', 'insideTopRight', 'insideBottomLeft', 'insideBottomRight'
        ];

        app.configParameters = {
            rotate: {
                min: -90,
                max: 90
            },
            align: {
                options: {
                    left: 'left',
                    center: 'center',
                    right: 'right'
                }
            },
            verticalAlign: {
                options: {
                    top: 'top',
                    middle: 'middle',
                    bottom: 'bottom'
                }
            },
            position: {
                options: echarts.util.reduce(posList, function (map, pos) {
                    map[pos] = pos;
                    return map;
                }, {})
            },
            distance: {
                min: 0,
                max: 100
            }
        };

        /*app.config = {
             rotate: 90,
             align: 'left',
             verticalAlign: 'middle',
             position: 'insideBottom',
             distance: 15,
             onChange: function () {
                 var labelOption = {
                     normal: {
                         rotate: app.config.rotate,
                         align: app.config.align,
                         verticalAlign: app.config.verticalAlign,
                         position: app.config.position,
                         distance: app.config.distance
                     }
                 };
                 myChart.setOption({
                     series: [{
                         label: labelOption
                     }, {
                         label: labelOption
                     }, {
                         label: labelOption
                     }, {
                         label: labelOption
                     }]
                 });
             }
         };
 */

        /*var labelOption = {
            normal: {
                show: true,
                position: app.config.position,
                distance: app.config.distance,
                align: app.config.align,
                verticalAlign: app.config.verticalAlign,
                rotate: app.config.rotate,
                formatter: '{c}  {name|{a}}',
                fontSize: 16,
                rich: {
                    name: {
                        textBorderColor: '#fff'
                    }
                }
            }
        };
        */$scope.index=1;
        var option2={};
        BaseApi.get("/infoannounce", {type:0,page:1,rows:3}, function (data) {
            $scope.infodata = data.rows;
            for(var i = 0;i < data.rows.length; i++){
                if((new Date().getTime() - data.rows[i].createTime )/(24*60*60*1000) < 30){
                    $scope.infodata[i].new = true;
                }else{
                    $scope.infodata[i].new = false;
                }
            }
        });
        BaseApi.get("/terminal/getTerminalCount","", function (data) {

            $scope.terminalCount = data.terminalCount;
            $scope.actCount = data.actCount;
            $scope.month = data.month;

            // 指定图表的配置项和数据
            var option = {
                backgroundColor: 'white',
                title: {
                    text: "Terminal",
                    //subtext: '纯属虚构'
                    textStyle:{
                        color:'#14181a',
                        fontSize:18,
                        fontWeight:'normal'
                    }

                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    //                  data: ['终端总数', '已激活终端'],
                    data: [
                        {   name:"all",
                            icon:'circle',
                        },
                        {   name:"activation",
                            icon:'circle'
                        }
                    ],
                    right:0,
                    textStyle:{
                        color:'#1b1e22',
                        fontSize:12
                    }
                },
                grid: {
                    left: '1%',
                    right: '6%',
                    bottom: '0%',
                    containLabel: true,
                    top:'30%',
                },
                toolbox: {},
                calculable: true,
                xAxis: [
                    {
                        //name:'(月)',
                        type: 'category',
                        splitLine: {show: false},
                        boundaryGap: false,
                        data: $scope.month
                    }
                ],
                yAxis: [
                    {
                        name:"",
                        type: 'value',
                        splitLine: {show: false},

                    }
                ],
                series: [
                    {
                        name: "all",
                        type: 'line',
                        smooth: true,
                        itemStyle: {normal: {color: '#5280e4', areaStyle: {type: 'default'}}},
                        data: $scope.terminalCount
                    },
                    {
                        name: "activation",
                        type: 'line',
                        smooth: true,
                        itemStyle: {normal: {color: '#7cd4f2', areaStyle: {type: 'default'}}},
                        data: $scope.actCount
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            firstEc.setOption(option);
        });
        BaseApi.get("/apkInfo/getApkCount","", function (data){
            $scope.apkCount = data.apkCount;
            $scope.recCount = data.recCount;
            $scope.month = data.month;
            option2= {
                backgroundColor: 'white',
                title : {
                    text: "Application",
                    subtext: '',
                    textStyle:{
                        color:'#14181a',
                        fontSize:18,
                        fontWeight:'normal',
                    },
                },
                tooltip : {
                    trigger: 'axis',
                    formatter:function(params){
                        if(params[0]){
                            var index = params[0].dataIndex;
                            return params[0].name+'<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#7cd4f2"></span>'+"All Applications"+':'+$scope.apkCount[index]
                                +'&nbsp;&nbsp;&nbsp;<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#5280e4"></span>'+"Released Applications"+':'+$scope.recCount[index]
                        }
                    }
                },
                legend: {
                    data: [
                        {   name:"all",
                            icon:'circle',
                        },
                        {   name:"published",
                            icon:'circle',
                        },
                    ],
                    right:0,
                    textStyle:{
                        color:'#1b1e22',
                        fontSize:12,
                    }
                },
                grid: {
                    left: '1%',
                    right: '0%',
                    bottom: '0%',
                    containLabel: true,
                    top:'20%',
                },
                calculable : true,
                //      		    grid: {y: 70, y2:30, x2:20},
                xAxis : [

                    {
                        type : 'category',
                        splitLine: {show:false},
                        data : $scope.month
                    },
                    {
                        type : 'category',
                        axisLine: {show:false},
                        axisTick: {show:false},
                        axisLabel: {show:false},
                        splitArea: {show:false},
                        splitLine: {show:false},
                        data :$scope.month
                    }
                ],
                yAxis : [
                    {
                        type : 'value',
                        splitLine: {show:false},
                        axisLabel:{formatter:'{value} '}
                    }
                ],
                series : [
                    {
                        name:"published",
                        type:'bar',
                        xAxisIndex:1,
                        itemStyle: {normal: {color:'rgba(68,149,241,1)', label:{show:true,textStyle:{color:'#E87C25'}}}},
                        data:$scope.recCount
                    },
                    {
                        name:"all",
                        type:'bar',
                        itemStyle: {normal: {color:'rgba(68,149,241,0.5)', label:{show:true,textStyle:{color:'#E87C25'}}}},
                        data:$scope.apkCount
                    }
                ]
            };

            secondEc.setOption(option2);
            secondEc.on('legendselectchanged', function (params){
            });
        });

        // 基于准备好的dom，初始化echarts实例
        var firstEc = echarts.init(document.getElementById('term_chart'));
        var secondEc = echarts.init(document.getElementById('app_chart'));
        BaseApi.query("/driverJob/jobListEcharts",{},function (result) {
            $scope.joblist = result;
        });

    };
});