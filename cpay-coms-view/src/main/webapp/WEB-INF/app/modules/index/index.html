<div ui-view></div>

<div class="container-fluid index-content">

    <div class="row-fluid">
        <div ng-show="true" class="span6 index-row" id="term_chart">
        </div>
        <div ng-show="true" class="span6 index-row" id="app_chart">
        </div>
    </div>
    <div class="row-fluid">
        <div class="span6 index-row" id="terminal-list">
            <div class="index-title">
                <strong>Update Task</strong>
                <span class="index-more-title" ng-click="$state.go('home.apkUpdateReport.list')">more+</span>
            </div>
            <div class="index-info-content" ng-repeat="job in joblist"   title="task total{{job.number}} ，success {{job.success}} ， fail {{job.failureUpdate}} , waiting {{job.waitSend}} , executing {{job.successDown}} ">
                <a href="javascript:;" ng-click="$state.go('home.driverJob.task', {id:job.id});"
                   class="index-info-title index-info-undo">{{job.jobName}}</a>
                <span class="index-info-date">
                         {{job.number}}/{{job.success}}/{{job.failureUpdate}}/{{job.waitSend}}/{{job.successDown}}</span>
            </div>

        </div>
        <div class="span6 index-row">
            <div class="index-title">
                <strong>Information</strong>
                <span class="index-more-title" ng-click="$state.go('home.info',{id:'more'})">more+</span>
            </div>
            <div class="index-info-content" ng-repeat="info in infodata" ng-if="$index<3">
                <a ng-href="#" ng-click="$state.go('home.info', {id: info.id})" title="{{info.title}}"
                   class="index-info-title index-info-undo">
                    {{info.title}} &nbsp;&nbsp;<span ng-show="info.new" class="badge badge-pill badge-important">new</span></a><span
                    class="index-info-date">{{info.createTime | datefmt}}</span>
            </div>
            <hr>
            <div class="index-title">
                <strong>ToDo</strong>
                <span class="index-more-title" ng-click="$state.go('home.matters')">more+</span>
            </div>
            <div class="index-info-content" ng-if="apkAuditCount>0">
                <a href="javascript:;" title="applications were not audited" ng-click="$state.go('home.audit.list')"
                   class="index-info-title index-info-undo">&nbsp;{{apkAuditCount}}&nbsp;applications were not audited。</a><span
                    class="index-info-date">{{date| datefmt}}</span>
            </div>
            <div class="index-info-content" ng-if="apkPubCount>0">
                <a href="javascript:;" title="applications were not released" ng-click="$state.go('home.pub.list')"
                   class="index-info-title index-info-undo">&nbsp;{{apkPubCount}}&nbsp;applications were not released。</a><span
                    class="index-info-date">{{date| datefmt}}</span>
            </div>
        </div>
    </div>
</div>