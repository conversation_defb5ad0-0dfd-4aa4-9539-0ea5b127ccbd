<div class="container-fluid" id="breadcrumb-scroll">
    <div class="row-fluid">
        <div class="span12">
            <div class="nav-breadcrumb">
                <div class="breadcrumb-icon" style="background-image:url(images/default/menu/pic_menu_setting_grey.png)"></div>         	           	
                <ul class="breadcrumb">
                    <li> <a ui-sref="home.index">Home</a> <i class="icon-angle-right"></i></li>
                    <li><span>HelpCenter</span></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="hr"></div>
</div>
<div class="content-scroller">
    <div class="content-main">
        <div ng-if="moreData" class="info-content-more">
            <div ng-repeat="info in moreData.rows" class="index-info-content">
                <a ng-href="#/home/<USER>/{{info.id}}" title="{{info.title}}" class="index-info-title index-info-undo">
                    {{info.title}}</a><span
                    class="index-info-date">{{info.createTime | datefmt}}</span>
                    <div class="hr"></div>
            </div>
        </div>
        <div class="info-content" ng-if="content">
            <h1>{{title}}</h1>
           <div class="index-info-detail-date">{{createTime|datefmt}}</div>
           <div class="hr"></div>
            <div class="index-info-detail-content">{{content}}</div>
        </div>
		<div class="row-fluid" ng-show="showFlag=='more'">
			<div class="span12">
				<div class="info-footer-container">
					<div class="pagination-footer">
						<div class="pull-left pagination-detail">
							<span class="pagination-info">
								total&nbsp; {{total}}&nbsp; record
							</span>
							<!-- <span class="page-list">
								Per page {{rowNum}} record
							</span> -->
						</div>
						<div class="twpagination-pager">
							<ul id="pagination-demo" class="pagination-sm"></ul>
						</div>
					</div>
				</div>
			</div>
		</div>
    </div>
</div>