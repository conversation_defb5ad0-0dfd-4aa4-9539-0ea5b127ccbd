'use strict';
define([ "dialogService", "organizationService", "formService",
		"modules/system/user/js/userService.js" ], function() {
	return function($scope, userService, BaseApi, $state, dialogService, formService, $http) {
			$scope.currentuser = BaseApi.getUser();
			$scope.readOnly = true;
			$scope.foot = true;
			$scope.codeFlag = false;
			$scope.emailFlag = false;
			$scope.user = {};
			var count;
			$scope.getUserInfo = function(){
				userService.getUser($scope.currentuser.id, function(data) {
					var status = {
						0 : "Disable",
						1 : "Unverified",
						2 : "Enable"
					};
					$scope.user = data;
					var arr = [];
					$scope.phone = $scope.user.telephone;
					$scope.email = $scope.user.email;
					for ( var i in status) {
						if (i == $scope.user.status) {
							$scope.user.status = status[i];
						}
					}
	//				userService.getRoleList(function(data) {
	//					// formService.initMutil();
	//					$scope.roleList = data.rows;
	//					for (var i = 0; i < $scope.roleList.length; i++) {
	//						for (var j = 0; j < $scope.user.roles.length; j++) {
	//							if ($scope.user.roles[j] == $scope.roleList[i].id) {
	//								arr.push($scope.roleList[i].name)
	//							}
	//						}
	//					}
	//					$scope.roleNames = arr.join("，");
	//					if($scope.roleNames==""){
	//						$scope.roleNames="无";
	//					}
	//				});
				});
			}
			$scope.getUserInfo();
			$scope.$watch('user.telephone', function(newValue, oldValue, scope) {
				if ($scope.phone != $scope.user.telephone) {
					$scope.codeFlag = true;
				} else
					$scope.codeFlag = false;
	
			})
			$scope.$watch('user.email', function(newValue, oldValue, scope) {
				if ($scope.email != $scope.user.email) {
					$scope.emailFlag = true;
				} else
					$scope.emailFlag = false;
	
			})
			$scope.sendCode = function() {
				if (!$scope.validatePhone()) {
					return;
				}
				count = 60;
				counttime();
				BaseApi.post("/user/sendCode?telephone=" + $scope.user.telephone,
						{
							telephone : $scope.user.telephone
						}, function(data) {
							if (data.status != 200) {
								dialogService.alertInfo("error", data.msg);
								return;
							}
							dialogService.alertInfo("success", "SMS verification code sent successfully");
						});
			};
			$scope.sendEmail = function() {
				if(!$scope.validateEmail()){
				   return;
				}
				BaseApi.post("/user/sendEmail?email="+ $scope.user.email,{email:$scope.user.email}, function(data) {
					if (data.status != 200) {
						dialogService.alertInfo("error", data.msg);
						return;
					}
					dialogService.alertInfo("success", "Email verification code sent successfully");
			    },60000);
			};
			var counttime = function() {
				if (count == 0) {
					$("#get-code").html("Obtain SMS verification code");
					$("#get-code").attr("class", "resend-code-nor");
					$("#get-code").click(function() {
						$scope.sendCode();
					});
					return;
				} else {
					$("#get-code").unbind("click");
					$("#get-code").attr("class", "resend-code-wait");
					$("#get-code").html(count + "Seconds");
					count--;
				}
				setTimeout(function() {
					counttime()
				}, 1000)
			};
			$scope.validateEmail = function() {
				if (!$scope.user || !$scope.user.email) {
					dialogService.alertInfo("error", "E-mail can not be empty");
					return false;
				}
				if (!(/^\s*$|^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/
						.test($scope.user.email))) {
					dialogService.alertInfo("error", "Incorrect mailbox format");
					return false;
				}
				return true;
			}
			$scope.validatePhone = function() {
				if (!$scope.user || !$scope.user.telephone) {
					dialogService.alertInfo("error", "Phone number can not be blank");
					return false;
				}
				if (!(/^(13[0-9]|14[0-9]|15[0-9]|17[0-9]|18[0-9])\d{8}$/
						.test($scope.user.telephone))) {
					dialogService.alertInfo("error", "Wrong format of phone number");
					return false;
				}
				return true;
			}
			$scope.form = formService.form(function() {
				var user = {
					email : $scope.user.email,
					telephone : $scope.user.telephone,
					realname : $scope.user.realname,
					emailCode : $scope.user.emailCode
				};
				if($scope.user.smsCode){
					user.smsCode=$scope.user.smsCode;
				}
				BaseApi.post("/user/updateUserInfo", user, function(data) {
					if(data.status!=200){
					 dialogService.alertInfo("success", data.msg);
					 return ;
					}
					$scope.getUserInfo();
					dialogService.alertInfo("success", "Successfully Modified Personal Information!");
					$state.go('home.index');
				});
			});
	};
});
