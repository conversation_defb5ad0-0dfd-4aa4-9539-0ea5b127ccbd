package com.centerm.cpay.common.utils;

public class CommConstant {

	/**
	 * 初始向量 0000000000000000
	 */
	public static final String INIT_VECTOR = "0000000000000000";
	/**
	 * 验证码类型 1-商户注册
	 */
	public static final String AUTHCODE_TYPE_REGISTER = "1";

	/**
	 * 套餐启用类型 1-立即生效
	 */
	public static final String PACKAGE_START_TYPE_SOON = "1";

	/**
	 * 套餐启用类型 2-套餐有效期截止后生效
	 */
	public static final String PACKAGE_START_TYPE_NEXT = "2";

	/**
	 * 订单状态 0-初始化
	 */
	public static final String ORDER_STATE_INT = "0";

	/**
	 * 订单状态 1-支付成功
	 */
	public static final String ORDER_STATE_SUCCESS = "1";
	
	/**
	 * 订单状态 1-支付失败
	 */
	public static final String ORDER_STATE_EXPIRED = "5";

	/**
	 * 订单类型 1-流量套餐
	 */
	public static final String ORDER_TYPE_PACKAGE = "1";

	/**
	 * 订单类型 2-延保服务
	 */
	public static final String ORDER_TYPE_WARRENTY = "2";
	
	/**
	 * 订单类型 4-充值
	 */
	public static final String ORDER_TYPE_RECHARGE = "4";
	
	
	/**
	 * 订单类型 2-充值
	 */
	public static final String DB_ORDER_TYPE_RECHARGE = "2";
	
	/**
	 * 订单类型 4-套餐变更
	 */
	public static final String DB_ORDER_TYPE_CHANGE = "4";
	
	/**
	 * 订单类型 1-账户预存
	 */
	public static final String DB_ORDER_TYPE_SAVE = "1";
	
	/**
	 * 订单类型 3-套餐消费
	 */
	public static final String DB_ORDER_TYPE_COMSUME = "3";

	/**
	 * 终端状态 0-未激活
	 */
	public static final String TERMINAL_STATE_UNACTIVE = "0";
	
	/**
	 * 终端状态 1-启用
	 */
	public static final int TERMINAL_STATE_ENABLED = 1;
	
	/**
	 * 终端状态 0-停用
	 */
	public static final int TERMINAL_STATE_DISENABLED = 0;

	/**
	 * 终端状态 1-已激活
	 */
	public static final String TERMINAL_STATE_ACTIVED = "1";

	/**
	 * 商户状态 0-未认证
	 */
	public static final String MERCHANT_STATE_UNAUTHORIZED = "0";

	/**
	 * 商户状态 1-已认证
	 */
	public static final String MERCHANT_STATE_AUTHORIZED = "1";

	
	/**
	 * 商户状态 2-认证失败
	 */
	public static final String MERCHANT_STATE_AUTHFIAL = "2";
	/**
	 * 商户查询类型 1-终端激活时查询
	 */
	public static final String QUERY_TYPE_TERINAL = "1";

	/**
	 * 商户查询类型 2-普通查询
	 */
	public static final String QUERY_TYPE_OTHER = "2";
	/**
	 * 应答成功
	 */
	public static final String ERQUST_SUCCESS_CODE = "00";
	
	/**
	 * 业务异常
	 */
	public static final String ERQUST_BUS_ERROR = "10";

	/**
	 * 操作失败
	 */
	public static final String ERQUST_FAIL_CODE = "01";

	/**
	 * 操作error
	 */
	public static final String ERQUST_ERROR_CODE = "03";

	/**
	 * 验证结果成功标识符
	 */
	public static final String VALIDATION_RESLUT_SUCCESS = "SUCCESS";
	/**
	 * 验证手机号码正则表达式
	 */
	public static final String EXPRESS_PHONE = "^(((13[0-9]{1})|(15[0-9]{1})|(17[0-9]{1})|(18[0-9]{1}))+\\d{8})$";
	
	/**
	 * 验证非0正整数正则表达式
	 */
	public static final String EXPRESS_MERCHANT_ID = "^\\+?[1-9][0-9]*$";
	/**
	 * 以字母开头的6到18位字符
	 */
	public static final String EXPRESS_PASSWORD = "^[a-zA-Z]\\w{5,17}$";
	/**
	 * 20位数字正则表达式
	 */
	public static final String EXPRESS_INTEGER_20 = "\\d{20}";

	/**
	 * 1~10位数字表达式
	 */
	public static final String EXPRESS_ID = "\\d{1,10}";
	/**
	 * 字母、数字、汉字、_、&
	 */
	public static final String EXPRESS_MERCHANT_NAME = "^[\u4e00-\u9fa5],{0,}$";
	/***
	 * 数字、字母与- 正则表达式
	 */
	public static final String EXPRESS_CREDNO = "[a-zA-Z0-9-]*";

	/***
	 * 【****】省
	 */
	public static final String EXPRESS_PROVINCE = "[u4e00-u9fa5]{2,10}+省";

	/***
	 * 【****】市
	 */
	public static final String EXPRESS_CITY = "[u4e00-u9fa5]{2,10}+市";

	/***
	 * 【****】区/县
	 */
	public static final String EXPRESS_COUNTRY = "[u4e00-u9fa5]{2,10}+区|[u4e00-u9fa5]{2,10}+县";

	/***
	 * 地址
	 */
	public static final String EXPRESS_ADDRESS = "([^\\x00-\\xff]|[A-Za-z0-9_])+";
	/**
	 * IP正在表达式
	 */
	public static final String EXPRESS_IP = "/^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])$/";
	/**
	 * 1位数字 正则表达式
	 */
	public static final String EXPRESS_INT_ONE = "^\\d{1}$";

	/**
	 * 身份证 正则表达式
	 */
	public static final String EXPRESS_IDCARD = "^(\\d{6})(\\d{4})(\\d{2})(\\d{2})(\\d{3})([0-9]|X|x)$";

	/**
	 * 姓名 正则表达式
	 */
	public static final String EXPRESS_NAME = "^([\\u4e00-\\u9fa5]+|([a-z]+\\s?)+)$";

	/**
	 * imsi 正则表达式
	 */
	// TODO
	public static final String EXPRESS_IMSI = "";

	/**
	 * m2mNo 正则表达式
	 */
	// TODO
	public static final String EXPRESS_M2MNO = "";
	/**
	 * 运营商编号 正则表达式
	 */
	public static final String EXPRESS_SERCICEPROVIRDER = "\\d{5}";

	public static final String MESSAGE_PHONE = "请输入有效的手机号码";
	
	public static final String MESSAGE_MAIL = "请输入有效的电子邮箱";

	public static final String MESSAGE_MERCHANT_ID = "请输入有效的商户编号";

	public static final String MESSAGE_AUTHCODE_TYPE = "请输入有效的验证码类型";

	public static final String MESSAGE_AUTHCODE = "请输入有效的验证码";

	public static final String MESSAGE_SETPASSWORD = "密码格式过于简单，请修改";

	public static final String MESSAGE_PASSWORD = "请输入密码";

	public static final String MESSAGE_ICCID = "请输入有效的ICCID";

	public static final String MESSAGE_CONFIRMPWD = "请输入确认密码";

	public static final String MESSAGE_OLDPASSWORD = "请输入原密码";

	public static final String MESSAGE_MERCHANT_NAME = "商户名称不允许为空";

	public static final String MESSAGE_CERTIFICATE = "请输入有效的证件类型";//证件号码包含数字、字母、-";

	public static final String MESSAGE_CERTIFICATE_TYPE = "请输入有效的证件类型";

	public static final String MESSAGE_PROVINCE = "请输入省份信息";

	public static final String MESSAGE_CITY = "请输入市级行政区域信息";

	public static final String MESSAGE_COUNTY = "请输入区县信息";

	public static final String MESSAGE_ADDRESS = "请输入地址信息";

	public static final String MESSAGE_IDCARD = "请输入有效的身份证件号码";

	public static final String MESSAGE_APPLER_NAME = "请输入有效法人的姓名";

	public static final String MESSAGE_RESOURCE = "请输入有效的终端来源";

	public static final String MESSAGE_IMSI = "请输入有效的IMSI编号";

	public static final String MESSAGE_M2MNO = "请输入有效的M2MNO编号";

	public static final String MESSAGE_SERCICEPROVIRDER = "请输入有效的运营商编号";

	public static final String MESSAGE_TYPE = "请输入有效的套餐类型";

	public static final String MESSAGE_QUERY_TYPE = "请输入有效的查询类型";

	public static final String MESSAGE_ACTIVE_CODE = "请输入激活码";

	public static final String MESSAGE_RANDOM_KEY = "请输入随机密钥";

	public static final String MESSAGE_ORDER_TYPE = "请输入有效的订单类型";

	public static final String MESSAGE_ORDER_STATE = "请输入有效的订单状态";

	public static final String MESSAGE_PAY_TYPE = "请输入有效的支付类型";

	public static final String MESSAGE_PAY_ID = "请输入支付方订单号";

	public static final String MESSAGE_ORDER_NO = "请输入有效的订单编号";

	public static final String MESSAGE_ITEM_ID = "请输入有效的套餐编号";

	public static final String MESSAGE_FLOW_INFO = "请输入有效的流量数据信息";
	
	public static final String MESSAGE_NAME = "请输入有效的姓名";
	
	public static final String MESSAGE_RANDOMKEY = "请输入RandomKey";
	
	public static final String MESSAGE_TERMINAL_TYPE = "请输入终端型号";
	
	public static final String MESSAGE_TERMINAL_FACTORY = "请输入终端厂商编码信息";
	
	public static final String MESSAGE_ACCESS_CODE = "请输入机构编码";
	
	public static final String MESSAGE_LONGITUDE = "请输入经度信息";
	
	public static final String MESSAGE_LATITUDE = "请输入纬度信息";
	
	public static final String MESSAGE_SCREENTYPE = "请输入应用屏幕类型";
	
	public static final String MESSAGE_APP_CODE = "请输入应用包名";
	
	public static final String MESSAGE_PAGE_SIZE = "请输入分页大小";
	
	public static final String MESSAGE_PAGE_INDEX = "请输入页码";
	
	public static final String MESSAGE_APKTYPE_ID = "请输入应用类别ID";
	
	public static final String MESSAGE_KEYWORD = "请输入关键字";
	
	public static final String MESSAGE_REQUEST_HEADER = "报文头不存在";
	
	public static final String MESSAGE_REQUEST_MAC = "报文校验值sign不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_VERSION = "报文版本信息不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_NETMARK = "报文终端网卡地址不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_DEVMASK = "报文终端序列号不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_IMEI = "报文终端IMEI不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_TIME = "报文时间戳不存在";
	
	public static final String MESSAGE_REQUEST_HEADER_RANDOM = "报文随机数不存在";
	
	/**
	 * 物联卡套餐 状态 1-使用中
	 */
	public static final String CARDPACKAGE_STATE_UESD = "1";

	/**
	 * 物联卡套餐 状态 0-未启用（初始化）
	 */
	public static final String CARDPACKAGE_STATE_INT = "0";

	/**
	 * 物联卡套餐 状态 2-已停用
	 */
	public static final String CARDPACKAGE_STATE_UNSED = "2";

	/**
	 * 套餐类型 1-基础套餐
	 */
	public static final String PACKAGE_TYPE_BASE = "1";

	/**
	 * 套餐类型 1-加油包套餐
	 */
	public static final String PACKAGE_TYPE_ADD = "2";
	/**
	 * 套餐状态  0-未使用
	 */
	public static final String PACKAGE_STATE_INIT = "0";
	
	/**
	 * 套餐状态  1-使用中
	 */
	public static final String PACKAGE_STATE_USING = "1";
	
	/**
	 * 套餐状态  2-已过期
	 */
	public static final String PACKAGE_STATE_USED = "1";
	/**
	 * 启用类型 1-启用
	 */
	public static final String OPEN_FLAG_YES = "1";

	/**
	 * 启用类型 1-不启用
	 */
	public static final String OPEN_FLAG_NO = "0";

	/**
	 * 商户信息来源 1-Cpay 2-其他
	 */
	public static final String MERCHANT_RESOURCE_CPAY = "1";
	
	/**
	 * 应用列表查询 90001-按推荐度查询
	 */
	public static final String APK_LIST_TYPE_RECOMMENDED  = "90001";
	
	/**
	 * 应用列表查询 90002-按下载量查询
	 */
	public static final String APK_LIST_TYPE_DOWNLOAD = "90002";
	
	/**
	 * 运营商编号 联通：46001
	 */
	public static final int SERVICE_PROVIDER_UNICOM = 46001;
	
	/**
	 * 运营商编号 电信：46003
	 */
	public static final int SERVICE_PROVIDER_TELECOM = 46003;
	
	/**
	 * 外部机构 999
	 */
	public static final String INSTITUITON_OUTTER = "999";
	
	/**
	 * 终端自动激活标志
	 */
	public static final int TERMINAL_AUTO_ACTIVE = 1;
	
	/**
	 * 系统参数配置模块 剩余流量限额提醒
	 */
	public static final String SYSCONFIG_LIMTFLOW_REMIND = "remindflow";
	
	/**
	 * 流量提醒标识 0-未提醒 、1-已提醒
	 */
	public static final Byte REMIND_FLAG_DONE = 1;
	
	/**
	 * 流量提醒标识 0-未提醒 、1-已提醒
	 */
	public static final Byte REMIND_FLAG_NODONE = 0;
	
	
	/**
	 * 消息类型 1-流量提醒短信
	 */
	public static final Byte M2M_MESSAGE_FLAG_REMIND = 1;
	
	/**
	 * 消息发送状态 1-成功 
	 */
	public static final String MESSAGE_SEND_SUCCESS ="1";
	
	/**
	 * 消息发送状态 0-初始化/待发送 
	 */
	public static final String MESSAGE_SEND_INIT ="3";
	
	/**
	 * 消息发送状态 2-失败   
	 */
	public static final String MESSAGE_SEND_FAIL ="2";
	
	/**
	 * 广告类型
	 */
	public static final String AD_TYPE_WEB = "4";
	
	/**
	 * 终端网卡地址字符串以000开头
	 */
	public static final String TERMINAL_NETMARK_FLAG = "000";
	
	public static final String DIC_SERVICE_PROIVIDER = "service_provider";
	
	public static final String MERCHANT_STATE_AUTHFAIL = "2";
	
	
	public static final String GAODE_MAP_KEY = "a9580c7ad04e146150af18f064558a3a";
	/**
	 * 已恢复达量断网
	 */
	public static final String FLOW_LIMIT_FLAG_TRUE = "1";
	/**
	 * 未恢复达量断网
	 */
	public static final String FLOW_LIMIT_FLAG_FALSE = "0";
	/**
	 * 是否存在子操作 1-存在
	 */
	public static final String CONTINUE_FLAG_TRUE = "1";
	/**
	 * 是否存在子操作 0-不存在
	 */
	public static final String CONTINUE_FLAG_FALSE = "0";
	/**
	 * 报文sign不存在
	 */
	public static final String MESSAGE_REQUEST_SIGN = "报文sign不存在";
	
	
	public static final String ALGORITHM_SHA256WITHRSA = "ALGORITHM_SHA256WITHRSA";
	
}