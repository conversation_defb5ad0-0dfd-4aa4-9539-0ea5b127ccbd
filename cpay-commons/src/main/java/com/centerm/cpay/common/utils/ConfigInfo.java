package com.centerm.cpay.common.utils;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description 配置文件对应类
 * <AUTHOR>
 */
public class ConfigInfo {

	public static String DOWNLOAD_URL = PropertyUtil.getValue("downloadUrl");// 文件下载地址
	public static String IS_FASTDFS = PropertyUtil.getValue("isFastDFS");// 是否开启分布式文件存储
	public static String APPSTORE_PATH = PropertyUtil.getValue("appstorepath");// 应用下载地址前缀
	public static String HEADER_PATH = PropertyUtil.getValue("headerpath");// 系统应用下载路径
	public static String AD_PATH = PropertyUtil.getValue("adpath");// 广告存储路径
	public static String LAUNCHER_PATH = PropertyUtil.getValue("launcherpath");// lanucher存储路径
	public static String KEY_FILEPATH = PropertyUtil.getValue("keyFilePath");// 签名key存放路径
	public static String APPT_PATH = PropertyUtil.getValue("apptpath");// #appt保存路径
	public static String APPT_TEMP_FILEPATH = PropertyUtil.getValue("apptTempFilePath");// appt保存的临时路径
	public static String TASK_MAX_DISTANCE = PropertyUtil.getValue("task.max.distance");// 终端移机最大距离(单位/米)
	public static Integer AUTHCODE_EXPIRED = Integer.valueOf(PropertyUtil.getValue("authcode.expired"));// #短信验证码过期时间
	public static Integer WARRANTY_MONTH = Integer.valueOf(PropertyUtil.getValue("warranty.month"));// 维保有效期
	public static Integer SEND_MAX_TIME = Integer.valueOf(PropertyUtil.getValue("send.max.time"));//短信发送最大次数
	public static String SYNC_FILE_PATH = PropertyUtil.getValue("sync.file.path");//终端商户信息数据待同步的文件存储路径
	public static String TERM_FILE_PATH_HEADER = PropertyUtil.getValue("term.file.path.header");//终端数据同步文件文件头
	public static String MER_FILE_PATH_HEADER = PropertyUtil.getValue("mer.file.path.header");//商户数据同步文件同步文件头
	public static String FILE_DATA_SEPARATOR = PropertyUtil.getValue("file.data.separator");//数据分隔符号
	public static String FILE_UPLOAD_TEMP = PropertyUtil.getValue("file.upload.temp");// 文件下载地址
	public static String FILE_SETTING_HEADER = PropertyUtil.getValue("file.setting.header");// 配置文件文件头
	public static String DL_RESET_NUM = PropertyUtil.getValue("dlresetnum");// 配置文件文件头
	public static String AMS_HOTWORDS_SEARCH = PropertyUtil.getValue("ams.hotwords.search");//ams热词搜索功能

	//【Cloudflare】R2存储信息
	public static String R2_URL = PropertyUtil.getValue("r2.url");
	public static String R2_ACCESS_KEY = PropertyUtil.getValue("r2.access.key");
	public static String R2_SECRET_KEY = PropertyUtil.getValue("r2.secret.key");
	public static String R2_BUCKET = PropertyUtil.getValue("r2.bucket");
	public static String R2_DOWNLOAD_URL = PropertyUtil.getValue("r2.download.url");

	/**
	 * 分布式文件存储不分类存储
	 */
	public static String getFilePath(String path) {
		if ("true".equals(IS_FASTDFS)) {
			return "";
		}
		return path;
	}
}