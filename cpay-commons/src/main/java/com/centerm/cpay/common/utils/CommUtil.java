package com.centerm.cpay.common.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.centerm.cpay.common.enums.M2mCardStatus;
import com.fasterxml.jackson.databind.JsonNode;

import net.sf.json.JSONObject;

public class CommUtil {

	static Logger logger = LoggerFactory.getLogger(CommUtil.class);
	private static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;

	/**
	 * 除去数组中的空值,同时将map中value全部转为String
	 * 
	 * @param sArray
	 *            签名参数组
	 * @return 去掉空值后的参数组
	 */
	public static Map<String, String> paraFilter(Map<String, String> sArray) {

		Map<String, String> result = new HashMap<String, String>();

		if (sArray == null || sArray.size() <= 0) {
			return result;
		}

		for (String key : sArray.keySet()) {
			Object value = sArray.get(key);
			if (value == null || value.equals("")) {
				continue;
			}
			if (value instanceof Integer || value instanceof Long || value instanceof Double
					|| value instanceof Float) {
				result.put(key, String.valueOf(value));
			} else if (value instanceof Map || value instanceof List) {
				result.put(key, value.toString());
			} else {
				result.put(key, (String) value);
			}

		}

		return result;
	}

	/**
	 * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
	 * 
	 * @param params
	 *            需要排序并参与字符拼接的参数组
	 * @return 拼接后字符串
	 */
	public static String createLinkString(Map<String, String> params) {

		List<String> keys = new ArrayList<String>(params.keySet());
		Collections.sort(keys);

		String prestr = "";

		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			String value = params.get(key);

			if (i == keys.size() - 1) {// 拼接时，不包括最后一个&字符
				prestr = prestr + key + "=" + value;
			} else {
				prestr = prestr + key + "=" + value + "&";
			}
		}

		return prestr;
	}

	public static float changeKBtoByte(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.multiply(step).floatValue();
	}

	public static float changeMBtoByte(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.multiply(step).multiply(step).floatValue();
	}

	public static long changeMBtoLongByte(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.multiply(step).multiply(step).longValue();
	}

	public static float changeGBtoMB(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.multiply(step).floatValue();
	}

	public static float changeKBtoMB(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.divide(step).floatValue();
	}

	public static float changeGBtoByte(BigDecimal flow) {
		BigDecimal step = new BigDecimal("1024");
		return flow.multiply(step).multiply(step).multiply(step).floatValue();
	}

	public static long changeMBtoByte(long flow) {
		BigDecimal step = new BigDecimal("1024");
		BigDecimal bigFlow = new BigDecimal(flow);
		return bigFlow.multiply(step).multiply(step).longValue();
	}

	public static float changeMBtoFloatByte(long flow) {
		BigDecimal step = new BigDecimal("1024");
		BigDecimal bigFlow = new BigDecimal(flow);
		return bigFlow.multiply(step).multiply(step).floatValue();
	}

	public static long changeGBtoByte(long flow) {
		BigDecimal step = new BigDecimal("1024");
		BigDecimal bigFlow = new BigDecimal(flow);
		return bigFlow.multiply(step).multiply(step).multiply(step).longValue();
	}

	public static float changeByte2MB(long bytes) {
		BigDecimal filesize = new BigDecimal(bytes);
		BigDecimal megabyte = new BigDecimal(1024 * 1024);
		float returnValue = filesize.divide(megabyte, 2, BigDecimal.ROUND_UP).floatValue();
		// if (returnValue > 1)
		return returnValue;
		// BigDecimal kilobyte = new BigDecimal(1024);
		// returnValue = filesize.divide(kilobyte, 2, BigDecimal.ROUND_UP)
		// .floatValue();
		// return returnValue ;
	}

	public static String changeUnicomCardStatusToM2mStatus(String unicomCardStatus) {
		switch (unicomCardStatus) {
		case "TEST_READY_NAME":
			return M2mCardStatus.INIT.getCode();
		case "INVENTORY_NAME":
			return M2mCardStatus.ACTIVATED.getCode();
		case "TRIAL_NAME":
			return M2mCardStatus.ACTIVATED.getCode();
		case "ACTIVATION_READY_NAME":
			return M2mCardStatus.ACTIVATED.getCode();
		case "ACTIVATED_NAME":
			return M2mCardStatus.ACTIVATED.getCode();
		case "DEACTIVATED_NAME":
			return M2mCardStatus.UNACTIVATED.getCode();
		case "RETIRED_NAME":
			return M2mCardStatus.UNACTIVATED.getCode();
		default:
			return M2mCardStatus.ACTIVATED.getCode();
		}
	}

	/**
	 * @Description: TODO 计算两点经纬距离
	 * @param @param
	 *            long1 维度1
	 * @param @param
	 *            lat1 经度1
	 * @param @param
	 *            long2 维度2
	 * @param @param
	 *            lat2 经度2
	 * @return double 单位米
	 */
	public static double distanceLogLat(double long1, double lat1, double long2, double lat2) {
		double a, b, R;
		R = 6378137; // 地球半径
		lat1 = lat1 * Math.PI / 180.0;
		lat2 = lat2 * Math.PI / 180.0;
		a = lat1 - lat2;
		b = (long1 - long2) * Math.PI / 180.0;
		double d;
		double sa2, sb2;
		sa2 = Math.sin(a / 2.0);
		sb2 = Math.sin(b / 2.0);
		d = 2 * R * Math.asin(Math.sqrt(sa2 * sa2 + Math.cos(lat1) * Math.cos(lat2) * sb2 * sb2));
		return d;
	}

	/**
	 * 将 GCJ-02 坐标转换成 BD-09 坐标 GoogleMap和高德map用的是同一个坐标系GCJ-02
	 */
	public static String[] bd_encrypt(String gg_lat, String gg_lon) {
		double bd_lat = 0.0;
		double bd_lon = 0.0;
		String location[] = new String[2];
		double x = Double.parseDouble(gg_lon), y = Double.parseDouble(gg_lat);
		double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
		bd_lon = z * Math.cos(theta) + 0.0065;
		bd_lat = z * Math.sin(theta) + 0.006;
		location[0] = String.valueOf(bd_lat);
		location[1] = String.valueOf(bd_lon);
		return location;
	}

	/**
	 * 将 BD-09 坐标转换成 GCJ-02 坐标 GoogleMap和高德map用的是同一个坐标系GCJ-02
	 */
	public static String[] bd_decrypt(String bd_lat, String bd_lon) {
		double gg_lat = 0.0;
		double gg_lon = 0.0;
		String location[] = new String[2];
		double x = Double.parseDouble(bd_lon) - 0.0065, y = Double.parseDouble(bd_lat) - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
		gg_lon = z * Math.cos(theta);
		gg_lat = z * Math.sin(theta);
		location[0] = String.valueOf(gg_lat);
		location[1] = String.valueOf(gg_lon);
		return location;
	}

	private static String KEY = "aa4a48297242d22d2b3fd6eddfe62217";

	private static Pattern pattern = Pattern.compile("\"location\":\"(\\d+\\.\\d+),(\\d+\\.\\d+)\"");

	public static double[] addressToGPS(String address) {
		try {

			String url = String.format("http://restapi.amap.com/v3/geocode/geo?&s=rsv3&address=%s&key=%s", address,
					KEY);
			URL myURL = null;
			URLConnection httpsConn = null;
			try {
				myURL = new URL(url);
			} catch (MalformedURLException e) {
				e.printStackTrace();
			}
			InputStreamReader insr = null;
			BufferedReader br = null;
			httpsConn = (URLConnection) myURL.openConnection();// 不使用代理
			if (httpsConn != null) {
				insr = new InputStreamReader(httpsConn.getInputStream(), "UTF-8");
				br = new BufferedReader(insr);
				String data = "";
				String line = null;
				while ((line = br.readLine()) != null) {
					data += line;
				}
				Matcher matcher = pattern.matcher(data);
				if (matcher.find() && matcher.groupCount() == 2) {
					double[] gps = new double[2];
					gps[0] = Double.valueOf(matcher.group(1));
					gps[1] = Double.valueOf(matcher.group(2));
					return gps;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return null;
	}

	public static String getAddressByPosition(String longtitude,String latitude) {
		
		Map<String, String> paraMap = new HashMap<>();
		paraMap.put("location", longtitude + "," + latitude);
		paraMap.put("extensions", "base");
		paraMap.put("output", "json");
		paraMap.put("key", "a9580c7ad04e146150af18f064558a3a");
		String positionResult = HttpClientUtil.doPost("http://restapi.amap.com/v3/geocode/regeo", paraMap, "GBK");
		logger.debug("地图定位信息结果：{}", positionResult);
		if (StringUtils.isEmpty(positionResult)) {
			return null;
		}
		JsonNode node = JsonUtils.stringToJsonNode(positionResult);
		if (!"1".equals(node.get("status").asText())) {
			return null;
		}
		node = node.get("regeocode").get("formatted_address");
		return node.asText();
	}
	public static void main(String[] args) {
		// double[] data = addressToGPS("福建省福州市仓山区建");
		// System.out.println("经度:" + data[0]);
		// System.out.println("纬度:" + data[1]);
		System.out.println(getAddressByPosition("118.33196946","30.911390430"));
	}
}
