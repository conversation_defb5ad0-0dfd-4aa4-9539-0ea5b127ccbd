package com.centerm.cpay.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 * @version $Id: OrderStatus.java, v 0.1 2016年7月14日 上午9:33:31 sup Exp $
 */
public enum OrderStatus {
	INITIALIZATION("0", "初始化"),
	
	PAY_SUCCESS ("1", "支付成功"),
	
	PAY_FAILURE("2", "支付失败"),
	
	PAY_CANCLE("3", "支付取消"),
    ;
	/* 编码 */
    private String code;

    /* 描述 */
    private String text;

    /**
     * 构造函数
     * @param code
     * @param text
     */
    OrderStatus(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    /**
     * 根据编码返回枚举值
     * @param code
     * @return
     */
    public static OrderStatus getEnums(String code) {
        for (OrderStatus enums : values()) {
            if (StringUtils.equalsIgnoreCase(code, enums.getCode())) {
                return enums;
            }
        }
        return null;
    }
}
