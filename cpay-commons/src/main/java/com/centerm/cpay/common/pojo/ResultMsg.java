package com.centerm.cpay.common.pojo;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 自定义响应结构
 * <p>
 * Title: ResultMsg
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Company: www.centerm.com
 * </p>
 * 
 * <AUTHOR> 赖煌生
 * @date 2016年5月4日 下午2:22:12
 * @version 1.0
 */
public class ResultMsg {
	static Logger logger = LoggerFactory.getLogger(ResultMsg.class);
	public static int SUCCESS_CODE = 200;//成功
	public static int ERROR_CODE = 300;//安全认证不通过
	public static int FAIL_CODE = 500;//程序异常
	public static int INVALID_CODE = 400;//session超时
	public static int BUS_CODE = 415;//session超时
	public static String  msg1 = "Successfully";
	public static String  msg2 = "Failed";
	public static String  msg3 = "Error";

	// 定义jackson对象
	private static final ObjectMapper MAPPER = new ObjectMapper();

	// 响应业务状态
	private Integer status;

	// 响应消息
	private String msg;

	// 响应中的数据
	private Object data;

	public static ResultMsg build(Integer status, String msg, Object data) {
		return new ResultMsg(status, msg, data);
	}

	public static ResultMsg success(Object data) {
		return build(SUCCESS_CODE, msg1, data);
	}

	public static ResultMsg success() {
		return build(SUCCESS_CODE, msg1, null);
	}

	public static ResultMsg error(Object data) {
		return build(ERROR_CODE, msg2, data);
	}

	public static ResultMsg error() {
		return build(ERROR_CODE, msg2, null);
	}

	public static ResultMsg fail(Object data) {
		return build(FAIL_CODE, msg3, data);
	}

	public static ResultMsg fail() {
		return build(FAIL_CODE, msg3, null);
	}

	public ResultMsg() {

	}

	public static ResultMsg build(Integer status, String msg) {
		return new ResultMsg(status, msg, null);
	}

	public ResultMsg(Integer status, String msg, Object data) {
		this.status = status;
		this.msg = msg;
		this.data = data;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	/**
	 * 将json结果集转化为ResultMsg对象
	 * 
	 * @param jsonData
	 *            json数据
	 * @param clazz
	 *            ResultMsg中的object类型
	 * @return
	 */
	public static ResultMsg formatToPojo(String jsonData, Class<?> clazz) {
		try {
			if (clazz == null) {
				return MAPPER.readValue(jsonData, ResultMsg.class);
			}
			JsonNode jsonNode = MAPPER.readTree(jsonData);
			JsonNode data = jsonNode.get("data");
			Object obj = null;
			if (clazz != null) {
				if (data.isObject()) {
					obj = MAPPER.readValue(data.traverse(), clazz);
				} else if (data.isTextual()) {
					obj = MAPPER.readValue(data.asText(), clazz);
				}
			}
			logger.debug(build(jsonNode.get("status").intValue(), jsonNode.get("msg").asText(), obj).toString());
			return build(jsonNode.get("status").intValue(), jsonNode.get("msg").asText(), obj);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 没有object对象的转化
	 * 
	 * @param json
	 * @return
	 */
	public static ResultMsg format(String json) {
		try {
			return MAPPER.readValue(json, ResultMsg.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String toString() {
		return "ResultMsg [status=" + status + ", msg=" + msg + ", data=" + data + "]";
	}

	/**
	 * Object是集合转化
	 * 
	 * @param jsonData
	 *            json数据
	 * @param clazz
	 *            集合中的类型
	 * @return
	 */
	public static ResultMsg formatToList(String jsonData, Class<?> clazz) {
		try {
			JsonNode jsonNode = MAPPER.readTree(jsonData);
			JsonNode data = jsonNode.get("data");
			Object obj = null;
			if (data.isArray() && data.size() > 0) {
				obj = MAPPER.readValue(data.traverse(),
						MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
			}
			return build(jsonNode.get("status").intValue(), jsonNode.get("msg").asText(), obj);
		} catch (Exception e) {
			return null;
		}
	}

}