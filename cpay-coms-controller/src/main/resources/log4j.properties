#\u5F00\u53D1\u65E5\u5FD7\u5C06\u5728\u672C\u5730\u8F93\u51FA\uFF0C\u5E76\u8F93\u51FASQL

log4j.rootLogger=INFO,A1,DRF
log4j.logger.com.centerm = INFO
log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
# log4j.appender.A1.layout.ConversionPattern=%d %5p [%t] (%F:%L) - %m%n
log4j.appender.A1.layout.ConversionPattern=%d %5p [%F:%L] : %m%n

log4j.appender.DRF=org.apache.log4j.RollingFileAppender
log4j.appender.DRF.Threshold=INFO
log4j.appender.DRF.File=logs/cpay-coms-web.log
log4j.appender.DRF.MaxFileSize=50MB
log4j.appender.DRF.MaxBackupIndex=500
log4j.appender.DRF.Append=true
log4j.appender.DRF.BufferSize=1024
log4j.appender.DRF.ImmediateFlush=false
log4j.appender.DRF.BufferedIO=true
log4j.appender.DRF.layout=org.apache.log4j.PatternLayout
log4j.appender.DRF.layout.ConversionPattern=[%t] [%-5p][%d{yyyyMMdd HH:mm:ss,SSS}][%C{1}:%L] %m%n


###\u8F93\u51FASQL 
log4j.logger.com.ibatis=INFO
log4j.logger.com.ibatis.common.jdbc.SimpleDataSource=INFO
log4j.logger.com.ibatis.common.jdbc.ScriptRunner=INFO
log4j.logger.com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate=INFO
log4j.logger.java.sql.Connection=INFO
log4j.logger.java.sql.Statement=INFO
log4j.logger.java.sql.PreparedStatement=INFO







