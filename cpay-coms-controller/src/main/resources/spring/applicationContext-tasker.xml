<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
	http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd">


<!--	&lt;!&ndash; 流水推送 &ndash;&gt;-->
<!--	<bean id="iotJob" class="com.centerm.cpay.coms.tasker.IotSendTask" />-->
<!--	<bean id="iotDitail"-->
<!--		  class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">-->
<!--		<property name="targetObject">-->
<!--			<ref bean="iotJob" />-->
<!--		</property>-->
<!--		<property name="targetMethod">-->
<!--			<value>process</value>-->
<!--		</property>-->
<!--		<property name="concurrent">-->
<!--			<value>false</value>-->
<!--		</property>-->
<!--	</bean>-->
<!--	<bean id="iotTigger"-->
<!--		  class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">-->
<!--		<property name="jobDetail" ref="iotDitail" />-->
<!--		&lt;!&ndash; 每2分钟一次调度 &ndash;&gt;-->
<!--		<property name="cronExpression" value="0 0/3 * * * ?" />-->
<!--	</bean>-->
<!--	-->
<!--	&lt;!&ndash; 定义调度器 &ndash;&gt;-->
<!--	<bean id="startQuartz" class="org.springframework.scheduling.quartz.SchedulerFactoryBean" destroy-method="destroy">-->
<!--		<property name="triggers">-->
<!--			<list>-->
<!--				<ref bean="iotTigger" />-->
<!--			</list>-->
<!--		</property>-->
<!--		<property name="quartzProperties"> -->
<!--			<props>-->
<!--			<prop key="org.quartz.scheduler.skipUpdateCheck">true</prop> -->
<!--			</props>-->
<!--		</property>-->
<!--		<property name="waitForJobsToCompleteOnShutdown" value="true"/>-->
<!--	</bean>-->
</beans>