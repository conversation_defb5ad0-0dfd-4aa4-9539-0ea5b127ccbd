<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/mvc
		http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.2.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.2.xsd ">
	<context:component-scan base-package="com.centerm.cpay.coms.controller" />
	<mvc:annotation-driven/>

	<mvc:resources mapping="/app/**" location="/WEB-INF/app/" />
	<mvc:resources mapping="/js/**" location="/WEB-INF/app/js/" />
	<mvc:resources mapping="/lib/**" location="/WEB-INF/app/lib/" />
	<mvc:resources mapping="/modules/**" location="/WEB-INF/app/modules/" />
	<mvc:resources mapping="/css/**" location="/WEB-INF/app/css/" />
	<mvc:resources mapping="/images/**" location="/WEB-INF/app/images/" />
	<mvc:resources mapping="/error/**" location="/WEB-INF/app/error/" />
	<!--<mvc:default-servlet-handler default-servlet-name="SimpleFileServlet"/>-->
	<!-- <bean
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/app/" />
		<property name="suffix" value=".jsp" />
	</bean> -->
	 <mvc:default-servlet-handler />

	 <bean
			class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/app/" />
		<property name="suffix" value=".html" />
	</bean>
	<!--&lt;!&ndash; 校验器 &ndash;&gt;
	<bean id="validator"
		class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
		&lt;!&ndash; hibernate校验器 &ndash;&gt;
		<property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
		&lt;!&ndash; 指定校验使用的资源文件，在文件中配置校验错误信息，如果不指定则默认使用classpath下的ValidationMessages.properties &ndash;&gt;
		&lt;!&ndash; <property name="validationMessageSource" ref="messageSource" /> &ndash;&gt;
	</bean>-->

	<!-- 全局异常处理器 只要实现HandlerExceptionResolver接口就是全局异常处理器 -->
	<bean class="com.centerm.cpay.coms.exception.CustomExceptionResolver"></bean>


	<!--拦截器 多个拦截器,顺序执行 -->
	<mvc:interceptors>
		<!-- 日志拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.LogInterceptor"></bean>
		</mvc:interceptor>
		<!-- Sql注入拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.AntiSqlInjectionFilter">
				<property name="allowUrls">
					<list>
					    <value>/terminalAdvQuery</value>
					</list>
				</property>
			</bean>
		</mvc:interceptor>
		<!-- 登陆认证拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.SessionInterceptor">
				<property name="allowUrls">
					<list>
					    <value>/resetPwd</value>
					    <value>/systemClean/deleteAlTerm</value>
						<value>/apk/uploadImg</value>
						<value>/apk/uploadIcon</value>
						<value>/ad/testInsert</value>
						<value>/install</value>
						<value>/login</value>
						<value>/js</value>
						<value>/lib</value>
						<value>/modules/</value>
						<value>/css</value>
						<value>/images</value>
						<value>/favicon</value>
						<value>/cpayiot/terminalonline</value>
					</list>
				</property>
			</bean>
		</mvc:interceptor>
		<!-- 安全拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.SecurityInterceptor">
				<property name="allowUrls">
					<list>
                        <value>/ad/fileView</value>
					    <value>/resetPwd</value>
						<value>/apk/uploadImg</value>
						<value>/apk/uploadIcon</value>
						<value>/install</value>
						<value>/login</value>
						<value>/index</value>
						<value>/uploadjob/fileDownload</value>
						<value>/uploadjob/importFile</value>
						<value>/uploadjob/importExcel</value>
						<value>/uploadtask/downloadlog</value>
						<value>/excelExport/exportTermFlowDay</value>
						<value>/excelExport/exportTermFlowMonthCount</value>
						<value>/excelExport/exportSimMonth</value>
						<value>/excelExport/exportAppInstall</value>
						<value>/excelExport/exportApkDownLoad</value>
						<value>/excelExport/exportTermWareHouse</value>
						<value>/excelExport/exportUserInfo</value>
						<value>/driverJob/exportExcel</value>
						<value>/clearCache/exportExcel</value>
						<value>/terminal/termSeqlModelDownload</value>
						<value>/terminal/termNoModelDownload</value>
						<value>/terminal/merchantNoModelDownload</value>
						<value>/terminal/termMerDownload</value>
						<value>/terminalWarehouse/termModelDownload</value>
						<value>/merchant/merchantModelDownload</value>
						<value>/unicomCard/telcomCardModelDownload</value>
						<value>/excelExport/exportTerminalAppInfo/</value>
						<value>/excelExport/exportTerminalSystemInfo/</value>
						<value>/excelExport/exportTermAdvQuery/</value>
						<value>/excelExport/exportTermAdvQueryNoCondition</value>
						<value>/excelExport/exportTermInfo/</value>
						<value>/excelExport/exportTermStatusInfo/</value>
						<value>/excelExport/exportTermRemoveTrail/</value>
						<value>/excelExport/exportUpdate/</value>
						<value>/excelExport/exportApp/</value>
						<value>/excelExport/exportTerminal/</value>
						<value>/factory/downloadPublicKey</value>
						<value>/uploadPic</value>
						<value>/systemClean/deleteAlTerm</value>
						<value>/fileDownload</value>
						<value>/js</value>
						<value>/lib</value>
						<value>/modules/</value>
						<value>/css</value>
						<value>/images</value>
						<value>/favicon</value>
						<value>/cpayiot/terminalonline</value>
					</list>
				</property>
				<property name="notAllowUrls">
					<list>
                        <!--<value>/ad</value>-->
                        <value>/apkDownLoad</value>
                        <value>/appDeleteApply</value>
                        <value>/appFlowUsed</value>
                        <value>/appInstall</value>
                        <value>/area</value>
                        <value>/warrantOrder</value>
                        <value>/developer</value>
                        <value>/devInfo</value>
                        <value>/devFeedBack</value>
                        <value>/rsMenu</value>
                        <value>/devRes</value>
                        <value>/devrole</value>
                        <value>/userNotice</value>
                        <value>/exceptionOrder</value>
                        <value>/file</value>
                        <value>/flow</value>
                        <value>/flowCurMonth</value>
                        <value>/flowInf</value>
                        <value>/flowPool</value>
                        <value>/instype</value>
                        <value>/item</value>
                        <value>/launcherApp</value>
                        <value>/launcherAppPush</value>
                        <value>/launcher</value>
                        <value>/launcherIssued</value>
                        <value>/oqcChcekHistory</value>
                        <value>/orderedWarr</value>
                        <value>/package</value>
                        <value>/packageOrder</value>
                        <value>/payTerminal</value>
                        <value>/signManage</value>
                        <value>/flowcharge</value>
                        <value>/simFlowMonth</value>
                        <value>/sms</value>
                        <value>/bootConf</value>
                        <value>/sysos</value>
                        <value>/systemMsgManage</value>
                        <value>/telecomRecord</value>
                        <value>/termChangeMer</value>
                        <value>/terminalAppExce</value>
                        <value>/terminalCert</value>
                        <value>/terminalElectricity</value>
                        <value>/terminalFault</value>
                        <value>/termFlowCountDay</value>
                        <value>/termFlowCountMonth</value>
                        <value>/terminalMoveStrategy</value>
                        <value>/TerminalMoveSwitch</value>
                        <value>/terminalPayMethod</value>
                        <!--<value>/position</value>-->
                        <value>/terminalRemoveMonitor</value>
                        <value>/themeApp</value>
                        <value>/unicomCard</value>
                        <value>/warranty</value>
					</list>
				</property>
			</bean>
		</mvc:interceptor>
		<!-- 操作权限拦截器 -->
		<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.AuthInterceptor">
				<property name="allowUrls">
					<list>
						<value>/driverJob/exportExcel</value>
						<value>/clearCache/exportExcel</value>
						<value>/excelExport</value>
					    <value>/resetPwd</value>
						<value>/apk/uploadImg</value>
						<value>/install</value>
						<value>/login</value>
						<value>/js</value>
						<value>/lib</value>
						<value>/modules/</value>
						<value>/css</value>
						<value>/images</value>
						<value>/favicon</value>
						<value>/systemClean/deleteAlTerm</value>
						<value>/cpayiot/terminalonline</value>
					</list>
				</property>
			</bean>
		</mvc:interceptor>
		<!-- 参数权限拦截器 -->
		<!--<mvc:interceptor>
			<mvc:mapping path="/**" />
			<bean class="com.centerm.cpay.coms.interceptor.ParamsInterceptor"></bean>
		</mvc:interceptor>-->
	</mvc:interceptors>

	<!-- 定义文件上传解析器 -->
	<bean id="multipartResolver"
		class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<!-- 设定默认编码 -->
		<property name="defaultEncoding" value="UTF-8"></property>
		<!-- 设定文件上传的最大值2000MB，2000*1024*1024 -->
		<property name="maxUploadSize" value="2097152000"></property>

	</bean>
	<bean id="propertyHolder"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE" />
		<property name="ignoreResourceNotFound" value="true" />
		<property name="locations">
			<list>
				<value>classpath:system.properties</value>
			</list>
		</property>
	</bean>
</beans>