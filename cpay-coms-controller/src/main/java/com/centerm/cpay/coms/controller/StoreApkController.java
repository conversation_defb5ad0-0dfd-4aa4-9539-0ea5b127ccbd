package com.centerm.cpay.coms.controller;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.centerm.cpay.coms.service.*;
import com.centerm.cpay.coms.util.*;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.apache.commons.lang.StringUtils;
import org.opensaml.xmlsec.signature.P;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.Constants.Constants;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.FileUtil;
import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.coms.apkstore.dao.mapper.ApkTemporaryMapper;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkAuditInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTypeInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo;
import com.centerm.cpay.coms.apkstore.service.ApkAuditInfoService;
import com.centerm.cpay.coms.apkstore.service.ApkRangeService;
import com.centerm.cpay.coms.apkstore.service.ApkTemporaryService;
import com.centerm.cpay.coms.apkstore.service.StoreApkService;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.SignManageInfo;
import com.centerm.cpay.coms.dao.pojo.User;

@Controller
@RequestMapping("/apk")
public class StoreApkController {
    @Autowired
    private StoreApkService storeApkService;
    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private InstitutionService institutionService;
    @Autowired
    private ApkTemporaryMapper apkTemporaryMapper;
    @Autowired
    private ApkTemporaryService apkTemporaryService;
    @Autowired
    private UserService userService;
    @Autowired
    private ApkAuditInfoService apkAuditInfoService;
    @Autowired
    private ApkRangeService apkRangeService;
    @Autowired
    private SysConfigureService configureService;
    String ngsPicPath = "";

    String appstorepath = ConfigInfo.getFilePath(ConfigInfo.APPSTORE_PATH);
    String fileheadpath = ConfigInfo.HEADER_PATH;
    String temDir = FilePathUtil.getTempDir();
    String keyFilePath = FilePathUtil.getValue("keyFilePath");
    String projectId = FilePathUtil.getValue("projectId");
    Logger logger = LoggerFactory.getLogger(StoreApkController.class);
    String isFastFds =ConfigInfo.IS_FASTDFS;

    @RequestMapping(value = "/insert", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg insert(@RequestBody ApkTemporary apkTemporary) {
        ResultMsg result;
        if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
            logger.info("开始进行传统文件上传模式进行应用上传");
            result = insertByTraditional(apkTemporary);
        } else {
            logger.info("上传分布式文件存储形式服务器");
            result = insertByFastFds(apkTemporary);
        }
        logger.info("上传完成结束结果：" + result);
        return result;
    }

    public ResultMsg insertByTraditional(ApkTemporary apkTemporary) {
        logger.info("开始进入保存应用阶段，其中图标地址为1：" + apkTemporary.getIcon_path());
        if(apkTemporary.getIcon_path().indexOf("..") > 0) {
            return new ResultMsg(1, "Icon parameter is invalid, application save failed", null);//图标参数不合法，应用保存失败
        }
        String resultMsg1 = "Please upload 3-5 pictures";//请上传3-5张图片
        String resultMsg2 = "Application signature failed";//应用签名失败
        String module = "Application management";//Application management
        String action = "App upload";//
        String opDesc = "Perform an application upload operation";//
        String resultMsg3 = "App upload succeeded";//应用上传成功

        User user = FrontEndHelper.getLoginUser();
        Map map = new HashMap();
        Integer userId = user.getId();
        Integer insId = apkTemporary.getInsId();
        HttpSession session = FrontEndHelper.getSession();
        ApkTemporary apkInf = (ApkTemporary) session.getAttribute(Constants.APK_INF);
        File newFile = (File) session.getAttribute(Constants.NEW_FILE);
        String myFileName = (String) session.getAttribute(Constants.MY_FILENAME);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        String iconPath = temDir + "/"+apkTemporary.getIcon_path();
        logger.debug("+++++++++++++++++++++++++:开始保存到正式目录");
        // 保存icon图片到正式目录
        logger.debug("+++++++++++++++++++++++++:保存icon图片到正式目录");
        FileInfo fileInfo;
        String apkUrl = newFile.getPath();
        if (pic_list == null || pic_list.size() < 3 || pic_list.size() > 5) {
            pic_list.clear();
            newFileForCutImgList.clear();
            session.setAttribute(Constants.PIC_LIST, pic_list);
            session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
            return new ResultMsg(1, resultMsg1, null);
        }
        try {
            logger.info("开始进入保存应用阶段，其中图标地址为2：" + apkInf.getIcon_path());
            logger.info("apkUrl:" + apkUrl + ",Icon_path:" + apkInf.getIcon_path() + "," + fileheadpath + appstorepath);
            fileInfo = uploadIcon3(iconPath, fileheadpath + appstorepath);
            apkInf.setIconId(fileInfo.getUuid());
            fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
            fileInfoService.AddFile(fileInfo);
        } catch (Exception e) {
            logger.debug("保存icon图片到正式目录目录");
            logger.error(e.toString());
        }
        // 保存apk到正式目录
        logger.debug("+++++++++++++++++++++++++:保存apk到正式目录");
        fileInfo = ApkUtil.uploadFile3(newFile, myFileName, fileheadpath + appstorepath);
        String file_absolute_path = fileInfo.getFile_absolute_path();
        String file_save_name = fileInfo.getFile_save_name();
        fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
        fileInfoService.AddFile(fileInfo);
        // 保存图片到正式目录
        logger.debug("+++++++++++++++++++++++++:保存图片到正式目录");
        List<String> picIdList = new ArrayList<String>();
        for (File newFileForCutImg : newFileForCutImgList) {
            if(newFileForCutImg.getPath().indexOf("..") > 0){
                return new ResultMsg(1, "截图参数不合法，应用保存失败", null);
            }
            fileInfo = ApkUtil.uploadFile3(newFileForCutImg, newFileForCutImg.getName(), fileheadpath + appstorepath);
            fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
            fileInfoService.AddFile(fileInfo);
            picIdList.add(fileInfo.getUuid());
        }
        String picList = StringUtils.strip(picIdList.toString(), "[]");
        picList = picList.replace(" ", "");
        apkTemporary.setPicId(picList);

        String filePath = file_absolute_path;
        logger.info("apk文件绝对路径：" + filePath);
        /***************文件上传到正式目录************************/
        try {
            fileInfo = ApkUtil.getSignedFileCustomer(fileheadpath + appstorepath, file_absolute_path,
                    file_save_name);
            if (fileInfo == null) {
                return new ResultMsg(3, resultMsg2, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            e.getMessage();
        }
        if (fileInfo == null) {
            logger.info("fileInfo内容为空");
            return new ResultMsg(3, resultMsg2, null);
        }
        fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());

        fileInfoService.deleteFileByuuid(fileInfo.getUuid());
        fileInfoService.AddFile(fileInfo);
        apkTemporary.setApkId(fileInfo.getUuid());
        apkTemporary.setSignMd5(fileInfo.getFileMd5());
        apkTemporary.setUserId(userId);
        apkTemporary.setInsId(insId);
        apkTemporary.setCreateTime(TimeUtil.getCurrentTime(new Date()));
        apkTemporary.setRecSt("0");
        apkTemporary.setFile_absolute_path(file_absolute_path);
        apkTemporary.setFile_save_name(file_save_name);
        FileInfo fileInf = fileInfoService.getFileByuuid(apkInf.getIconId());
        String logoPath = fileInf.getFileSavePath();
        apkTemporary.setIcon_path(logoPath);
        apkTemporary.setIconId(apkInf.getIconId());
        Institution institution = institutionService.selectByInsId(insId);
        apkTemporary.setInsCd(institution.getCode());
        apkTemporary.setNgsPicPath(ngsPicPath);
        int result = storeApkService.insert(apkTemporary, insId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("userName", user.getUsername());
        map.put("module", module);
        map.put("action", action);
        map.put("opDesc", opDesc);
        map.put("opTime", sdf.format(new Date()));
        storeApkService.addLog(map);
        logger.info("文件上传处理完毕。");
        pic_list.clear();
        newFileForCutImgList.clear();
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        logger.debug("保存后pic_list的长度：" + pic_list.size());
        return new ResultMsg(4, resultMsg3, null);
    }

    public ResultMsg insertByFastFds(ApkTemporary apkTemporary) {
        logger.info("开始进入保存应用阶段，其中图标地址为1：" + apkTemporary.getIcon_path());
        if(apkTemporary.getIcon_path().indexOf("..") > 0){
            return new ResultMsg(1, "图标参数不合法，应用保存失败", null);
        }

        String resuletMsg1 = "Please upload 3-5 pictures";//请上传3-5张图片
        String resuletMsg2 = "Application signature failed";//应用签名失败
        String module = "Application management";
        String action = "App Upload";
        String opDesc = "The App Executes A Upload Process";
        String resuletMsg3 = "App upload succeeded";//应用上传成功
        User user = FrontEndHelper.getLoginUser();
        Map map = new HashMap();
        Integer userId = user.getId();
        Integer insId = user.getInsId();
        HttpSession session = FrontEndHelper.getSession();
        ApkTemporary apkInf = (ApkTemporary) session.getAttribute(Constants.APK_INF);
        File newFile = (File) session.getAttribute(Constants.NEW_FILE);
        String myFileName = (String) session.getAttribute(Constants.MY_FILENAME);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        String iconPath = temDir+"/"+apkTemporary.getIcon_path();
        logger.debug("+++++++++++++++++++++++++:开始保存到正式目录");
        // 保存icon图片到正式目录
        logger.debug("+++++++++++++++++++++++++:保存icon图片到正式目录");
        FileInfo fileInfo = null;
        if (pic_list == null || pic_list.size() < 3 || pic_list.size() > 5) {
            pic_list.clear();
            newFileForCutImgList.clear();
            session.setAttribute(Constants.PIC_LIST, pic_list);
            session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
            return new ResultMsg(1, resuletMsg1, null);
        }
        try {
            logger.info("开始进入保存应用阶段，其中图标地址为2：" + apkInf.getIcon_path());
            fileInfo = ApkFastFdsUtil.uploadFileByFastFds(new File(iconPath));
            apkInf.setIconId(fileInfo.getUuid());
            fileInfo.setFileSavePath(fileInfo.getFileSavePath());
            fileInfoService.AddFile(fileInfo);
        } catch (Exception e) {
            logger.debug("保存icon图片到正式目录cuowu");
            // TODO
            e.printStackTrace();
        }
        // 保存apk到正式目录
        logger.debug("+++++++++++++++++++++++++:保存apk到正式目录");
        fileInfo = ApkUtil.uploadFile3(newFile, myFileName, fileheadpath + appstorepath);
        String file_absolute_path = fileInfo.getFile_absolute_path();
        String file_save_name = fileInfo.getFile_save_name();
        fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
        fileInfoService.AddFile(fileInfo);
        // 保存图片到正式目录
        logger.debug("+++++++++++++++++++++++++:保存图片到正式目录");
        List<String> picIdList = new ArrayList<String>();
        for (File newFileForCutImg : newFileForCutImgList) {
            if(newFileForCutImg.getPath().indexOf("..") > 0){
                return new ResultMsg(1, "图标参数不合法，应用保存失败", null);
            }
            fileInfo = ApkFastFdsUtil.uploadFileByFastFds(newFileForCutImg);
            fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
            fileInfoService.AddFile(fileInfo);
            picIdList.add(fileInfo.getUuid());
        }
        String picList = StringUtils.strip(picIdList.toString(), "[]");
        picList = picList.replace(" ", "");
        apkTemporary.setPicId(picList);

        String filePath = file_absolute_path;
        logger.info("apk文件绝对路径：" + filePath);
        /*************** 客户签名----支持fastfds文件上传 ************************/
        try {
            fileInfo = ApkFastFdsUtil.getSignedFileCustomer(file_absolute_path, file_save_name);
            if (fileInfo == null) {
                return new ResultMsg(3, resuletMsg2, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            e.getMessage();
        }
        if (fileInfo == null) {
            logger.info("fileInfo内容为空");
            return new ResultMsg(3, resuletMsg2, null);
        }
        // 添加appstore头部路径
        fileInfo.setFileSavePath(fileInfo.getFileSavePath());
        fileInfoService.deleteFileByuuid(fileInfo.getUuid());
        fileInfoService.AddFile(fileInfo);
        apkTemporary.setApkId(fileInfo.getUuid());
        apkTemporary.setSignMd5(fileInfo.getFileMd5());
        apkTemporary.setUserId(userId);
        apkTemporary.setInsId(insId);
        apkTemporary.setCreateTime(TimeUtil.getCurrentTime(new Date()));
        apkTemporary.setRecSt("0");
        apkTemporary.setFile_absolute_path(file_absolute_path);
        apkTemporary.setFile_save_name(file_save_name);
        FileInfo fileInf = fileInfoService.getFileByuuid(apkInf.getIconId());
        String logoPath = fileInf.getFileSavePath();
        apkTemporary.setIcon_path(logoPath);
        apkTemporary.setIconId(apkInf.getIconId());
        Institution institution = institutionService.selectByInsId(insId);
        apkTemporary.setInsCd(institution.getCode());
        apkTemporary.setNgsPicPath(ngsPicPath);
        int result = storeApkService.insert(apkTemporary, insId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("userName", user.getUsername());
        map.put("module", module);
        map.put("action", action);
        map.put("opDesc", opDesc);
        map.put("opTime", sdf.format(new Date()));
        storeApkService.addLog(map);
        logger.info("文件上传处理完毕。");
        pic_list.clear();
        newFileForCutImgList.clear();
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        logger.debug("保存后pic_list的长度：" + pic_list.size());
        return new ResultMsg(4, resuletMsg3, null);
    }

    @RequestMapping(value = "/getApkPage", method = { RequestMethod.GET })
    @ResponseBody
    public EUDataGridResult getApkPage(StoreApkInfo storeApkInfo, @RequestParam Integer page,
                                       @RequestParam Integer rows) {
        User user = FrontEndHelper.getLoginUser();
        Integer insId = user.getInsId();
        // String recSt = "3";
        if ("".equals(storeApkInfo.getAppScreen())) {
            storeApkInfo.setAppScreen(null);
        }
        if (storeApkInfo.getInsId() == null) {
            storeApkInfo.setInsId(insId);
        }
        if (storeApkInfo.getNgsPicPath() == null) {
            ngsPicPath = configureService.queryValueByKey("download_url_intranet");
            storeApkInfo.setNgsPicPath(ngsPicPath);
        }
        // storeApkInfo.setRecSt(recSt);
        EUDataGridResult result = storeApkService.getApkPage(storeApkInfo, page, rows);
        return result;
    }

    /**
     * <AUTHOR> 获取已发布应用列表
     * @param storeApkInfo
     * @param page
     * @param rows
     * @return
     */
    @RequestMapping(value = "/getApkPublished", method = { RequestMethod.GET })
    @ResponseBody
    public EUDataGridResult getApkPublishedPage(StoreApkInfo storeApkInfo, @RequestParam Integer page,
                                                @RequestParam Integer rows) {
        User user = FrontEndHelper.getLoginUser();
        Integer insId = user.getInsId();
        if (storeApkInfo.getInsId() == null) {

            storeApkInfo.setInsId(insId);
        }
        EUDataGridResult result = storeApkService.getApkPublishedPage(storeApkInfo, page, rows);
        return result;
    }

    @RequestMapping(value = "/getAuditList", method = { RequestMethod.GET })
    @ResponseBody
    public EUDataGridResult getAuditList(ApkTemporary apkTemporary, @RequestParam Integer page,
                                         @RequestParam Integer rows) {
        User user = FrontEndHelper.getLoginUser();
        Integer insId = user.getInsId();
        if ("".equals(apkTemporary.getAppScreen())) {
            apkTemporary.setAppScreen(null);
        }
        if (apkTemporary.getInsId() == null) {
            apkTemporary.setInsId(insId);
        }
        if (apkTemporary.getNgsPicPath() == null) {
            ngsPicPath = configureService.queryValueByKey("download_url_intranet");
            apkTemporary.setNgsPicPath(ngsPicPath);
        }

        EUDataGridResult result = storeApkService.getAuditList(apkTemporary, page, rows);
        return result;
    }

    @RequestMapping(value = "/auditApk", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg auditApk(@RequestBody ApkTemporary apkInfo, HttpSession session) {
        String resuletMsg1 = "Upload failed, user is not login";//上传失败，用户未登录
        String resuletMsg2 = "Audit failure";//审核失败
        String module = "Application management";
        String action = "App Audit";
        String opDesc = "The App Initiates An Audit Operation.";
        String resuletMsg3 = "Audit success";//Approved successfully

        User user = FrontEndHelper.getLoginUser();
        Map map = new HashMap();
        if (user == null) {
            return new ResultMsg(1, resuletMsg1, null);
        }
        Integer userId = user.getId();
        if (apkInfo == null) {
            return new ResultMsg(2, resuletMsg2, null);
        }
        Date auditTime = TimeUtil.getCurrentTime(new Date());
        apkInfo.setAuditUserId(userId);
        apkInfo.setAuditTime(auditTime);
        ApkTemporary apkInfo2 = storeApkService.getApkById(apkInfo.getId());
        if (!CtUtils.isEmpty(apkInfo2.getAuditMsg())) {
            apkInfo.setAuditMsg(apkInfo.getAuditMsg());
        }
        storeApkService.auditApk(apkInfo);

        ApkAuditInfo apkAuditInfo = new ApkAuditInfo();
        apkAuditInfo.setAppCode(apkInfo2.getAppCode());
        apkAuditInfo.setAppVersion(apkInfo2.getAppVersion());
        apkAuditInfo.setAuditOpinion(apkInfo.getAuditMsg());
        apkAuditInfo.setAuditUserId(apkInfo.getAuditUserId());
        apkAuditInfo.setUserId(apkInfo2.getUserId());
        apkAuditInfo.setCreateTime(auditTime);
        apkAuditInfo.setApkId(apkInfo2.getApkId());
        apkAuditInfoService.insert(apkAuditInfo);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("userName", user.getUsername());
        map.put("module", module);
        map.put("action", action);
        map.put("opDesc", opDesc);
        map.put("opTime", sdf.format(new Date()));
        storeApkService.addLog(map);
        return new ResultMsg(3, resuletMsg3, null);
    }

    @RequestMapping(value = "/getstoreApkById", method = { RequestMethod.GET })
    @ResponseBody
    public StoreApkInfo getstoreApkById(@RequestParam Integer id) {
        if (id == null) {
            return null;
        }
        StoreApkInfo result = storeApkService.getstoreApkById(id);
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        result.setNgsPicPath(ngsPicPath);
        return result;
    }

    @RequestMapping(value = "/getauditUserById", method = { RequestMethod.GET })
    @ResponseBody
    public User getauditUserById(@RequestParam Integer id) {
        if (id == null) {
            return null;
        }
        User result = userService.getUser(id);
        return result;
    }

    @RequestMapping(value = "/getApkById", method = { RequestMethod.GET })
    @ResponseBody
    public ApkTemporary getApkById(@RequestParam Integer id) {
        if (id == null) {
            return null;
        }
        ApkTemporary result = storeApkService.getApkById(id);
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        result.setNgsPicPath(ngsPicPath);
        return result;
    }

    @RequestMapping(value = "/getApkTempById", method = { RequestMethod.GET })
    @ResponseBody
    public ApkTemporary getApkTempById(@RequestParam Integer id) {
        if (id == null) {
            return null;
        }
        ApkTemporary result = storeApkService.getApkTempById(id);
        if (result == null) {
            return null;
        }
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        result.setNgsPicPath(ngsPicPath);
        return result;
    }

    @RequestMapping(value = "/getdownApkById", method = { RequestMethod.GET })
    @ResponseBody
    public StoreApkInfo getDownApkById(@RequestParam Integer id) {
        if (id == null) {
            return null;
        }
        StoreApkInfo result = storeApkService.getstoreApkById(id);
        if (result == null) {
            return null;
        }
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        result.setNgsPicPath(ngsPicPath);
        return result;
    }

    @RequestMapping(value = "/getAppType", method = { RequestMethod.GET })
    @ResponseBody
    public List<ApkTypeInfo> getAppType(Integer insId) {

        return storeApkService.getAppType();
    }

    // @RequestMapping(value = "/getApkTypeInfo", method = { RequestMethod.GET
    // })
    // @ResponseBody
    // public List<ApkTypeInfo> getApkTypeInfo() {
    // User user = FrontEndHelper.getLoginUser();
    // if (user == null) {
    // return null;
    // }
    // Integer insId = user.getInsId();
    // //取得上级机构ID
    // Institution institution =institutionService.selectByInsId(insId);
    // Integer Parent_id = institution.getParentId();
    //
    // List<ApkTypeInfo> ApkTypeInfolist = storeApkService.getAppType(insId);
    // while(Parent_id!=0){
    // Parent_id =institutionService.selectByInsId(Parent_id).getParentId();
    // ApkTypeInfolist.addAll(storeApkService.getAppType(Parent_id));
    // }
    //
    //
    // return ApkTypeInfolist;
    // }
    @RequestMapping(value = "/getApkTypeInfo", method = { RequestMethod.GET })
    @ResponseBody
    public List<ApkTypeInfo> getApkTypeInfo() {

        return storeApkService.getAppType();
    }

    @RequestMapping(value = "/deleteImg", method = { RequestMethod.GET })
    @ResponseBody
    public ResultMsg deleteImg(String uuid) {
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (newFileForCutImgList == null) {
            newFileForCutImgList = new ArrayList<>();
        }
        if (pic_list != null && pic_list.contains(uuid)) {
            pic_list.remove(uuid);
            for (int i = 0; i < newFileForCutImgList.size(); i++) {
                if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
                    newFileForCutImgList.remove(i);
                }
            }
        }
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        return null;
    }

    @RequestMapping(value = "/updatePicList", method = { RequestMethod.GET })
    @ResponseBody
    public ResultMsg updatePicList() {
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (pic_list == null) {
            return null;
        }
        if (newFileForCutImgList == null) {
            newFileForCutImgList = new ArrayList<>();
        }
        pic_list.clear();
        newFileForCutImgList.clear();
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        logger.debug("刷新后pic_list：" + pic_list.size());
        return null;
    }

    @RequestMapping(value = "/deleteStoredImg", method = { RequestMethod.DELETE })
    @ResponseBody
    public ResultMsg deleteStoredImg(String uuid) {
        ResultMsg result = null;
        if (!CtUtils.isEmpty(uuid)) {
            result = storeApkService.deleteStoredImg(uuid, fileheadpath + appstorepath);
        }
        return result;
    }

    @RequestMapping(value = "/changeImg", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg changeImg(HttpServletRequest request, String uuid) {
        String resuletMsg1 = "No image selected";//未选择图片
        String resuletMsg2 = "Please select image file in png or jpg format";//请选择png或jpg格式的图片文件
        String resuletMsg3 = "Image uploaded successfully";//图片上传成功
        String resuletMsg4 = "Image save failed";//图片保存失败
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        logger.debug("+++++++++++++++++开始更改图片");
        if (pic_list == null) {
            pic_list = new ArrayList();
        }
        if (newFileForCutImgList == null) {
            newFileForCutImgList = new ArrayList<>();
        }
        if (pic_list != null && pic_list.size() > 0) {
            pic_list.remove(uuid);
        }
        if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
            for (int i = 0; i < newFileForCutImgList.size(); i++) {
                if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
                    newFileForCutImgList.remove(i);
                }
            }
        }
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        String path = "";
        if (multipartResolver.isMultipart(request)) {
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                MultipartFile file = multiRequest.getFile(iter.next());
                if (file == null) {
                    return new ResultMsg(1, resuletMsg1, null);
                }
                FileInfo fileInf = new FileInfo();
                String myFileName = file.getOriginalFilename();
                String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                if(CtUtils.isEmpty(fileType)||!(fileType.equalsIgnoreCase("png")
                        ||fileType.equalsIgnoreCase("jpg"))){
                    return new ResultMsg(2, resuletMsg2, null);
                }

                try {
                    String fileSavePath =  "/" +CtUtils.getCurrentTime("yyyyMMdd") + "/";
                    try {
                        FileUtil.setFilePermissions(new File(temDir + fileSavePath));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    String imgUuid = UUID.randomUUID().toString();
                    File newFileForCutImg = new File(temDir + fileSavePath + imgUuid + "." + fileType);
                    // 保存应用截图到临时目录下
                    file.transferTo(newFileForCutImg);
                    pic_list.add(imgUuid);
                    newFileForCutImgList.add(newFileForCutImg);
                    session.setAttribute(Constants.PIC_LIST, pic_list);
                    session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
                    String str = imgUuid + ";" + fileSavePath + imgUuid + "." + fileType;
                    logger.debug("图片更改成功");
                    return new ResultMsg(5, resuletMsg3, str);
                } catch (Exception e) {
                    return new ResultMsg(3, resuletMsg4, null);
                }
            }
        }
        return null;
    }

    @RequestMapping(value = "/changeStoredImg", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg changeStoredImg(HttpServletRequest request, String uuid) {
        // 创建一个通用的多部分解析器
        String resuletMsg1 = "No image selected";//未选择图片
        String resuletMsg2 = "Please select image file in png or jpg format";//请选择png或jpg格式的图片文件
        String resuletMsg3 = "Image save failed";//图片保存失败
        String resuletMsg4 = "Image uploaded successfully";//图片上传成功

        logger.debug("开始更改changeStoredImg图片");
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (pic_list == null) {
            pic_list = new ArrayList();
        }
        if (newFileForCutImgList == null) {
            newFileForCutImgList = new ArrayList<>();
        }
        if (pic_list != null && pic_list.size() > 0) {
            pic_list.remove(uuid);
        }
        if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
            for (int i = 0; i < newFileForCutImgList.size(); i++) {
                if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
                    newFileForCutImgList.remove(i);
                }
            }
        }
        logger.debug("更改changeStoredImg图片成功");
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                MultipartFile file = multiRequest.getFile(iter.next());
                if (file == null) {
                    return new ResultMsg(400, resuletMsg1, null);
                }
                FileInfo fileInf = new FileInfo();
                String myFileName = file.getOriginalFilename();
                String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                if(CtUtils.isEmpty(fileType)||!(fileType.equalsIgnoreCase("png")
                        ||fileType.equalsIgnoreCase("jpg"))){
                    return new ResultMsg(2, resuletMsg2, null);
                }
                // 如果更改的是新增的图片，则应该修改临时文件中的图片。
                try {
                    String fileSavePath =   "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
                    try {
                        FileUtil.setFilePermissions(new File(temDir +fileSavePath));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    String imgUuid = uuid;
                    File newFileForCutImg = new File(temDir + fileSavePath + imgUuid + "." + fileType);
                    // 保存应用截图到临时目录下
                    file.transferTo(newFileForCutImg);
                    pic_list.add(uuid);
                    logger.debug("++++++++++++++++++++++++++uuid:" + uuid);
                    newFileForCutImgList.add(newFileForCutImg);
                    session.setAttribute(Constants.PIC_LIST, pic_list);
                    session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
                    String str = imgUuid + ";" + fileSavePath + imgUuid + "." + fileType;
                    logger.debug("图片更改成功");
                    return new ResultMsg(200, resuletMsg4, str);
                } catch (Exception e) {
                    e.printStackTrace();
                    return new ResultMsg(500, resuletMsg3, null);
                }
            }

        }

        return null;

    }

    @RequestMapping(value = "/uploadImg", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg uploadImg(HttpServletRequest request) {
        String resuletMsg1 = "The selected image file is too large！";//选择的图片文件太大！
        String resuletMsg2 = "Image uploaded successfully";//图片上传成功
        String resuletMsg3 = "Image save failed";//图片保存失败
        logger.debug("++++++++++++++++++++：进行图片上传");
        MultipartFile file = ApkUtil.uploadFile(request);
        // 取得当前上传文件的文件名称
        String myFileNameForCutImg = file.getOriginalFilename();
        if ((int) file.getSize() > 5 * 1024 * 1024) {
            return new ResultMsg(1, resuletMsg1, null);
        }
        String fileType = myFileNameForCutImg.substring(myFileNameForCutImg.lastIndexOf(".") + 1);
        if(CtUtils.isEmpty(fileType)||!(fileType.equalsIgnoreCase("png")
                ||fileType.equalsIgnoreCase("jpg"))){
            return new ResultMsg(1, "Image format is incorrect, please upload jpg or png file", null);//图片格式不正确,请上传jpg或png文件
        }
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (pic_list == null) {
            pic_list = new ArrayList();
        }
        if (newFileForCutImgList == null) {
            newFileForCutImgList = new ArrayList<>();
        }
        try {
            String fileSavePath = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
            try {
                FileUtil.setFilePermissions(new File(temDir + fileSavePath));
            } catch (Exception e) {
                e.printStackTrace();
            }
            String imgUuid = UUID.randomUUID().toString();
            File newFileForCutImg = new File(temDir + fileSavePath + imgUuid + "." + fileType);
            newFileForCutImgList.add(newFileForCutImg);
            // 保存应用截图到临时目录下
            file.transferTo(newFileForCutImg);
            pic_list.add(imgUuid);
            session.setAttribute(Constants.PIC_LIST, pic_list);
            session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
            logger.debug("++++++++++++++++++pic_list:" + pic_list.size());
            String str = imgUuid + ";" + fileSavePath + imgUuid + "." + fileType;
            logger.debug("++++++++++++++++++++++++++++++++++++:图片上传成功！");
            return new ResultMsg(3, resuletMsg2, str);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultMsg(2, resuletMsg3, null);
        }

    }

    @RequestMapping(value = "/updateApkTempInfo", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg updateApkTempInfo(@RequestBody ApkTemporary apkTemporary) {
        ResultMsg result;
        if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
            logger.info("开始进行传统文件上传模式进行应用上传");
            result = updateApkTempInfoByTraditional(apkTemporary);
        } else {
            logger.info("上传分布式文件存储形式服务器");
            result = updateApkTempInfoByFastFds(apkTemporary);
        }
        logger.info("上传完成结束结果：" + result);
        return result;
    }

    public ResultMsg updateApkTempInfoByTraditional(ApkTemporary apkTemporary) {
        logger.debug("+++++++++++++++++++++++++++开始保存到正式目录");
        String resuletMsg1 = "Fail to edit";//修改失败
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        if (apkTemporary == null) {
            return new ResultMsg(1, resuletMsg1, null);
        }

        if (apkTemporary.getAppTypeId() == null) {
            apkTemporary.setAppTypeId(0);
        }
        if (apkTemporary.getGroupId() == null) {
            apkTemporary.setGroupId(0);
        }
        if (apkTemporary.getRecSt() == "4" || apkTemporary.getRecSt().equals("4")) {
            apkTemporary.setApkId(apkTemporary.getApkId());
            apkTemporary.setSignMd5(apkTemporary.getSignMd5());
            apkTemporary.setUserId(apkTemporary.getUserId());
            apkTemporary.setInsId(apkTemporary.getInsId());
            apkTemporary.setCreateTime(TimeUtil.getCurrentTime(new Date()));
            apkTemporary.setRecSt("1");
            apkTemporary.setIconId(apkTemporary.getIconId());
            Institution institution = institutionService.selectByInsId(apkTemporary.getInsId());
            apkTemporary.setInsCd(institution.getCode());
            apkTemporary.setNgsPicPath(ngsPicPath);
            int result = storeApkService.insert(apkTemporary, apkTemporary.getInsId());
        }

        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
            String[] picIdArray = apkTemporaryMapper.getApkTempById(apkTemporary.getId()).getPicId().split(",");
            List<String> picidList = new ArrayList(Arrays.asList(picIdArray));
            if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
                for (int i = 0; i < newFileForCutImgList.size(); i++) {// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
                    String fileName = newFileForCutImgList.get(i).getName();
                    if (picidList.contains(fileName.substring(0, fileName.lastIndexOf(".")))) {// 为更改的图片
                        String imgUUId = pic_list.get(i);
                        String imgPath = fileInfoService.getFileByuuid(imgUUId).getFileSavePath();
                        File newFile = new File(fileheadpath + imgPath);
                        try {
                            FileInputStream inputStream = new FileInputStream(newFileForCutImgList.get(i));
                            FileOutputStream outputStream = new FileOutputStream(newFile);
                            byte b[] = new byte[1024];
                            int len;
                            try {
                                len = inputStream.read(b);
                                while (len != -1) {
                                    outputStream.write(b, 0, len);
                                    len = inputStream.read(b);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        } catch (FileNotFoundException e) {
                            // TODO
                            e.printStackTrace();
                        }

                    } else {// 为新增的图片
                        FileInfo fileInfo = null;
                        try {
                            fileInfo = ApkUtil.uploadFile3(newFileForCutImgList.get(i), fileName,
                                    fileheadpath + appstorepath);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (fileInfo != null) {
                            fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
                            String picId = apkTemporary.getPicId();
                            if (picId == null || picId.trim().equals("")) {
                                picId = fileInfo.getUuid();
                            } else {
                                picId = picId + "," + fileInfo.getUuid();
                            }

                            apkTemporary.setPicId(picId);
                            fileInfoService.AddFile(fileInfo);
                        }
                    }
                }
            }
        }
        logger.debug("picId:" + apkTemporary.getPicId());

        if (pic_list == null) {
            return apkTemporaryService.updateApkTempInfoById(apkTemporary, fileheadpath + appstorepath);
        }
        pic_list.clear();
        newFileForCutImgList.clear();
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        return apkTemporaryService.updateApkTempInfoById(apkTemporary, fileheadpath + appstorepath);
    }

    public ResultMsg updateApkTempInfoByFastFds(ApkTemporary apkTemporary) {
        logger.debug("+++++++++++++++++++++++++++开始保存到正式目录");
        String resuletMsg1 = "Fail to edit";//修改失败
        if (apkTemporary == null) {
            return new ResultMsg(1, resuletMsg1, null);
        }
        if (apkTemporary.getAppTypeId() == null) {
            apkTemporary.setAppTypeId(0);
        }
        if (apkTemporary.getGroupId() == null) {
            apkTemporary.setGroupId(0);
        }
        if (apkTemporary.getRecSt() == "4" || apkTemporary.getRecSt().equals("4")) {
            apkTemporary.setApkId(apkTemporary.getApkId());
            apkTemporary.setSignMd5(apkTemporary.getSignMd5());
            apkTemporary.setUserId(apkTemporary.getUserId());
            apkTemporary.setInsId(apkTemporary.getInsId());
            apkTemporary.setCreateTime(TimeUtil.getCurrentTime(new Date()));
            apkTemporary.setRecSt("1");
            apkTemporary.setIconId(apkTemporary.getIconId());
            Institution institution = institutionService.selectByInsId(apkTemporary.getInsId());
            apkTemporary.setInsCd(institution.getCode());
            apkTemporary.setNgsPicPath(ngsPicPath);
            int result = storeApkService.insert(apkTemporary, apkTemporary.getInsId());
        }
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        String picId = apkTemporary.getPicId();
        if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
            String[] picIdArray = apkTemporaryMapper.getApkTempById(apkTemporary.getId()).getPicId().split(",");
            List<String> picidList = new ArrayList(Arrays.asList(picIdArray));
            if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
                for (int i = 0; i < newFileForCutImgList.size(); i++) {// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
                    String fileName = newFileForCutImgList.get(i).getName();
                    if (picidList.contains(fileName.substring(0, fileName.lastIndexOf(".")))) {// 为更改的图片
                        FileInfo fileInfo = null;
                        fileInfo = ApkFastFdsUtil.uploadFileByFastFds(newFileForCutImgList.get(i));
                        fileInfo.setFileSavePath(fileInfo.getFileSavePath());
                        fileInfoService.AddFile(fileInfo);
                        picId.replace(fileName.substring(0, fileName.lastIndexOf(".")), fileInfo.getUuid());
                    } else {// 为新增的图片
                        FileInfo fileInfo = null;
                        fileInfo = ApkFastFdsUtil.uploadFileByFastFds(newFileForCutImgList.get(i));
                        fileInfo.setFileSavePath(fileInfo.getFileSavePath());
                        fileInfoService.AddFile(fileInfo);
                        if (picId == null || picId.trim().equals("")) {
                            picId = fileInfo.getUuid();
                        } else {
                            picId = picId + "," + fileInfo.getUuid();
                        }
                    }
                }
            }
        }
        apkTemporary.setPicId(picId);
        pic_list.clear();
        newFileForCutImgList.clear();
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        return apkTemporaryService.updateApkTempInfoById(apkTemporary, fileheadpath + appstorepath);
    }

    @RequestMapping(value = "/updateStoreApkInfo", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg updateStoreApkInfo(@RequestBody StoreApkInfo StoreApkInfo) {
        logger.debug("+++++++++++++++++++修改正式目录");
        String resuletMsg1 = "Fail to edit";//修改失败
        ngsPicPath = configureService.queryValueByKey("download_url_intranet");
        ApkTemporary newapkTemporary = new ApkTemporary();
        ApkTemporary newapkTemporaryWhere = new ApkTemporary();
        if (StoreApkInfo == null) {
            return new ResultMsg(1, resuletMsg1, null);
        }

        if (StoreApkInfo.getAppTypeId() == null) {
            newapkTemporary.setAppTypeId(0);
        } else {
            newapkTemporary.setAppTypeId(StoreApkInfo.getAppTypeId());
        }
        if (StoreApkInfo.getGroupId() == null) {
            newapkTemporary.setGroupId(0);
        } else {
            newapkTemporary.setGroupId(StoreApkInfo.getGroupId());
        }
        newapkTemporary.setApkId(StoreApkInfo.getApkId());
        newapkTemporary.setSignMd5(StoreApkInfo.getSignMd5());
        newapkTemporary.setUserId(StoreApkInfo.getUserId());
        newapkTemporary.setInsId(StoreApkInfo.getInsId());
        newapkTemporary.setCreateTime(TimeUtil.getCurrentTime(new Date()));
        newapkTemporary.setRecSt("1");
        newapkTemporary.setIconId(StoreApkInfo.getIconId());
        Institution institution = institutionService.selectByInsId(StoreApkInfo.getInsId());
        newapkTemporary.setInsCd(institution.getCode());
        newapkTemporary.setNgsPicPath(ngsPicPath);
        newapkTemporary.setAppCode(StoreApkInfo.getAppCode());
        newapkTemporary.setAppName(StoreApkInfo.getAppName());
        newapkTemporary.setPicId(StoreApkInfo.getPicId());
        newapkTemporary.setKeyWord(StoreApkInfo.getKeyWord());
        newapkTemporary.setAppDesc(StoreApkInfo.getAppDesc());
        newapkTemporary.setVerDesc(StoreApkInfo.getVerDesc());
        newapkTemporary.setDeveloperId(StoreApkInfo.getDeveloperId());
        newapkTemporary.setCreateTime(new Date());
        newapkTemporary.setAppScreen(StoreApkInfo.getAppScreen());
        newapkTemporary.setAppVersionName(StoreApkInfo.getAppVersionName());
        newapkTemporary.setSignMd5(StoreApkInfo.getSignMd5());
        newapkTemporary.setIsDelete("0");
        newapkTemporary.setAppVersion(StoreApkInfo.getAppVersion());
        newapkTemporary.setFactoryId(StoreApkInfo.getFactoryId());
        newapkTemporary.setTermTypeCodeStr(StoreApkInfo.getTermTypeCodeStr());
        newapkTemporary.setTermTypeNameStr(StoreApkInfo.getTermTypeNameStr());
        // 新增临时应用表
        /*try {
            newapkTemporaryWhere = apkTemporaryService.getApkTemporaryList(StoreApkInfo.getAppCode(),
                    StoreApkInfo.getAppVersion(), StoreApkInfo.getInsId());
        } catch (Exception e) {
            return ResultMsg.build(ResultMsg.ERROR_CODE, "修改失败，请检查是否存在相同版本");
        }*/
        HttpSession session = FrontEndHelper.getSession();
        List<String> pic_list = (List<String>) session.getAttribute(Constants.PIC_LIST);
        List<File> newFileForCutImgList = (List<File>) session.getAttribute(Constants.NEWFILE_FORCUTIMGLIST);
        if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
            String[] picIdArray = newapkTemporary.getPicId().split(",");
            List<String> picidList = new ArrayList(Arrays.asList(picIdArray));
            if (newFileForCutImgList != null && newFileForCutImgList.size() > 0) {
                for (int i = 0; i < newFileForCutImgList.size(); i++) {// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
                    String fileName = newFileForCutImgList.get(i).getName();
                    if (picidList.contains(fileName.substring(0, fileName.lastIndexOf(".")))) {// 为更改的图片
                        String imgUUId = pic_list.get(i);
                        String imgPath = fileInfoService.getFileByuuid(imgUUId).getFileSavePath();
                        File newFile = new File(fileheadpath + imgPath);
                        try {
                            FileInputStream inputStream = new FileInputStream(newFileForCutImgList.get(i));
                            FileOutputStream outputStream = new FileOutputStream(newFile);
                            byte b[] = new byte[1024];
                            int len;
                            try {
                                len = inputStream.read(b);
                                while (len != -1) {
                                    outputStream.write(b, 0, len);
                                    len = inputStream.read(b);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        } catch (FileNotFoundException e) {
                            // TODO
                            e.printStackTrace();
                        }

                    } else {// 为新增的图片
                        FileInfo fileInfo = null;
                        try {
                            fileInfo = ApkUtil.uploadFile3(newFileForCutImgList.get(i), fileName,
                                    fileheadpath + appstorepath);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (fileInfo != null) {
                            fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
                            String picId = newapkTemporary.getPicId();
                            if (picId == null || picId.trim().equals("")) {
                                picId = fileInfo.getUuid();
                            } else {
                                picId = picId + "," + fileInfo.getUuid();
                            }

                            newapkTemporary.setPicId(picId);
                            fileInfoService.AddFile(fileInfo);
                        }
                    }
                }
            }
        }
        logger.debug("picId:" + newapkTemporary.getPicId());
        // 删除原表
        storeApkService.deleteApkbyId(StoreApkInfo.getId());

        // if (pic_list == null) {
        // return apkTemporaryService.updateApkTempInfoById(newapkTemporary,
        // fileheadpath + appstorepath);
        // }
        if (!CtUtils.isEmpty(pic_list)) {
            pic_list.clear();
        }
        if (!CtUtils.isEmpty(newFileForCutImgList)) {
            newFileForCutImgList.clear();
        }
        session.setAttribute(Constants.PIC_LIST, pic_list);
        session.setAttribute(Constants.NEWFILE_FORCUTIMGLIST, newFileForCutImgList);
        return apkTemporaryService.addApkTempoary(newapkTemporary, fileheadpath + appstorepath);
    }

    @RequestMapping(value = "/uploadIcon", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg uploadIcon(HttpServletRequest request, HttpServletResponse response) {

        String resuletMsg1 = "file size too big";//文件太大
        String resuletMsg2 = "The size of the image is larger than 128px×128px. Please reselect the image！";//图片的尺寸大于128px×128px，请重新选择图片！
        String resuletMsg3 = "Icon uploaded successfully";//图标上传成功
        String resuletMsg4 = "Icon format is incorrect";//图标格式不正确
        MultipartFile file = ApkUtil.uploadFile(request);
        // 取得当前上传文件的文件名称
        String myFileName = file.getOriginalFilename();
        String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
        if(CtUtils.isEmpty(fileType)||!(fileType.equalsIgnoreCase("png")
                ||fileType.equalsIgnoreCase("jpg"))){
            return new ResultMsg(400, resuletMsg4, null);
        }
        String uuid = UUID.randomUUID().toString();
        String startDir = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
        File dir = new File(temDir + startDir);
        if(!dir.exists()){
            dir.mkdirs();
        }
        String fileSavePath = startDir + uuid + "." + fileType;
        File newFile = new File(temDir + fileSavePath);
        if (file.getSize() > 10 * 1024 * 1024) {
            return new ResultMsg(1, resuletMsg1, null);
        }
        try {
            file.transferTo(newFile);
        } catch (IllegalStateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(newFile);
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            if (width > 128 || height > 128) {
                return new ResultMsg(400, resuletMsg2, null);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        logger.info("filePath1" + newFile.getPath());
        logger.info("filePath2" + newFile.getAbsolutePath());
        Map map = new HashMap<String, String>(2);
        map.put("filePath", fileSavePath);
        map.put("iconId", uuid);
        return new ResultMsg(2, resuletMsg3, map);

    }

    @RequestMapping(value = "/uploadApk", method = { RequestMethod.POST })
    @ResponseBody
    public ResultMsg uploadApk(HttpServletRequest request, HttpServletResponse response, HttpSession session) {
        logger.info("进入方法！");
        String resultMsg1 = "Login timed out or not login";//登录超时或未登录
        String resultMsg2 = "The file is not in apk format";//该文件不是apk格式
        String resultMsg3 = "App package size does not exceed 500M";//应用包大小不超过500M
        String resultMsg5 = "System exception: parsing application package failed!";//系统异常：解析应用包失败!
        String resultMsg6 = "Upload success";//上传成功
        logger.info("准备进入文件解析器");

        //【判断】登录是否成功
        User user = FrontEndHelper.getLoginUser();
        if (CtUtils.isEmpty(user)) {
            return new ResultMsg(1, resultMsg1, null);
        }

        //【上传】文件信息
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
        ApkTemporary apkInf = new ApkTemporary();
        if (multipartResolver.isMultipart(request)) {
            //【转换】成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                MultipartFile file = multiRequest.getFile(iter.next());
                if (file == null) {
                    return ResultMsg.fail();
                }

                //【获取】当前上传文件的文件名称
                String myFileName = file.getOriginalFilename();
                logger.debug("----------------------------------------------myFileName:" + myFileName);
                if (myFileName.trim().equals("")) {
                    return ResultMsg.fail();
                }
                if (!myFileName.endsWith("apk")) {
                    return new ResultMsg(2, resultMsg2, null);
                }
                if ((int) file.getSize() > 500 * 1024 * 1024) {
                    return new ResultMsg(3, resultMsg3, null);
                }
                session.setAttribute(Constants.MY_FILENAME, myFileName);
                logger.info("temDir--" + temDir);
                String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                String fileSavePath =  temDir + "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
                try {
                    FileUtil.setFilePermissions(new File(fileSavePath));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                File newFile = new File(fileSavePath + UUID.randomUUID() + "." + fileType);
                try {// 上传
                    file.transferTo(newFile);
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
                session.setAttribute(Constants.NEW_FILE, newFile);
                String apkUrl = newFile.getPath();
                logger.info("apkUrl--" + apkUrl);
                try {
                    // 解析apk
                    apkInf = ApkUtil.decryptApk2(apkUrl);
                    // 保存apk图标到临时路径下
                    logger.info("准备进入解析过程");
                    FileInfo fileInfo = uploadIcon2(apkUrl, apkInf.getIcon_path(), temDir);
                    String iconPath = apkInf.getIcon_path();
                    logger.info("解析过程完成" + fileInfo);
                    apkInf.setIconId(fileInfo.getUuid());
                    apkInf.setIcon_path(fileInfo.getFileSavePath());
                    session.setAttribute(Constants.APK_INF, apkInf);
                    session.setAttribute(Constants.ICON_PATH, iconPath);
                } catch (Exception e) {
                    logger.error(e.getMessage());
                    logger.error("APK文件上传失败");
                    return new ResultMsg(5, resultMsg5, null);
                }
            }
        }
        return new ResultMsg(6, resultMsg6, apkInf);
    }

    @RequestMapping(value = "/deleteApkById", method = { RequestMethod.GET })
    @ResponseBody
    public ResultMsg deleteApkById(Integer id) {
        String resuletMsg1 = "Failed to delete";//删除失败
        String resuletMsg2 = "Success to delete";//删除成功
        int result = storeApkService.deleteApk(id);
        if (result < 0) {
            return new ResultMsg(1, resuletMsg1, null);
        }
        return new ResultMsg(2, resuletMsg2, null);
    }

    @RequestMapping(value = "/deleteApks", method = { RequestMethod.DELETE })
    @ResponseBody
    public ResultMsg deleteApks(@RequestParam String ids, @RequestParam String temIds) {
        String resuletMsg1 = "Application deleted successfully！";//应用删除成功

        if (!ids.equals("")) {
            String[] idsArray = ids.split(",");
            for (int i = 0; i < idsArray.length; i++) {
                StoreApkInfo apk = storeApkService.getstoreApkById(Integer.parseInt(idsArray[i]));
                storeApkService.deleteApkById(Integer.parseInt(idsArray[i]), fileheadpath + appstorepath);
                storeApkService.deletePermission(apk.getAppCode(), apk.getAppVersion());
                apkRangeService.deleteByAppId(Integer.parseInt(idsArray[i]));
                //storeApkService.updateAppDeleteApply(apk);

				/*if(!isFastFds.equals("true")){
					//删除传统方式存储的文件
					String appUrl = apk.getAppUrl();
					File appFile = new File(fileheadpath + appUrl);
					if (appFile.exists()) {
						appFile.delete();
					}
					File iconPathFile = new File(fileheadpath+apk.getIconUrl());
					if(iconPathFile.exists()){
						iconPathFile.delete();
					}
					List picUrls = apk.getPicList();
					for (Object picUrl :picUrls){
						File picUrlFile = new File(fileheadpath+picUrl);
						if(picUrlFile.exists()){
							picUrlFile.delete();
						}
					}
				}else {
					*//*
                 *	删除分布式方式存储的文件
                 *//*
					String appPath = apk.getAppUrl();
					if(!CtUtils.isEmpty(appPath)){
						ApkFastFdsUtil.deleteFile(appPath);
					}
					String iconPath =apk.getIconUrl();
					if(!CtUtils.isEmpty(iconPath)){
						ApkFastFdsUtil.deleteFile(iconPath);
					}
					List picUrls = apk.getPicList();
					for (Object picUrl : picUrls ){
						if(!CtUtils.isEmpty(picUrl)){
							ApkFastFdsUtil.deleteFile((String) picUrl);
						}
					}
				}*/
            }
        }
        if (!temIds.equals("")) {
            String[] idsTemArray = temIds.split(",");
            for (int i = 0; i < idsTemArray.length; i++) {
                ApkTemporary apk = storeApkService.getApkById(Integer.parseInt(idsTemArray[i]));
                storeApkService.deleteApkTemById(Integer.parseInt(idsTemArray[i]), fileheadpath + appstorepath);
                storeApkService.deletePermission(apk.getAppCode(), apk.getAppVersion());

            }
        }
        return new ResultMsg(200, resuletMsg1, null);
    }

    @RequestMapping(value = "/toAuditApks", method = { RequestMethod.PATCH })
    @ResponseBody
    public ResultMsg toAuditApks(@RequestParam String ids) {
        String resuletMsg1 = "Submit a successful review";//提交审核成功
        String resuletMsg2 = "Please select a record";//请选择记录

        ResultMsg result = null;
        if (!ids.equals("")) {
            String[] idsArray = ids.split(",");
            for (int i = 0; i < idsArray.length; i++) {
                apkTemporaryService.toAuditApks(Integer.parseInt(idsArray[i]));
            }
            result = new ResultMsg(2, resuletMsg1, null);
        } else {
            result = new ResultMsg(1, resuletMsg2, null);
        }
        return result;
    }

    public FileInfo uploadIcon2(String apkPath, String iconPath, String destPath) throws Exception {
        File iconFile = ApkUtil.getIconFileFromApk(apkPath, iconPath);
        String iconName = iconPath.substring(iconPath.lastIndexOf("/") + 1);
        FileInfo fileInf = ApkUtil.uploadFile3(iconFile, iconName, destPath);
        return fileInf;

    }

    public FileInfo uploadIcon3(String iconPath, String destPath) throws Exception {
        File iconFile = new File(iconPath);
        String iconName = iconPath.substring(iconPath.lastIndexOf("/") + 1);
        FileInfo fileInf = ApkUtil.uploadFile3(iconFile, iconName, destPath);
        return fileInf;

    }

    public String getPaentInsCode(Integer insId) {
        Institution ins = institutionService.selectByInsId(insId);
        String detail = ins.getDetail();
        if (detail.indexOf(",") < 0) {
            return detail;
        }
        String str[] = detail.split(",");
        return str[1];
    }

    /**
     * 取得父级机构的方法；
     */
    public List getALLPaentInsCode(Integer insId) {
        Institution ins = institutionService.selectByInsId(insId);
        String detail = ins.getDetail();
        ArrayList list = new ArrayList();
        if (detail.indexOf(",") < 0) {
            list.add(detail);
            return list;
        } else {
            String str[] = detail.split(",");
            for (int i = 0; i < str.length; i++) {
                list.add(str[i]);
            }
        }
        return list;
    }

    /**
     * 应用历史审核意见 input：ID
     */
    @RequestMapping(value = "/getApkAuditOpinion", method = { RequestMethod.GET })
    @ResponseBody
    public List<ApkAuditInfo> getAuditMsg(ApkTemporary apkTemporary) {
        ApkAuditInfo apkAuditInfo = new ApkAuditInfo();
        String appCode = apkTemporary.getAppCode();
        String appVersion = apkTemporary.getAppVersion();
        Integer userId = apkTemporary.getUserId();
        apkAuditInfo.setAppCode(appCode);
        apkAuditInfo.setAppVersion(appVersion);
        apkAuditInfo.setUserId(userId);
        apkAuditInfo.setApkId(apkTemporary.getApkId());
        List<ApkAuditInfo> listapkAuditInfo = apkAuditInfoService.getApkAuditInfo(apkAuditInfo);
        return listapkAuditInfo;

    }

}