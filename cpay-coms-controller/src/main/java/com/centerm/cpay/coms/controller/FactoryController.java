
package com.centerm.cpay.coms.controller;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.FileUtil;
import com.centerm.cpay.coms.dao.pojo.Factory;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.service.FactoryService;
import com.centerm.cpay.coms.util.ApkUtil;
import com.centerm.cpay.coms.util.FilePathUtil;

/**
 * @Title: OperInfController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年5月22日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/factory")
public class FactoryController {
	@Autowired
	private FactoryService factoryService;
	

	Logger logger = LoggerFactory.getLogger(FactoryController.class);
	String factoryAsyncUrl="";
	String temDir = FilePathUtil.getTempDir();
	String fileheadpath = ConfigInfo.HEADER_PATH;
	String isFastFds =ConfigInfo.IS_FASTDFS;
	String filesavepath = FilePathUtil.getrelativePath("keypath");
	@Autowired
	private SysConfigureService configureService;
	String ngsPicPath = "";



	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getFactoryList(Factory factory, @RequestParam Integer page, @RequestParam Integer rows) {
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		factory.setNgsPicPath(ngsPicPath);
		EUDataGridResult result = factoryService.getFactoryList(factory, page, rows);
		return result;
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(HttpServletRequest request, HttpServletResponse response,@PathVariable Integer id) {
		return factoryService.deleteById(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody Factory factory) {

		ResultMsg result = factoryService.updateFactory(factory);
		
		return result;
	}

	
	
	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(@RequestBody Factory factory) {
		ResultMsg result = factoryService.insertFactory(factory);
		return result;
	}
	@RequestMapping(value = "/downloadPublicKey/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public void downloadPublicKey(HttpServletRequest request, HttpServletResponse response,@PathVariable Integer id) throws IOException {
		Factory factory = factoryService.getFactoryById(id);
		ServletOutputStream ouputStream = response.getOutputStream();
		response.setContentType("text/plain");
		response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode(factory.getName(), "UTF-8")+"_pubKey.pem");
		BufferedOutputStream buff = new BufferedOutputStream(ouputStream);
		buff.write(factory.getPublicKeyPath().getBytes("UTF-8"));
		buff.flush();
		buff.close();
		ouputStream.close();
	}
	public void insertByTraditional(Factory factory){
		String privatePath = factory.getPrivateKeyPath();
		String publicPath = factory.getPublicKeyPath();
		
		String privateFileType = privatePath.substring(privatePath.lastIndexOf("/") + 1);
		String publicFileType = publicPath.substring(publicPath.lastIndexOf("/") + 1);
		
		String newPrivateFilePath = fileheadpath +filesavepath + factory.getCode() + "/"+privateFileType;
		String newPubicFilePath = fileheadpath + filesavepath + factory.getCode()+ "/" +publicFileType;
		
		File f = new File(fileheadpath + filesavepath + factory.getCode());// 如果当前文件不存在，那么就创建
		logger.debug("newPrivateFilePath:" + newPrivateFilePath);
		logger.debug("newPubicFilePath:" + newPubicFilePath);
		try {
			FileUtil.setFilePermissions(f);
		} catch (Exception e) {
			e.printStackTrace();
		}
		ApkUtil.fileCopy(newPrivateFilePath, privatePath);
		ApkUtil.fileCopy(newPubicFilePath,publicPath);
		factory.setPrivateKeyPath(filesavepath + factory.getCode()+ "/" +privateFileType);
		factory.setPublicKeyPath(filesavepath + factory.getCode() + "/" +publicFileType);
		logger.info("证书私钥++++++++++++++++++++++++++++++++"+newPrivateFilePath);
		logger.info("证书公钥++++++++++++++++++++++++++++++++"+newPubicFilePath);
	}
	
	public void insertByFastFds(Factory factory){
		String privatePath = factory.getPrivateKeyPath();
		String publicPath = factory.getPublicKeyPath();
		//文件名
		String privateFileName = privatePath.substring(privatePath.lastIndexOf("/")+1);
		String publicFileName = publicPath.substring(publicPath.lastIndexOf("/")+1);
		//保存文件到正式目录
		FileInfo privateFileInfo = ApkFastFdsUtil.getSignedFileCustomer(privatePath,privateFileName);
		FileInfo publicFileInfo = ApkFastFdsUtil.getSignedFileCustomer(publicPath,publicFileName);
		logger.info("证书私钥++++++++++++++++++++++++++++++++"+privateFileInfo.getFile_absolute_path());
		logger.info("证书公钥++++++++++++++++++++++++++++++++"+publicFileInfo.getFile_absolute_path());
		logger.info("证书公钥大小++++++++++++++++++++++++++++++++"+publicFileInfo.getFileSize());
		logger.info("证书私钥大小++++++++++++++++++++++++++++++++"+privateFileInfo.getFileSize());
		factory.setPrivateKeyPath(privateFileInfo.getFileSavePath());
		factory.setPublicKeyPath(publicFileInfo.getFileSavePath());
		
	}
		
	
	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public Factory getFactoryById(@PathVariable Integer id) {
		logger.info("---factory---" + id);
		return factoryService.getFactoryById(id);
	}
	@RequestMapping(value = "/view/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public Factory getFactoryViewById(@PathVariable Integer id) {
		logger.info("---factory---" + id);
		return factoryService.getFactoryById(id);
	}
	@RequestMapping(value = "/type", method = RequestMethod.GET)
	@ResponseBody
	public List<Factory> getFactory() {
		return factoryService.getFactory();
	}
	
	@RequestMapping(value = "/uploadKey", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadKey(HttpServletRequest request, HttpServletResponse response, HttpSession session) {
		String keyPath = "";
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file == null) {
					return ResultMsg.fail();
				}
				// 取得当前上传文件的文件名称
				String myFileName = file.getOriginalFilename();
				logger.debug("----------------------------------------------myFileName:" + myFileName);
//				String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
				String fileSavePath = temDir + "/" ;
				File newFile = new File(fileSavePath +myFileName);
				logger.debug("----------------------------------------------fileSavePath:" + fileSavePath);
				keyPath = newFile.getAbsolutePath();
				try {// 上传
					file.transferTo(newFile);
				} catch (Exception e1) {
					e1.printStackTrace();
					return new ResultMsg(6, "Upload failed", keyPath);
				}
			}
		}
		return new ResultMsg(6, "Upload success", keyPath);
	}
}