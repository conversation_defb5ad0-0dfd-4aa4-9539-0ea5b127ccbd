package com.centerm.cpay.coms.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * 记录系统日志拦截器
 * <p>Title: LogInterceptor</p>
 * <p>Description: </p>
 * <p>Company: www.centerm.com</p> 
 * <AUTHOR> 赖煌生
 * @date	2016年6月29日 
 * @version 1.0
 */
public class LogInterceptor implements HandlerInterceptor {

	
	//进入 Handler方法之前执行
	
	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception {
		
		
	    return true;
	}

	//进入Handler方法之后，返回modelAndView之前执行
	@Override
	public void postHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		
		
	}

	//执行Handler完成执行此方法
	@Override
	public void afterCompletion(HttpServletRequest request,
			HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		
		//System.out.println("LogInterceptor...afterCompletion");
	}

}
