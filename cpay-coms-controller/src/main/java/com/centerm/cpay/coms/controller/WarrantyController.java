
package com.centerm.cpay.coms.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.Constants.Constants;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.ComsCasWarranty;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.WarrantyService;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.coms.util.SessionInfo;

/**
 * 
 * <AUTHOR>
 * @version $Id: WarrantyController.java, v 0.1 2016年7月11日 上午9:15:05 sup Exp $
 */
@Controller
@RequestMapping("/warranty")
public class WarrantyController {
	@Autowired
	private WarrantyService warrantyService;
	Logger logger = LoggerFactory.getLogger(WarrantyController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getFlowInfList(HttpSession session,ComsCasWarranty entity, @RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		EUDataGridResult result = warrantyService.getGridList(entity, page, rows);
		return result;
	}
	
	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsCasWarranty getFlowInf(@PathVariable Integer id) {
		return warrantyService.queryById(id);
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(HttpSession session,@PathVariable Integer id) {
		User user = FrontEndHelper.getLoginUser();
		ResultMsg resultMsg = warrantyService.deleteById(id);
		warrantyService.insertSysLog(user, "删除");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByIds(HttpSession session,@RequestParam String ids) {
		User user = FrontEndHelper.getLoginUser();
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		ResultMsg resultMsg = warrantyService.deleteByIds(idsList);
		warrantyService.insertSysLog(user, "删除");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(HttpSession session,@RequestBody ComsCasWarranty entity) {
		User user = FrontEndHelper.getLoginUser();
		ResultMsg resultMsg = warrantyService.updateByEntity(entity);
		warrantyService.insertSysLog(user, "更新");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(HttpSession session,@RequestBody ComsCasWarranty entity) {
		User user = FrontEndHelper.getLoginUser();
		ResultMsg resultMsg = warrantyService.insertEntity(entity);
		warrantyService.insertSysLog(user, "新增");
		return resultMsg;
	}
	
	@RequestMapping(value = "/{ids}/{param}", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg operate(@PathVariable String ids,@PathVariable Integer param) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return	param==1?warrantyService.passByIds(idsList):warrantyService.rejectByIds(idsList);
	}
}
