package com.centerm.cpay.coms.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.centerm.cpay.common.enums.OffLineStatus;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkPublishCount;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.utils.Base64Api;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.ExcelUtil;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo;
import com.centerm.cpay.coms.apkstore.service.StoreApkService;
import com.centerm.cpay.coms.dao.mapper.TerminalSystemInfoMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalWarehouseMapper;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.flow.dao.pojo.SimFlowMonthCount;
import com.centerm.cpay.flow.service.ExcelExportService;

import jodd.util.URLDecoder;

/******
 * 
 * <AUTHOR>
 * @see20160715
 * excel导出
 *
 */
@Controller
@RequestMapping("/excelExport")
public class ExcelExportController {

	Logger logger = LoggerFactory.getLogger(ExcelExportController.class);
	@Autowired
	private ExcelExportService excelService;
	@Autowired
	private AppInstallReportService appInstallReportService;
	@Autowired
	private StoreApkService storeApkService;
	@Autowired
	private TerminalSystemInfoMapper terminalSystemInfoMapper;
	@Autowired
	private TerminalSystemInfoService terminalSystemInfoService;
	@Autowired
	private LogService logService;
	@Autowired
	private TerminalAdvQueryService terminalAdvQueryService; 
	@Autowired
	private TerminalService terminalService; 
	@Autowired
	private TerminalWarehouseMapper terminalWarehouseMapper;
	@Autowired
	private TerminalRemoveMonitorService terminalRemoveMonitorService;
	@Autowired
	private UserService userService;

	@Autowired
	private DriverJobService driverJobService;

	@Autowired
	private InstitutionService institutionService;

	//【成功比例】0%
	private static final String ZERO_STRING = "0";

	//【比例】%标志位
	private static final String PERCENT_STRING = "%";

	//【任务汇总】字段显示
	private static final String ALL_ABOVE = "All Above";

	//【机构汇总】字段
	private static final String ALL_DATA = "allData";

	/**
	 * sim卡流量按日统计
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTermFlowDay/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportExcelTermDay(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        User user = FrontEndHelper.getLoginUser();
        TerminalFlowCount entity = new TerminalFlowCount();
		condition = condition.replace("^", ".");
		String[] arr = condition.split("&");

//		response.setContentType("application/vnd.ms-excel");    
//      response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端系统信息", "UTF-8")+".xlsx");    
        
   
        if(!CtUtils.isEmpty(arr[1].replace("termSeq=", "").trim())){
        	entity.setTermSeq(arr[1].replace("termSeq=", "").trim());
        }
		if(!CtUtils.isEmpty(arr[2].replace("startTime=", "").trim())){
			entity.setStartTime(arr[2].replace("startTime=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[3].replace("endTime=", "").trim())){
			entity.setEndTime(arr[3].replace("endTime=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[4].replace("minFlow=", "").trim())){
			entity.setMinFlow(Float.parseFloat(arr[4].replace("minFlow=", "").trim()));
		}
		if(!CtUtils.isEmpty(arr[5].replace("maxFlow=", "").trim())){
			entity.setMaxFlow(Float.parseFloat(arr[5].replace("maxFlow=", "").trim()));
		}
		response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端_"+entity.getTermSeq()+"_流量日统计表", "UTF-8")+".xlsx");
		entity.setInsId(user.getInsId());
		excelService.exportExcelTermDay(entity, ouputStream);
        ouputStream.flush();
        ouputStream.close();
	}
	
	/**
	 * sim卡流量按月统计
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTermFlowMonthCount/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportExcelTermMonth(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端流量月统计表", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
		condition = condition.replace("^", ".");
		String[] arr = condition.split("&");
		TerminalFlowCount entity = new TerminalFlowCount();
//		response.setContentType("application/vnd.ms-excel");    
//      response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端系统信息", "UTF-8")+".xlsx");    
        
        if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
        	entity.setTermSeq(arr[0].replace("termSeq=", "").trim());
        }
		if(!CtUtils.isEmpty(arr[1].replace("startTime=", "").trim())){
			entity.setStartTime(arr[1].replace("startTime=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[2].replace("endTime=", "").trim())){
			entity.setEndTime(arr[2].replace("endTime=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[4].replace("minFlow=", "").trim())){
			entity.setMinFlow(Float.parseFloat(arr[4].replace("minFlow=", "").trim()));
		}
		if(!CtUtils.isEmpty(arr[5].replace("maxFlow=", "").trim())){
			entity.setMaxFlow(Float.parseFloat(arr[5].replace("maxFlow=", "").trim()));
		}
		entity.setInsId(user.getInsId());
		excelService.exportExcelTermMonth(entity, ouputStream);
        ouputStream.flush();
        ouputStream.close();
	}
	/**
	 * sim月订购流量统计
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportSimMonth/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportExcelSimMonth(HttpServletRequest request, HttpServletResponse response,
			@PathVariable String condition) throws IOException {
		condition = condition.replace("^", ".");
		String[] arr = condition.split("&");
		SimFlowMonthCount entity = new SimFlowMonthCount();
		if (!CtUtils.isEmpty(arr[0].replace("serviceProviderName=", "").trim())) {
			entity.setServiceProviderName(arr[0].replace("serviceProviderName=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[1].replace("cardNo=", "").trim())) {
			entity.setCardNo(arr[1].replace("cardNo=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[2].replace("startTime=", "").trim())) {
			entity.setStartTime(arr[2].replace("startTime=", "").trim());
		}
		if (CtUtils.isEmpty(arr[3].replace("endTime=", "").trim())) {
			entity.setEndTime(arr[3].replace("endTime=", "").trim());
		}
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition",
				"attachment;filename=" + java.net.URLEncoder.encode("sim卡月订购流量统计", "UTF-8") + ".xlsx");
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		excelService.exportExcelSimMonth(entity, ouputStream);
		ouputStream.flush();
		ouputStream.close();
	}
	
	/**
	 * 驱动安装统计表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportAppInstall", method = { RequestMethod.GET })
	@ResponseBody
	public void exportExcelAppInstall(HttpServletRequest request, HttpServletResponse response) throws IOException {
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("驱动安装统计表", "UTF-8")+".xlsx"); 
		User user = FrontEndHelper.getLoginUser();
		AppInstall entity = new AppInstall();
		entity.setInsId(user.getInsId());		
		List<AppInstall> list = appInstallReportService.getList(entity);
		String[] excelHeader = { "终端序列号", "机构名称", "终端厂商", "终端型号", "驱动名称", "驱动版本号", "等待下发次数", "下发成功次数", "下载成功字数", "下载失败次数", "更新成功次数", "更新失败次数", "取消下发次数"};
		String[] fields = { "termSeq", "insName", "termMfrName", "termTypeName", "appName", "appVersion", "waitNum", "issuedNum", "downloadSuccessNum", "downloadErrorNum", "updateSuccessNum", "updateErrorNum", "cancelNum"};
		ExcelUtil.exportExcel("驱动安装统计表", excelHeader, fields, list, ouputStream);
        ouputStream.flush();
        ouputStream.close();
	}
	
	/**
	 * 应用下载量统计表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportApkDownLoad", method = { RequestMethod.GET })
	@ResponseBody
	public void exportExcelApkDownLoad(HttpServletRequest request, HttpServletResponse response) throws IOException {
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("应用下载量统计表", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
		StoreApkInfo entity = new StoreApkInfo();
		entity.setInsId(user.getInsId());		
		List<StoreApkInfo> list = storeApkService.getApkDownLoadList(entity);
		String[] excelHeader = { "机构名称", "应用名称", "应用包名", "应用版本号", "发布时间", "总下载量"};
		String[] fields = { "insName", "appName", "appCode", "appVersion", "pubTime", "dlNum"};
		ExcelUtil.exportExcel("业务应用下载量统计", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	/**
	 * 终端应用信息-终端列表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTermAdvQuery/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermAdvQuery(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		TerminalAdvQuery terminalAdvQuery = new TerminalAdvQuery();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端高级查询信息表", "UTF-8")+".xlsx");    
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(terminalAdvQuery.getInsId())) {
			terminalAdvQuery.setInsId(user.getInsId());
		}
		terminalAdvQuery.setQueryCriteria(URLDecoder.decode(Base64Api.decode(condition),"UTF-8"));
		List<Terminal> list = terminalAdvQueryService.getTerminalListExel(terminalAdvQuery);
		String[] excelHeader = {"Serial No.","Organization","Group","Manufacturer","Model"};
//		String[] excelHeader = {"终端序列号","终端编号","商户编号","所属机构","所属组","终端厂商","终端型号","终端类型","商户名称","是否支持DCC","银联间直连","业务类型"};
		String[] fields = {"termSeq","insName","groupName","termMfrName","termTypeName"};
//		ExcelUtil.exportExcel("终端高级查询信息表", excelHeader, fields, list, ouputStream);
		ExcelUtil.exportExcel("Terminal Advanced Query Information Table", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	
	/**
	 * 终端应用信息-终端列表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTermAdvQueryNoCondition", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermAdvQueryNoCondition(HttpServletRequest request, HttpServletResponse response) throws IOException {
		TerminalAdvQuery terminalAdvQuery = new TerminalAdvQuery();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
//        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端高级查询信息表", "UTF-8")+".xlsx");   
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("Terminal Advanced Query Information Table", "UTF-8")+".xlsx");    
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(terminalAdvQuery.getInsId())) {
			terminalAdvQuery.setInsId(user.getInsId());
		}
		List<Terminal> list = terminalAdvQueryService.getTerminalListExel(terminalAdvQuery);
		String[] excelHeader = {"Serial No.","Organization","Group","Manufacturer","Model"};
//		String[] excelHeader = {"终端序列号","终端编号","商户编号","所属机构","所属组","终端厂商","终端型号","终端类型","商户名称","是否支持DCC","银联间直连","业务类型"};
		String[] fields = {"termSeq","insName","groupName","termMfrName","termTypeName"};
		ExcelUtil.exportExcel("Terminal Advanced Query Information Table", excelHeader, fields, list, ouputStream);
//		ExcelUtil.exportExcel("终端高级查询信息表", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	
	/**
	 * 终端应用信息-终端列表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTerminalAppInfo/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTerminalAppInfo(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		condition = condition.replace("^", ".");
		String[] arr = condition.split("&");
		TerminalApp terminalApp = new TerminalApp();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
//        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端应用信息表", "UTF-8")+".xlsx"); 
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("Terminal Application Information Table", "UTF-8")+".xlsx"); 
        User user = FrontEndHelper.getLoginUser();
        terminalApp.setInsId(user.getInsId());
        if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
        	terminalApp.setTermSeq(arr[0].replace("termSeq=", "").trim());
        }
		if(!CtUtils.isEmpty(arr[1].replace("appCode=", "").trim())){
			terminalApp.setAppCode(arr[1].replace("appCode=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[2].replace("appName=", "").trim())){
			terminalApp.setAppName(arr[2].replace("appName=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[3].replace("appVersion=", "").trim())){
			terminalApp.setAppVersion(arr[3].replace("appVersion=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[4].replace("appVersionOutSide=", "").trim())){
			terminalApp.setAppVersionOutSide(arr[4].replace("appVersionOutSide=", "").trim());
		}
		List<TerminalApp> list = terminalSystemInfoMapper.selectTerminalAppInfoList(terminalApp);
//		String[] excelHeader = {"终端序列号"};
		String[] excelHeader = {"Serial No.", "App Code", "App Name", "Version", "Inner Version"};
		String[] fields = {"termSeq", "appCode", "appName", "appVersionOutSide", "appVersion"};
		ExcelUtil.exportExcel("Terminal Application Information - Terminal List", excelHeader, fields, list, ouputStream);
//		ExcelUtil.exportExcel("终端应用信息-终端列表", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	
	/**************终端信息导出表*****************/
	@RequestMapping(value = "/exportTermInfo/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermInfo(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		Terminal terminal = new Terminal();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
//        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端信息表", "UTF-8")+".xlsx");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("Terminal Information Table", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
        terminal.setInsId(user.getInsId());
        if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
        	terminal.setTermSeq(arr[0].replace("termSeq=", "").trim());
        }
        if(!CtUtils.isEmpty(arr[1].replace("insId=", "").trim())){
        	terminal.setInsId(Integer.parseInt(arr[1].replace("insId=", "").trim()));
        }
		if(!CtUtils.isEmpty(arr[2].replace("termMfrId=", "").trim())){
			terminal.setTermMfrId(Integer.parseInt(arr[2].replace("termMfrId=", "").trim()));
		}
        if(!CtUtils.isEmpty(arr[3].replace("terminalGroupId=", "").trim())){
        	terminal.setTerminalGroupId(Integer.parseInt(arr[3].replace("terminalGroupId=", "").trim()));
        }
        if(!CtUtils.isEmpty(arr[4].replace("termTypeCode=", "").trim())){
        	terminal.setTermTypeCode(arr[4].replace("termTypeCode=", "").trim());
        }
		if(!CtUtils.isEmpty(arr[5].replace("termNo=", "").trim())){
			terminal.setTermNo(arr[5].replace("termNo=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[6].replace("payMerchantNo=", "").trim())){
			terminal.setPayMerchantNo(arr[6].replace("payMerchantNo=", "").trim());
		}
        if(!CtUtils.isEmpty(arr[7].replace("remark=", "").trim())){
            terminal.setRemark(arr[7].replace("remark=", "").trim());
        }
		
		if(!CtUtils.isEmpty(arr[8].replace("dccSupFlag=", "").trim())){
			terminal.setDccSupFlag(arr[8].replace("dccSupFlag=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[9].replace("activateType=", "").trim())){
			terminal.setActivateType(arr[9].replace("activateType=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[10].replace("cupConnMode=", "").trim())){
			terminal.setCupConnMode(arr[10].replace("cupConnMode=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[11].replace("bussType=", "").trim())){
			terminal.setBussType(arr[11].replace("bussType=", "").trim());
		}
		List<Terminal> list = terminalService.getTermInfAll(terminal);
		String[] excelHeader = {"Serial No.","Organization","Group","Manufacturer","Model", "TimeZone", "Remark", "Status"};
		list.forEach(term -> term.setOnlineStatus(OffLineStatus.getEnglishName(((term.getOnlineStatus())))));
//		String[] excelHeader = {"终端序列号","终端编号","商户编号","所属机构","所属组","终端厂商","终端型号","终端类型","商户名称","是否支持DCC","银联间直连","业务类型"};
		String[] fields = {"termSeq","insName","groupName","termMfrName","termTypeName", "timeZone", "remark", "onlineStatus"};
//		ExcelUtil.exportExcel("终端信息表", excelHeader, fields, list, ouputStream);
		ExcelUtil.exportExcel("Terminal information table", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	
	
	
	
	/**************到处终端状态信息表*****************/
	@RequestMapping(value = "/exportTermStatusInfo/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermStatusInfo(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		TerminalRealTimeStatus terminalRealTimeStatus = new TerminalRealTimeStatus();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端信息表", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
        terminalRealTimeStatus.setInsId(user.getInsId());
        if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
        	terminalRealTimeStatus.setTermSeq(arr[0].replace("termSeq=", "").trim());
        }
        if(!CtUtils.isEmpty(arr[1].replace("insId=", "").trim())){
        	terminalRealTimeStatus.setInsId(Integer.parseInt(arr[1].replace("insId=", "").trim()));
        }
		List<TerminalRealTimeStatus> list = logService.getTerminalRealTimeStatus(terminalRealTimeStatus);
		String[] excelHeader = {"终端序列号","所属机构","最近通讯时间","经度","纬度","详细地址"};
		String[] fields = {"termSeq","insName","timestamp","longitude","latitude","address"};
		ExcelUtil.exportExcel("终端实时位置情况统计表", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	
	
	
	
	/**
	 * 终端系统信息-终端列表
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@RequestMapping(value = "/exportTerminalSystemInfo/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTerminalSystemInfo(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		condition = condition.replace("^", ".");
		String[] arr = condition.split("&");
		TerminalSystemInfo terminalSystemInfo = new TerminalSystemInfo();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode("Terminal System Information", "UTF-8") + ".xlsx");
		User user = FrontEndHelper.getLoginUser();
		terminalSystemInfo.setInsId(user.getInsId());
		if (!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())) {
			terminalSystemInfo.setTermSeq(arr[0].replace("termSeq=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[1].replace("osVer=", "").trim())) {
			terminalSystemInfo.setOsVer(arr[1].replace("osVer=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[2].replace("androidVer=", "").trim())) {
			terminalSystemInfo.setAndroidVer(arr[2].replace("androidVer=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[3].replace("safeModVer=", "").trim())) {
			terminalSystemInfo.setSafeModVer(arr[3].replace("safeModVer=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[4].replace("tmsSDK=", "").trim())) {
			terminalSystemInfo.setTmsSDK(arr[4].replace("tmsSDK=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[5].replace("paySDK=", "").trim())) {
			terminalSystemInfo.setPaySDK(arr[5].replace("paySDK=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[6].replace("emvVer=", "").trim())) {
			terminalSystemInfo.setEmvVer(arr[6].replace("emvVer=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[7].replace("payAppCode=", "").trim())) {
			terminalSystemInfo.setPayAppCode(arr[7].replace("payAppCode=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[8].replace("payAppVersion=", "").trim())) {
			terminalSystemInfo.setPayAppVersion(arr[8].replace("payAppVersion=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[9].replace("tmsAppVersion=", "").trim())) {
			terminalSystemInfo.setTmsAppVersion(arr[9].replace("tmsAppVersion=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[10].replace("networkType=", "").trim())) {
			terminalSystemInfo.setNetworkType(arr[10].replace("networkType=", "").trim());
		}
		List<TerminalSystemInfo> list = terminalSystemInfoMapper.selectByCondition(terminalSystemInfo);
		String[] excelHeader = {"Serial No.", "OS Version", "Parameter Version", "Android Version", "TMS SDK", "EMV version", "Tms App Version", "Tms App Version OutSide", "Modify Time"};
		String[] fields = {"termSeq", "osVer", "safeModVer", "androidVer", "tmsSDK", "emvVer", "tmsAppVersion", "tmsAppVersionOutSide", "updateTime"};
		ExcelUtil.exportExcel("Terminal System Info", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
		ouputStream.close();
	}
	/****
	 * 导出终端库存信息
	 */
	/**************终端信息导出表*****************/
	@RequestMapping(value = "/exportTermWareHouse/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermWareHouse(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		TerminalWarehouse terminalWarehouse = new TerminalWarehouse();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
//        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端进销存信息表", "UTF-8")+".xlsx");
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("Terminal Invoicing Management Table", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
        terminalWarehouse.setInsId(user.getInsId());
        if(!CtUtils.isEmpty(arr[0].replace("insId=", "").trim())){
        	terminalWarehouse.setInsId(Integer.parseInt(arr[0].replace("insId=", "").trim()));
        }
        List<TerminalWarehouse> list = terminalWarehouseMapper.showTermKcList(terminalWarehouse);
//		String[] excelHeader = {"所属机构","商户数","终端总量","本月前总库存量","本月入库量","本月前总出库量","本月出库量","报废量"};
        String[] excelHeader = {"Organization","Number Of Merchants","Number of Terminal","Total Inventory Before This Month","Warehousing Volume This Month","Total shipments before this month","The amount of shipments this month","Scrap volume"};
		String[] fields = {"insName","merchantCount","termCount","termLastMonKCCount","termCurMonRKCount","termLastMonCKCount","termCurMonCKCount","termBFCount"};
		//ExcelUtil.exportExcel("终端进销信息表", excelHeader, fields, list, ouputStream);
		ExcelUtil.exportExcel("Terminal Invoicing Management Table", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	/****
	 * 终端移动信息表
	 * 
	 * 
	 */
	@RequestMapping(value = "/exportTermRemoveTrail/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTermRemoveTrail(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		TerminalRemoveTrail entity = new TerminalRemoveTrail();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("终端移动信息表", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
        entity.setInsId(user.getInsId());
        if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
        	entity.setTermSeq(arr[0].replace("termSeq=", "").trim());
        }
        if(!CtUtils.isEmpty(arr[1].replace("insId=", "").trim())){
        	entity.setInsId(Integer.parseInt(arr[1].replace("insId=", "").trim()));
        }
        List<TerminalRemoveTrail> list = terminalRemoveMonitorService.exportList(entity);
		String[] excelHeader = {"终端序列号","所属机构","经度","纬度","地址","偏移距离(米)","记录时间"};
		String[] fields = {"termSeq","insName","longitude","latitude","address","distance","createTime"};
		ExcelUtil.exportExcel("终端移动信息表", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}
	/**
	 * 导出系统用户信息
	 */
	@RequestMapping(value = "/exportUserInfo/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportUserInfo(HttpServletRequest request, HttpServletResponse response,@PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");    
        response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("系统用户信息表", "UTF-8")+".xlsx");    
        User user = FrontEndHelper.getLoginUser();
        User userWhere = new User();
        if(CtUtils.isEmpty(arr[0].replace("insId=", "").trim())) {
        	userWhere.setInsId(user.getInsId());
        }else {
        	userWhere.setInsId(Integer.parseInt(arr[0].replace("insId=", "").trim()));
        }
        if(!CtUtils.isEmpty(arr[1].replace("username=", "").trim())) {
        	userWhere.setUsername(arr[1].replace("username=", "").trim());
        }
        if(!CtUtils.isEmpty(arr[2].replace("realname=", "").trim())) {
        	userWhere.setRealname(arr[2].replace("realname=", "").trim());
        }
        if(!CtUtils.isEmpty(arr[3].replace("roleId=", "").trim())) {
        	userWhere.setRoleId(Integer.parseInt(arr[3].replace("roleId=", "").trim()));
        }
        List<User> list = userService.exportList(userWhere);
		String[] excelHeader = {"Organization Name","Username","Real Name","Role","Phone","Email"};
		String[] fields = {"insName","username","realname","roleNames","telephone","email"};
		ExcelUtil.exportExcel("System User Info", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
        ouputStream.close();
	}

	/**
	 *【导出】软件更新数据
	 */
	@RequestMapping(value = "/exportUpdate/{condition}", method = {RequestMethod.GET})
	@ResponseBody
	public void exportUpdateStatistics(HttpServletResponse response, @PathVariable String condition) throws IOException {
		//【初始化】输出流
		String[] arr = condition.split("&");
		DriverJob driverJob = new DriverJob();
		OutputStream outputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode("Software Update Statistics", "UTF-8") + ".xlsx");

		//【配置】查询请求参数（机构ID）
		User user = FrontEndHelper.getLoginUser();
		Integer institutionId;
		if (CtUtils.isEmpty(arr[0].replace("insId=", "").trim())) {
			institutionId = Objects.requireNonNull(user).getInsId();
		} else {
			institutionId = Integer.parseInt(arr[0].replace("insId=", "").trim());
		}

		//【配置】其他参数查询
		driverJob.setInsId(institutionId);
		if (!CtUtils.isEmpty(arr[1].replace("startTime=", "").trim())) {
			driverJob.setStartTime(arr[1].replace("startTime=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[2].replace("endTime=", "").trim())) {
			driverJob.setEndTime(arr[2].replace("endTime=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[3].replace("rangeType=", "").trim())) {
			driverJob.setId(Integer.parseInt(arr[3].replace("rangeType=", "").trim()));
		}

		//【计算】任务成功比例
		//【导出】Excel（软件更新信息）
		List<DriverJob> driverJobList = driverJobService.getApkUpdateReport(driverJob);
		calculateSummary(driverJobList, institutionId);
		String[] excelHeader = {"Organization", "Task Name", "All Task", "Update Success", "Download Success", "Download Failed", "Update Failed", "Waiting Release", "Update Success Percentage"};
		String[] fields = {"insName", "jobName", "number", "success", "successDown", "failureDown", "failureUpdate", "waitSend", "rate"};
		ExcelUtil.exportExcel("Software Update Statistics", excelHeader, fields, driverJobList, outputStream);
		outputStream.flush();
		outputStream.close();
	}
	/**
	 *【计算】成功更新比例
	 * @param driverJobList 更新队列
	 */
	private void calculateSummary(List<DriverJob> driverJobList, Integer institutionId) {
		//【获取】汇总机构类型
		Institution summaryInstitution = institutionService.selectByInsId(institutionId);

		//【构造】更新任务成功比例参数
		for (DriverJob job : driverJobList) {
			job.setRate(calculatePercentage(job));
		}

		//【构造】导出软件更新数据（总计）
		DriverJob allJob = new DriverJob();
		allJob.setJobName(ALL_ABOVE);
		allJob.setInsName(summaryInstitution.getName());
		allJob.setNumber(driverJobList.stream().map(DriverJob::getNumber).reduce(Integer::sum).orElse(0));
		allJob.setSuccess(driverJobList.stream().map(DriverJob::getSuccess).reduce(Integer::sum).orElse(0));
		allJob.setSuccessDown(driverJobList.stream().map(DriverJob::getSuccessDown).reduce(Integer::sum).orElse(0));
		allJob.setFailureDown(driverJobList.stream().map(DriverJob::getFailureDown).reduce(Integer::sum).orElse(0));
		allJob.setFailureUpdate(driverJobList.stream().map(DriverJob::getFailureUpdate).reduce(Integer::sum).orElse(0));
		allJob.setWaitSend(driverJobList.stream().map(DriverJob::getWaitSend).reduce(Integer::sum).orElse(0));
		allJob.setRate(calculatePercentage(allJob));
		driverJobList.add(0, allJob);
	}

	/**
	 *【计算】成功更新比例
	 * @param job 任务
	 * @return 成功比例
	 */
	private String calculatePercentage(DriverJob job) {
		if (job.getNumber() == 0) {
			return ZERO_STRING + PERCENT_STRING;
		} else {
			return Math.round((float) job.getSuccess() / job.getNumber() * 100) + PERCENT_STRING;
		}
	}

	/**
	 *【导出】App发布数据
	 */
	@RequestMapping(value = "/exportApp/{condition}", method = { RequestMethod.GET})
	public void exportAppStatistics(HttpServletResponse response, @PathVariable String condition) throws IOException {
		//【初始化】输出流
		String[] arr = condition.split("&");
		StoreApkPublishCount storeApkPublishCount = new StoreApkPublishCount();
		OutputStream outputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode("App Released Statistics", "UTF-8") + ".xlsx");

		//【配置】查询请求参数（机构ID）
		User user = FrontEndHelper.getLoginUser();
		Integer institutionId;
		if (CtUtils.isEmpty(arr[0].replace("insId=", "").trim())) {
			institutionId = Objects.requireNonNull(user).getInsId();
		} else {
			institutionId = Integer.parseInt(arr[0].replace("insId=", "").trim());
		}

		//【配置】其他参数查询
		storeApkPublishCount.setInsId(institutionId);
		Institution ins = institutionService.selectByInsId(institutionId);
		if (!CtUtils.isEmpty(arr[1].replace("startTime=", "").trim())) {
			storeApkPublishCount.setStartTime(arr[1].replace("startTime=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[2].replace("endTime=", "").trim())) {
			storeApkPublishCount.setEndTime(arr[2].replace("endTime=", "").trim());
		} else {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
			storeApkPublishCount.setEndTime(sdf.format(new Date()));
		}
		if (!CtUtils.isEmpty(arr[3].replace("rangeType=", "").trim())) {
			storeApkPublishCount.setIsShowAll(Integer.parseInt(arr[3].replace("rangeType=", "").trim()));
		}

		//【查询】报表结果
		StoreApkPublishCount publishCount = storeApkService.getAppPublishReport(storeApkPublishCount);
		EasyExcel.write(outputStream).head(buildDynamicHeader(publishCount.getMonth())).sheet("Sheet1").doWrite(buildAppReleasedInfo(publishCount, ins));
	}

	@RequestMapping(value = "/exportTerminal/{condition}", method = { RequestMethod.GET })
	@ResponseBody
	public void exportTerminal(HttpServletResponse response, @PathVariable String condition) throws IOException {
		//【初始化】输出流
		String[] arr = condition.split("&");
		TermInfoReportCount termInfoReportCount = new TermInfoReportCount();
		OutputStream outputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode("Terminal Statistics", "UTF-8") + ".xlsx");

		//【配置】查询请求参数（机构ID）
		User user = FrontEndHelper.getLoginUser();
		Integer institutionId;
		if (CtUtils.isEmpty(arr[0].replace("insId=", "").trim())) {
			institutionId = Objects.requireNonNull(user).getInsId();
		} else {
			institutionId = Integer.parseInt(arr[0].replace("insId=", "").trim());
		}

		//【配置】其他参数查询
		termInfoReportCount.setInsId(institutionId);
		Institution ins = institutionService.selectByInsId(institutionId);
		if (!CtUtils.isEmpty(arr[1].replace("startTime=", "").trim())) {
			termInfoReportCount.setStartTime(arr[1].replace("startTime=", "").trim());
		}
		if (!CtUtils.isEmpty(arr[2].replace("endTime=", "").trim())) {
			termInfoReportCount.setEndTime(arr[2].replace("endTime=", "").trim());
		} else {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
			termInfoReportCount.setEndTime(sdf.format(new Date()));
		}
		if (!CtUtils.isEmpty(arr[3].replace("rangeType=", "").trim())) {
			termInfoReportCount.setIsShowAll(Integer.parseInt(arr[3].replace("rangeType=", "").trim()));
		}

		//【查询】报表结果
		TermInfoReportCount reportCount = terminalService.getTermInfoReport(termInfoReportCount);
		EasyExcel.write(outputStream).head(buildDynamicHeader(reportCount.getMonth())).sheet("Sheet1").doWrite(buildTerminalInfo(reportCount, ins));
	}

	/**
	 *【配置】动态数据（终端信息）
	 * @param reportCount 报表查询返回结果
	 * @return Excel数据
	 */
	private List<List<String>> buildTerminalInfo(TermInfoReportCount reportCount, Institution ins) {
		//【初始化】机构参数
		List<List<String>> dataList = new ArrayList<>();
		Map<String, int[]> dataMap = reportCount.getFactoryData();

		//【遍历】Map数据
		for (Map.Entry<String, int[]> entry : dataMap.entrySet()) {
			List<String> lineData = new ArrayList<>();
			if (Objects.equals(entry.getKey(), ALL_DATA)) {
				lineData.add(ins.getName());
			} else {
				lineData.add(entry.getKey());
			}

			//【构造】每月详细数据
			int[] monthDataList = entry.getValue();
			for (Integer monthData : monthDataList) {
				lineData.add(monthData.toString());
			}

			//【更新】动态数据列表
			dataList.add(lineData);
		}
		return dataList;
	}

	/**
	 *【配置】动态数据（App发布）
	 * @param publishCount 报表查询返回结果
	 * @return Excel数据
	 */
	private List<List<String>> buildAppReleasedInfo(StoreApkPublishCount publishCount, Institution ins) {
		//【初始化】机构参数
		List<List<String>> dataList = new ArrayList<>();
		Map<String, int[]> dataMap = publishCount.getFactoryData();

		//【遍历】Map数据
		for (Map.Entry<String, int[]> entry : dataMap.entrySet()) {
			List<String> lineData = new ArrayList<>();
			if (Objects.equals(entry.getKey(), ALL_DATA)) {
				lineData.add(ins.getName());
			} else {
				lineData.add(entry.getKey());
			}

			//【构造】每月详细数据
			int[] monthDataList = entry.getValue();
			for (Integer monthData : monthDataList) {
				lineData.add(monthData.toString());
			}

			//【更新】动态数据列表
			dataList.add(lineData);
		}
		return dataList;
	}

	/**
	 *【构造】动态表头
	 * @param header 表头
	 * @return 表头格式化
	 */
	private List<List<String>> buildDynamicHeader(String[] header) {
		//【初始化】表头参数
		List<List<String>> headerList = new ArrayList<>();
		List<String> organizedHeader = new ArrayList<>();
		organizedHeader.add("Organization");
		headerList.add(organizedHeader);

		//【遍历】动态表头参数
		for (String headerLine : header) {
			List<String> list = new ArrayList<>();
			list.add(headerLine);
			headerList.add(list);
		}

		//【返回】表头结果
	 	return headerList;
	}
}