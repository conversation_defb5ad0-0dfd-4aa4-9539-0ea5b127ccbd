
package com.centerm.cpay.coms.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.flow.dao.pojo.OrderException;
import com.centerm.cpay.flow.service.OrderExceptionService;

/**
 * @Title: ExceptionOrderController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年12月1日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/exceptionOrder")
public class ExceptionOrderController {
	Logger logger = LoggerFactory.getLogger(ApkDownLoadController.class);

	@Autowired
	private OrderExceptionService orderExceptionService;

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getSimFlowMonthList(OrderException orderException, @RequestParam Integer page, @RequestParam Integer rows) {
		EUDataGridResult result = orderExceptionService.getOrderExceptionList(orderException, page, rows);
		return result;
	}
	
	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg updateByPrimaryKey(@RequestBody OrderException orderException) {
		return orderExceptionService.updateByEntity(orderException);
	}
}
