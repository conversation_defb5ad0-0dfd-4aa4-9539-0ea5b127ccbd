package com.centerm.cpay.coms.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.SystemClean;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.flow.dao.pojo.ComsSimFlowusageDay;
import com.centerm.cpay.flow.service.FlowInfService;

/******
 * 
 * <AUTHOR>
 * @see20170615
 * 流量监控
 *
 */
@Controller
@RequestMapping("/flow")
public class FlowController {
	@Autowired
	private FlowInfService flowInfService;

	Logger logger = LoggerFactory.getLogger(TermFlowCountDayController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getList(ComsSimFlowusageDay ComsSimFlowusageDay, @RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		ComsSimFlowusageDay.setInsId(user.getInsId());
		EUDataGridResult result = flowInfService.queryFlowUsedByDayReport(ComsSimFlowusageDay,page,rows);
		return result;
	}
}
