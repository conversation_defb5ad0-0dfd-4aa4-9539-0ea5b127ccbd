package com.centerm.cpay.coms.exception;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.util.FrontEndHelper;

/**
 * 全局异常处理器
 * <p>Title: CustomExceptionResolver</p>
 * <p>Description: </p>
 * <p>Company: www.centerm.com</p> 
 * <AUTHOR> 赖煌生
 * @date	2016年6月29日 
 * @version 1.0
 */
public class CustomExceptionResolver implements HandlerExceptionResolver {
	
	Logger logger = LoggerFactory.getLogger(CustomExceptionResolver.class);

	/**
	 * <p>Title: resolveException</p>
	 * <p>Description: </p>
	 * @param request
	 * @param response
	 * @param handler
	 * @param ex 系统 抛出的异常
	 * @return
	 */
	@Override
	public ModelAndView resolveException(HttpServletRequest request,
			HttpServletResponse response, Object handler, Exception ex) {
		//handler就是处理器适配器要执行Handler对象（只有method）
		ex.printStackTrace();
		CustomException customException = null;
		if(ex instanceof CustomException){
			customException = (CustomException)ex;
		}else{
			customException = new CustomException("System Error！Contact your administrator for more Information");
		}
		//错误信息
		String msg = customException.getMessage();	
		ResultMsg result=ResultMsg.build(ResultMsg.FAIL_CODE, msg);;
		FrontEndHelper.responseOutWithJson(response,result);
		return null;
	}
	


}