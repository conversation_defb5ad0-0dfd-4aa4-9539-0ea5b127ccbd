
package com.centerm.cpay.coms.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.centerm.cpay.coms.dao.mapper.SysConfigureMapper;
import com.centerm.cpay.coms.dao.pojo.DriverJob;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.Constants.Constants;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ExcelUtil;
import com.centerm.cpay.common.utils.PropertyUtil;
import com.centerm.cpay.coms.dao.pojo.ComsUploadJob;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.UploadJobService;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.coms.util.SessionInfo;

/**
 * 
 * <AUTHOR>
 * @version $Id: UploadJobController.java, v 0.1 2016年7月1日 下午2:20:06 sup Exp $
 */
@Controller
@RequestMapping("/uploadjob")
public class UploadJobController {
	@Autowired
	private UploadJobService uploadJobService;
	
	Logger logger = LoggerFactory.getLogger(UploadJobController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getLauncherList(HttpSession session, ComsUploadJob entity, @RequestParam Integer page,
			@RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		EUDataGridResult result = uploadJobService.getUploadJobList(entity, page, rows);
		return result;
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsUploadJob getUser(@PathVariable Integer id) {
		return uploadJobService.getJob(id);
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(HttpSession session, @PathVariable Integer id) {
		User user = FrontEndHelper.getLoginUser();
		ResultMsg resultMsg = uploadJobService.deleteById(id);
		uploadJobService.insertSysLog(user, "Delete");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteJobs(HttpSession session, @RequestParam String ids) {
		User user = FrontEndHelper.getLoginUser();
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		ResultMsg resultMsg = uploadJobService.deleteJobs(idsList);
		uploadJobService.insertSysLog(user, "Delete");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(HttpSession session, @RequestBody ComsUploadJob entity) {
		User user = FrontEndHelper.getLoginUser();
		ResultMsg resultMsg = uploadJobService.updateByEntity(entity);
		uploadJobService.insertSysLog(user, "Update");
		return resultMsg;
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(HttpSession session, @RequestBody ComsUploadJob entity) {
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		entity.setUserId(user.getId().toString());
		ResultMsg resultMsg = uploadJobService.insertEntity(entity);
		uploadJobService.insertSysLog(user, "Add");
		return resultMsg;
	}

	@RequestMapping(value = "/subPublish", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg subPublish(@RequestBody ComsUploadJob entity) {
		return uploadJobService.updateJobToTask(entity);
	}

	@RequestMapping(value = "/importFile", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadFile(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// 重命名上传后的文件名
						/*String fileName = UUID.randomUUID()+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						path = PropertyUtil.getValue("filesavepath") + fileName;
						File localFile = new File(path);
						file.transferTo(localFile);
						String[][] resultArray = ExcelUtil.getStringArray(new FileInputStream(localFile));*/
						
						// 解析excel
						String type = myFileName.substring(myFileName.indexOf(".") + 1, myFileName.length());
						String[][] resultArray;
						if ("xls".equals(type)) {
							resultArray = ExcelUtil.readXls(file.getInputStream());
						} else {
							resultArray = ExcelUtil.readXlsx(file.getInputStream());
						}
						List<String> resultList = new ArrayList<String>();
						for (int i = 1; i < resultArray.length; i++) {
							String[] data = resultArray[i];
							if (!StringUtils.isEmpty(data[0])) {
								List<Map> list = uploadJobService.queryTerminalExist(data[0]);
								if(list != null && !list.isEmpty()){
									resultList.add(data[0]);	
								}
							}
						}
						resultList = removeDuplicateWithOrder(resultList);
						return ResultMsg.success(resultList);
					}
				}
			}
		}
		return ResultMsg.fail();
	}

	@RequestMapping(value = "/importExcel", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadExcel(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		Terminal terminalParam = new Terminal();
		//获取上传时的限定参数
		if(request.getParameter("termMfrId") != null){
			Integer termMfrId = Integer.valueOf(request.getParameter("termMfrId"));
			terminalParam.setTermMfrId(termMfrId);
		}

		String termTypeCodes = request.getParameter("termTypeCodes");
		if(request.getParameter("activateType") != null){
			String activateType = request.getParameter("activateType");
			terminalParam.setActivateType(activateType);
		}else if(request.getParameter("activateTypes") != null){
			String activateTypes = request.getParameter("activateTypes");
			terminalParam.setActivateTypes(activateTypes);
		}
		terminalParam.setTermTypeCodes(termTypeCodes);
		if(request.getParameter("cupConnMode") != null){
			terminalParam.setCupConnMode(request.getParameter("cupConnMode"));
		}
		if(request.getParameter("bussType") != null){
			terminalParam.setBussType(request.getParameter("bussType"));
		}
		if(request.getParameter("dccSupFlag") != null){
			terminalParam.setDccSupFlag(request.getParameter("dccSupFlag"));
		}
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// 解析excel
						String type = myFileName.substring(myFileName.lastIndexOf(".") + 1, myFileName.length());
						String[][] resultArray;
						if ("xls".equals(type)) {
							resultArray = ExcelUtil.readXls(file.getInputStream());
						} else {
							resultArray = ExcelUtil.readXlsx(file.getInputStream());
						}
						List<String> resultList = new ArrayList<String>();
						List<String> errorList = new ArrayList<String>();
						for (int i = 1; i < resultArray.length; i++) {
							String[] data = resultArray[i];
							if (!StringUtils.isEmpty(data[0])) {

								terminalParam.setTermSeq(data[0].replace(" ",""));
								User user = FrontEndHelper.getLoginUser();
								terminalParam.setInsId(user.getInsId());
								List<Map> list = uploadJobService.queryTerminalForExist(terminalParam);
								if(list != null && !list.isEmpty()){
									resultList.add(data[0]);	
								}else{
									errorList.add(data[0]);
								}
							}
						}
						resultList = removeDuplicateWithOrder(resultList);
						errorList = removeDuplicateWithOrder(errorList);
						List <List> list =  new ArrayList<List>();
						list.add(resultList);
						list.add(errorList);
						
						return ResultMsg.success(list);
					}
				}
			}
		}
		return ResultMsg.fail();
	}
	
	@RequestMapping(value = "/fileDownload", method = { RequestMethod.GET })
	public String download(HttpServletRequest request, HttpServletResponse response) throws IOException {
		response.setCharacterEncoding("utf-8");
		response.setContentType("multipart/form-data");
		response.setHeader("Content-Disposition", "attachment;fileName=terminal_job.xls");
		try {
			//InputStream inputStream = new FileInputStream(new File(PropertyUtil.getValue("filesavepath") + "terminal_job.xls"));
			ClassLoader classLoader = getClass().getClassLoader();
			InputStream inputStream = classLoader.getResourceAsStream("terminal_job.xls");
			OutputStream os = response.getOutputStream();
			byte[] b = new byte[2048];
			int length;
			while ((length = inputStream.read(b)) > 0) {
				os.write(b, 0, length);
			}
			os.close();
			inputStream.close();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	public  List removeDuplicateWithOrder(List list) {
		   Set set = new HashSet();
		   List newList = new ArrayList();
		   for (Iterator iter = list.iterator(); iter.hasNext();) {
		          Object element = iter.next();
		          if (set.add(element))
		             newList.add(element);
		       }
		      list.clear();
		      list.addAll(newList);
		      return list;
		}
}