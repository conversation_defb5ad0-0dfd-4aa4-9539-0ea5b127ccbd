package com.centerm.cpay.coms.util;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.apache.log4j.Logger;

import com.centerm.cpay.common.utils.FileUtil;
/**
 * Apk签名
 * <AUTHOR>
 *
 */
public class ApkSignUtil {
	
	private static Logger logger = Logger.getLogger(ApkSignUtil.class);

	/**签名目录*/  
    private static final String SIGN_PATH_NAME = "META-INF";  
    /**JDK路径**/
    //private static final String JDK_PATH = Constants.properties.getProperty("JDK_PATH");
    //private static final String KEY_STORE_PATH = Constants.properties.getProperty("KEY_STORE_PATH");
    //private static final String KEY_STORE_NAME = Constants.properties.getProperty("KEY_STORE_NAME");
    // private static final String KEY_STORE_PASS = Constants.properties.getProperty("KEY_STORE_PASS");
    private static final String  JDK_PATH = FilePathUtil.getValue("JDK_PATH");
    private static final String KEY_STORE_PATH = FilePathUtil.getValue("KEY_STORE_PATH");
    private static final String KEY_STORE_NAME = FilePathUtil.getValue("KEY_STORE_NAME");
    private static final String KEY_STORE_PASS = FilePathUtil.getValue("KEY_STORE_PASS");
    /**
     * 重新签名
     * @param apkPath
     * @throws Exception
     */
    public static void reSign(String apkPath) throws Exception {
    	//解压apk文件
		String zipDirPath = apkPath.substring(0, apkPath.lastIndexOf("."));
		unzip(apkPath,zipDirPath);
		
		//删除原apk文件
		FileUtil.deleteFile(apkPath);
		
		//删除签名文件
		deleteSignDir(zipDirPath);
		
		//压缩成apk
		File file = new File(zipDirPath);
		String unSignApkPath = apkPath.substring(0,apkPath.lastIndexOf("."))+"_unsign.apk";
		try {
			zip(unSignApkPath,file);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		//删除解压出来的文件夹
		try {
			FileUtil.delFolder(zipDirPath);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		//签名
		sign(unSignApkPath, apkPath);
		
		//删除未签名的apk文件
		FileUtil.deleteFile(unSignApkPath);
    }
    
	
	/**
	 * 解压apk文件
	 * @throws IOException 
	 */
	public static void unzip(String apkPath,String zipDirPath) throws Exception {
		File source = new File(apkPath);
		ZipFile zipFile = null;
		File zipDir = new File(zipDirPath);
		if(!zipDir.exists()) {
			zipDir.mkdirs();
		}
		ZipInputStream zis = null;
		BufferedOutputStream bos = null;
		InputStream input = null;
        
		try {
			zipFile = new ZipFile(source);
			zis = new ZipInputStream(new FileInputStream(source));
			ZipEntry entry = null;
			while ((entry = zis.getNextEntry()) != null) {
				File target = new File(zipDir, entry.getName());
				if(entry.isDirectory()) {
					if (!target.exists()) {
						target.mkdirs();
					}
					continue;
				}
				if(!target.getParentFile().exists()) {
					target.getParentFile().mkdirs();
				}
				input = zipFile.getInputStream(entry);
				// 写入文件
				bos = new BufferedOutputStream(new FileOutputStream(target));
				int read = 0;
				byte[] buffer = new byte[1024 * 10];
				while ((read = input.read(buffer, 0, buffer.length)) != -1) {
					bos.write(buffer, 0, read);
				}
				bos.flush();
				bos.close();
				input.close();
			}
			zis.closeEntry();
			zis.close();
			zipFile.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			if(null != bos) {
				bos.close();
				bos = null;
			}
			if(null != input) {
				input.close();
				input = null;
			}
			if(null != zis) {
				zis.close();
				zis = null;
			}
			if(null != zipFile) {
				zipFile.close();
				zipFile = null;
			}
		}
	}
	
	
	/**
	 * 删除解压后的签名文件  
	 * @param zipDirPath
	 */
	public static void deleteSignDir(String zipDirPath) {
        String signPathName = zipDirPath + File.separator + SIGN_PATH_NAME;  
        File signFile = new File(signPathName);  
        if(signFile.exists()){
            File sonFiles[] = signFile.listFiles();  
            if(sonFiles!=null && sonFiles.length>0){  
                //循环删除签名目录下的文件  
                for(File f : sonFiles){  
                    f.delete();  
                }  
            }  
            signFile.delete();  
        }  
	}
	
	/**
	 * 压缩
	 * @param zipFileName
	 * @param inputFile
	 * @throws Exception
	 */
	private static void zip(String zipFileName, File inputFile) throws Exception {  
		logger.info("压缩中...");  
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(  
                zipFileName));  
        BufferedOutputStream bo = new BufferedOutputStream(out);  
        //zip(out, inputFile, inputFile.getName(), bo);
        
        File[] fl = inputFile.listFiles();  
        for (int i = 0; i < fl.length; i++) {  
            zip(out, fl[i], fl[i].getName(), bo); // 递归遍历子文件夹  
        }
        bo.close();  
        out.close(); // 输出流关闭  
        logger.info("压缩完成");  
    }  
	
	private static void zip(ZipOutputStream out, File f, String base,  
            BufferedOutputStream bo) throws Exception { // 方法重载  
        if (f.isDirectory()) {  
            File[] fl = f.listFiles();  
            if (fl.length == 0) {  
                out.putNextEntry(new ZipEntry(base + "/")); // 创建zip压缩进入点base  
                System.out.println(base + "/");  
            }  
            for (int i = 0; i < fl.length; i++) {  
                zip(out, fl[i], base + "/" + fl[i].getName(), bo); // 递归遍历子文件夹  
            }  
        } else {  
            out.putNextEntry(new ZipEntry(base)); // 创建zip压缩进入点base  
            FileInputStream in = new FileInputStream(f);
            byte[] buf = new byte[1024]; 
            int len; 
            while((len=in.read(buf))>0){ 
                out.write(buf,0,len); 
            } 
            out.closeEntry();
            in.close();
        }  
    }
	
	/**
     * 签名
     * @param apkPath
     */
    public static void sign(String unSignApkPath, String apkPath) throws Exception {
    	try {
			// 签名
	    	logger.info("签名中...");
			String osName = System.getProperty("os.name");
			String cmd = "";
			Process process = null;
			if(osName.toLowerCase().startsWith("win")){
				File bin = new File(JDK_PATH);
				cmd = "cmd.exe /c jarsigner -keystore " + KEY_STORE_PATH
						+ " -storepass " + KEY_STORE_PASS + " -signedjar " + apkPath + " " + unSignApkPath + " " + KEY_STORE_NAME;
				process = Runtime.getRuntime().exec(cmd, null, bin);
			} else {
				cmd = JDK_PATH + File.separator + "jarsigner -keystore " + KEY_STORE_PATH
						+ " -storepass " + KEY_STORE_PASS + " -signedjar " + apkPath + " " + unSignApkPath + " " + KEY_STORE_NAME;
				process = Runtime.getRuntime().exec(new String[] {"/bin/sh", "-c", cmd});
			}
			System.out.println(cmd);
			printInfo(process);
			if (process.waitFor() != 0) {
				throw new Exception("签名失败");
			}
			logger.info("签名成功...");
    	} catch(Exception e) {
    		File file = new File(unSignApkPath);
    		file.delete();
    		throw e;
    	}
	}
    
    private static void printInfo(Process process) {
		try {
			BufferedReader br = null;
			br = new BufferedReader(new InputStreamReader(
					process.getInputStream()));
			String line = null;
			StringBuilder sb = new StringBuilder();
			while ((line = br.readLine()) != null) {
				System.out.println(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
