
package com.centerm.cpay.coms.controller;

import java.io.File;
import java.util.List;

import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.factory.StorageModeFactory;
import com.centerm.cpay.coms.service.DriverJobService;
import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.coms.service.DriverService;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.util.FrontEndHelper;

import javax.annotation.Resource;

/**
 * @Title: OperInfController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年5月22日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/driver")
public class DriverController {
	@Autowired
	private DriverService driverService;
	@Autowired
	private DriverUploadService driverUploadService;
	@Autowired
	private SysConfigureService configureService;
	@Autowired
	private DriverJobService driverJobService;

	@Resource
	private StorageModeFactory storageModeFactory;

	String ngsPicPath = "";

	String fileheadpath= ConfigInfo.HEADER_PATH;
	Logger logger = LoggerFactory.getLogger(DriverController.class);
	String isFastFds =ConfigInfo.IS_FASTDFS;

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getGridList(ComsAppInf entity, @RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		if(entity.getNgsPicPath() == null){
			ngsPicPath = configureService.queryValueByKey("download_url_intranet");
			entity.setNgsPicPath(ngsPicPath);
		}
		EUDataGridResult result = driverService.getGridList(entity, page, rows);
		return result;
	}
	/**
	 * 获取软件名称列表
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/getDriverList", method = { RequestMethod.GET })
	@ResponseBody
	public List<ComsAppInf> getList(ComsAppInf entity) {
		List<ComsAppInf> result = driverService.getList(entity);
		return result;
	}
	/**
	 * 根据软件类型获取软件名称列表
	 * @param entity
	 * @return
	 */
	@RequestMapping(value = "/getDriverListByType", method = { RequestMethod.GET })
	@ResponseBody
	public List<ComsAppInf> getListByType(ComsAppInf entity) {
		User user = FrontEndHelper.getLoginUser();
		entity.setInsId(user.getInsId());
		List<ComsAppInf> result = driverService.getListByInsId(entity);
		return result;
	}
	
	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsAppInf selectByPrimaryKey(@PathVariable Integer id) {
		return driverService.selectByPrimaryKey(id);
	}
	@RequestMapping(value = "/view/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsAppInf selectByAppInfo(@PathVariable Integer id) {
		ComsAppInf comsAppInf = new ComsAppInf();
		comsAppInf.setId(id);
		User user = FrontEndHelper.getLoginUser();
		if(comsAppInf.getNgsPicPath() == null){
			ngsPicPath = configureService.queryValueByKey("download_url_intranet");
			comsAppInf.setNgsPicPath(ngsPicPath);
		}
		return driverService.selectByAppInfo(comsAppInf);
	}
	@RequestMapping(value = "/validate", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg validate(ComsAppInf entity) {
		return driverService.validate(entity);
	}

	/**
	 * 删除appId对应的所有应用版本的文件
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(@PathVariable Integer id) {
		List<ComsAppVersion> comsAppVersionList = driverUploadService.selectByPrimaryKey(id);
		DriverJob driverJob = new DriverJob();
		driverJob.setAppCode(driverService.selectByPrimaryKey(id).getCode());
		driverJob.setAppId(id);
		for(ComsAppVersion comsAppVersion : comsAppVersionList){
			driverJob.setAppVersion(comsAppVersion.getAppVersion());
			if(driverJobService.countWorkingJob(driverJob) > 0) {
				return ResultMsg.build(1,"The software is in a remote update and cannot be deleted");
			}
		}

		//【删除】存储文件
		for (ComsAppVersion comsAppVersion : comsAppVersionList) {
			storageModeFactory.deleteExistedFile(comsAppVersion);
		}
		return driverService.deleteById(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByIds(@RequestParam Integer id) {
		List<ComsAppVersion> comsAppVersionList = driverUploadService.selectByPrimaryKey(id);
		if(!isFastFds.equals("true")){
			//删除传统方式存储的文件
			for (ComsAppVersion comsAppVersion : comsAppVersionList) {
				String appPath = comsAppVersion.getAppPath();
				File appFile = new File(fileheadpath + appPath);
				if (appFile.exists()) {
					appFile.delete();
				}

			}
		}else {
			//删除分布式方式存储的文件
			for (ComsAppVersion comsAppVersion : comsAppVersionList) {
				String appPath = comsAppVersion.getAppPath();
				if(!CtUtils.isEmpty(appPath)){
					ApkFastFdsUtil.deleteFile(appPath);
				}
			}
		}
		return driverService.deleteById(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody ComsAppInf entity) {
		return driverService.updateByEntity(entity);
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(@RequestBody ComsAppInf entity) {
		ResultMsg resultMsg = driverService.insertEntity(entity);
		if(resultMsg.getStatus() == 200){
			ComsAppInf query=new ComsAppInf();
			query.setCode(entity.getCode());
			List<ComsAppInf> list =driverService.getEntityByCode(query);
			ComsAppInf record = new ComsAppInf();
			if(list != null && !list.isEmpty()){
				record = list.get(0);
			}
			return ResultMsg.success(record);
		}else{
			return resultMsg;
		}
	}
	
}