package com.centerm.cpay.coms.controller;

import java.net.UnknownHostException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.mail.internet.MimeMessage;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.centerm.cpay.common.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.Constants.Constants;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.service.SmsService;
import com.centerm.cpay.coms.apkstore.service.ApkInfoService;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.AdvertService;
import com.centerm.cpay.coms.service.DeveloperService;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.service.LoginService;
import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.service.TerminalMonitorService;
import com.centerm.cpay.coms.service.UserService;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.coms.util.SessionInfo;
import com.centerm.cpay.flow.service.FlowInfService;
import com.centerm.cpay.flow.service.OrderExceptionService;
import com.centerm.cpay.security.des.DesUtils;

@Controller
@RequestMapping("/login")
public class LoginController {
	@Autowired
	private LoginService loginService;
	@Autowired
	private SmsService smsService;
	@Autowired
	private UserService userService;
	@Autowired
	private ApkInfoService apkInfoService;
	@Autowired
	private AdvertService advertService;
	@Autowired
	private InstitutionService institutionService;
	@Autowired
	private SysConfigureService sysConfigureService;

	Logger logger = LoggerFactory.getLogger(LoginController.class);

	public ResultMsg judge(User user, HttpSession session, HttpServletRequest request) throws UnknownHostException {
		User retUser = user;
		if (user == null) {
			logger.debug("用户名和密码为空");
			return ResultMsg.build(1, "The user name and password are empty", retUser);
		}

		if (user.getUsername() == null) {
			logger.debug("用户名为空");
			return ResultMsg.build(2, "Username empty", retUser);
		}
		if (user.getPassword() == null) {
			logger.debug("密码为空");
			return ResultMsg.build(3, "Password is empty", retUser);
		}
		User result = loginService.login(user);
		if (result == null) {
			logger.debug("用户名错误");
			return ResultMsg.build(4, "error incorrect username or password", retUser);
		}
		if (!result.getPassword().equalsIgnoreCase(user.getPassword())) {
			User olduser = loginService.login(user);
			Integer loginNum = olduser.getLoginNum();
			
			if (loginNum == null || loginNum == 0) {
				loginNum = 2;
				olduser.setLoginNum(loginNum);
				userService.updateByUser(olduser);
			} else {
				loginNum++;
				olduser.setLoginNum(loginNum);
				userService.updateByUser(olduser);
			}
			retUser.setLoginNum(loginNum);
			logger.debug("ERROR Incorrect username or password");
			return ResultMsg.build(4, "ERROR Incorrect username or password", retUser);
		}
		return ResultMsg.build(200, "success", result);
	}

	@RequestMapping(value = "/judgePwd", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg judgePwd() {
		User loginUser = FrontEndHelper.getLoginUser();
		Integer id = loginUser.getId();
		User user = userService.getUser(id);
		if(user.getPassword().equals(PropertyUtil.getValue("user.default.password"))){
			return ResultMsg.build(0,"Please change the default password");
		}
		if((System.currentTimeMillis()-user.getLastUpPwdTime().getTime())/(24*60*60*1000) >= 90){
			return ResultMsg.build(1,"The password has expired. Please change the new password");
		}
		return ResultMsg.build(2,"Check success");
	}
	@RequestMapping(value = "/judgeCode", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg judgeCode(String code, String email, HttpSession session) {
		String authCode = (String) session.getAttribute(email);
		if (authCode == null) {
			logger.debug("该邮箱未发送验证码");
			return ResultMsg.build(500, "Verification code is not sent by this mailbox", null);
		}
		if (!authCode.equalsIgnoreCase(code)) {
			logger.debug("验证码错误");
			return ResultMsg.build(500, "Verification code error", null);
		}
		// String pwd = IDUtils.getRandNum(4);
		User user = new User();
		user.setEmail(email);
		User result = userService.getUserByCondition(user);
		if (result == null) {
			logger.debug("该用户不是系统用户");
			return ResultMsg.build(500, "ERROR Incorrect username or password", null);
		}
		logger.debug("帐号密码验证通过");
		return ResultMsg.build(3, "Account password verified", null);
	}

	@RequestMapping(value = "/resetPwd", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg resetPwd(User user, HttpSession session) {
		User user2 = new User();
		String code = String.valueOf(user.getEmailCode());
		String authVailCode = (String) session.getAttribute(Constants.RANDOM_CODE_PWD);
		if (CtUtils.isEmpty(code) || !code.equals(String.valueOf(user.getValidateCode()))) {
			return ResultMsg.build(500, "Verification code error, please re - enter", null);
		}
		/********此处先不定义临时变量，用userName替代存放图片验证码*******/
		if (CtUtils.isEmpty(authVailCode) || !authVailCode.equals(String.valueOf(user.getTelephone()))) {
			return ResultMsg.build(500, "Verification Code Error,please re - enter", null);
		}
		user2.setEmail(user.getEmail());
		User result = userService.getUserByCondition(user2);
		result.setPassword(MD5Util.MD5(user.getPassword()));
		result.setLoginNum(0);
		userService.updateBySelective(result);
		logger.debug("密码修改成功");
		return ResultMsg.build(3, "Password changed successfully", null);
	}

	@RequestMapping(value = "/getLoginNum", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg getLoginNum(String userName, HttpServletRequest request) {
		User user = new User();
		if (user == null) {
			logger.debug("获取登录次数成功");
			return ResultMsg.build(3, "Gets the number of successful logins", 0);
		}
		user.setUsername(userName);
		int loginNum = 0;
		if (loginService.login(user) != null) {
			loginNum = loginService.login(user).getLoginNum();
		} else {
			return ResultMsg.build(1, "User does not exist", loginNum);
		}

		logger.debug("获取登录次数成功");
		return ResultMsg.build(3, "Gets the number of successful logins", loginNum);
	}

	@RequestMapping(value = "/login", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg login(User user, String smsCode, String vailCode, HttpSession session, HttpServletRequest request)
			throws Exception {
		String loginCountNum = null;// 登录错误超过一次，loginCountNum不为空，需要验证码
		String ip = null;
		int loginNum = 0;
		Cookie[] cookie = request.getCookies();
		for (int i = 0; i < cookie.length; i++) {
			Cookie cook = cookie[i];
			if (cook.getName().equalsIgnoreCase("loginCountNum")) {
				loginCountNum = cook.getValue().toString();
			}
			if (cook.getName().equalsIgnoreCase("loginIp")) {
				ip = cook.getValue().toString();
			}
		}
		if(CtUtils.isEmpty(ip)){
			ip = "0.0.0.0";
		}
		if (loginCountNum != null) {// 如果不为空，需要做图片验证
			if (vailCode == null) {
				logger.debug("Verification code cannot be empty");
				return ResultMsg.build(1, "Verification code cannot be empty", loginNum);
			}
			String authVailCode = (String) session.getAttribute(Constants.RANDOM_CODE);
			if (!vailCode.equals(authVailCode)) {
				logger.debug("Verification Code Error");
				return ResultMsg.build(2, "Verification Code Error", loginNum);
			}
		}
		if (user == null) {
			logger.debug("Username or password cannot be empty");
			return ResultMsg.build(3, "Username or password cannot be empty", null);
		}
		if (user.getUsername() == null) {
			logger.debug("Username cannot be empty");
			return ResultMsg.build(4, "Username cannot be empty", null);
		}
		if (user.getPassword() == null) {
			logger.debug("Password cannot be empty");
			return ResultMsg.build(5, "Password cannot be empty", null);
		}
		ResultMsg resultMsg = judge(user, session, request);// 校验用户名和密码，以及ip是否切换
		User result = new User();
		result = (User) resultMsg.getData();
		loginNum = result.getLoginNum();
		
		if (result.getLoginNum() > 10 && result.getLoginNum() <= 15) {
			return ResultMsg.build(6, "Too many failed attempts would disable the account");
		}
		if (result.getLoginNum() > 15) {
			User updateUser = new User();
			updateUser.setId(result.getId());
			updateUser.setStatus((byte)0);
			userService.updateBySelective(updateUser);
			return ResultMsg.build(6, "Too many failed attempts, the account has been disabled");
		}
		if (!resultMsg.getStatus().equals(200)) {
			return ResultMsg.build(resultMsg.getStatus(), resultMsg.getMsg(), loginNum);
		}
//		SysConfigure SysConfigure = sysConfigureService.selectByKey("loginNumValidate");
//		int loginNumSysCon = 10;
//		if (!CtUtils.isEmpty(SysConfigure)) {
//			loginNumSysCon = Integer.parseInt(SysConfigure.getValue());
//		}
//		if (result.getLoginNum() > loginNumSysCon) {
//			User updateUser = new User();
//			updateUser.setId(result.getId());
//			updateUser.setStatus((byte)0);
//			userService.updateBySelective(updateUser);
//			return ResultMsg.build(6, "Too many login errors, the account has been disabled");
//		}
		// String ip = getIpAddress(request);

		if (CtUtils.isEmpty(result)) {
			logger.debug("+++++++++++++++ERROR Incorrect username or password");
			return ResultMsg.build(6, "ERROR Incorrect username or password", loginNum);
		}
		if (result.getStatus() == 0) {
			logger.debug("This user is disabled. Please contact the administrator to activate");
			return ResultMsg.build(6, "This user is disabled. Please contact the admin to activate", loginNum);
		}
		if (!ip.equals(result.getLoginIp())) {
			User updateUser = new User();
			updateUser.setId(result.getId());
			updateUser.setLoginIp(ip);
			userService.updateBySelective(updateUser);
		}

		SessionInfo sessionInfo = new SessionInfo();
		User userInfo = new User();
		userInfo.setCodeNum(result.getCodeNum());
		userInfo.setUsername(result.getUsername());
		userInfo.setInsName(result.getInsName());
		userInfo.setInsId(result.getInsId());
		userInfo.setId(result.getId());
		userInfo.setIndexType(result.getIndexType());
		sessionInfo.setUser(result);
		sessionInfo.setDefaultPwd(true);
		String randomKey = DesUtils.getRandomKey(32);
		sessionInfo.setRandomKey(randomKey);
		session.setAttribute(Constants.SESSION_NAME, sessionInfo);
		Map<String, Object> data = new HashMap<>();
		data.put("user", userInfo);
		data.put("randomKey", randomKey);
		User olduser = loginService.login(user);
		olduser.setLoginNum(0);
		userService.updateByUser(olduser);
		logger.debug("登录成功");
		return ResultMsg.build(200, "login success", data);
	}

	public boolean insEnabled(Integer insId) {
		Institution ins = institutionService.selectByInsId(insId);
		if (ins == null || ins.getEnabled().equals(0)) {
			return false;
		}
		return true;
	}

	@RequestMapping(value = "/logout", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg logout() {
		HttpSession session = FrontEndHelper.getSession();
		session.invalidate();
		logger.debug("退出成功");
		return ResultMsg.build(200, "Exit the success", null);
	}

	@RequestMapping(value = "/smsVerifylogin", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg smsVerifylogin(String authCode, HttpSession session) throws Exception {
		/*
		 * int codeType = AuthCodeService.MERCHANT_PHONE_BIND; User user =
		 * (User) session.getAttribute("session-bean"); String phoneNum =
		 * user.getTelephone(); ResultMsg resultMsg =
		 * authCodeService.validAuthCode(phoneNum, codeType, authCode, 1);
		 */
		logger.debug("登录成功");
		return ResultMsg.build(200, "Login successful", null);

	}

	@RequestMapping(value = "/reSend", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg reSend(User user, HttpSession session) {
		if (user == null) {
			logger.debug("User name password cannot be empty");
			return ResultMsg.build(1, "User name password cannot be empty", null);
		}
		if (user.getUsername() == null) {
			logger.debug("User name cannot be empty");
			return ResultMsg.build(2, "User name cannot be empty", null);
		}
		if (user.getPassword() == null) {
			logger.debug("password cannot be empty");
			return ResultMsg.build(3, "password cannot be empty", null);
		}
		User result = loginService.login(user);

		if (result.getTelephone() == null) {
			logger.debug("手机号码不存在");
			return ResultMsg.build(4, "The phone number doesn't exist", null);
		}
		if (result != null) {
			SysConfigure SysConfigure = sysConfigureService.selectByKey("sendCodeNumDay");
			int sendCodeNum = 100;
			if (CtUtils.isEmpty(SysConfigure)) {

			} else {
				sendCodeNum = Integer.parseInt(SysConfigure.getValue());
			}
			if (result.getCodeNum() > sendCodeNum) {
				logger.debug("该手机号当天获取的短信数量超过限定");
				return ResultMsg.build(4, "The number exceeded a limit on the number of messages it could receive that day", null);
			}
		}
		String ipChangeValue = sysConfigureService.queryIpChange("ipChangeValidate");
		if (CtUtils.isEmpty(ipChangeValue) || ipChangeValue.equals("0")) {

		} else {
			if (!result.getPassword().equalsIgnoreCase(user.getPassword())) {
				logger.debug("验证码发送失败");
				return ResultMsg.build(4, "Verification code sending failed", null);
			}
		}

		String code = IDUtils.getRandNum(4);
		String str = "";
		try {
			str = smsService.sendSms(result.getTelephone(), "运维管理平台系统通知：您本次登录的验证码为：" + code);

		} catch (Exception e) {
			logger.info("异常" + e);
			return ResultMsg.build(4, "Verification code sending failed", null);
		}
		session.setAttribute(result.getTelephone(), code);
		if (str.equals("2")) {
			logger.debug("短信发送失败，请稍后重试");
			return ResultMsg.build(500, "短信发送失败，请稍后重试", null);
		}
		if (str.equals("3")) {
			logger.debug("当前号码短信发送条数已达当天上限");
			return ResultMsg.build(500, "当前号码短信发送条数已达当天上限", null);
		}
		result.setCodeNum(result.getCodeNum() + 1);
		userService.updateByUser(result);
		logger.debug("重新发送成功");
		return ResultMsg.build(3, "重新发送成功", null);
	}

	@RequestMapping(value = "/sendCode", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg sendCode(String email, HttpSession session) {
		if (email == null) {
			logger.debug("验证码发送失败，邮箱为空");
			return ResultMsg.build(1, "验证码发送失败，邮箱为空", null);
		}
		User user = new User();
		user.setEmail(email);
		User capyUser = loginService.sendCode(user);
		if (capyUser == null) {
			logger.debug("验证码发送失败，该邮箱不是系统用户");
			// return ResultMsg.build(2, "验证码发送失败，该手机号不是系统用户", null);
			return ResultMsg.build(3, "验证码发送失败", null);
		}
		
		String code = IDUtils.getRandNum(4);
		//发送邮箱验证码
		ApplicationContext ctx = new ClassPathXmlApplicationContext("classpath:spring/applicationContext.xml");
		final JavaMailSender ms = (JavaMailSender) ctx.getBean("mailSender");
		final SimpleMailMessage sMailMessage = (SimpleMailMessage) ctx.getBean("mailMessage");
		final String fromMail = sMailMessage.getFrom(); // 发送邮件
		//邮件内容
		final String msg ="尊敬的Cpay平台用户，您本次修改密码操作的验证码为："+code+"，请及时处理！";
        final MimeMessagePreparator preparator = new MimeMessagePreparator() {  
            public void prepare(MimeMessage mimeMessage) throws Exception {  
                MimeMessageHelper message = new MimeMessageHelper(mimeMessage,true,"UTF-8");  
                message.setSubject("【升腾资讯】邮箱验证码");  
                message.setTo(email);  
                message.setFrom(fromMail);  
                message.setText("text/html;charset=utf-8",msg); 
            }  
        };
        try {
			Thread thread = new Thread() {// 异步发送邮件
				public void run() {
					ms.send(preparator);
				}
			};
			thread.start();
		} catch (MailException e) {
			logger.info("异常：" + e + "\n");
			e.printStackTrace();

		}
		//String result = smsService.sendSms(user.getTelephone(), "运维管理平台系统通知：您本次重置密码的验证码为：" + code);//发送手机验证码
		session.setAttribute(email, code);
//		if (result.equals("2")) {
//			logger.debug("验证码发送失败，请稍后重试");
//			return ResultMsg.build(500, "验证码发送失败，请稍后重试", null);
//		}
//		if (result.equals("3")) {
//			logger.debug("当前号码短信发送条数已达当天上限");
//			return ResultMsg.build(500, "当前号码短信发送条数已达当天上限", null);
//		}
		capyUser.setCodeNum(capyUser.getCodeNum() + 1);
		userService.updateByUser(capyUser);
		logger.debug("验证码发送成功");
		return ResultMsg.build(3, "验证码发送成功", null);
	}

	@RequestMapping(value = "/getMatters", method = { RequestMethod.POST })
	@ResponseBody
	public Map getMatters() {
		User user = FrontEndHelper.getLoginUser();
		if (user == null) {
			return null;
		}
		Integer insId = user.getInsId();
		Integer apkAuditCount = apkInfoService.getApkAuditCount(insId);
		Integer apkPubCount = apkInfoService.getApkPubCount(insId);
		Map map = new HashMap<String, Integer>();
		map.put("apkAuditCount", apkAuditCount);
		map.put("apkPubCount", apkPubCount);
		return map;

	}

	@RequestMapping(value = "/changeValidateCode", method = { RequestMethod.GET })
	public void changeValidateCode(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("image/jpeg");// 设置相应类型,告诉浏览器输出的内容为图片
		response.setHeader("Pragma", "No-cache");// 设置响应头信息，告诉浏览器不要缓存此内容
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expire", 0);
		RandomValidateCode randomValidateCode = new RandomValidateCode();
		try {
			randomValidateCode.getRandcode(request, response,"login");// 输出图片方法
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	@RequestMapping(value = "/changeValidateCodeForPwd", method = { RequestMethod.GET })
	public void changeValidateCodeForPwd(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("image/jpeg");// 设置相应类型,告诉浏览器输出的内容为图片
		response.setHeader("Pragma", "No-cache");// 设置响应头信息，告诉浏览器不要缓存此内容
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expire", 0);
		RandomValidateCode randomValidateCode = new RandomValidateCode();
		try {
			randomValidateCode.getRandcode(request, response,"resetPwd");// 输出图片方法
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	

	public User changeLoginNum(User user) {
		User olduser = loginService.login(user);
		Integer loginNum = olduser.getLoginNum();
		if (loginNum == null) {
			loginNum = 1;
			olduser.setLoginNum(loginNum);
			userService.updateByUser(olduser);
		} else {
			loginNum++;
			olduser.setLoginNum(loginNum);
			userService.updateByUser(olduser);
		}
		return olduser;
	}
}