package com.centerm.cpay.coms.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert;
import com.centerm.cpay.coms.dao.pojo.OqcChcekHistory;
import com.centerm.cpay.coms.service.OqcChcekHistoryService;

@Controller
@RequestMapping("/oqcChcekHistory")
public class OqcChcekHistoryController {
	@Autowired
	private OqcChcekHistoryService oqcChcekHistoryService;
	Logger logger = LoggerFactory.getLogger(OqcChcekHistoryController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getOqcChcekHistoryList(OqcChcekHistory oqcChcekHistory, @RequestParam Integer page,
			@RequestParam Integer rows) {
		EUDataGridResult result = oqcChcekHistoryService.getOqcChcekHistoryList(oqcChcekHistory, page, rows);
		return result;
	}
	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg oqcChcekHistory(@PathVariable Integer id) {
		return oqcChcekHistoryService.deleteByPrimaryKey(id);
	}
	@RequestMapping(value = "/delPL", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg delPL(@RequestParam String ids) {
		List<String> idsList = new ArrayList<String>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return oqcChcekHistoryService.deleteByIds(idsList);
	}
}
