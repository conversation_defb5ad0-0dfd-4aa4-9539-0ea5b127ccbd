
package com.centerm.cpay.coms.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.flow.dao.pojo.ComsFlowPool;
import com.centerm.cpay.flow.service.FlowPoolService;

/**
 * 流量池管理
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/flowPool")
public class FlowPoolController {
	@Autowired
	private FlowPoolService flowPoolService;
	Logger logger = LoggerFactory.getLogger(FlowPoolController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getFlowInfList(ComsFlowPool entity, @RequestParam Integer page, @RequestParam Integer rows) {
		EUDataGridResult result = flowPoolService.getGridList(entity, page, rows);
		return result;
	}
	
	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsFlowPool getFlowInfList(@PathVariable Integer id) {
		ComsFlowPool result = flowPoolService.selectByPrimaryKey(id);
		return result;
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(@PathVariable Integer id) {
		return flowPoolService.deleteById(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByIds(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return flowPoolService.deleteByIds(idsList);
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody ComsFlowPool entity) {
		return flowPoolService.updateByEntity(entity);
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(@RequestBody ComsFlowPool entity) {
		return flowPoolService.insertEntity(entity);
	}
}
