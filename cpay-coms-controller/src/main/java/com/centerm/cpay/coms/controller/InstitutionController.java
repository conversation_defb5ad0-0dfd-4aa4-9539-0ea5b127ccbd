package com.centerm.cpay.coms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.service.TerminalGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.mapper.TerminalCertMapper;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.util.ApkUtil;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FrontEndHelper;

@Controller
@RequestMapping("/institution")
public class InstitutionController {
	@Autowired
	private InstitutionService InstitutionService;
	@Autowired
	private TerminalGroupService terminalGroupService;

	String filesavepath = FilePathUtil.getrelativePath("appsystempath");
	String fileheadpath =  ConfigInfo.HEADER_PATH;

	@RequestMapping(value = "/tree", method = RequestMethod.GET)
	@ResponseBody
	public List<Institution> getTree(HttpServletRequest request) {
		String parentId = request.getParameter("pId");
		User user = FrontEndHelper.getLoginUser();
		if (null == parentId || "".equals(parentId)) {
			parentId = String.valueOf(user.getInsId());
		}
		return InstitutionService.getInstitutionTree(parentId);
	}
	@RequestMapping(value = "/limitTree", method = RequestMethod.GET)
	@ResponseBody
	public List<Institution> getLimitTree(HttpServletRequest request) {
		String parentId = request.getParameter("pId");
		String groupId = request.getParameter("groupId");
		Map params = new HashMap(2);
		User user = FrontEndHelper.getLoginUser();
		if (null == parentId || "".equals(parentId)) {
			parentId = String.valueOf(user.getInsId());
		}
		params.put("pId",parentId);
		if(!CtUtils.isEmpty(groupId)){
			TerminalGroup terminalGroup = terminalGroupService.selectByPrimaryKey(Integer.valueOf(groupId));
			params.put("insId",terminalGroup.getInsId());
		}

		return InstitutionService.getInstitutionLimitTree(params);
	}
	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getOperInfList(Institution Institution, @RequestParam Integer page,
			@RequestParam Integer rows) {
		EUDataGridResult result = InstitutionService.InstitutionList(Institution, page, rows);
		return result;
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public Institution getInstitution(@PathVariable Integer id) {
		return InstitutionService.selectByInsId(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg createInstitution(@RequestBody Institution Institution) {
		return InstitutionService.createInsInf(Institution);
	}

	@RequestMapping(value = "/update", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg updateInstitution(@RequestBody Institution Institution) {
		return InstitutionService.updateInstitution(Institution);
	}

	@RequestMapping(value = "/updateOrganizationElements", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg updateOrganizationElements(@RequestBody Institution Institution) {
		return InstitutionService.updateOrganizationElements(Institution);
	}

	@RequestMapping(value = "/getOrganizationElements/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg getOrganizationElements(@PathVariable Integer id) {
		Institution institution = InstitutionService.selectByInsId(id);
		List<String> elementsList = new ArrayList<String>();
		String elements = institution.getElements();
		System.out.println(elements);
		if (elements != null) {
			String[] elementsArray = elements.split(",");
			for (int i = 0; i < elementsArray.length; i++) {
				elementsList.add(elementsArray[i]);
			}
			return InstitutionService.getOrganizationElements(elementsList);
		}
		return ResultMsg.success();
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteInstitution(@PathVariable Integer id) {
		return InstitutionService.deleteById(id);
	}

	@RequestMapping(value = "/validate", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg validate(Institution Institution) {
		return InstitutionService.validate(Institution);
	}

	@RequestMapping(value = "/addDefaultMerchant", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg addDefaultMerchant(Institution Institution) {
		return InstitutionService.validate(Institution);
	}

}
