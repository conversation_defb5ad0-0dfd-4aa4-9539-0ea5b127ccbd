
package com.centerm.cpay.coms.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Iterator;
import java.util.UUID;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.coms.util.FilePathUtil;
/**
 * 文件操作
 * ClassName: FileController <br/> 
 * Function: TODO ADD FUNCTION. <br/> 
 * Reason: TODO ADD REASON(可选). <br/> 
 * date: 2016年7月8日 下午7:30:02 <br/> 
 * 
 * <AUTHOR> 
 * @version  
 *
 */
@Controller
@RequestMapping("/file")
public class FileController {
	Logger logger = LoggerFactory.getLogger(FileController.class);
	String filesavepath=ConfigInfo.getFilePath(ConfigInfo.LAUNCHER_PATH);
	String fileheadpath= ConfigInfo.HEADER_PATH;
	@RequestMapping(value = "/uploadFile", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadFile(HttpServletRequest request,HttpServletResponse response) throws IllegalStateException, IOException {
		//创建一个通用的多部分解析器  
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());  
        //判断 request 是否有文件上传,即多部分请求  
        String path="" ;
        if(multipartResolver.isMultipart(request)){  
            //转换成多部分request    
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest)request;  
            //取得request中的所有文件名  
            Iterator<String> iter = multiRequest.getFileNames();  
            while(iter.hasNext()){  
                //记录上传过程起始时的时间，用来计算上传时间  
                int pre = (int) System.currentTimeMillis();  
                //取得上传文件  
                MultipartFile file = multiRequest.getFile(iter.next());  
                if(file != null){  
                    //取得当前上传文件的文件名称  
                    String myFileName = file.getOriginalFilename();  
                    //如果名称不为“”,说明该文件存在，否则说明该文件不存在  
                    if(myFileName.trim() !=""){  
                        //System.out.println(myFileName);  
                        //重命名上传后的文件名  
                        String fileName =UUID.randomUUID() + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));  
                        //定义上传路径  
                        path = fileheadpath+filesavepath + fileName;
                        //路径和原文件名
                        File localFile = new File(path);  
                        file.transferTo(localFile);
                        path=filesavepath + fileName+","+file.getOriginalFilename();
                    }  
                }  
                //记录上传该文件后的时间  
                // int finaltime = (int) System.currentTimeMillis();  
                //System.out.println(finaltime - pre);  
            }  
        }
        if(path.trim() !=""){
        	return ResultMsg.success(path);
        }
		return ResultMsg.fail();
	}
	@RequestMapping(value = "/uploadPic", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadPic(HttpServletRequest request,HttpServletResponse response) throws IllegalStateException, IOException {
		//创建一个通用的多部分解析器  
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());  
        //判断 request 是否有文件上传,即多部分请求  
        String path="" ;
        if(multipartResolver.isMultipart(request)){  
            //转换成多部分request    
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest)request;  
            //取得request中的所有文件名  
            Iterator<String> iter = multiRequest.getFileNames();  
            while(iter.hasNext()){  
                //记录上传过程起始时的时间，用来计算上传时间  
                int pre = (int) System.currentTimeMillis();  
                //取得上传文件  
                MultipartFile file = multiRequest.getFile(iter.next());  
                if(file != null){  
                    //取得当前上传文件的文件名称  
                    String myFileName = file.getOriginalFilename();  
                    //如果名称不为“”,说明该文件存在，否则说明该文件不存在  
                    if(myFileName.trim() !=""){  
                        //System.out.println(myFileName);  
                        //重命名上传后的文件名  
                        String fileName =  UUID.randomUUID() + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));  
                        //定义上传路径  
                        path = fileheadpath+filesavepath + fileName;
                        //path = propertiesService.filesavepath + fileName;
                        //路径和原文件名
                        File localFile = new File(path);  
                        file.transferTo(localFile);
                        path=filesavepath + fileName+","+file.getOriginalFilename();
                    }  
                }  
                //记录上传该文件后的时间  
                // int finaltime = (int) System.currentTimeMillis();  
                //System.out.println(finaltime - pre);  
            }  
        }
        if(path.trim() !=""){
        	return ResultMsg.success(path);
        }
		return ResultMsg.fail();
	}
	/**
	 * 预览服务器图片
	 */
	@RequestMapping(value = "/fileView", method = { RequestMethod.GET })
	@ResponseBody
	public void viewPic(HttpServletRequest request,HttpServletResponse response) throws IOException{
        response.reset();  
        String imagePath=request.getParameter("imagePath");
        ServletOutputStream output = response.getOutputStream();
        response.setContentType("image/jpeg;charset=GB2312");
        InputStream imageIn = new FileInputStream(new File(fileheadpath+imagePath));  
        BufferedInputStream bis = new BufferedInputStream(imageIn); 
        BufferedOutputStream bos = new BufferedOutputStream(output);
        byte data[] = new byte[4096];
        int size = 0;  
        size = bis.read(data);  
        while (size != -1) {  
            bos.write(data, 0, size);  
            size = bis.read(data);  
        }  
        bis.close();  
        bos.flush();
        bos.close(); 
        output.close();  
	}
	/**
	 * 文件下载
	 */
	@RequestMapping(value = "/fileDownload", method = { RequestMethod.GET })
	@ResponseBody
	public void fileDownload(HttpServletRequest request,HttpServletResponse response) throws IOException{
		String filename = request.getParameter("filename");  
        response.setHeader("Content-Disposition", "attachment;filename="+filename);  
        response.setContentType("application/x-download");
        String fullFileName = fileheadpath+filesavepath+filename;  
        InputStream in = new FileInputStream(fullFileName);  
        OutputStream out = response.getOutputStream();  
        int b;  
        while((b=in.read())!= -1)  
        {  
            out.write(b);  
        }  
        in.close();  
        out.close();  
	}
}
