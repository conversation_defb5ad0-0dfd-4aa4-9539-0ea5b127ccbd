 package com.centerm.cpay.coms.controller;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;

import com.centerm.cpay.coms.apkstore.dao.pojo.ApkCountInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTypeInfo;
import com.centerm.cpay.coms.apkstore.dao.pojo.StoreApkInfo;
import com.centerm.cpay.coms.apkstore.service.ApkAuditInfoService;
import com.centerm.cpay.coms.apkstore.service.ApkInfoService;
import com.centerm.cpay.coms.apkstore.service.ApkTemporaryService;
import com.centerm.cpay.coms.apkstore.service.StoreApkService;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.FileInfoService;
import com.centerm.cpay.coms.util.ApkUtil;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FrontEndHelper;

@Controller
@RequestMapping("/apkInfo")
public class ApkInfoController {
	@Autowired
	private ApkInfoService apkInfoService;
	@Autowired
	private StoreApkService storeApkService; 
	@Autowired
	private ApkTemporaryService apkTemporaryService;
	@Autowired 
	private ApkAuditInfoService apkAuditInfoService;
	@Autowired
	private FileInfoService fileInfoService;
	@Autowired
	private SysConfigureService configureService;
	String ngsPicPath = "";

	String appstorepath = FilePathUtil.getrelativePath("appstorepath");
	String fileheadpath =  ConfigInfo.HEADER_PATH;
	String temDir = FilePathUtil.getTempDir();
	Logger logger = LoggerFactory.getLogger(StoreApkController.class);
	
	@RequestMapping(value = "/getPubApk", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getPubApk(HttpServletRequest request,StoreApkInfo apkInfo,@RequestParam Integer page, @RequestParam Integer rows){
		User user = FrontEndHelper.getLoginUser();
		Integer insId = user.getInsId();
		if(apkInfo.getInsId()==null){
		   apkInfo.setInsId(insId);
		}
		if(apkInfo.getNgsPicPath()==null){
			ngsPicPath = configureService.queryValueByKey("download_url_intranet");
			apkInfo.setNgsPicPath(ngsPicPath);
	    }
		return apkInfoService.getPubApk(apkInfo,page,rows);
		
	}
	@RequestMapping(value = "/getPubApkList", method = { RequestMethod.GET })
	@ResponseBody
	public List<StoreApkInfo> getPubApkList(HttpServletRequest request,StoreApkInfo apkInfo){
		User user = FrontEndHelper.getLoginUser();
		Integer insId = user.getInsId();
		if(apkInfo.getInsId()==null){
			apkInfo.setInsId(insId);
		}
		if(apkInfo.getNgsPicPath()==null){
			ngsPicPath = configureService.queryValueByKey("download_url_intranet");
			apkInfo.setNgsPicPath(ngsPicPath);
		}
		return apkInfoService.getPubApkList(apkInfo);
	}
	@RequestMapping(value = "/getApkInfo/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public StoreApkInfo getApkInfo(@PathVariable Integer id){
		return apkInfoService.getApkInfo(id);
	}
	@RequestMapping(value = "/getPubApkForApkRange", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getPubApkForApkRange(HttpServletRequest request,StoreApkInfo apkInfo,@RequestParam Integer page, @RequestParam Integer rows){
		User user = FrontEndHelper.getLoginUser();
		Integer insId = user.getInsId();
		if(apkInfo.getInsId()==null){
		   apkInfo.setInsId(insId);
		}
		if(apkInfo.getNgsPicPath()==null){
			ngsPicPath = configureService.queryValueByKey("download_url_intranet");
			apkInfo.setNgsPicPath(ngsPicPath);
	    }
		return apkInfoService.getPubApkForApkRange(apkInfo,page,rows);
		
	}
	@RequestMapping(value = "/pubApk", method = {RequestMethod.POST})
	@ResponseBody
	public ResultMsg pubApk(@RequestBody StoreApkInfo apkInfo) {
		String  module = "Application Management";
		String  action = "Application Release";
		String  opDesc = "Application Releasing";
		User user = FrontEndHelper.getLoginUser();
		Map map = new HashMap();
		if (apkInfo == null) {
			return null;
		}
		/**
		 * 权限重新分配过程，分配不同机构
		 */
		
		
		map.put("userName", user.getUsername());
		map.put("module", module);
		map.put("action", action);
		map.put("opDesc", opDesc);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		map.put("opTime", sdf.format(new Date()));
		storeApkService.addLog(map);
		return storeApkService.pubApk(apkInfo);
	}
	
	@RequestMapping(value = "/afreshPubApk", method = {RequestMethod.POST})
	@ResponseBody
	public ResultMsg afreshPubApk(@RequestBody StoreApkInfo apkInfo) {
		String  module = "Application Management";
		String  action = "Application Release";
		String  opDesc = "Application Releasing";
		User user = FrontEndHelper.getLoginUser();
		Map map = new HashMap();
		if (apkInfo == null) {
			return null;
		}
		
		apkInfo.setRecSt("3");
		apkInfo.setUpFlag("1");
		
		map.put("userName", user.getUsername());
		map.put("module", module);
		map.put("action", action);
		map.put("opDesc", opDesc);
		map.put("opTime", CtUtils.getCurrentTime());
		storeApkService.addLog(map);
		return storeApkService.afreshPubApk(apkInfo);
	}
	
	@RequestMapping(value = "/getApkTypePage", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getApkTypePage(ApkTypeInfo apkTypeInfo,@RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		if(user==null){
			return null;
		}
		if(apkTypeInfo.getInsId()==null){
			Integer insId = user.getInsId();
			apkTypeInfo.setInsId(insId);
		}


		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		EUDataGridResult result = apkInfoService.getApkTypePage(apkTypeInfo, page, rows);
		List<ApkTypeInfo> list = (List<ApkTypeInfo>) result.getRows();
		Iterator<ApkTypeInfo> iter = list.iterator();
		while(iter.hasNext()){

			iter.next().setNgsPicPath(ngsPicPath);
		}
		return result;
	}
	
	
	//查询应用类型
	@RequestMapping(value = "/getApkTypeList", method = { RequestMethod.GET })
	@ResponseBody
	public List getApkTypeList() {
		
		List<ApkTypeInfo> list = apkInfoService.getApkTypeList() ;
		
		return list;
	}


	@RequestMapping(value = "/addApkType", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg addApkType(@RequestBody ApkTypeInfo apkTypeInfo) {
		logger.info("iconPath:"+apkTypeInfo.getIconPath());
		String  resuletMsg1 = "类型编号或名称已存在。";
		User user = FrontEndHelper.getLoginUser();
		Integer insId = user.getInsId();
		if(apkTypeInfo.getIconPath().indexOf("..") > 0){
			return new ResultMsg(1, "Image path error", null);
		}
		apkTypeInfo.setInsId(insId);
		boolean flag = apkInfoService.isExist(apkTypeInfo);
		if(flag){
			return new ResultMsg(1, resuletMsg1, null);
		}
		FileInfo fileInfo = new FileInfo();
		try {
			if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
				fileInfo = uploadIcon3(temDir +apkTypeInfo.getIconPath(), fileheadpath + appstorepath);
				fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
			}else{
				fileInfo = ApkFastFdsUtil.uploadFileByFastFds(new File(temDir+apkTypeInfo.getIconPath()));
			}
		} catch (Exception e) {
			// TODO
			e.printStackTrace();
		}
		fileInfoService.AddFile(fileInfo);
		apkTypeInfo.setIconId(fileInfo.getUuid());
		return  apkInfoService.addApkType(apkTypeInfo);
	}

	public FileInfo uploadIcon3(String iconPath, String destPath) throws Exception {
		File iconFile = new File(iconPath);
		String iconName = iconPath.substring(iconPath.lastIndexOf("/") + 1);
		FileInfo fileInf = ApkUtil.uploadFile3(iconFile, iconName, destPath);
		return fileInf;

	}
	
	@RequestMapping(value = "/getApkTypeById", method = { RequestMethod.GET })
	@ResponseBody 
	public ResultMsg getApkTypeById(Integer id) {
		String  resuletMsg1 = "Query Success";
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		ApkTypeInfo apkTypeInfo = apkInfoService.getApkTypeById(id);
		apkTypeInfo.setNgsPicPath(ngsPicPath);
	    return new ResultMsg(1, resuletMsg1, apkTypeInfo);
	}
	@RequestMapping(value = "/updateApkType", method = { RequestMethod.PATCH })
	@ResponseBody  
	public ResultMsg updateApkType(@RequestBody ApkTypeInfo apkTypeInfo) {
		String  resuletMsg1 = "Query Success";
		
		ApkTypeInfo apkType = apkInfoService.getApkTypeById(apkTypeInfo.getId());
		if(apkTypeInfo.getIconPath().equals(apkType.getIconPath())){
			apkTypeInfo.setIconId(null);
		}else{
			FileInfo fileInfo = new FileInfo();
			try {
				if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
					fileInfo = uploadIcon3(temDir +apkTypeInfo.getIconPath(), fileheadpath + appstorepath);
					fileInfo.setFileSavePath(appstorepath + fileInfo.getFileSavePath());
				}else{
					fileInfo = ApkFastFdsUtil.uploadFileByFastFds(new File(temDir+apkTypeInfo.getIconPath()));
				}
			} catch (Exception e) {
				// TODO
				e.printStackTrace();
			}
			fileInfoService.AddFile(fileInfo);
			apkTypeInfo.setIconId(fileInfo.getUuid());
		}
		apkInfoService.updateApkType(apkTypeInfo);
	    return new ResultMsg(1, resuletMsg1, apkTypeInfo);
	}
	
	@RequestMapping(value = "/recycleApkInfoById", method = { RequestMethod.PATCH })
	@ResponseBody 
	public ResultMsg recycleApkInfoById(@RequestParam String ids,@RequestParam String recSts) {
		ResultMsg result = null;
		String[] idsArray = ids.split(",");
		String[] recStsArray = recSts.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			Integer id = Integer.parseInt(idsArray[i]);
			String recSt = recStsArray[i];
			List <StoreApkInfo> list= apkInfoService.getApkByAppYlpackageId(id);
			if(recSt != null && !"null".equals(result)){
				if("3".equals(recSt) || "4".equals(recSt)){
					if(list.size()>0){
						return ResultMsg.build(ResultMsg.ERROR_CODE, "The current application is dependent by other applications and cannot be deleted");
					}
					StoreApkInfo storeApkInfo = apkInfoService.getStoreApkInfobyID(id);
					result = apkInfoService.reCycleApkInfoById(id);
					apkAuditInfoService.deleteByApkAuditInfo(storeApkInfo.getAppCode(), storeApkInfo.getAppVersion(), storeApkInfo.getUserId());
				}else{
					if(list.size()>0){
						return ResultMsg.build(ResultMsg.ERROR_CODE, "The current application is dependent by other applications and cannot be deleted");
					}
					ApkTemporary apkTemporary = apkTemporaryService.getApkTemporaryById(id);
					result = apkTemporaryService.reCycleApkTempById(id);
					apkAuditInfoService.deleteByApkAuditInfo(apkTemporary.getAppCode(), apkTemporary.getAppVersion(), apkTemporary.getUserId());
				}
			}else{
				result = ResultMsg.fail();
			}
		}
		
		return result;
	}
	
	@RequestMapping(value = "/deleteApkTypeById", method = { RequestMethod.DELETE })
	@ResponseBody 
	public ResultMsg deleteApkTypeById(@RequestParam Integer id) {
		String  resuletMsg1 = "The current category is in use and cannot be deleted";
		String  resuletMsg2 = "delete success";
		 int result = apkInfoService.isUsed(id);
		 if(result>0){
			 return new ResultMsg(1, resuletMsg1, null);
		}
		 
		apkInfoService.deleteApkTypeById(id);
		
	    return new ResultMsg(2, resuletMsg2, null);
	}
	
	@RequestMapping(value = "/deleteApkTypeByIds", method = { RequestMethod.DELETE })
	@ResponseBody 
	public ResultMsg deleteApkTypeByIds(@RequestParam String ids) {
		String  resuletMsg1 = "Some classifications are in use, deletion failed";
		String  resuletMsg2= "delete success";

		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			Integer id = Integer.parseInt(idsArray[i]);
			int result = apkInfoService.isUsed(id);
			if(result>0){
				 return new ResultMsg(1, resuletMsg1, null);
			}
		}
		for (int i = 0; i < idsArray.length; i++) {
			Integer id = Integer.parseInt(idsArray[i]);
			apkInfoService.deleteApkTypeById(id);
			
		}
	    return new ResultMsg(1, resuletMsg2, null);
	}
	
	@RequestMapping(value = "/getApkCount", method = { RequestMethod.GET })
	@ResponseBody 
	public ApkCountInfo getApkCount() {
		User user = FrontEndHelper.getLoginUser();
		return apkInfoService.getApkCount(user.getInsId());
	}
	
	@RequestMapping(value = "/getApkRecycleList", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getApkRecycleList(StoreApkInfo storeApkInfo,
			@RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");

		Integer insId = user.getInsId();
		if(storeApkInfo.getInsId() == null){
			storeApkInfo.setInsId(insId);
		}
		if (storeApkInfo.getNgsPicPath()==null){
			storeApkInfo.setNgsPicPath(ngsPicPath);
		}
		EUDataGridResult result = storeApkService.getApkRecycleList(storeApkInfo,page, rows);
		
		return result;
	}
	
	@RequestMapping(value = "/restoreApk", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg restoreApk(@RequestBody ApkTemporary apkTemp) {
		ResultMsg result = null;
		String recSt = apkTemp.getRecSt();
		Integer id = apkTemp.getId();
		if(recSt != null && !"null".equals(result)){
			if("3".equals(recSt) || "4".equals(recSt)){
				result = apkInfoService.restoreApkById(apkTemp);
			}else{
				result = apkTemporaryService.restoreApkTempById(id);
			}
		}else{
			result = ResultMsg.fail();
		}
		return result;
	}
	
	@RequestMapping(value = "/offLineApk", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg offLineApk(@RequestBody ApkTemporary apkTemp, @RequestParam String id) {
		String  module = "App Management";
		String  action = "App Release";
		String  opDesc = "The App is Booted";
		User user = FrontEndHelper.getLoginUser();
		Map map = new HashMap();
		map.put("userName", user.getUsername());
		map.put("module", module);
		map.put("action", action);
		map.put("opDesc", opDesc);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		map.put("opTime", sdf.format(new Date()));
		storeApkService.addLog(map);
	
		apkInfoService.offLineApk(apkTemp);
		return ResultMsg.success();
	}
	@RequestMapping(value = "/appYL", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg appYL(@RequestParam String appId, @RequestParam String appIdYl) {
		Map map = new HashMap();
		map.put("id", appId);
		map.put("appIdYl", appIdYl);
		apkInfoService.appYL(map);
		return ResultMsg.success();
	}
	@RequestMapping(value = "/calcelApkYL", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg calcelApkYL(@RequestParam String appId) {
		return apkInfoService.calcelApkYL(appId);
	}
	
	@RequestMapping(value = "/getTermTypeName", method = { RequestMethod.GET })
	@ResponseBody
	public List<StoreApkInfo> getTermTypeName(StoreApkInfo apkinfo){
		return apkInfoService.getTermTypeName(apkinfo);
	}

}