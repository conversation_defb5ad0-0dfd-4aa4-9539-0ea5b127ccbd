package com.centerm.cpay.coms.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.FileUtil;

import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.InformationAnnounce;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.FileInfoService;
import com.centerm.cpay.coms.service.InfoAnounceService;
import com.centerm.cpay.coms.util.ApkUtil;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FrontEndHelper;

@Controller
@RequestMapping("/infoannounce")
public class InfoAnnouceController {
	@Autowired
	private InfoAnounceService infoAnounceService;
	@Autowired
	private FileInfoService fileInfoService;
	@Autowired
	private SysConfigureService configureService;
	String ngsPicPath = "";

	String appstorepath = FilePathUtil.getrelativePath("appstorepath");
	String fileheadpath =  ConfigInfo.HEADER_PATH;
	String temDir = FilePathUtil.getTempDir();
	Logger logger = LoggerFactory.getLogger(StoreApkController.class);
	List<File> newFileForCutImgList = new ArrayList<>();// 临时文件存放应用截图
	List<String> pic_list = new ArrayList<String>();// 图片uuid

	@RequestMapping(value = "", method = RequestMethod.GET)
	@ResponseBody
	public EUDataGridResult getInfoList(InformationAnnounce infoannounce, @RequestParam int page,
			@RequestParam int rows) {
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(infoannounce.getInsId())) {
			infoannounce.setInsId(user.getInsId());
			infoannounce.setIsQuery(false);
		} else {
			infoannounce.setIsQuery(true);
		}

		return infoAnounceService.getinfoList(infoannounce, page, rows);
	}

	@RequestMapping(value = "/updatStatus/{id}", method = RequestMethod.GET)
	@ResponseBody
	public ResultMsg updatStatus(@PathVariable Integer id) {
		InformationAnnounce informationAnnounce = new InformationAnnounce();
		informationAnnounce.setId(id);
		informationAnnounce.setStatus(1);
		return infoAnounceService.updateStatus(informationAnnounce);
	}
	@RequestMapping(value = "/{id}", method = RequestMethod.GET)
	@ResponseBody
	public InformationAnnounce getInfoAnnounce(@PathVariable Integer id) {
		InformationAnnounce informationAnnounce = infoAnounceService.getInfoAnnounceById(id);
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		informationAnnounce.setNgsPicPath(ngsPicPath);
		return informationAnnounce;
	}

	@RequestMapping(value = "", method = RequestMethod.POST)
	@ResponseBody
	public ResultMsg insertInfoAnnounce(@RequestBody InformationAnnounce informationAnnounce) {
		// 保存图片到正式目录
		logger.debug("+++++++++++++++++++++++++++开始保存到正式目录");
		String resuletMsg1 = "Modify failure";
		FileInfo fileInfo =null;
		String newfileSavePath = "";
		if (informationAnnounce == null) {
			return new ResultMsg(1, resuletMsg1, null);
		}
		logger.debug("++++++++++++++++++++++++++++++++++++++++newFileForCutImgList长度:"+newFileForCutImgList.size());
		String[] picIdArray = {};
		List<String> picidList = new ArrayList<>();
		if (informationAnnounce.getId()!=null) {
			picIdArray = infoAnounceService.getInfoAnnounceById(informationAnnounce.getId()).getPicId().split(",");
			picidList = new ArrayList(Arrays.asList(picIdArray));
		}
		
		if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
			logger.info("开始进行传统文件上传模式进行应用上传");
			for (int i = 0; i < newFileForCutImgList.size(); i++) {
				// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
				String fileName = newFileForCutImgList.get(i).getName();
				if (picidList.contains(fileName.substring(0,fileName.lastIndexOf(".")))) {
					String imgUUId = pic_list.get(i);//为更改的图片
					String imgPath = fileInfoService.getFileByuuid(imgUUId).getFileSavePath();
					File newFile = new File(fileheadpath + imgPath);
					try {
						FileInputStream inputStream = new FileInputStream(newFileForCutImgList.get(i));
						FileOutputStream outputStream = new FileOutputStream(newFile);
						byte b[] = new byte[1024];
						int len;
						try {
							outputStream.flush();
							len = inputStream.read(b);
							while (len != -1) {
								outputStream.write(b, 0, len);
								len = inputStream.read(b);
							}	
						} catch (IOException e) {
							e.printStackTrace();
						}finally{
							try {
								inputStream.close();
								outputStream.close();
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
					} catch (FileNotFoundException e) {
						e.printStackTrace();
					}

				} else {//为新增的图片
					logger.debug("++++++++++++++++++++++++++++++++++++:新增图片");
					try {
						fileInfo = ApkUtil.uploadFile3(newFileForCutImgList.get(i), fileName, fileheadpath + appstorepath);
						newfileSavePath = appstorepath + fileInfo.getFileSavePath();
					} catch (Exception e) {
						e.printStackTrace();
					}
					
					if (fileInfo != null) {
						fileInfo.setFileSavePath(newfileSavePath);
						String picId = informationAnnounce.getPicId();
						if (picId == null || picId.trim().equals("")) {
							picId = fileInfo.getUuid();
						} else {
							picId = picId + "," + fileInfo.getUuid();
						}

						informationAnnounce.setPicId(picId);
						fileInfoService.AddFile(fileInfo);
					}
				}
			
			}
		}else{
			logger.info("上传分布式文件存储形式服务器");
			String picId = informationAnnounce.getPicId();
			for (int i = 0; i < newFileForCutImgList.size(); i++) {
				String fileName = newFileForCutImgList.get(i).getName();
				if (picidList.contains(fileName.substring(0,fileName.lastIndexOf(".")))) {
					String imgUUId = pic_list.get(i);//为更改的图片
					picId.replace(imgUUId+",", "");
				}
				try {
					fileInfo = ApkFastFdsUtil.uploadFileByFastFds(newFileForCutImgList.get(i));
					newfileSavePath = fileInfo.getFileSavePath();
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				if (fileInfo != null) {
					fileInfo.setFileSavePath(newfileSavePath);
					if (picId == null || picId.trim().equals("")) {
						picId = fileInfo.getUuid();
					} else {
						picId = picId + "," + fileInfo.getUuid();
					}

					informationAnnounce.setPicId(picId);
					fileInfoService.AddFile(fileInfo);
				}
			}
		}
		
		User user = FrontEndHelper.getLoginUser();
		informationAnnounce.setAuthorId(user.getId());
		if (pic_list.size() !=0) {
			pic_list.clear();
			newFileForCutImgList.clear();
		}
		informationAnnounce.setStatus(0);
		Byte type = new Byte("2");
		if(informationAnnounce.getType().equals(type)) {
			int tmsNoticeCount = infoAnounceService.queryTmsNoticeCount();
			if(tmsNoticeCount>=5) {
				return ResultMsg.build(ResultMsg.ERROR_CODE, "终端通知消息超过5条，请先删除过时消息");
			}
		}
		return infoAnounceService.insert(informationAnnounce);
	}

	@RequestMapping(value = "/{id}", method = RequestMethod.PATCH)
	@ResponseBody
	public ResultMsg updateInfoAnnounce(@RequestBody InformationAnnounce informationAnnounce) {
		logger.debug("+++++++++++++++++++++++++++开始保存到正式目录");
		String resuletMsg1 = "Modify failure";
		FileInfo fileInfo =null;
		String newfileSavePath = "";
		if (informationAnnounce == null) {
			return new ResultMsg(1, resuletMsg1, null);
		}
		logger.debug("++++++++++++++++++++++++++++++++++++++++newFileForCutImgList长度:"+newFileForCutImgList.size());
		String[] picIdArray = {};
		List<String> picidList = new ArrayList<>();
		if (informationAnnounce.getId()!=null) {
			System.out.println(infoAnounceService.getInfoAnnounceById(informationAnnounce.getId()).getPicId());
			if(infoAnounceService.getInfoAnnounceById(informationAnnounce.getId()).getPicId()!=null){
				picIdArray = infoAnounceService.getInfoAnnounceById(informationAnnounce.getId()).getPicId().split(",");
				picidList = new ArrayList(Arrays.asList(picIdArray));
			}
		}
		if (!"true".equals(ConfigInfo.IS_FASTDFS)) {
			logger.info("开始进行传统文件上传模式进行应用上传");
			for (int i = 0; i < newFileForCutImgList.size(); i++) {// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
				String fileName = newFileForCutImgList.get(i).getName();
				if (picidList.contains(fileName.substring(0,fileName.lastIndexOf(".")))) {//如果原先的数据库表中的picId有该imgUUId，则直接利用该imgUUid，只做图片内容覆盖
					String imgUUId = pic_list.get(i);//为更改的图片
					String imgPath = fileInfoService.getFileByuuid(imgUUId).getFileSavePath();
					File newFile = new File(fileheadpath + imgPath);
					try {
						FileInputStream inputStream = new FileInputStream(newFileForCutImgList.get(i));
						FileOutputStream outputStream = new FileOutputStream(newFile);
						byte b[] = new byte[1024];
						int len;
						try {
							outputStream.flush();
							len = inputStream.read(b);
							while (len != -1) {
								outputStream.write(b, 0, len);
								len = inputStream.read(b);
							}	
						} catch (IOException e) {
							e.printStackTrace();
						}finally{
							try {
								inputStream.close();
								outputStream.close();
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
					} catch (FileNotFoundException e) {
						e.printStackTrace();
					}

				} else {//为新增的图片
					logger.debug("++++++++++++++++++++++++++++++++++++:新增图片");
					try {
						fileInfo = ApkUtil.uploadFile3(newFileForCutImgList.get(i), fileName, fileheadpath + appstorepath);
						newfileSavePath = appstorepath+fileInfo.getFileSavePath();
					} catch (Exception e) {
						e.printStackTrace();
					}
					
					if (fileInfo != null) {
						fileInfo.setFileSavePath(newfileSavePath);
						String picId = informationAnnounce.getPicId();
						if (picId == null || picId.trim().equals("")) {
							picId = fileInfo.getUuid();
						} else {
							picId = picId + "," + fileInfo.getUuid();
						}

						informationAnnounce.setPicId(picId);
						fileInfoService.AddFile(fileInfo);
					}
				}
			
			}
		}else{
			logger.info("上传分布式文件存储形式服务器");
			String picId = informationAnnounce.getPicId();
			for (int i = 0; i < newFileForCutImgList.size(); i++) {// 用新的图片内容替代原来图片，其他都不变。newFileForCutImgList中即可能有新增的图片也可能有更改的图片。
				String fileName = newFileForCutImgList.get(i).getName();
				if (picidList.contains(fileName.substring(0,fileName.lastIndexOf(".")))) {
					String imgUUId = pic_list.get(i);//为更改的图片
					picId=picId.replace(imgUUId+",", "");
					picId=picId.replace(","+imgUUId, "");//该文件id保存在picId的最后一个时
					picId=picId.replace(imgUUId, "");//文件有一个的时候
				}
				try {
					fileInfo = ApkFastFdsUtil.uploadFileByFastFds(newFileForCutImgList.get(i));
					newfileSavePath = fileInfo.getFileSavePath();
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				if (fileInfo != null) {
					fileInfo.setFileSavePath(newfileSavePath);
					
					if (picId == null || picId.trim().equals("")) {
						picId = fileInfo.getUuid();
					} else {
						picId = picId + "," + fileInfo.getUuid();
					}
					informationAnnounce.setPicId(picId);
					fileInfoService.AddFile(fileInfo);
				}
			
			}
		}
		
		
		if (pic_list == null) {
			return infoAnounceService.update(informationAnnounce,fileheadpath + appstorepath);
		}
		pic_list.clear();
		newFileForCutImgList.clear();
		informationAnnounce.setStatus(0);
		return infoAnounceService.update(informationAnnounce, fileheadpath + appstorepath);
	}

	@RequestMapping(value = "", method = RequestMethod.DELETE)
	@ResponseBody
	public ResultMsg deleteInfoAnnounce(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return infoAnounceService.deleteInfos(idsList);
	}

	@RequestMapping(value = "/deleteImg", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg deleteImg(String uuid) {
		if (pic_list != null && pic_list.contains(uuid)) {
			pic_list.remove(uuid);
			for (int i = 0; i < newFileForCutImgList.size(); i++) {
				if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
					newFileForCutImgList.remove(i);
				}
			}
		}
		return null;
	}

	@RequestMapping(value = "/updatePicList", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg updatePicList() {
		if (pic_list == null) {
			return null;
		}
		pic_list.clear();
		newFileForCutImgList.clear();
		logger.debug("刷新后pic_list：" + pic_list.size());
		return null;
	}

	@RequestMapping(value = "/changeImg", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg changeImg(HttpServletRequest request, String uuid) {
		String resuletMsg1 = "Image not selected";
		String resuletMsg2 = "Please select a picture file in PNG or JPG format";
		String resuletMsg3 = "Image uploaded successfully";
		String resuletMsg4 = "Image save failed";

		logger.debug("+++++++++++++++++开始更改图片");
		pic_list.remove(uuid);
		for (int i = 0; i < newFileForCutImgList.size(); i++) {
			if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
				newFileForCutImgList.remove(i);
			}
		}
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file == null) {
					return new ResultMsg(1, resuletMsg1, null);
				}
				FileInfo fileInf = new FileInfo();
				String myFileName = file.getOriginalFilename();
				if (!myFileName.endsWith("png") && !myFileName.endsWith("jpg") && !myFileName.endsWith("PNG")) {
					return new ResultMsg(2, resuletMsg2, null);
				}
				try {
					String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
					String returnFileSavePath =  "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
					String fileSavePath = temDir +returnFileSavePath;
					File f = new File(fileSavePath);
				    try {
						FileUtil.setFilePermissions(f);
					} catch (Exception e) {
						e.printStackTrace();
					}
					String imgUuid = UUID.randomUUID().toString();
					File newFileForCutImg = new File(fileSavePath + imgUuid + "." + fileType);
					// 保存应用截图到临时目录下
					file.transferTo(newFileForCutImg);
					pic_list.add(imgUuid);
					newFileForCutImgList.add(newFileForCutImg);
					String str = imgUuid + ";" + returnFileSavePath + imgUuid + "." + fileType;
					logger.debug("图片更改成功");
					return new ResultMsg(5, resuletMsg3, str);
				} catch (Exception e) {
					return new ResultMsg(3, resuletMsg4, null);
				}
			}

		}
		return null;
	}

	@RequestMapping(value = "/changeStoredImg", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg changeStoredImg(HttpServletRequest request, String uuid) {
		/*
		 * if(!CtUtils.isEmpty(uuid)){ storeApkService.deleteStoredImg(uuid,
		 * fileheadpath + appstorepath); }
		 */

		// 创建一个通用的多部分解析器
		String resuletMsg1 = "Image not selected";
		String resuletMsg2 = "Please select a picture file in PNG or JPG format";
		String resuletMsg3 = "Image uploaded successfully";
		String resuletMsg4 = "Image save failed";
		logger.debug("开始更改changeStoredImg图片");
		pic_list.remove(uuid);
		for (int i = 0; i < newFileForCutImgList.size(); i++) {
			if (newFileForCutImgList.get(i).getAbsolutePath().contains(uuid)) {
				newFileForCutImgList.remove(i);
			}
		}
		logger.debug("更改changeStoredImg图片成功");
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file == null) {
					return new ResultMsg(400, resuletMsg1, null);
				}
				FileInfo fileInf = new FileInfo();
				String myFileName = file.getOriginalFilename();
				if (!myFileName.endsWith("png") && !myFileName.endsWith("jpg") && !myFileName.endsWith("PNG")) {
					return new ResultMsg(2, resuletMsg2, null);
				}
				// 如果更改的是新增的图片，则应该修改临时文件中的图片。
				try {

					String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);
					String returnFileSavePath = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
					String fileSavePath = temDir + returnFileSavePath;
				    try {
						FileUtil.setFilePermissions(new File(fileSavePath));
					} catch (Exception e) {
						e.printStackTrace();
					}
					String imgUuid = uuid;
					File newFileForCutImg = new File(fileSavePath + imgUuid + "." + fileType);
					// 保存应用截图到临时目录下
					file.transferTo(newFileForCutImg);
					pic_list.add(uuid);
					logger.debug("++++++++++++++++++++++++++uuid:" + uuid);
					newFileForCutImgList.add(newFileForCutImg);
					String str = imgUuid + ";" + returnFileSavePath + imgUuid + "." + fileType;
					logger.debug("图片更改成功");
					return new ResultMsg(200, resuletMsg4, str);
				} catch (Exception e) {
					e.printStackTrace();
					return new ResultMsg(500, resuletMsg3, null);
				}
				// String str = fileInf.getUuid()+";" +
				// fileInf.getFileSavePath();
			}

		}

		return null;
	}
	@RequestMapping(value = "/uploadImg", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadImg(HttpServletRequest request) {
		String resuletMsg1 = "The selected image file is too large！";
		String resuletMsg2 = "Image uploaded successfully";
		String resuletMsg3 = "Image save failed";
		logger.debug("++++++++++++++++++++：进行图片上传");
		MultipartFile file = ApkUtil.uploadFile(request);
		// 取得当前上传文件的文件名称
		String myFileNameForCutImg = file.getOriginalFilename();
		if ((int) file.getSize() > 5 * 1024 * 1024) {
			return new ResultMsg(1, resuletMsg1, null);
		}
		if (pic_list == null) {
			pic_list = new ArrayList();
		}
		try {
			String fileType = myFileNameForCutImg.substring(myFileNameForCutImg.lastIndexOf(".") + 1);
			String returnFileSavePath = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
			String fileSavePath = temDir + returnFileSavePath;
			try {
				FileUtil.setFilePermissions(new File(fileSavePath));
			} catch (Exception e) {
				e.printStackTrace();
			}
			String imgUuid = UUID.randomUUID().toString();
			File newFileForCutImg = new File(fileSavePath + imgUuid + "." + fileType);
			newFileForCutImgList.add(newFileForCutImg);
			// 保存应用截图到临时目录下
			file.transferTo(newFileForCutImg);
			pic_list.add(imgUuid);
			logger.debug("++++++++++++++++++pic_list:" + pic_list.size());
			String str = imgUuid + ";" + returnFileSavePath + imgUuid + "." + fileType;
			logger.debug("++++++++++++++++++++++++++++++++++++:图片上传成功！");
			return new ResultMsg(3, resuletMsg2, str);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResultMsg(2, resuletMsg3, null);
		}

	}
}
