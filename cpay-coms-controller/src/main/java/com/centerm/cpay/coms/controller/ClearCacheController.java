package com.centerm.cpay.coms.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.centerm.cpay.common.enums.JobStatus;
import com.centerm.cpay.common.utils.ExcelUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.mapper.SysConfigureMapper;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.TerminalOperation;
import com.centerm.cpay.coms.dao.pojo.TerminalOperationJob;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.TerminalOperationService;
import com.centerm.cpay.coms.util.FrontEndHelper;

/**
 * Remote Operation
 * 
 * @version $Id: ClearCacheController.java, v 0.1 2021年12月28日 上午9:39:05 sup Exp $
 */
@Controller
@RequestMapping("/clearCache")
public class ClearCacheController {
	Logger logger = LoggerFactory.getLogger(DriverJobController.class);
	@Autowired
	private TerminalOperationService terminalOperationService;
    @Autowired
    private SysConfigureMapper sysconfigureMapper;
	
	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getDriverJobList(TerminalOperationJob entity, @RequestParam Integer page, @RequestParam Integer rows){ 
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(entity.getInsId())) {
			entity.setInsId(user.getInsId());
		}
		EUDataGridResult result = terminalOperationService.getList(entity, page, rows);
		return result;
	}
	@RequestMapping(value = "/startOperation/{id}", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg startOperation(@PathVariable Integer id) {
        SysConfigure sysConfigure = sysconfigureMapper.selectByKey("iotUrl");
		return terminalOperationService.startOperation(id, sysConfigure.getValue());
	}
	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(@RequestBody TerminalOperationJob operationJob) {
		User user = FrontEndHelper.getLoginUser();
		operationJob.setReleaseIns(user.getInsId());
		if(CtUtils.isEmpty(operationJob.getInsId())){
			operationJob.setInsId(user.getInsId());
		}
		operationJob.setUserId(user.getId());
		operationJob.setJobStatus(JobStatus.INIT_STATUS.getCode());
		operationJob.setCreateTime(new Date());
		if(operationJob.getReleaseType().equals("1")){
			int countTermSeqs = terminalOperationService.countTermSeqs(operationJob);
			if(countTermSeqs == 0){
				return new ResultMsg(3, "该机构组别下的不存在适配型号的终端", null);
			}
		}
		if ("19".equals(operationJob.getOperateType())){
			JSONObject contentJson = new JSONObject();
			contentJson.set("appPackage", operationJob.getAppPakage());
			contentJson.set("appActivity", operationJob.getAppActivity());
			contentJson.set("appPwd", operationJob.getAppPwd());
			operationJob.setContent(contentJson.toString());
		}
		terminalOperationService.insertJob(operationJob);
		return ResultMsg.success();
	}
	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody TerminalOperationJob operationJob) {
		User user = FrontEndHelper.getLoginUser();
		operationJob.setReleaseIns(user.getInsId());
        if(CtUtils.isEmpty(operationJob.getInsId())){
            operationJob.setInsId(user.getInsId());
        }
		operationJob.setJobStatus(JobStatus.INIT_STATUS.getCode());
		operationJob.setCreateTime(new Date());
		if(operationJob.getReleaseType().equals("1")){
			int countTermSeqs = terminalOperationService.countTermSeqs(operationJob);
			if(countTermSeqs == 0){
				return new ResultMsg(3, "该机构组别下的不存在适配型号的终端", null);
			}
		}
		if ("19".equals(operationJob.getOperateType())){
			JSONObject contentJson = new JSONObject();
			contentJson.set("appPackage", operationJob.getAppPakage());
			contentJson.set("appActivity", operationJob.getAppActivity());
			contentJson.set("appPwd", operationJob.getAppPwd());
			operationJob.setContent(contentJson.toString());
		}
		terminalOperationService.updateJob(operationJob);
		return ResultMsg.success();
	}
	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public TerminalOperationJob selectById(@PathVariable Integer id) {
		TerminalOperationJob terminalOperationJob = terminalOperationService.selectJobById(id);
		if ("19".equals(terminalOperationJob.getOperateType())){
			JSONObject contenJson = JSONUtil.parseObj(terminalOperationJob.getContent());
			terminalOperationJob.setAppActivity(contenJson.getStr("appActivity"));
			terminalOperationJob.setAppPakage(contenJson.getStr("appPackage"));
			terminalOperationJob.setAppPwd(contenJson.getStr("appPwd"));
		}
		return terminalOperationJob;
	}
	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteJobs(HttpSession session, @RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		ResultMsg resultMsg = terminalOperationService.deleteByIds(idsList);
		return resultMsg;
	}
	
	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByPrimaryKey(@PathVariable Integer id) {
		return terminalOperationService.deleteByPrimaryKey(id);
	}
	
	@RequestMapping(value = "/task", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getTaskList(TerminalOperation entity, @RequestParam Integer page, @RequestParam Integer rows){ 
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(entity.getInsId())) {
			entity.setInsId(user.getInsId());
		}
		EUDataGridResult result = terminalOperationService.getGridList(entity, page, rows);
		return result;
	}
	
	@RequestMapping(value = "/issuedAgain", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg issuedAgain(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		ResultMsg resultMsg = terminalOperationService.issuedAgain(idsList);
		return resultMsg;
	}
	
	@RequestMapping(value = "/issuedCancel", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg issuedCancel(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		ResultMsg resultMsg = terminalOperationService.issuedCancel(idsList);
		return resultMsg;
	}
	
	@RequestMapping(value = "/issuedCancelAll/{id}", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg issuedCancelAll(@PathVariable Integer id) {
		ResultMsg resultMsg = terminalOperationService.issuedCancelAll(id);
		return resultMsg;
	}
	@RequestMapping(value = "/exportExcel/{condition}", method = {RequestMethod.GET})
	@ResponseBody
	public void exportExcel(HttpServletRequest request, HttpServletResponse response, @PathVariable String condition) throws IOException {
		String[] arr = condition.split("&");
		String jobName = "";
		if(!CtUtils.isEmpty(arr[3].replace("jobName=", "").trim())){
			jobName = arr[3].replace("jobName=", "").trim();
		}
		TerminalOperation terminalOperation = new TerminalOperation();
		OutputStream ouputStream = response.getOutputStream();
		response.setContentType("application/vnd.ms-excel");
		response.setHeader("Content-disposition", "attachment;filename="+java.net.URLEncoder.encode("Remote Task_"+jobName+"_Execution Table", "UTF-8")+".xlsx");
		User user = FrontEndHelper.getLoginUser();
		if(!CtUtils.isEmpty(arr[0].replace("termSeq=", "").trim())){
			terminalOperation.setTermSeq(arr[0].replace("termSeq=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[1].replace("operateStatus=", "").trim())){
			terminalOperation.setOperateStatus(arr[1].replace("operateStatus=", "").trim());
		}
		if(!CtUtils.isEmpty(arr[2].replace("jobId=", "").trim())){
			terminalOperation.setJobId(Integer.valueOf(arr[2].replace("jobId=", "").trim()));
		}
		List<TerminalOperation> list = terminalOperationService.getTaskListExcel(terminalOperation);

		String[] excelHeader = {"Serial No","Status","Operation Command","Organization","Terminal Manufacturer","Terminal Type","Create Time"};
		String[] fields = {"termSeq","operateStatus","operateCommand","insName","factoryName","termTypeName","recordCreateTime"};
		ExcelUtil.exportExcel("Task_"+jobName+"_Execution Table", excelHeader, fields, list, ouputStream);
		ouputStream.flush();
		ouputStream.close();
	}
	
	
}