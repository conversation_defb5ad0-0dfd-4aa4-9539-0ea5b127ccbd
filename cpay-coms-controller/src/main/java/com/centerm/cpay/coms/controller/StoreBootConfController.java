package com.centerm.cpay.coms.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.Date;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.StoreBootConf;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.FileInfoService;
import com.centerm.cpay.coms.service.StoreBootConfService;
import com.centerm.cpay.coms.util.ApkUtil;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FrontEndHelper; 

@Controller
@RequestMapping("/bootConf")
public class StoreBootConfController {
	@Autowired 
	private StoreBootConfService storeBootConfService;
	@Autowired
	private FileInfoService fileInfoService;
	String appsystempath = FilePathUtil.getrelativePath("appsystempath");
	
	String fileheadpath = ConfigInfo.HEADER_PATH;
	
	@RequestMapping(value = "/getConfById", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg getConfById(Integer id) {
		StoreBootConf storeBootConf = storeBootConfService.getConfById(id);
		return new ResultMsg(200, "success", storeBootConf);
	}
	@RequestMapping(value = "/deleteRows", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg deleteRows(String ids) {
		if (!ids.equals("")) {
			String[] idsArray = ids.split(",");
			for(int i=0;i<idsArray.length;i++){
				storeBootConfService.deleteConfById(Integer.parseInt(idsArray[i]),fileheadpath+appsystempath);
		    }
		}
		return new ResultMsg(200, "Successfully Deleted!", null);
	}
	@RequestMapping(value = "/getBootConfPage", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getBootConfPage(StoreBootConf storeBootConf,
			@RequestParam Integer page, @RequestParam Integer rows) {
		EUDataGridResult result = storeBootConfService.getBootConfPage(
				storeBootConf, page, rows);
		return result;
	}

	@RequestMapping(value = "/deleteConfById", method = { RequestMethod.GET })
	@ResponseBody
	public ResultMsg deleteConfById(Integer id) {
		storeBootConfService.deleteConfById(id,fileheadpath+appsystempath);
		return new ResultMsg(200, "Successfully Deleted!", null);
	}
	@RequestMapping(value = "/update", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody StoreBootConf storeBootConf) {
		String xmlUuid;
		try {
			xmlUuid = createXmlFile(storeBootConf);
			if(xmlUuid==null){
				return new ResultMsg(500, "Failed modification", null);
			}
			storeBootConf.setXmlUuid(xmlUuid);
			storeBootConfService.update(storeBootConf);
		} catch (IOException e) {
			return new ResultMsg(500, "Failed modification!", null);
		}
		return new ResultMsg(200, "Successfully Modified!", null);
	}
	@RequestMapping(value = "/add", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg add(@RequestBody Map map) throws IOException {
		User user = FrontEndHelper.getLoginUser();
		Integer operId = user.getId();
		String packageName = (String) map.get("packageName");
		String autoOpen = (String) map.get("autoOpen");
		String activityUrl = (String) map.get("activityUrl");
		Integer openTime = Integer.valueOf((String) map.get("openTime"));
		StoreBootConf storeBootConf = new StoreBootConf();
		storeBootConf.setPackageName(packageName);
		storeBootConf.setActivityUrl(activityUrl);
		storeBootConf.setAutoOpen(autoOpen);
		storeBootConf.setOpenTime(openTime);
		storeBootConf.setOperTime(TimeUtil.getCurrentTime(new Date()));
		storeBootConf.setOperId(operId);
		if (storeBootConf == null) {
			return new ResultMsg(500, "Fail to add!", null);
		}
		String xmlUuid = createXmlFile(storeBootConf);
		if(xmlUuid==null){
			return new ResultMsg(500, "Fail to add!", null);
		}
		storeBootConf.setXmlUuid(xmlUuid);
		int result = storeBootConfService.add(storeBootConf);
		if (result < 0) {
			return new ResultMsg(500, "Fail to add!", null);
		}
		return new ResultMsg(200, "Successfully Added!", null);
	}

	public String createXmlFile(StoreBootConf storeBootConf) throws IOException {
		String str = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"+"\n";
		str += "<resource>"+"\n";
		str += "<auto packageName=\"" + storeBootConf.getPackageName()+"\" "
				+ "activityUrl=\"" + storeBootConf.getActivityUrl() + "\"  autoOpen= \""
				+ storeBootConf.getAutoOpen() + "\" openTime=\""
				+ storeBootConf.getOpenTime() + "\" />" + "\n";
		str += "</resource>";
		String uuid = CtUtils.uuid();
		String fileSaveName = uuid+FilePathUtil.getValue("boot.conf.xml.name")+"."+"xml";
		String datePath =CtUtils.getCurrentTime("yyyyMMdd")+"/";
		String path = fileheadpath+appsystempath+datePath;
		File file = new File(path);
		File file1 = new File(path+fileSaveName);
		if(!file.isDirectory()){
			file.mkdirs();
			file.setExecutable(true);
			file.setReadable(true);
			file.setWritable(true);
		}	
		try {
			
			createFile(file1) ;
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
		  boolean flag = writeXmlFile(str,file1,appsystempath+datePath+fileSaveName, uuid);
		  if(!flag){
			  return null ;
		  }
		} catch (Exception e) {
			return null ;
		}
		return uuid;
	}

	public  boolean createFile(File fileName) throws Exception {
		boolean flag = false;
		try {
			if (!fileName.exists()) {
				fileName.createNewFile();
				flag = true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	public  boolean writeXmlFile(String content, File fileName,String savePath,String uuid)
			throws Exception {
		RandomAccessFile mm = null;
		boolean flag = false;
		FileOutputStream o = null;
		try {
			o = new FileOutputStream(fileName);
			o.write(content.getBytes("GBK"));
			o.close();
			FileInfo fileInfo = new FileInfo();
			fileInfo.setUuid(uuid);
			fileInfo.setFileSavePath(savePath);
			fileInfo.setFileOldName("auto_app.xml");
			fileInfo.setFileSize((int)fileName.length());
			fileInfo.setFileMd5(ApkUtil.getFileMD5String(fileName));
			fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
			fileInfoService.AddFile(fileInfo);
			flag = true;
		} catch (Exception e) {
			return false;
		} finally {
			if (mm != null) {
				mm.close();
			}
		}
		
		return flag;
	}
}
