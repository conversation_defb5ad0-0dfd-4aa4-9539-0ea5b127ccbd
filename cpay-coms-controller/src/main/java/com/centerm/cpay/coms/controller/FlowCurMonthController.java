package com.centerm.cpay.coms.controller;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.flow.dao.pojo.ComsSimFlowusageDay;
import com.centerm.cpay.flow.service.FlowInfService;

/**
 * @Title: OperInfController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年5月22日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/flowCurMonth")
public class FlowCurMonthController {
	@Autowired
	private FlowInfService flowInfService;
	Logger logger = LoggerFactory.getLogger(FlowCurMonthController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getFlowCurMonthList(ComsSimFlowusageDay entity, @RequestParam Integer page, @RequestParam Integer rows) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		entity.setRecordMonth(sdf.format(new Date()));
		EUDataGridResult result = flowInfService.getFlowCurMonthList(entity, page, rows);
		return result;
	}
}