
package com.centerm.cpay.coms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.coms.dao.pojo.InsTypeInf;
import com.centerm.cpay.coms.service.InsTypeService;

/**
 * @Title: InsTypeController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年6月4日 下午2:56:31
 * @version V1.0
 */
@Controller
@RequestMapping("/instype")
public class InsTypeController {
	@Autowired
	private InsTypeService insTypeService;

	@RequestMapping(value = "/list", method = RequestMethod.GET)
	@ResponseBody
	public List<InsTypeInf> getInsTypeList() {
		return insTypeService.getInsTypeList();
	}
}
