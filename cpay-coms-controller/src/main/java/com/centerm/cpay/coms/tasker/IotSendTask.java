package com.centerm.cpay.coms.tasker;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;

import com.centerm.cpay.common.enums.JobReleaseStatus;
import com.centerm.cpay.common.enums.JobStatus;
import com.centerm.cpay.common.enums.TakUploadStatus;
import com.centerm.cpay.common.utils.Config;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.OperationCmdEnum;
import com.centerm.cpay.common.utils.OperationResponseStatusEnum;
import com.centerm.cpay.common.utils.OperationTypeEnum;
import com.centerm.cpay.common.utils.SignUtil;
import com.centerm.cpay.coms.dao.mapper.ComsUploadJobMapper;
import com.centerm.cpay.coms.dao.mapper.ComsUploadTaskMapper;
import com.centerm.cpay.coms.dao.mapper.DriverJobMapper;
import com.centerm.cpay.coms.dao.mapper.DriverJobTaskMapper;
import com.centerm.cpay.coms.dao.mapper.SysConfigureMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalOperationJobMapper;
import com.centerm.cpay.coms.dao.mapper.TerminalOperationMapper;
import com.centerm.cpay.coms.dao.pojo.ComsUploadJob;
import com.centerm.cpay.coms.dao.pojo.ComsUploadTask;
import com.centerm.cpay.coms.dao.pojo.DriverJob;
import com.centerm.cpay.coms.dao.pojo.DriverJobTask;
import com.centerm.cpay.coms.dao.pojo.OperationRequest;
import com.centerm.cpay.coms.dao.pojo.OperationResponse;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalOperation;
import com.centerm.cpay.coms.dao.pojo.TerminalOperationJob;
import com.github.pagehelper.PageHelper;


public class IotSendTask {
	private static final Logger LOGGER = LoggerFactory.getLogger(IotSendTask.class);
	@Autowired
	private TerminalOperationJobMapper terminalOperationJobMapper;
	@Autowired
    private TerminalOperationMapper terminalOperationMapper;
	@Autowired
	private TerminalMapper terminalMapper;
    @Autowired
    private SysConfigureMapper sysconfigureMapper;
    @Autowired
    private DriverJobTaskMapper driverJobTaskMapper;
    @Autowired
    private DriverJobMapper driverJobMapper;
    
    @Autowired
    private ComsUploadJobMapper comsUploadJobMapper;
    @Autowired
    private ComsUploadTaskMapper comsUploadTaskMapper;

    public void process() throws Exception {
        LOGGER.info("IotSendTask>>>");
        terminalOperationJob();
        terminalDriverJob();
        comsUploadJob();
    }
    
    
    private void terminalOperationJob() {
        TerminalOperationJob terminalOperationJob = new TerminalOperationJob();
        terminalOperationJob.setJobStatus(JobStatus.START_STATUS.getCode());
        List<TerminalOperationJob> list = terminalOperationJobMapper.selectByCondition(terminalOperationJob);
        for (TerminalOperationJob opJob : list) {
            TerminalOperation queryTerminalOperation = new TerminalOperation();
            queryTerminalOperation.setId(opJob.getId());
            queryTerminalOperation.setOptCmdStatus(0);
            PageHelper.startPage(0, 1000);
            List<TerminalOperation> terminalOperationList = terminalOperationMapper.selectByJobId(queryTerminalOperation);
            for (TerminalOperation terminalOperation : terminalOperationList) {
                if("0".equals(terminalOperation.getOperateStatus())){
                    Terminal terminal = terminalMapper.selectByTermSeq(terminalOperation.getTermSeq());
                    if("1".equals(terminal.getNetworkStatus())){
                        boolean result = sendToIot(terminalOperation.getTermSeq(), terminalOperation.getOperateCommand(), OperationTypeEnum.OPERATION.getValue());
                        if(result){
                            TerminalOperation record = new TerminalOperation();
                            record.setId(terminalOperation.getId());
                            record.setOptCmdStatus(1);
                            terminalOperationMapper.updateByPrimaryKeySelective(record);
                        }
                    }
                }
            }
        }   
    }

    private void terminalDriverJob() {
        DriverJob queryDriverJob = new DriverJob();
        queryDriverJob.setReleaseStatus("5");
        List<DriverJob> joblist = driverJobMapper.selectByCondition(queryDriverJob);
        for (DriverJob driverJob : joblist) {
            DriverJobTask driverJobTask = new DriverJobTask();
            driverJobTask.setJobId(driverJob.getId());
            driverJobTask.setOptCmdStatus(0);
            PageHelper.startPage(0, 1000);
            List<DriverJobTask> taksList = driverJobTaskMapper.selectByCondition(driverJobTask);
            for (DriverJobTask driverJobTask2 : taksList) {
                if("1".equals(driverJobTask2.getDlFlag())){
                    Terminal terminal = terminalMapper.selectByTermSeq(driverJobTask2.getTermSeq());
                    if("1".equals(terminal.getNetworkStatus())){
                        boolean result = sendToIot(driverJobTask2.getTermSeq(), OperationCmdEnum.UPLOAD_INFO.getCode(), OperationTypeEnum.SOFTWARE.getValue());
                        if(result){
                            DriverJobTask record = new DriverJobTask();
                            record.setId(driverJobTask2.getId());
                            record.setOptCmdStatus(1);
                            driverJobTaskMapper.updateByPrimaryKeySelective(record);
                        }
                    }
                }
            }
        }
        
    }
    
    
    private void comsUploadJob() {
        ComsUploadJob comsUploadJob = new ComsUploadJob();
        comsUploadJob.setReleaseStatus(JobReleaseStatus.JOB_POST_SUCESS.getCode());
        List<ComsUploadJob> comsUploadJobList = comsUploadJobMapper.selectSuccessJob(comsUploadJob);
        for (ComsUploadJob comsUploadJob2 : comsUploadJobList) {
            ComsUploadTask comsUploadTask = new ComsUploadTask();
            comsUploadTask.setJobId(comsUploadJob2.getId());
            comsUploadTask.setOptCmdStatus(0);
            PageHelper.startPage(0, 1000);
            List<ComsUploadTask> taskList = comsUploadTaskMapper.selectByCondition(comsUploadTask);
            for (ComsUploadTask comsUploadTask2 : taskList) {
                if(TakUploadStatus.TASK_PENDING_DOWN.getCode().equals(comsUploadTask2.getUploadFlag())){
                    Terminal terminal = terminalMapper.selectByTermSeq(comsUploadTask2.getTermSeq());
                    if("1".equals(terminal.getNetworkStatus())){
                        boolean result = sendToIot(comsUploadTask2.getTermSeq(), OperationCmdEnum.UPLOAD_INFO.getCode(), OperationTypeEnum.DOWNLOAD.getValue());
                        if(result){
                            ComsUploadTask record = new ComsUploadTask();
                            record.setId(comsUploadTask2.getId());
                            record.setOptCmdStatus(1);
                            comsUploadTaskMapper.updateByPrimaryKeySelective(record);
                        }
                    }
                }
            }
        }
    }

    /**
     *【IoT推送】
     * @param termSeq 终端号
     * @param operateCommand 操作类型
     * @param type 操作种类
     * @return 推送结果
     */
    private boolean sendToIot(String termSeq, String operateCommand, Integer type) {
        //【配置】IoT推送请求参数
        OperationRequest operationRequest = new OperationRequest();
        operationRequest.setVersion("1.0.0");
        operationRequest.setCid(Config.CID);
        operationRequest.setSn(termSeq);
        operationRequest.setCommand(operateCommand);
        operationRequest.setRandom(CtUtils.getRandom(6));
        operationRequest.setType(type);
        Map requetMap = JSONUtil.toBean(JSONUtil.toJsonStr(operationRequest), Map.class);

        //【签名】请求体
        String sign = null;
        try {
            sign = SignUtil.generateSignature(requetMap, Config.IOT_KEY, "HMAC-SHA256");
        } catch (Exception e) {
            LOGGER.error("【IoT推送】签名生产发生异常：{}", e.toString());
        }
        operationRequest.setSign(sign);

        //【请求】IoT推送接口
        String iotUrl = sysconfigureMapper.selectByKey("iotUrl").getValue();
        String request = JSONUtil.toJsonStr(operationRequest);
        LOGGER.info("【IoT推送】任务请求报文体为：{}", request);
        String response = HttpUtil.post(iotUrl , request);
        LOGGER.info("【IoT推送】任务响应报文体为：{}", response);

        //【处理】接口返回参数
        if (response == null || "".equals(response)) {
            return false;
        }
        OperationResponse operationResponse = JSONUtil.toBean(response, OperationResponse.class);
        if (operationResponse == null) {
            return false;
        }
        return OperationResponseStatusEnum.SUCCESS.getCode().equals(operationResponse.getStatus());
    }
}