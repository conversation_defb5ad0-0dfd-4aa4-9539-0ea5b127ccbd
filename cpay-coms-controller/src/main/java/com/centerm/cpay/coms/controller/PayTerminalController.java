
package com.centerm.cpay.coms.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.service.TerminalService;
import com.centerm.cpay.coms.util.FrontEndHelper;

/**
 * @Title: OperInfController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年5月22日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/payTerminal")
public class PayTerminalController {
	@Autowired
	private TerminalService terminalService;
	@Autowired
	private InstitutionService institutionService;
	Logger logger = LoggerFactory.getLogger(PayTerminalController.class);
	
	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getTermInfList(Terminal termInf, @RequestParam Integer page, @RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		if (CtUtils.isEmpty(termInf.getInsId())) {
			termInf.setInsId(user.getInsId());
		}
		Institution ins = institutionService.selectByInsCode("4444");
		termInf.setInsId(ins.getId());
		EUDataGridResult result = terminalService.getTermInfList(termInf, page, rows);
		return result;
	}
	@RequestMapping(value = "/testAuto", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg testAuto(@RequestBody Terminal terminal) {
		User user = FrontEndHelper.getLoginUser();
		return terminalService.testAuto(terminal,user.getId());
	}
	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByPrimaryKey(@PathVariable Integer id) {
		User user = FrontEndHelper.getLoginUser();
		return terminalService.delTestAuto(id,user.getId());
	}
}
