
package com.centerm.cpay.coms.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.centerm.cpay.common.utils.*;
import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.AdvertService;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FileUnzip;
import com.centerm.cpay.coms.util.FrontEndHelper;

/**
 * @Title: AdVertController.java
 * @Description: TODO
 * <AUTHOR>
 * @version V1.0
 */
@Controller
@RequestMapping("/ad")
public class AdVertController {
	@Autowired
	private AdvertService advertService;
	@Autowired
	private SysConfigureService configureService;
	String ngsPicPath = "";

	String filesavepath =ConfigInfo.getFilePath(ConfigInfo.AD_PATH);
	String fileheadpath = ConfigInfo.HEADER_PATH;
	String temDir = FilePathUtil.getTempDir() + File.separator;
	Logger logger = LoggerFactory.getLogger(AdVertController.class);
	String isFastFds = ConfigInfo.IS_FASTDFS;

	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsStoreAdvert getByPrimaryKey(@PathVariable Integer id) {
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		ComsStoreAdvert comsStoreAdvert = advertService.getByPrimaryKey(id);
		if (comsStoreAdvert.getAdFileType().equals("2")) {
			if (comsStoreAdvert.getAdPath() != null) {
				comsStoreAdvert
						.setH5Iframe(ngsPicPath + comsStoreAdvert.getAdPath().replace(".zip", "") + "/index.html");
			} else {
				comsStoreAdvert.setH5Iframe("");
			}
		}
		String path = comsStoreAdvert.getAdPicPath();
		if (!CtUtils.isEmpty(path)) {
			path.replace(fileheadpath, "");
			comsStoreAdvert.setAdPicPath(ngsPicPath + path);
		} else {
			comsStoreAdvert.setAdPicPath("");
		}
		if (path.contains(".zip")) {
			comsStoreAdvert.setAdPicPath("");
		}
		return comsStoreAdvert;
	}

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getADList(ComsStoreAdvert entity, @RequestParam Integer page, @RequestParam Integer rows) {
		if (null == entity.getInsId()) {
			User user = FrontEndHelper.getLoginUser();
			entity.setInsId(user.getInsId());
		}
		EUDataGridResult result = advertService.getAdvertList(entity, page, rows);
		return result;
	}

	@RequestMapping(value = "/audit", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getADAuditList(ComsStoreAdvert entity, @RequestParam Integer page,
			@RequestParam Integer rows) {
		if (null == entity.getInsId()) {
			User user = FrontEndHelper.getLoginUser();
			entity.setInsId(user.getInsId());
		}
		EUDataGridResult result = advertService.getAdAuditList(entity, page, rows);
		return result;
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(@PathVariable Integer id) {
		return advertService.deleteById(id);
	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByIds(@RequestParam String ids) {
		List<String> idsList = new ArrayList<String>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
			ComsStoreAdvert comsStoreAdvert = advertService.getByPrimaryKey(Integer.valueOf(idsArray[i]));
			String adPath = comsStoreAdvert.getAdPath();
			File adFile = new File(adPath);
			if (adFile.exists()) {
				adFile.delete();
			}
			String adPicPath = comsStoreAdvert.getAdPicPath();
			File adPicFile = new File(adPicPath);
			if (adPicFile.exists()) {
				adPicFile.delete();
			}
		}
		return advertService.deleteByIds(idsList);
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody ComsStoreAdvert entity) {
		getComsStoreAdvert(entity);
		return advertService.updateByEntity(entity);
	}

	@RequestMapping(value = "", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg insert(@RequestBody ComsStoreAdvert entity) {
		getComsStoreAdvert(entity);
		return advertService.insertEntity(entity);
	}

	@RequestMapping(value = "/audit", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg audit(@RequestBody ComsStoreAdvert object) {
		// System.out.println(object.getId());
		if (!CtUtils.isEmpty(object.getAdPath())) {
			object.setAdPicPath(object.getAdPath().replace(fileheadpath, ""));
		}
		advertService.audit(object);
		/*
		 * List<String> idsList = new ArrayList<>(); String[] idsArray =
		 * ids.split(","); for (int i = 0; i < idsArray.length; i++) {
		 * idsList.add(idsArray[i]); } if (param == 1) {
		 * advertService.passByIds(idsList);
		 * 
		 * ComsStoreAdvert entity=new ComsStoreAdvert(); entity.setPubTime(new
		 * Date()); advertService.updateByEntity(entity);
		 * 
		 * } else { advertService.rejectByIds(idsList); }
		 */
		return ResultMsg.success();

	}

	@RequestMapping(value = "/adEnabled", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg adEnabled(@RequestParam Integer id, @RequestParam Integer param) {
		ComsStoreAdvert entity = new ComsStoreAdvert();
		entity.setId(id);
		if (param == 1) {// 停用
			entity.setIsDisabled("1");
		} else {// 启用
			entity.setIsDisabled("0");
		}
		entity.setPubTime(new Date());
		advertService.adEnabled(entity);
		return ResultMsg.success();

	}

	@RequestMapping(value = "/uploadFile", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadFile(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 记录上传过程起始时的时间，用来计算上传时间
				int pre = (int) System.currentTimeMillis();
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// System.out.println(myFileName);
						// 重命名上传后的文件名
						String fileName = UUID.randomUUID()
								+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						// 定义上传路径
						path = fileheadpath + filesavepath + fileName;
						try {
							if (!(new File(fileheadpath + filesavepath).isDirectory())) {
								new File(fileheadpath + filesavepath).mkdir();
							}
						} catch (SecurityException e) {
							e.printStackTrace();
						}
						// 路径和原文件名
						File localFile = new File(path);
						file.transferTo(localFile);
						path = filesavepath + fileName + "," + file.getOriginalFilename();
					}
				}
				// 记录上传该文件后的时间
				// int finaltime = (int) System.currentTimeMillis();
				// System.out.println(finaltime - pre);
			}
		}
		if (path.trim() != "") {
			return ResultMsg.success(path);
		}
		return ResultMsg.fail();
	}

	@RequestMapping(value = "/uploadPic", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadPic(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 记录上传过程起始时的时间，用来计算上传时间
				int pre = (int) System.currentTimeMillis();
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// System.out.println(myFileName);
						// 重命名上传后的文件名
						String fileName = UUID.randomUUID()
								+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						// 定义上传路径
						path = temDir + File.separator + CtUtils.getCurrentTime("yyyyMMdd") + File.separator + fileName;
						// path = propertiesService.filesavepath + fileName;
						// 路径和原文件名
						try {
							if (!(new File(
									temDir + File.separator + CtUtils.getCurrentTime("yyyyMMdd") + File.separator)
											.isDirectory())) {
								new File(temDir + File.separator + CtUtils.getCurrentTime("yyyyMMdd") + File.separator)
										.mkdir();
							}
						} catch (SecurityException e) {
							e.printStackTrace();
						}
						File localFile = new File(path);

						file.transferTo(localFile);
						path = temDir + File.separator + CtUtils.getCurrentTime("yyyyMMdd") + File.separator + fileName
								+ "," + file.getOriginalFilename();
					}
				}
				// 记录上传该文件后的时间
				// int finaltime = (int) System.currentTimeMillis();
				// System.out.println(finaltime - pre);
			}
		}
		if (path.trim() != "") {
			return ResultMsg.success(path);
		}
		return ResultMsg.fail();
	}

	/**
	 * 预览服务器图片
	 */
	@RequestMapping(value = "/fileView", method = { RequestMethod.GET })
	@ResponseBody
	public void viewPic(HttpServletRequest request, HttpServletResponse response) throws IOException {
		response.reset();
		String imagePathParam = request.getParameter("imagePath");
		if(CtUtils.isEmpty(imagePathParam)||imagePathParam.indexOf("..") >0){
			ResultMsg result=ResultMsg.build(ResultMsg.ERROR_CODE, "Image path error");
			FrontEndHelper.responseOutWithJson(response,result);
		    return;
        }
		String imagePath = FilePathUtil.getTempDir()+imagePathParam;
		ServletOutputStream output = response.getOutputStream();
		response.setContentType("image/jpeg;charset=GB2312");
		File file = new File(imagePath);
		if(!file.exists()){
			ResultMsg result=ResultMsg.build(ResultMsg.ERROR_CODE, "Image path error");
			FrontEndHelper.responseOutWithJson(response,result);
			return;
		}
		InputStream imageIn = new FileInputStream(file);
		BufferedInputStream bis = new BufferedInputStream(imageIn);
		BufferedOutputStream bos = new BufferedOutputStream(output);
		byte data[] = new byte[4096];
		int size = 0;
		size = bis.read(data);
		while (size != -1) {
			bos.write(data, 0, size);
			size = bis.read(data);
		}
		bis.close();
		bos.flush();
		bos.close();
		output.close();
	}

	/**
	 * 文件下载
	 */
	@RequestMapping(value = "/fileDownload", method = { RequestMethod.GET })
	@ResponseBody
	public void fileDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String filename = request.getParameter("filename");
		response.setHeader("Content-Disposition", "attachment;filename=" + filename);
		response.setContentType("application/x-download");
		String fullFileName = fileheadpath + filesavepath + filename;
		InputStream in = new FileInputStream(fullFileName);
		OutputStream out = response.getOutputStream();
		int b;
		while ((b = in.read()) != -1) {
			out.write(b);
		}
		in.close();
		out.close();
	}

	private ComsStoreAdvert getComsStoreAdvert(ComsStoreAdvert entity) {
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");

		FileInfo fileInfo = new FileInfo();
		if (!CtUtils.isEmpty(entity.getAdPicPath())) {
			ComsStoreAdvert ad = advertService.getByPrimaryKey(entity.getId());
			if(ad != null){
				if(entity.getAdPicPath().equals(ngsPicPath+ad.getAdPicPath())){
					entity.setAdPicPath(ad.getAdPicPath());
				}else{
					fileInfo = savePic(entity, fileInfo);
				}
			}else{
				fileInfo = savePic(entity, fileInfo);
			}
		}
			
		if (entity.getAdPath() != null) {
			try {
				if (CtUtils.isEmpty(entity.getAdPath())) {
					entity.setMd5(FileMD5.getFileMD5String(fileheadpath + entity.getAdPicPath()));
				} else {
					if (!isFastFds.equals("true")) {
						entity.setMd5(FileMD5.getFileMD5String(fileheadpath + entity.getAdPath()));
					}else{
						entity.setMd5(fileInfo.getFileMd5());
					}
				}
			} catch (IOException e) {
				// TODO
				e.printStackTrace();
			}
			if (entity.getAdFileType().equals("2")) {
				String zipFilePath = fileheadpath + entity.getAdPath();
				String unzipFilePath = fileheadpath + entity.getAdPath().replace(".zip", "");
				try {
					FileUnzip.unzip(zipFilePath, unzipFilePath);
				} catch (Exception e) {
					e.printStackTrace();
					logger.info("H5广告的zip文件压缩包解压失败");
				}
			}
		}
		entity.setAdAudit("0");
		entity.setCrTime(new Date());
		if (entity.getType().equals("1")) {
			if (!entity.getAdFileType().equals("1")) {
				entity.setAdPicPath("");
			}
		}
		return entity;
	}

	private FileInfo savePic(ComsStoreAdvert entity, FileInfo fileInfo) {
		UUID uuid = UUID.randomUUID();
		String fileName = "";
		if (!entity.getType().equals("1")) {
			fileName = uuid + "." + entity.getAdPicPath().substring(entity.getAdPicPath().lastIndexOf(".") + 1);
		} else {
			fileName = uuid + "." + entity.getFileName().substring(entity.getFileName().lastIndexOf(".") + 1);
		}
		File newFileFolder = new File(fileheadpath + filesavepath);
		try {
			FileUtil.setFilePermissions(newFileFolder);
		} catch (Exception e) {

			e.printStackTrace();
		}
		if (!isFastFds.equals("true")) {
			logger.info("开始执行传统文件上传模式");
			File newFilePath = new File(fileheadpath + filesavepath + fileName);
			File oldFilePath = new File(entity.getAdPicPath());
			uploadAd(oldFilePath, newFilePath);
			entity.setAdPicPath(filesavepath + fileName);
			logger.info("执行传统文件上传模式完成");
			logger.debug("path=" + entity.getAdPicPath());
		} else {
			logger.info("开始执行FastFds文件上传模式");
			fileInfo = ApkFastFdsUtil.getSignedFileCustomer(entity.getAdPicPath(), entity.getFileName());
			entity.setAdPicPath(fileInfo.getFileSavePath());
			entity.setAdPath(fileInfo.getFileSavePath());
			logger.info("执行FastFds文件上传模式完成");
			logger.debug("path=" + entity.getAdPicPath());
		}
		return fileInfo;
	}

	public void uploadAd(File tempFilePath, File newFilePath) {
		FileInputStream inputStream = null;
		FileOutputStream outputStream = null;
		try {
			inputStream = new FileInputStream(tempFilePath);
			outputStream = new FileOutputStream(newFilePath);
			byte b[] = new byte[1024];
			int len;
			try {
				len = inputStream.read(b);
				while (len != -1) {
					outputStream.write(b, 0, len);
					len = inputStream.read(b);
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		} catch (FileNotFoundException e1) {
			// TODO
			e1.printStackTrace();
		} finally {
			try {
				if (inputStream != null)
					inputStream.close();
				if (outputStream != null)
					outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
