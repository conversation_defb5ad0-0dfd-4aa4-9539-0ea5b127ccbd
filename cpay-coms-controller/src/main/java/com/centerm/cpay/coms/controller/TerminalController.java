package com.centerm.cpay.coms.controller;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.centerm.cpay.common.enums.MonitorType;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.Config;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.ExcelUtil;
import com.centerm.cpay.common.utils.OperationResponseStatusEnum;
import com.centerm.cpay.common.utils.OperationTypeEnum;
import com.centerm.cpay.common.utils.SignUtil;
import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.coms.dao.mapper.SysConfigureMapper;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.service.*;
import com.centerm.cpay.coms.util.FilePathUtil;
import com.centerm.cpay.coms.util.FrontEndHelper;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Controller
@RequestMapping({"/terminal"})
public class TerminalController {

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private TerminalGroupService terminalGroupService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private InstitutionService institutionService;
    @Autowired
    private FactoryService factoryService;
    @Autowired
    private SysConfigureMapper sysconfigureMapper;
    private static ExecutorService fixedThreadPool = Executors.newFixedThreadPool(5);
    Logger logger = LoggerFactory.getLogger(TerminalController.class);

    @Autowired
    SysPropertiesService propertiesService;

    String filesavepath = ConfigInfo.getFilePath(ConfigInfo.AD_PATH);
    @Autowired
    private SysConfigureService configureService;
    String ngsPicPath = "";
    String fileheadpath = ConfigInfo.HEADER_PATH;
    String baiduApiUrl = FilePathUtil.getValue("baiduApiUrl");

    @RequestMapping(value = {""}, method = {org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public EUDataGridResult getTermInfList(Terminal termInf, @RequestParam Integer page, @RequestParam Integer rows) {
        User user = FrontEndHelper.getLoginUser();
        if (CtUtils.isEmpty(termInf.getInsId())) {
            termInf.setInsId(user.getInsId());
        }
        if (termInf.getTermTypeCodes() != null) {
            String[] str = termInf.getTermTypeCodes().split(",");
            String codes = "";
            for (int i = 0; i < str.length; i++) {
                codes = codes + "'" + str[i] + "'" + ",";
            }
            codes = codes.substring(0, codes.length() - 1);
            termInf.setTermTypeCodes(codes);
        }
        EUDataGridResult result = this.terminalService.getTermInfList(termInf, page.intValue(), rows.intValue());
        return result;
    }

    @RequestMapping(value = {"/group/{groupId}"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public EUDataGridResult selectTerminalByGroupId(Terminal terminal, @PathVariable Integer groupId,
                                                    @RequestParam Integer page, @RequestParam Integer rows) {
        User user = FrontEndHelper.getLoginUser();
        terminal.setTerminalGroupId(groupId.intValue());
        if (CtUtils.isEmpty(terminal.getInsId())) {
            terminal.setInsId(user.getInsId());
        }
        EUDataGridResult result = this.terminalService.selectTerminalByGroupId(terminal, page, rows);
        return result;
    }

    @RequestMapping(value = "/getTerminalCurrentPosition/{id}", method = {RequestMethod.GET})
    @ResponseBody
    public TerminalPosition getTerminalCurrentPosition(@PathVariable Integer id) {
        Terminal terminal = terminalService.selectByPrimaryKey(id);
        TerminalPosition t = terminalService.getTerminalCurrentPosition(id);
        if (CtUtils.isEmpty(t)) {
            TerminalPosition t1 = new TerminalPosition();
            t1.setLatitude(terminal.getLatitude());
            t1.setLongitude(terminal.getLongitude());
            return t1;
        }
        return t;
    }

    @RequestMapping(value = {"/nogroup/{groupId}"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public EUDataGridResult selectTerminalByNoGroupId(Terminal terminal, @PathVariable Integer groupId,
                                                      @RequestParam Integer page, @RequestParam Integer rows) {
        terminal.setTerminalGroupId(groupId.intValue());
        TerminalGroup terminalGroup = terminalGroupService.selectByPrimaryKey(groupId);

        User user = FrontEndHelper.getLoginUser();
        if (CtUtils.isEmpty(terminal.getInsId())) {
            terminal.setInsId(user.getInsId());
        }
        EUDataGridResult result = this.terminalService.selectTerminalByNoGroupId(terminal, page, rows);
        return result;
    }

    @RequestMapping(value = {"/basicApp"}, method = {org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public EUDataGridResult basicApp(TerminalApp terminalApp, @RequestParam Integer page, @RequestParam Integer rows) {
        EUDataGridResult result = this.terminalService.basicApp(terminalApp, page, rows);
        return result;
    }

    @RequestMapping(value = {"/app"}, method = {org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public EUDataGridResult selectApp(TerminalApp terminalApp, @RequestParam Integer page, @RequestParam Integer rows) {
        EUDataGridResult result = this.terminalService.selectApp(terminalApp, page, rows);
        return result;
    }


    @RequestMapping(value = {"/{id}"}, method = {org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public Terminal selectByPrimaryKey(@PathVariable Integer id) {
        User user = FrontEndHelper.getLoginUser();
        Map map = new HashMap();
        map.put("insId", user.getInsId());
        map.put("id", id);
        List list = this.terminalService.queryTermBelong(map);
        if (CtUtils.isEmpty(list)) {
            return null;
        }
        return this.terminalService.selectByPrimaryKey(id);
    }

    @RequestMapping(value = {"getOtpPwd/{termSeq}"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public Terminal getOtpPwd(@PathVariable String termSeq) {
        return null;
    }

    @RequestMapping(value = {"/sysDetail/{id}"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public TermSysDetail sysDetail(@PathVariable Integer id) {
        User user = FrontEndHelper.getLoginUser();
        Map map = new HashMap();
        map.put("insId", user.getInsId());
        map.put("id", id);
        List list = this.terminalService.queryTermBelong(map);
        if (CtUtils.isEmpty(list)) {
            return null;
        }
        return this.terminalService.sysDetail(id);
    }

    @RequestMapping(value = {"/getTerminalCount"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public TerminalCountInfo getTerminalCount() {
        User user = FrontEndHelper.getLoginUser();
        return this.terminalService.getTerminalCount(user.getInsId());
    }

    @RequestMapping(value = {""}, method = {org.springframework.web.bind.annotation.RequestMethod.PATCH})
    @ResponseBody
    public ResultMsg update(@RequestBody Terminal terminal) {
        return this.terminalService.update(terminal);
    }

	/*@RequestMapping(value = { "/reContactBinding" }, method = {
			org.springframework.web.bind.annotation.RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg reContactBinding(@RequestParam Integer id) {
		User user = FrontEndHelper.getLoginUser();
		Map map2 = new HashMap();
		map2.put("insId", user.getInsId());
		map2.put("id", id);
		List list = this.terminalService.queryTermBelong(map2);
		if (CtUtils.isEmpty(list)) {
			return null;
		}
		Map map = new HashMap();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		map.put("userName", user.getUsername());
		map.put("module", "终端管理");
		map.put("action", "终端重绑");
		map.put("opDesc", "终端进行重绑操作");
		map.put("opTime", sdf.format(new Date()));
		this.terminalService.addLog(map);
		return this.terminalService.reContactBinding(id);
	}*/

    @RequestMapping(value = {"/termSeqlModelDownload"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public String termMerModelDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=termSeqModel.xls");
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            InputStream inputStream = classLoader.getResourceAsStream("termSeqModel.xls");
            OutputStream os = response.getOutputStream();
            byte[] b = new byte[2048];
            int length;
            while ((length = inputStream.read(b)) > 0) {
                os.write(b, 0, length);
            }
            os.close();
            inputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = {"/termNoModelDownload"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public String termModelDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=termNoModel.xls");
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            InputStream inputStream = classLoader.getResourceAsStream("termNoModel.xls");
            OutputStream os = response.getOutputStream();
            byte[] b = new byte[2048];
            int length;
            while ((length = inputStream.read(b)) > 0) {
                os.write(b, 0, length);
            }
            os.close();
            inputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = {"/merchantNoModelDownload"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public String merchantNoModelDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=merchantNoModel.xls");
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            InputStream inputStream = classLoader.getResourceAsStream("merchantNoModel.xls");
            OutputStream os = response.getOutputStream();
            byte[] b = new byte[2048];
            int length;
            while ((length = inputStream.read(b)) > 0) {
                os.write(b, 0, length);
            }
            os.close();
            inputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /****
     * 进行终端商户变更
     * @param entity
     * @return
     */
    @RequestMapping(value = {"/changeMerchant"}, method = {RequestMethod.POST})
    @ResponseBody
    public ResultMsg changeMerchant(@RequestBody TerminalChangeMerchant entity) {
        return this.terminalService.changeMerchant(entity);
    }

    @RequestMapping(value = "/getCircleByTermSeq/{termSeq}", method = {RequestMethod.GET})
    @ResponseBody
    public TerminalCircle getCircleByTermSeq(@PathVariable String termSeq) {
        TerminalCircle terminalCircle = terminalService.getCircleByTermSeq(termSeq);
        return terminalCircle;

    }

    @RequestMapping(value = "/getCheckTerminalPosition/{termSeq}", method = {RequestMethod.GET})
    @ResponseBody
    public List<TerminalPosition> getCheckTerminalPosition(@PathVariable String termSeq) {
        String[] termSeqs = termSeq.split(",");
        List<TerminalPosition> list = new ArrayList();
        for (int i = 0; i < termSeqs.length; i++) {
            Terminal terminal = terminalService.queryTerminalInfoByTermSeq(termSeqs[i]);
            TerminalPosition t = terminalService.getCheckTerminalPosition(termSeqs[i]);
            if (CtUtils.isEmpty(t)) {
                TerminalPosition t1 = new TerminalPosition();
                t1.setLatitude(terminal.getLatitude());
                t1.setLongitude(terminal.getLongitude());
                list.add(t1);
            } else {
                list.add(t);
            }
        }
        return list;
    }

    @RequestMapping(value = "/getTerminalActPosition/{id}", method = {RequestMethod.GET})
    @ResponseBody
    public TerminalPosition getTerminalActPosition(@PathVariable Integer id) {
        Terminal terminal = terminalService.selectByPrimaryKey(id);
        TerminalPosition t1 = new TerminalPosition();
        t1.setLatitude(terminal.getLatitude());
        t1.setLongitude(terminal.getLongitude());
        return t1;
    }

    @RequestMapping(value = "/getTermPosiByTermSeq/{termSeq}", method = {RequestMethod.GET})
    @ResponseBody
    public TerminalRealTimeStatus getTermPosiByTermSeq(@PathVariable String termSeq) {
        TerminalRealTimeStatus terminalRealTimeStatus = new TerminalRealTimeStatus();
        TerminalRealTimeStatus terminalRealTimeStatusWhere = terminalService.getTermPosiByTermSeq(termSeq);
//		SysConfigure sysConfigure = sysConfigureService.selectByKey("isStartEle");
        Terminal terminal = terminalService.selectByTermSeq(termSeq);
        if (CtUtils.isEmpty(terminalRealTimeStatusWhere) || terminalRealTimeStatusWhere.getLatitude() == null) {
//			Terminal terminal = terminalService.selectByTermSeq(termSeq);
            terminalRealTimeStatus.setLongitude(terminal.getLongitude());
            terminalRealTimeStatus.setLatitude(terminal.getLatitude());
        } else {
            terminalRealTimeStatus.setLongitude(terminalRealTimeStatusWhere.getLongitude());
            terminalRealTimeStatus.setLatitude(terminalRealTimeStatusWhere.getLatitude());
        }
        Institution inst = institutionService.selectByInsId(terminal.getInsId());
        terminalRealTimeStatus.setPower(inst == null ? MonitorType.MONITOR_BY_CIRCLE.getCode() : inst.getMoveMonitorType());//借用此字段
        return terminalRealTimeStatus;
    }

    @RequestMapping(value = "/getTermByTermSeq/{termSeq}", method = {RequestMethod.GET})
    @ResponseBody
    public Terminal getTermByTermSeq(@PathVariable String termSeq) {
        return terminalService.selectByTermSeq(termSeq);
    }

    @RequestMapping(value = "/getTermPositionByTermSeq/{termSeq}", method = {RequestMethod.GET})
    @ResponseBody
    public TerminalRealTimeStatus getTermPositionByTermSeq(@PathVariable String termSeq) {
        TerminalRealTimeStatus terminalRealTimeStatus = new TerminalRealTimeStatus();
        terminalRealTimeStatus = terminalService.getTermPosiByTermSeq(termSeq);
        return terminalRealTimeStatus;
    }

    /**
     * 下载终端导入的模板
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = {"/termMerDownload"}, method = {
            org.springframework.web.bind.annotation.RequestMethod.GET})
    @ResponseBody
    public String termMerDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=termMerImport.xls");
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            InputStream inputStream = classLoader.getResourceAsStream("termMerImport.xls");
            OutputStream os = response.getOutputStream();
            byte[] b = new byte[2048];
            int length;
            while ((length = inputStream.read(b)) > 0) {
                os.write(b, 0, length);
            }
            os.close();
            inputStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = {"/uploadTermMerFile"}, method = {RequestMethod.POST})
    @ResponseBody
    public ResultMsg uploadTermMerFile(HttpServletRequest request, HttpServletResponse response){
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        List list = new ArrayList();
        int count = 0;
        try {
            SysConfigure sysConfigure = sysconfigureMapper.selectByKey("iotTerminal");
            User user = FrontEndHelper.getLoginUser();
            if (multipartResolver.isMultipart(request)) {
                MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                MultipartFile file = multiRequest.getFile("file");
                if (file != null) {
                    String myFileName = file.getOriginalFilename();

                    if (myFileName.trim() != "") {
                        Terminal terminal = new Terminal();
                        String type = myFileName.substring(myFileName.lastIndexOf(".") + 1, myFileName.length());
                        String[][] excel;
                        if ("xls".equals(type))
                            excel = ExcelUtil.readXls(file.getInputStream());
                        else {
                            excel = ExcelUtil.readXlsx(file.getInputStream());
                        }
                        if (excel.length > 0) {
                            List<Institution> insList = institutionService.getInstitutionTree(String.valueOf(user.getInsId()));
                            long begin = System.currentTimeMillis();
                            //this.logger.info("开始导入" + myFileName + "文件中的终端数据");
                            for (int i = 1; i < excel.length; i++) {
                                try {
                                    //存入终端序列号
                                    if (CtUtils.isEmpty(excel[i][0])) {
                                        //list.add("第" + (i + 1) + "行：终端序列号不得为空\r\n");
                                        list.add("the line" + (i + 1) + "：terminal Serial No. must not be empty \r\n");
                                        continue;
                                    }
                                    if (CtUtils.isEmpty(excel[i][1])) {
                                        //list.add("第" + (i + 1) + "行：终端厂商(编码)不得为空\r\n");
                                        list.add("the line" + (i + 1) + "：terminal manufacturer (code) must not be empty \r\n");
                                        continue;
                                    }
                                    if (CtUtils.isEmpty(excel[i][2])) {
                                        //list.add("第" + (i + 1) + "行：终端型号(编码)不得为空\r\n");
                                        list.add("the line" + (i + 1) + "：terminal model (code) must not be empty \r\n");
                                        continue;
                                    }
                                    if (CtUtils.isEmpty(excel[i][3])) {
                                        //list.add("第" + (i + 1) + "行：所属机构(编码)不得为空\r\n");
                                        list.add("the line" + (i + 1) + "：terminal organization (code) must not be empty\r\n");
                                        continue;
                                    } else {
                                        Terminal terminal1 = terminalService.selectByTermSeq(excel[i][0]);
                                        if (!CtUtils.isEmpty(terminal1)) {
                                            //list.add("第" + (i + 1) + "行：终端序列号为"+excel[i][0]+" 已经存在\r\n");
                                            list.add("the line" + (i + 1) + "：terminal Serial No. " + excel[i][0] + "already exists \r\n");
                                            continue;
                                        }
                                        terminal.setTermSeq(excel[i][0].trim());
                                    }
                                    //存入厂商id
                                    if (CtUtils.isEmpty(excel[i][1])) {
                                        terminal.setTermMfrId(0);
                                    } else {
                                        Factory factory = factoryService.queryByFaCode(excel[i][1]);
                                        if (CtUtils.isEmpty(factory)) {
                                            //list.add("第" + (i + 1) + "行：终端厂商(编码)不存在,请检查\r\n");
                                            list.add("the line " + (i + 1) + "terminal manufacturer (" + excel[i][1] + ") not exists \r\n");
                                            continue;
                                        } else {
                                            terminal.setTermMfrId(factory.getId());
                                        }

                                    }
                                    //存入厂商型号
                                    if (CtUtils.isEmpty(excel[i][2])) {
                                        terminal.setTermTypeCode("0");
                                    } else {
                                        terminal.setTermTypeCode(excel[i][2]);
                                    }
                                    //存入机构id
                                    String insCode = excel[i][3];
                                    if (CtUtils.isEmpty(insCode)) {
                                        terminal.setInsId(0);
                                    } else {
                                        for (Institution institution : insList) {
                                            if (institution.getCode().equals(insCode)) {
                                                terminal.setInsId(institution.getId());
                                                break;
                                            }
                                        }
                                    }
                                    //时区  不填，则为默认的服务器时间
                                    if (!CtUtils.isEmpty(excel[i][4])) {
                                        //判断时区格式是否正确
                                        if (!TimeUtil.isTimeZone(excel[i][4])) {
                                            list.add("the line " + (i + 1) + "corresponding time zone cannot be found \r\n");
                                            continue;
                                        } else {
                                            //存入时区
                                            terminal.setTimeZone(excel[i][4]);
                                        }
                                    }

                                    //备注（TID）  不填，则默认为空
                                    if (!CtUtils.isEmpty(excel[i][5])) {
                                        terminal.setRemark(excel[i][5]);
                                    }

                                    //存入必填字段
                                    terminal.setCreateTime(new Date());
                                    terminal.setStatus(0);
                                    terminal.setTerminalGroupId(0);

                                    fixedThreadPool.submit(new Runnable() {
                                        @Override
                                        public void run() {
                                            sendTerminalToIot(terminal, sysConfigure.getValue());
                                        }
                                    });

                                    terminalService.insertTerminalInfo(terminal);
                                    count++;
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    //list.add("第" + (i + 1) + "行：终端序列号为" + excel[i][0] + " 数据有错\r\n");
                                    list.add("the line " + (i + 1) + "： wrong data \r\n");
                                    //this.logger.info("第" + (i + 1) + "行：终端序列号为" + excel[i][0] + " 数据有错");
                                }

                            }

                            //long end = System.currentTimeMillis();
                            //this.logger.info("成功导入" + count + "条记录,共用时:" + (end - begin) / 1000L + "秒");
                            //list.add(0, "成功导入" + count + "条记录\r\n");
                            //list.add(1, "共用时:" + (end - begin) / 1000L + "秒\r\n");
                            list.add(0, "upload total " + count + " record successful \r\n");
                            //list.add(1, "共用时:" + (end - begin) / 1000L + "秒\r\n");
                        }
                    }
                }
            }
            //return ResultMsg.build(Integer.valueOf(ResultMsg.SUCCESS_CODE), "成功导入" + count + "条数据", list);
            return ResultMsg.build(Integer.valueOf(ResultMsg.SUCCESS_CODE), "total upload" + count + " data", list);
        }catch(Exception e){
            e.printStackTrace();
            return ResultMsg.fail();
        }
    }
    
    
    
    private boolean sendTerminalToIot(Terminal terminal,String iotUrl){
        JSONObject  requestJson = new JSONObject();
        requestJson.putOpt("version", "1.0.0");
        requestJson.putOpt("cid", Config.CID);
        requestJson.putOpt("opt", "001");
        requestJson.putOpt("sn", terminal.getTermSeq());
        requestJson.putOpt("producer", terminal.getTermMfrName());
        requestJson.putOpt("model", terminal.getTermTypeCode());
        requestJson.putOpt("random", CtUtils.getRandom(6));
        Map requetMap = JSONUtil.toBean(requestJson, Map.class);
        String sign = null;
        try {
            sign = SignUtil.generateSignature(requetMap, Config.IOT_KEY, "HMAC-SHA256");
        } catch (Exception e) {

        }
        requestJson.putOpt("sign", sign);
        String request = requestJson.toString();
        logger.info("terminal request:{}", request);

        String response = HttpUtil.post(iotUrl , request);
        logger.info("terminal response:{}", response);

        if (response == null || "".equals(response)) {
            return false;
        }

        OperationResponse operationResponse = JSONUtil.toBean(response, OperationResponse.class);
        if (operationResponse == null) {
            return false;
        }

        if(OperationResponseStatusEnum.SUCCESS.getCode().equals(operationResponse.getStatus())) {
            return true;
        }
       
        return false;
        
    }
}