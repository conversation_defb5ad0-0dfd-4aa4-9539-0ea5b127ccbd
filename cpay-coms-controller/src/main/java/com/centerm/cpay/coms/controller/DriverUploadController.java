
package com.centerm.cpay.coms.controller;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.centerm.cpay.common.utils.*;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.enums.FileUploadStrategyEnum;
import com.centerm.cpay.coms.factory.StorageModeFactory;
import com.centerm.cpay.coms.service.*;
import com.centerm.cpay.coms.util.*;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import com.centerm.cpay.coms.util.xmlUtils.Config;
import com.centerm.cpay.coms.util.xmlUtils.Item;
import com.centerm.cpay.coms.util.xmlUtils.XmlToBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.apkstore.dao.pojo.ApkTemporary;

/**
 * @Title: OperInfController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年5月22日 上午3:11:10
 * @version V1.0
 */
@Controller
@RequestMapping("/driverUpload")
public class DriverUploadController {
	@Autowired
	private DriverUploadService driverUploadService;
	@Autowired
	private InstitutionService institutionService;
	@Autowired
	private DriverService driverService;
	@Autowired
	private AppVersionService appVersionService;
	@Autowired
	private SysConfigureService configureService;
	@Autowired
	private DriverJobService driverJobService;

	@Resource
	private StorageModeFactory storageModeFactory;

	String ngsPicPath = "";
	String filesavepath = FilePathUtil.getrelativePath("appsystempath");
	String fileheadpath =  ConfigInfo.HEADER_PATH;
	String temDir = FilePathUtil.getTempDir() + "/";
	String isFastFds =ConfigInfo.IS_FASTDFS;
	String unzipRootDir = ConfigInfo.FILE_UPLOAD_TEMP;
	String settingsHeader = ConfigInfo.FILE_SETTING_HEADER;
	Logger logger = LoggerFactory.getLogger(DriverUploadController.class);

	@RequestMapping(value = "", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getDriverUploadList(ComsAppVersion entity, @RequestParam Integer page,
												@RequestParam Integer rows) {
		EUDataGridResult result = driverUploadService.getGridList(entity, page, rows);
		return result;
	}

	@RequestMapping(value = "/getBelow", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getDriverUploadListBelow(ComsAppVersion entity, @RequestParam Integer page,
													 @RequestParam Integer rows) {
		EUDataGridResult result = new EUDataGridResult();
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		entity.setNgsPicPath(ngsPicPath);
		if (entity.getAppId() != null) {
			result = driverUploadService.getGridList(entity, page, rows);
		}
		return result;
	}

	@RequestMapping(value = "/uploadFile", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadFile(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 记录上传过程起始时的时间，用来计算上传时间
				int pre = (int) System.currentTimeMillis();
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// System.out.println(myFileName);
						// 重命名上传后的文件名
						String fileName = UUID.randomUUID()
								+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						// 定义上传路径
						path = fileheadpath + filesavepath + fileName;
						// 路径和原文件名
						File localFile = new File(path);
						file.transferTo(localFile);
						path = filesavepath + fileName + "," + file.getOriginalFilename();
					}
				}
				// 记录上传该文件后的时间
				// int finaltime = (int) System.currentTimeMillis();
				// System.out.println(finaltime - pre);
			}
		}
		if (path.trim() != "") {
			return ResultMsg.success(path);
		}
		return ResultMsg.fail();
	}

	@RequestMapping(value = "/uploadDriver", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadDriver(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		User user = FrontEndHelper.getLoginUser();
		ngsPicPath = configureService.queryValueByKey("download_url_intranet");
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";

		String AppInfType = request.getParameter("type");
		String[] fileSource = new String[12];
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 记录上传过程起始时的时间，用来计算上传时间
				int pre = (int) System.currentTimeMillis();
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// System.out.println(myFileName);
						// 重命名上传后的文件名
						String uuidStr = UUID.randomUUID()+"";
						String fileName = uuidStr
								+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						// 定义上传路径
						path = temDir + fileName;
						// 路径和原文件名
						File localFile = new File(path);
						file.transferTo(localFile);
						//path = temDir + "," + file.getOriginalFilename();
						String fileType = myFileName.substring(myFileName.lastIndexOf(".") + 1);

						if (fileType.equals("apk")) {
							ApkTemporary apkInf = new ApkTemporary();
							try {
								apkInf = ApkUtil.decryptApp(localFile.getPath());
								logger.info("apk存储路径L:" + temDir + fileName);
								fileSource[0] = apkInf.getAppName();
								fileSource[1] = apkInf.getAppCode();
								Pattern p=Pattern.compile("([\\w.]+)");
								Matcher m=p.matcher(fileSource[1]);
								if(!m.matches()){
									return new ResultMsg(6,"Software code supports English, number, '.' and '_' only",null);
								}
								fileSource[2] = apkInf.getAppVersion();
								fileSource[3] = temDir + fileName;
								fileSource[4] = fileName;
								//存外部版本
								fileSource[7] = apkInf.getAppVersionName();
								//保存图片到临时路径
								FileInfo fileInfo = null;

								File iconFile = ApkUtil.getIconFileFromApk(localFile.getPath(), apkInf.getIcon_path());
								if(!isFastFds.equals("true")){
									logger.info("开始进行传统文件上传模式进行图标上传");
									String uuid = CtUtils.uuid();
									String saveIconPath =filesavepath + CtUtils.getCurrentTime("yyyyMMdd")+"/";
									String newIconName = uuid + "."+apkInf.getIcon_path().substring(apkInf.getIcon_path().lastIndexOf(".") + 1);

									String newIconPath = fileheadpath + saveIconPath;
									ApkUtil.fileCopy(newIconPath,newIconName, iconFile.getPath());
									logger.info("图片原地址："+iconFile.getPath());
									logger.info("图片保存地址："+newIconPath+ newIconName);
									fileSource[5] = saveIconPath+newIconName;
									fileSource[6] = ngsPicPath;
									apkInf.setIconId(uuid);
									apkInf.setIcon_path(saveIconPath+newIconName);
								}else{
									logger.info("开始进行分布式文件上传模式进行图标上传");
									fileInfo = ApkFastFdsUtil.uploadFileByFastFds(iconFile);
									logger.info("图片原地址："+iconFile.getPath());
									logger.info("图片保存地址："+fileInfo.getFileSavePath());
									fileSource[5] = fileInfo.getFileSavePath();
									fileSource[6] = ngsPicPath;
									apkInf.setIconId(fileInfo.getUuid());
									apkInf.setIcon_path(fileInfo.getFileSavePath());
								}

							} catch (Exception e) {
								e.printStackTrace();
							}
						} else {
							fileSource[0] = myFileName.substring(0, file.getOriginalFilename().lastIndexOf("."));
							fileSource[1] = myFileName.substring(0, file.getOriginalFilename().lastIndexOf("."));
							Pattern p=Pattern.compile("([\\w.]+)");
							Matcher m=p.matcher(fileSource[1]);
							if(!m.matches()){
								return new ResultMsg(6,"Software code supports English, number, '.' and '_' only",null);
							}
							fileSource[3] = temDir + fileName;
							fileSource[4] = fileName;
							fileSource[5] = "";//默认图标
							if(AppInfType.equals("3")||AppInfType.equals("2")){
								String unzipDir = unzipRootDir+"/"+uuidStr;
								logger.info("解压系统镜像路径："+unzipDir);
								logger.info("待解压文件："+fileName);
								ZipUtil.unzip(temDir + fileName,unzipDir);
								try {
									//获取解压文件的settings信息
									Config config = XmlToBean.getConfig(unzipDir+"/"+settingsHeader);
									if(CtUtils.isEmpty(config)){
										return ResultMsg.build(ResultMsg.ERROR_CODE, "File error, parsing failed (configuration file does not exist)");
									}

									List<Item> items = config.getItems().getItems();
									if(CtUtils.isEmpty(config.getVersion())
											||CtUtils.isEmpty(config.getVersionCode())){
										logger.info("settings.xml文件config参数缺失");
										return ResultMsg.build(ResultMsg.ERROR_CODE, "File error, parsing failed (missing profile parameter)");
									}

									logger.info("系统镜像差分包文件个数"+items.size());
									for(Item item : items){
										if(CtUtils.isEmpty(item.getFilename())
												||CtUtils.isEmpty(item.getVersion())
												||CtUtils.isEmpty(item.getMd5())){
											logger.info("settings.xml文件参数有误");
											return ResultMsg.build(ResultMsg.ERROR_CODE, "File error, parsing failed (missing profile parameter)");
										}
										File file1 = new File(unzipDir+"/"+item.getFilename());
										if(CtUtils.isEmpty(file1)
												|| !FileMD5.getFileMD5String(file1).equalsIgnoreCase(item.getMd5())){
											logger.info("系统镜像差分包文件有误");
											return ResultMsg.build(ResultMsg.ERROR_CODE, "File error, parsing failed(file package does not exist or md5 validation failed)");
										}
									}
									fileSource[2] = config.getVersionCode();
									fileSource[7] = config.getVersion();
								} catch (Exception e) {
									return ResultMsg.build(ResultMsg.ERROR_CODE, "File error, parsing failed");
								}
							}
						}
						//存上传文件名称
						fileSource[8] = myFileName;
						/**
						 * 获取数据库中该软件的信息
						 * 将dcc、间直连信息返回到前端
						 */
						ComsAppInf record = new ComsAppInf();
						record.setCode(fileSource[1]);
						record.setInsId(user.getInsId());
						record.setType(AppInfType);
						ComsAppInf comsAppInf = appVersionService.getComsAppInf(record);
						if(!CtUtils.isEmpty(comsAppInf)){
							fileSource[9] = comsAppInf.getDccSupFlag();
							fileSource[10] = comsAppInf.getCupConnMode();
							//存在该软件记录，返回"T"
							fileSource[11] = "T";
						}else{
							fileSource[11] = "F";
						}
					}
				}
				// 记录上传该文件后的时间
				// int finaltime = (int) System.currentTimeMillis();
				// System.out.println(finaltime - pre);
			}
		}
		if (path.trim() != "") {
			return ResultMsg.success(fileSource);
		}
		return ResultMsg.fail();
	}

	/**
	 * uploadRom上传定制ROM
	 * @param
	 * @param response
	 * @return
	 * @throws IllegalStateException
	 * @throws IOException
	 */

	@RequestMapping(value = "/uploadRom", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg uploadRom(HttpServletRequest request, HttpServletResponse response)
			throws IllegalStateException, IOException {
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
				request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		String path = "";
		String[] fileSource = new String[5];
		if (multipartResolver.isMultipart(request)) {
			// 转换成多部分request
			MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
			// 取得request中的所有文件名
			Iterator<String> iter = multiRequest.getFileNames();
			while (iter.hasNext()) {
				// 记录上传过程起始时的时间，用来计算上传时间
				int pre = (int) System.currentTimeMillis();
				// 取得上传文件
				MultipartFile file = multiRequest.getFile(iter.next());
				if (file != null) {
					// 取得当前上传文件的文件名称
					String myFileName = file.getOriginalFilename();
					// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
					if (myFileName.trim() != "") {
						// 重命名上传后的文件名
						String fileName = UUID.randomUUID()
								+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
						// 定义上传路径
						path = temDir + fileName;
						// 路径和原文件名
						File localFile = new File(path);
						file.transferTo(localFile);
						path = temDir + "," + file.getOriginalFilename();

						if (myFileName.indexOf("-") != -1) {
							fileSource[0] = myFileName.substring(0, file.getOriginalFilename().lastIndexOf("."));
							fileSource[1] = "component.os.rom";
							fileSource[2] = myFileName.substring(8, file.getOriginalFilename().lastIndexOf("."));
							fileSource[3] = temDir + fileName;
							fileSource[4] = fileName;
						} else {
							fileSource[0] = myFileName.substring(0, file.getOriginalFilename().lastIndexOf("."));
							fileSource[1] = "full.os.rom";
							fileSource[2] = myFileName.substring(8, file.getOriginalFilename().lastIndexOf("."));
							fileSource[3] = temDir + fileName;
							fileSource[4] = fileName;
						}
					}
				}
			}
		}
		if (path.trim() != "") {
			return ResultMsg.success(fileSource);
		}
		return ResultMsg.fail();
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteById(@PathVariable Integer id) {
		//注意 driverUploadService的方法命名和mapper调用存在不一致的情况
		//返回id 对应的应用版本信息
		ComsAppVersion comsAppVersion = driverUploadService.queryByJobId(id);
		DriverJob driverJob = new DriverJob();
		ComsAppInf comsAppInf =driverService.selectByPrimaryKey(comsAppVersion.getAppId());
		driverJob.setAppCode(comsAppInf.getCode());
		driverJob.setAppVersion(comsAppVersion.getAppVersion());
		driverJob.setAppId(comsAppVersion.getAppId());
		if(driverJobService.countWorkingJob(driverJob) > 0) {
			return ResultMsg.build(2,"The software is in a remote update and cannot be deleted");
		}
		List<ComsAppVersion> list = driverUploadService.selectByPrimaryKey(comsAppVersion.getAppId());
		//当对应的文件在表中仅剩一行信息的时候，可以删除文件存储系统中该文件。否则不删除

		storageModeFactory.deleteExistedFile(comsAppVersion);
		if(list.size()== 1){
			driverService.deleteById(comsAppVersion.getAppId());
			return ResultMsg.build(0,"All versions of the software have been removed");
		}else{
			driverUploadService.deleteById(id);
			return ResultMsg.build(1,"Software version "+comsAppVersion.getAppVersion()+"have deleted");
		}

	}

	@RequestMapping(value = "", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteByIds(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}

		return driverUploadService.deleteByIds(idsList);
	}

	@RequestMapping(value = "", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg update(@RequestBody ComsAppVersion entity) {
		if (entity.getAppPath() != null) {
			String md5 = "";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			String time = sdf.format(new Date());
			String filePath = fileheadpath + entity.getAppPath();
			try {
				md5 = FileMD5.getFileMD5String(fileheadpath + entity.getAppPath());
				if (!md5.isEmpty() && !md5.equals(entity.getMd5())) {
					entity.setMd5(md5);
				}
				entity.setMd5(FileMD5.getFileMD5String(filePath));
			} catch (IOException e) {
				e.printStackTrace();
			}
			ComsAppInf comsAppInf = driverService.selectByPrimaryKey(entity.getAppId());
			if (comsAppInf.getType().equals("1") || comsAppInf.getType().equals("0")
					|| comsAppInf.getType().equals("6")) {
				ApkUtil apkUtil = new ApkUtil();
				ApkTemporary apkInf = new ApkTemporary();
				try {
					apkInf = apkUtil.decryptApp(filePath);
					logger.info(apkInf.getAppCode() + "---" + apkInf.getAppVersion());
				} catch (Exception e) {
					e.printStackTrace();
				}

				entity.setAppVersion(apkInf.getAppVersion());
				entity.setAppCode(apkInf.getAppCode());
			} else if (comsAppInf.getType().equals("2")) {
				entity.setAppVersion("sysDriverDefVer" + time);
			}
			if (comsAppInf.getType().equals("3")) {
				entity.setAppVersion("osDefVer" + time);
			}
			entity.setOperFlag("1");
			entity.setType(comsAppInf.getType());
		}
		return driverUploadService.updateByEntity(entity);
	}
	@RequestMapping(value = "{publish}", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg publish(@PathVariable String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return driverUploadService.deleteByIds(idsList);
	}

	@RequestMapping(value = "/driverUploadAdd", method = { RequestMethod.POST })
	@ResponseBody
	public ResultMsg driverUploadAdd(@RequestBody ComsAppVersion entity) {
		entity.setInsId(Objects.requireNonNull(FrontEndHelper.getLoginUser()).getInsId());
		return storageModeFactory.getUploadStrategy(entity.getUploadStrategy()).uploadDriverFile(entity);
	}

	@RequestMapping(value = "/driverUploadupdate", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg driverUploadupdate(@RequestBody ComsAppVersion entity) {
		if(!appVersionService.getComsAppVersion(entity)){
			return new ResultMsg(6,"The software version already exists under the current vendor",null);
		}
		return driverUploadService.driverUploadupdate(entity);
	}

	@RequestMapping(value = "/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public List<ComsAppVersion> selectByPrimaryKey(@PathVariable Integer id) {
		return driverUploadService.selectByPrimaryKey(id);
	}
	@RequestMapping(value = "/getAppVersions", method = { RequestMethod.GET })
	@ResponseBody
	public List<ComsAppVersion> getAppVersions(ComsAppVersion entity) {
		return driverUploadService.getAppVersions(entity);
	}
	@RequestMapping(value = "/queryByJobId/{id}", method = { RequestMethod.GET })
	@ResponseBody
	public ComsAppVersion queryByJobId(@PathVariable Integer id) {
		ComsAppVersion ComsAppVersion = driverUploadService.queryByJobId(id);
		return ComsAppVersion;
	}

	@RequestMapping(value = "/updateDriverUpload", method = { RequestMethod.PATCH })
	@ResponseBody
	public ResultMsg updateDriverUpload(@RequestBody ComsAppVersion entity) {

		return driverUploadService.updateDriverUpload(entity);
	}
}