
package com.centerm.cpay.coms.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.common.Constants.Constants;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.DriverJob;
import com.centerm.cpay.coms.dao.pojo.DriverJobTask;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.DriverJobService;
import com.centerm.cpay.coms.util.FrontEndHelper;
import com.centerm.cpay.coms.util.SessionInfo;

/**
 * 任务调度-Launcher参数下发控制类
 * 
 * @Title: DriverJobController.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年7月6日 上午
 * @version V2.0
 */
@Controller
@RequestMapping("/apkPush")
public class ApkPushController {
	Logger logger = LoggerFactory.getLogger(ApkPushController.class);
	@Autowired
	private DriverJobService driverJobService;

	@RequestMapping(value = "/apkPush", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getApkPushList(DriverJob driverJob, @RequestParam Integer page,
			@RequestParam Integer rows, HttpSession session) {
		User user = FrontEndHelper.getLoginUser();
		logger.info("---InsId---" + user.getInsId());
		driverJob.setJobType("0");
		driverJob.setReleaseIns(user.getInsId());
		EUDataGridResult result = driverJobService.getDriverJobList(driverJob, page, rows);
		return result;
	}
	@RequestMapping(value = "/apkPushDel", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getApkPushDelList(DriverJob driverJob, @RequestParam Integer page,
			@RequestParam Integer rows, HttpSession session) {
		User user = FrontEndHelper.getLoginUser();
		logger.info("---InsId---" + user.getInsId());
		driverJob.setJobType("4");
		driverJob.setReleaseIns(user.getInsId());
		EUDataGridResult result = driverJobService.getDriverJobList(driverJob, page, rows);
		return result;
	}
	@RequestMapping(value = "/getBootConfList", method = { RequestMethod.GET })
	@ResponseBody
	public EUDataGridResult getBootConfList(DriverJobTask driverJobTask, @RequestParam Integer page,
			@RequestParam Integer rows) {
		User user = FrontEndHelper.getLoginUser();
		Map map = new HashMap();
		map.put("termSeq", driverJobTask.getTermSeq());
		map.put("appCode", driverJobTask.getAppCode());
		map.put("dlFlag", driverJobTask.getDlFlag());
		map.put("insId", user.getInsId());
		EUDataGridResult result = driverJobService.getBootConfList(map, page, rows);
		return result;
	}
	@RequestMapping(value = "/issuedAgain", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg issuedAgain(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return driverJobService.issuedAgain(idsList);
	}

	@RequestMapping(value = "/issuedCancel", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg issuedCancel(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return driverJobService.issuedCancel(idsList);
	}
	@RequestMapping(value = "/deleteApkPush", method = { RequestMethod.DELETE })
	@ResponseBody
	public ResultMsg deleteApkPush(@RequestParam String ids) {
		List<String> idsList = new ArrayList<>();
		String[] idsArray = ids.split(",");
		for (int i = 0; i < idsArray.length; i++) {
			idsList.add(idsArray[i]);
		}
		return driverJobService.deleteApkPush(idsList);
	}
	
}
