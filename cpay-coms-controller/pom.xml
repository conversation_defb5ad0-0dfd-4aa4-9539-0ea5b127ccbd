<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.centerm</groupId>
		<artifactId>cpay-coms</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>cpay-coms-controller</artifactId>
	<!-- 依赖管理 -->
	<dependencies>
		<!-- <dependency> <groupId>com.centerm</groupId> <artifactId>cpay-sms-facade</artifactId> 
			<version>0.0.1-SNAPSHOT</version> </dependency> -->
		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-system-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-developer-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-cas-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-terminal-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!-- <dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-afterSale-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency> -->

		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-taskScheduling-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>coms-apkstore-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!-- <dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-theme-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency> -->
		<!-- Spring -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<!-- 单元测试 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- JSP相关 -->
		<dependency>
			<groupId>jstl</groupId>
			<artifactId>jstl</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jsp-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<!-- 文件上传组件 -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.2</version>
		</dependency>
		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-flow-service</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!-- dubbo 需要的jar start -->
		<!-- <dependency> <groupId>com.alibaba</groupId> <artifactId>dubbo</artifactId> 
			</dependency> <dependency> <groupId>org.apache.zookeeper</groupId> <artifactId>zookeeper</artifactId> 
			</dependency> <dependency> <groupId>com.github.sgroschupf</groupId> <artifactId>zkclient</artifactId> 
			</dependency> <dependency> <groupId>com.esotericsoftware.kryo</groupId> <artifactId>kryo</artifactId> 
			</dependency> <dependency> <groupId>de.javakaffee</groupId> <artifactId>kryo-serializers</artifactId> 
			</dependency> -->
		<!-- dubbo 需要的jar end -->

		<dependency>
			<groupId>de.schlichtherle.truelicense</groupId>
			<artifactId>truelicense-core</artifactId>
			<version>1.33</version>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>javax.el-api</artifactId>
			<version>2.2.4</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish.web</groupId>
			<artifactId>javax.el</artifactId>
			<version>2.2.4</version>
		</dependency>
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.5.0-b01</version>
		</dependency>
		<dependency>
			<groupId>sign</groupId>
			<artifactId>signapi</artifactId>
			<version>1.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-bindings-soap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-mail</artifactId>
			<version>4.3.1.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.druid</groupId>
			<artifactId>druid-wrapper</artifactId>
			<version>0.2.7</version>
		</dependency>
		<dependency>
		    <groupId>org.quartz-scheduler</groupId>
		    <artifactId>quartz</artifactId>
		    <version>2.2.1</version>
		</dependency>
	</dependencies>
	<distributionManagement>
		<snapshotRepository>
			<id>cpay-snapshots</id>
			<name>cpay-snapshots Repository</name>
			<url>http://192.168.48.66:8081/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
</project>