package com.centerm.cpay.common.service.impl;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.service.ValidationBySignService;
import com.centerm.cpay.common.utils.CommUtil;
import com.centerm.cpay.common.utils.JsonUtils;
import com.centerm.cpay.common.utils.MyRSAUtils;
import com.centerm.cpay.cpay.security.CentermTermPublicKey;
import com.centerm.cpay.cpay.security.ServerPriviteKey;
import com.centerm.cpay.security.utils.ByteUtil;

@Service
public class ValidationBySignServiceImpl implements ValidationBySignService {

	private static final Logger logger = LoggerFactory.getLogger(ValidationServiceImpl.class);

	@Autowired
	public ServerPriviteKey serverPriviteKey;

	public static final String ALGORITHM_SHA256WITHRSA = "sha256withRSA";

	/**
	 * 报文头校验
	 */
	@Override
	public ResultMsg validtateRequestMsgBySign(Map<String, String> map, String requestSign,String pubKey) throws Exception {

		String originalData = CommUtil.createLinkString(map);
		logger.debug("【云POS运维服务中心】报文头校验模块,&字符拼接字符：" + CommUtil.createLinkString(map));
		PublicKey publicKey = MyRSAUtils.getPublicKey(pubKey);
		boolean isRight = MyRSAUtils.verifyByPublicKey(publicKey, ByteUtil.hexStringToByte(originalData),
				ByteUtil.hexStringToByte(requestSign), ALGORITHM_SHA256WITHRSA	);// 开启验签
		if (isRight) {
			return ResultMsg.success();
		} else {
			return ResultMsg.build(ResultMsg.FAIL_CODE, "报文签名验证失败");
		}
		//return ResultMsg.success();
	}

	@Override
	public ResponseMsg rsponseMsgSetSign(ResponseMsg responseMsg) {

		PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();
		String jsonStr = JsonUtils.objectToJson(responseMsg);
		logger.debug("【云POS运维服务中心】json to string" + jsonStr);
		logger.debug("【云POS运维服务中心】应答报文termSeq：" + responseMsg.getHeader().getDevMask());
		Map<String, String> tempMap = JsonUtils.jsonToMap(jsonStr);
		logger.debug("【云POS运维服务中心】应答报文sign生成模块，mapString：" + tempMap.toString());
		String originalData = CommUtil.createLinkString(tempMap);
		logger.debug("【云POS运维服务中心】应答报文sign生成模块，排序后转为&字符数据：" + originalData);

		byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(originalData),
				ALGORITHM_SHA256WITHRSA); 
		logger.debug("【云POS运维服务中心】返回报文sign生成模块，sign：" + ByteUtil.bytesToHexString(signBytes));
		responseMsg.setSign(ByteUtil.bytesToHexString(signBytes));
		return responseMsg;
	}

	@Override
	public ResponseMsg rsponseMsgSetSign(ResponseMsg responseMsg, PrivateKey PrivateKey) {
		String jsonStr = JsonUtils.objectToJson(responseMsg);
		logger.debug("【云POS运维服务中心】json to string" + jsonStr);
		logger.debug("【云POS运维服务中心】应答报文termSeq：" + responseMsg.getHeader().getDevMask());
		Map<String, String> tempMap = JsonUtils.jsonToMap(jsonStr);
		logger.debug("【云POS运维服务中心】应答报文sign生成模块，mapString：" + tempMap.toString());
		String originalData = CommUtil.createLinkString(tempMap);
		logger.debug("【云POS运维服务中心】应答报文sign生成模块，排序后转为&字符数据：" + originalData);

		byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(originalData),
				ALGORITHM_SHA256WITHRSA);
		logger.debug("【云POS运维服务中心】返回报文sign生成模块，sign：" + ByteUtil.bytesToHexString(signBytes));
		responseMsg.setSign(ByteUtil.bytesToHexString(signBytes));
		return responseMsg;
	}
}
