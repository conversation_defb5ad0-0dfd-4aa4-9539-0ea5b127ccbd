<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.common.dao.mapper.TerminalWkeyMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.common.dao.pojo.TerminalWkey" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="tmk" property="tmk" jdbcType="CHAR" />
    <result column="tmk_checkvalue" property="tmkCheckvalue" jdbcType="CHAR" />
    <result column="pik" property="pik" jdbcType="CHAR" />
    <result column="pik_checkvalue" property="pikCheckvalue" jdbcType="CHAR" />
    <result column="mak" property="mak" jdbcType="CHAR" />
    <result column="mak_checkvalue" property="makCheckvalue" jdbcType="CHAR" />
    <result column="tdk" property="tdk" jdbcType="CHAR" />
    <result column="tdk_checkvalue" property="tdkCheckvalue" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, term_seq, tmk, tmk_checkvalue, pik, pik_checkvalue, mak, mak_checkvalue, tdk, 
    tdk_checkvalue, create_time, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.centerm.cpay.common.dao.pojo.TerminalWkeyExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from cpay_terminal_wkey
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from cpay_terminal_wkey
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from cpay_terminal_wkey
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.common.dao.pojo.TerminalWkey" >
    insert into cpay_terminal_wkey (term_seq, tmk, 
      tmk_checkvalue, pik, pik_checkvalue, 
      mak, mak_checkvalue, tdk, tdk_checkvalue, 
      create_time, update_time)
    values (#{termSeq,jdbcType=VARCHAR}, #{tmk,jdbcType=CHAR}, 
      #{tmkCheckvalue,jdbcType=CHAR}, #{pik,jdbcType=CHAR}, #{pikCheckvalue,jdbcType=CHAR}, 
      #{mak,jdbcType=CHAR}, #{makCheckvalue,jdbcType=CHAR}, #{tdk,jdbcType=CHAR}, #{tdkCheckvalue,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.common.dao.pojo.TerminalWkey" >
    insert into cpay_terminal_wkey
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="tmk != null" >
        tmk,
      </if>
      <if test="tmkCheckvalue != null" >
        tmk_checkvalue,
      </if>
      <if test="pik != null" >
        pik,
      </if>
      <if test="pikCheckvalue != null" >
        pik_checkvalue,
      </if>
      <if test="mak != null" >
        mak,
      </if>
      <if test="makCheckvalue != null" >
        mak_checkvalue,
      </if>
      <if test="tdk != null" >
        tdk,
      </if>
      <if test="tdkCheckvalue != null" >
        tdk_checkvalue,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="tmk != null" >
        #{tmk,jdbcType=CHAR},
      </if>
      <if test="tmkCheckvalue != null" >
        #{tmkCheckvalue,jdbcType=CHAR},
      </if>
      <if test="pik != null" >
        #{pik,jdbcType=CHAR},
      </if>
      <if test="pikCheckvalue != null" >
        #{pikCheckvalue,jdbcType=CHAR},
      </if>
      <if test="mak != null" >
        #{mak,jdbcType=CHAR},
      </if>
      <if test="makCheckvalue != null" >
        #{makCheckvalue,jdbcType=CHAR},
      </if>
      <if test="tdk != null" >
        #{tdk,jdbcType=CHAR},
      </if>
      <if test="tdkCheckvalue != null" >
        #{tdkCheckvalue,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.common.dao.pojo.TerminalWkey" >
    update cpay_terminal_wkey
    <set >
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="tmk != null" >
        tmk = #{tmk,jdbcType=CHAR},
      </if>
      <if test="tmkCheckvalue != null" >
        tmk_checkvalue = #{tmkCheckvalue,jdbcType=CHAR},
      </if>
      <if test="pik != null" >
        pik = #{pik,jdbcType=CHAR},
      </if>
      <if test="pikCheckvalue != null" >
        pik_checkvalue = #{pikCheckvalue,jdbcType=CHAR},
      </if>
      <if test="mak != null" >
        mak = #{mak,jdbcType=CHAR},
      </if>
      <if test="makCheckvalue != null" >
        mak_checkvalue = #{makCheckvalue,jdbcType=CHAR},
      </if>
      <if test="tdk != null" >
        tdk = #{tdk,jdbcType=CHAR},
      </if>
      <if test="tdkCheckvalue != null" >
        tdk_checkvalue = #{tdkCheckvalue,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.common.dao.pojo.TerminalWkey" >
    update cpay_terminal_wkey
    set term_seq = #{termSeq,jdbcType=VARCHAR},
      tmk = #{tmk,jdbcType=CHAR},
      tmk_checkvalue = #{tmkCheckvalue,jdbcType=CHAR},
      pik = #{pik,jdbcType=CHAR},
      pik_checkvalue = #{pikCheckvalue,jdbcType=CHAR},
      mak = #{mak,jdbcType=CHAR},
      mak_checkvalue = #{makCheckvalue,jdbcType=CHAR},
      tdk = #{tdk,jdbcType=CHAR},
      tdk_checkvalue = #{tdkCheckvalue,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByTermSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from cpay_terminal_wkey
    where term_seq = #{termSeq,jdbcType=VARCHAR}
  </select>
  
</mapper>