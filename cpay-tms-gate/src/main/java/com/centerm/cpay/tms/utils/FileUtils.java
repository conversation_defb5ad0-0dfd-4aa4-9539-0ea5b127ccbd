package com.centerm.cpay.tms.utils;

import java.io.*;

/**
 * <AUTHOR>
 * 
 *         TODO To change the template for this generated type comment go to
 *         Window - Preferences - Java - Code Style - Code Templates
 */
public class FileUtils {

	private static final int CACHE_SIZE = 1024;// 文件读取缓冲区大小

	/**
	 * 文件是否存在
	 * 
	 * @param fileName
	 * @return
	 */
	public static boolean isExists(String fileName) {
		return new File(fileName).exists();
	}

	/**
	 * 文件是否存在
	 * 
	 * @param fileName
	 * @return
	 */
	public static long getLength(String fileName) {
		return new File(fileName).length();
	}

	/**
	 * 删除文件
	 * 
	 * @param filePath
	 * @return
	 */
	public static boolean deleteFile(String filePath) {
		File curFile = new File(filePath);
		int i = 0;
		for (; i < 20 && !curFile.delete(); i++) {
			try {
				System.gc();
				Thread.sleep(10);
			} catch (InterruptedException e) {

			}
		}
		return i < 20;
	}

	/**
	 * 当目录为空时删除目录
	 * 
	 * @param dir
	 * @return
	 */
	public static boolean deleteDirectoryIfEmpty(String dir) {
		return new File(dir).delete();
	}

	/**
	 * 删除目录
	 * 
	 * @param dir
	 * @return
	 */
	public static boolean deleteDirectory(String dir) {
		return deleteDirectory(new File(dir), true);
	}

	/**
	 * 删除目录,递归函数
	 * 
	 * @param dir
	 * @return
	 */
	private static boolean deleteDirectory(File dir, boolean deleteSelf) {
		if (dir.exists()) {
			File[] files = dir.listFiles();
			for (int i = files.length - 1; i >= 0; i--) {
				if (files[i].isDirectory()) {
					deleteDirectory(files[i], true);
				} else {
					deleteFile(files[i].getPath());
				}
			}
			if (deleteSelf) {
				dir.delete();
			}
		}
		return true;
	}

	/**
	 * 创建目录
	 * 
	 * @param baseDir
	 * @param dir
	 * @return
	 */
	public static String createDirectory(String baseDir, String dir) {
		if (baseDir == null) {
			baseDir = "";
		}
		File file = new File(baseDir + dir);
		if (file.exists()) {
			return baseDir + dir;
		}
		if (!baseDir.endsWith("/") && !baseDir.equals("")) {
			baseDir += "/";
		}
		String[] names = dir.split("/");
		for (int i = 0; i < names.length; i++) {
			baseDir += names[i] + "/";
			file = new File(baseDir);
			if (!file.exists()) {
				for (int j = 0; j < 10 && !file.mkdir(); j++) {
					try {
						Thread.sleep(5);
					} catch (InterruptedException e) {

					}
				}
				if (!file.exists()) {
					throw new Error("Create new directory " + file.getPath()
							+ " failed.");
				}
			}
		}
		return baseDir;
	}

	/**
	 * 拷贝文件
	 * 
	 * @param srcFilePath
	 * @param toFilePath
	 * @return
	 */
	public static String copyFile(String src, String dest) {
		File destFile = new File(dest);
		if (destFile.isDirectory()) { // 目录
			if (!dest.endsWith("/")) {
				dest += "/";
			}
			int index = src.lastIndexOf('/');
			if (index == -1) {
				index = src.lastIndexOf('\\');
			}
			dest += src.substring(index + 1);
			destFile = new File(dest);
		}
		File srcFile = new File(src);
		if (destFile.exists()) { // 文件已存在,且不允许覆盖
			if (srcFile.lastModified() == destFile.lastModified()) { // 文件最后修改时间相同,不需要拷贝
				return dest;
			}
		}
		byte[] buffer = new byte[Math.max(1,
				Math.min((int) srcFile.length(), 81920))];
		FileInputStream in = null;
		FileOutputStream out = null;
		try {
			in = new FileInputStream(srcFile);
			out = new FileOutputStream(destFile);
			int readLen;
			while ((readLen = in.read(buffer)) > 0) {
				out.write(buffer, 0, readLen);
			}
		} catch (Exception e) {
			return null;
		} finally {
			try {
				in.close();
			} catch (Exception e) {

			}
			try {
				out.close();
			} catch (Exception e) {

			}
		}
		destFile.setLastModified(srcFile.lastModified());
		return dest;
	}

	/**
	 * 拷贝目录
	 * 
	 * @param src
	 * @param dest
	 * @return
	 */
	public static void copyDirectory(String src, String dest) {
		if (!src.endsWith("/")) {
			src += "/";
		}
		if (!dest.endsWith("/")) {
			dest += "/";
		}
		File srcDir = new File(src);
		File[] files = srcDir.listFiles();
		if (files == null) {
			return;
		}
		for (int i = 0; i < files.length; i++) {
			if (files[i].isDirectory()) { // 目录
				File destDir = new File(dest + files[i].getName());
				if (!destDir.exists()) {
					destDir.mkdir();
				}
				copyDirectory(src + files[i].getName(),
						dest + files[i].getName());
			} else { // 文件
				copyFile(src + files[i].getName(), dest + files[i].getName());
			}
		}
	}

	/**
	 * 写入文本内容
	 * 
	 * @param file
	 * @param content
	 * @return
	 */
	public static boolean saveStringToFile(String fileName, String content,
			String encoding) {
		FileOutputStream out = null;
		encoding = encoding.toUpperCase();
		try {
			// Unicode：FF FE, Unicode big_endian：EF FF, UTF-8： EF BB BF
			out = new FileOutputStream(fileName);
			if ("UTF-8".equals(encoding)) {
				out.write(0xEF);
				out.write(0xBB);
				out.write(0xBF);
			} else if ("UNICODE".equals(encoding)) {
				out.write(0xFF);
				out.write(0xFE);
			}
			out.write(encoding == null ? content.getBytes() : content
					.getBytes(encoding));
			return true;
		} catch (Exception ex) {
			deleteFile(fileName); // 保存失败,删除文件
			return false;
		} finally {
			try {
				out.close();
			} catch (IOException ex1) {

			}
		}
	}

	/**
	 * 读取文件内容
	 * 
	 * @param filePath
	 *            文件路径
	 * @return
	 * @throws Exception
	 */
	public static String readStringFromFile(String fileName) {
		FileInputStream inputStream = null;
		try {
			File file = new File(fileName);
			int fileSize = (int) file.length();
			inputStream = new FileInputStream(file);
			byte[] bytes = new byte[fileSize];
			inputStream.read(bytes);
			return new String(bytes, "UTF-8");
		} catch (Exception ex) {
			return null;
		} finally {
			try {
				inputStream.close();
			} catch (Exception e) {

			}
		}
	}

	public static byte[] readBytesFromFile(String fileName) {
		FileInputStream inputStream = null;
		try {
			File file = new File(fileName);
			int fileSize = (int) file.length();
			inputStream = new FileInputStream(file);
			byte[] bytes = new byte[fileSize];
			inputStream.read(bytes);
			return bytes;
		} catch (Exception ex) {
			System.out.println(ex);
			return null;
		} finally {
			try {
				inputStream.close();
			} catch (Exception e) {

			}
		}
	}

	/**
	 * 文件转换为二进制数组
	 * 
	 * @param filePath
	 *            文件路径
	 * @return
	 * @throws Exception
	 */
	public static byte[] fileToByte(String filePath) {

		byte[] data = null;
		try {
			data = new byte[0];
			File file = new File(filePath);
			if (file.exists()) {
				FileInputStream in = new FileInputStream(file);
				ByteArrayOutputStream out = new ByteArrayOutputStream(2048);
				byte[] cache = new byte[CACHE_SIZE];
				int nRead = 0;
				while ((nRead = in.read(cache)) != -1) {
					out.write(cache, 0, nRead);
					out.flush();
				}
				out.close();
				in.close();
				data = out.toByteArray();
			}
		} catch (Exception e) {
			return null;
		}
		
		return data;
	}
}
