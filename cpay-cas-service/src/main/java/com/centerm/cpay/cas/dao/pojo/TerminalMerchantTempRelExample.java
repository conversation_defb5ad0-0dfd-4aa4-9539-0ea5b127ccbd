package com.centerm.cpay.cas.dao.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TerminalMerchantTempRelExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TerminalMerchantTempRelExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTermIdIsNull() {
            addCriterion("term_id is null");
            return (Criteria) this;
        }

        public Criteria andTermIdIsNotNull() {
            addCriterion("term_id is not null");
            return (Criteria) this;
        }

        public Criteria andTermIdEqualTo(Integer value) {
            addCriterion("term_id =", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdNotEqualTo(Integer value) {
            addCriterion("term_id <>", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdGreaterThan(Integer value) {
            addCriterion("term_id >", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("term_id >=", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdLessThan(Integer value) {
            addCriterion("term_id <", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdLessThanOrEqualTo(Integer value) {
            addCriterion("term_id <=", value, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdIn(List<Integer> values) {
            addCriterion("term_id in", values, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdNotIn(List<Integer> values) {
            addCriterion("term_id not in", values, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdBetween(Integer value1, Integer value2) {
            addCriterion("term_id between", value1, value2, "termId");
            return (Criteria) this;
        }

        public Criteria andTermIdNotBetween(Integer value1, Integer value2) {
            addCriterion("term_id not between", value1, value2, "termId");
            return (Criteria) this;
        }

        public Criteria andTermSeqIsNull() {
            addCriterion("term_seq is null");
            return (Criteria) this;
        }

        public Criteria andTermSeqIsNotNull() {
            addCriterion("term_seq is not null");
            return (Criteria) this;
        }

        public Criteria andTermSeqEqualTo(String value) {
            addCriterion("term_seq =", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqNotEqualTo(String value) {
            addCriterion("term_seq <>", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqGreaterThan(String value) {
            addCriterion("term_seq >", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqGreaterThanOrEqualTo(String value) {
            addCriterion("term_seq >=", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqLessThan(String value) {
            addCriterion("term_seq <", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqLessThanOrEqualTo(String value) {
            addCriterion("term_seq <=", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqLike(String value) {
            addCriterion("term_seq like", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqNotLike(String value) {
            addCriterion("term_seq not like", value, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqIn(List<String> values) {
            addCriterion("term_seq in", values, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqNotIn(List<String> values) {
            addCriterion("term_seq not in", values, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqBetween(String value1, String value2) {
            addCriterion("term_seq between", value1, value2, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTermSeqNotBetween(String value1, String value2) {
            addCriterion("term_seq not between", value1, value2, "termSeq");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdIsNull() {
            addCriterion("temp_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdIsNotNull() {
            addCriterion("temp_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdEqualTo(Integer value) {
            addCriterion("temp_merchant_id =", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdNotEqualTo(Integer value) {
            addCriterion("temp_merchant_id <>", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdGreaterThan(Integer value) {
            addCriterion("temp_merchant_id >", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("temp_merchant_id >=", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdLessThan(Integer value) {
            addCriterion("temp_merchant_id <", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdLessThanOrEqualTo(Integer value) {
            addCriterion("temp_merchant_id <=", value, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdIn(List<Integer> values) {
            addCriterion("temp_merchant_id in", values, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdNotIn(List<Integer> values) {
            addCriterion("temp_merchant_id not in", values, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdBetween(Integer value1, Integer value2) {
            addCriterion("temp_merchant_id between", value1, value2, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andTempMerchantIdNotBetween(Integer value1, Integer value2) {
            addCriterion("temp_merchant_id not between", value1, value2, "tempMerchantId");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIsNull() {
            addCriterion("validate_code is null");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIsNotNull() {
            addCriterion("validate_code is not null");
            return (Criteria) this;
        }

        public Criteria andValidateCodeEqualTo(String value) {
            addCriterion("validate_code =", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotEqualTo(String value) {
            addCriterion("validate_code <>", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeGreaterThan(String value) {
            addCriterion("validate_code >", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("validate_code >=", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeLessThan(String value) {
            addCriterion("validate_code <", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeLessThanOrEqualTo(String value) {
            addCriterion("validate_code <=", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeLike(String value) {
            addCriterion("validate_code like", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotLike(String value) {
            addCriterion("validate_code not like", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIn(List<String> values) {
            addCriterion("validate_code in", values, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotIn(List<String> values) {
            addCriterion("validate_code not in", values, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeBetween(String value1, String value2) {
            addCriterion("validate_code between", value1, value2, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotBetween(String value1, String value2) {
            addCriterion("validate_code not between", value1, value2, "validateCode");
            return (Criteria) this;
        }

        public Criteria andTermStatusIsNull() {
            addCriterion("term_status is null");
            return (Criteria) this;
        }

        public Criteria andTermStatusIsNotNull() {
            addCriterion("term_status is not null");
            return (Criteria) this;
        }

        public Criteria andTermStatusEqualTo(String value) {
            addCriterion("term_status =", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusNotEqualTo(String value) {
            addCriterion("term_status <>", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusGreaterThan(String value) {
            addCriterion("term_status >", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusGreaterThanOrEqualTo(String value) {
            addCriterion("term_status >=", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusLessThan(String value) {
            addCriterion("term_status <", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusLessThanOrEqualTo(String value) {
            addCriterion("term_status <=", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusLike(String value) {
            addCriterion("term_status like", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusNotLike(String value) {
            addCriterion("term_status not like", value, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusIn(List<String> values) {
            addCriterion("term_status in", values, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusNotIn(List<String> values) {
            addCriterion("term_status not in", values, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusBetween(String value1, String value2) {
            addCriterion("term_status between", value1, value2, "termStatus");
            return (Criteria) this;
        }

        public Criteria andTermStatusNotBetween(String value1, String value2) {
            addCriterion("term_status not between", value1, value2, "termStatus");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIsNull() {
            addCriterion("warranty_period is null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIsNotNull() {
            addCriterion("warranty_period is not null");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodEqualTo(Date value) {
            addCriterionForJDBCDate("warranty_period =", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotEqualTo(Date value) {
            addCriterionForJDBCDate("warranty_period <>", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodGreaterThan(Date value) {
            addCriterionForJDBCDate("warranty_period >", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("warranty_period >=", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodLessThan(Date value) {
            addCriterionForJDBCDate("warranty_period <", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("warranty_period <=", value, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodIn(List<Date> values) {
            addCriterionForJDBCDate("warranty_period in", values, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotIn(List<Date> values) {
            addCriterionForJDBCDate("warranty_period not in", values, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("warranty_period between", value1, value2, "warrantyPeriod");
            return (Criteria) this;
        }

        public Criteria andWarrantyPeriodNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("warranty_period not between", value1, value2, "warrantyPeriod");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}