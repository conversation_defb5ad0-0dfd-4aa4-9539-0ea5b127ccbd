package com.centerm.cpay.cas.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.cas.dao.mapper.WarrantyInfoMapper;
import com.centerm.cpay.cas.dao.pojo.WarrantyInfo;
import com.centerm.cpay.cas.dao.pojo.WarrantyInfoExample;
import com.centerm.cpay.cas.service.WarrantyInfoService;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.service.TerminalService;

@Service
public class WarrantyInfoServiceImpl implements WarrantyInfoService {

	@Autowired
	private WarrantyInfoMapper warrantyInfoMapper;
	
	@Autowired
	private	TerminalService terminalService;
	@Override
	public WarrantyInfo queryById(Integer id) {
		// TODO
		return warrantyInfoMapper.selectByPrimaryKey(id);
	}
	
	public ResultMsg queryListByTermInfo(String termSeq){
		Terminal terminalInfo = terminalService.queryTerminalInfoByTermSeq(termSeq);
		if(terminalInfo == null){
			return ResultMsg.fail("无此终端信息");
		}
		WarrantyInfoExample example = new WarrantyInfoExample();
		WarrantyInfoExample.Criteria criteria = example.createCriteria();
		criteria.andTermFacturerIdEqualTo(terminalInfo.getTermMfrId()).andTermTypeCodeEqualTo(terminalInfo.getTermTypeCode());
		
		List<WarrantyInfo> warrantyInfoList = warrantyInfoMapper.selectByExample(example);
		if(warrantyInfoList == null || warrantyInfoList.size() == 0){
			return ResultMsg.fail("无此终端型号延保套餐");
		}
		return ResultMsg.success(warrantyInfoList);
	}
}
