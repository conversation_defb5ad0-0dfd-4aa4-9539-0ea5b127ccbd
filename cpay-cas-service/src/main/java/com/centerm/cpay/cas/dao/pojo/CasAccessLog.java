package com.centerm.cpay.cas.dao.pojo;

public class CasAccessLog {
    private Integer id;

    private String termSeq;

    private String timestamp;

    private String random;

    private String url;

    private String result;

    private Integer requestLength;

    private Integer reponseLength;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTermSeq() {
        return termSeq;
    }

    public void setTermSeq(String termSeq) {
        this.termSeq = termSeq == null ? null : termSeq.trim();
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp == null ? null : timestamp.trim();
    }

    public String getRandom() {
        return random;
    }

    public void setRandom(String random) {
        this.random = random == null ? null : random.trim();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result == null ? null : result.trim();
    }

    public Integer getRequestLength() {
        return requestLength;
    }

    public void setRequestLength(Integer requestLength) {
        this.requestLength = requestLength;
    }

    public Integer getReponseLength() {
        return reponseLength;
    }

    public void setReponseLength(Integer reponseLength) {
        this.reponseLength = reponseLength;
    }
}