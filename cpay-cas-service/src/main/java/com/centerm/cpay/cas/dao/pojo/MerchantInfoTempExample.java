package com.centerm.cpay.cas.dao.pojo;

import java.util.ArrayList;
import java.util.List;

public class MerchantInfoTempExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MerchantInfoTempExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCredTypeIsNull() {
            addCriterion("cred_type is null");
            return (Criteria) this;
        }

        public Criteria andCredTypeIsNotNull() {
            addCriterion("cred_type is not null");
            return (Criteria) this;
        }

        public Criteria andCredTypeEqualTo(String value) {
            addCriterion("cred_type =", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeNotEqualTo(String value) {
            addCriterion("cred_type <>", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeGreaterThan(String value) {
            addCriterion("cred_type >", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeGreaterThanOrEqualTo(String value) {
            addCriterion("cred_type >=", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeLessThan(String value) {
            addCriterion("cred_type <", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeLessThanOrEqualTo(String value) {
            addCriterion("cred_type <=", value, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeIn(List<String> values) {
            addCriterion("cred_type in", values, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeNotIn(List<String> values) {
            addCriterion("cred_type not in", values, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeBetween(String value1, String value2) {
            addCriterion("cred_type between", value1, value2, "credType");
            return (Criteria) this;
        }

        public Criteria andCredTypeNotBetween(String value1, String value2) {
            addCriterion("cred_type not between", value1, value2, "credType");
            return (Criteria) this;
        }

        public Criteria andCredNoIsNull() {
            addCriterion("cred_no is null");
            return (Criteria) this;
        }

        public Criteria andCredNoIsNotNull() {
            addCriterion("cred_no is not null");
            return (Criteria) this;
        }

        public Criteria andCredNoEqualTo(String value) {
            addCriterion("cred_no =", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoNotEqualTo(String value) {
            addCriterion("cred_no <>", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoGreaterThan(String value) {
            addCriterion("cred_no >", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoGreaterThanOrEqualTo(String value) {
            addCriterion("cred_no >=", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoLessThan(String value) {
            addCriterion("cred_no <", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoLessThanOrEqualTo(String value) {
            addCriterion("cred_no <=", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoLike(String value) {
            addCriterion("cred_no like", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoNotLike(String value) {
            addCriterion("cred_no not like", value, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoIn(List<String> values) {
            addCriterion("cred_no in", values, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoNotIn(List<String> values) {
            addCriterion("cred_no not in", values, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoBetween(String value1, String value2) {
            addCriterion("cred_no between", value1, value2, "credNo");
            return (Criteria) this;
        }

        public Criteria andCredNoNotBetween(String value1, String value2) {
            addCriterion("cred_no not between", value1, value2, "credNo");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCountyIsNull() {
            addCriterion("county is null");
            return (Criteria) this;
        }

        public Criteria andCountyIsNotNull() {
            addCriterion("county is not null");
            return (Criteria) this;
        }

        public Criteria andCountyEqualTo(String value) {
            addCriterion("county =", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotEqualTo(String value) {
            addCriterion("county <>", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyGreaterThan(String value) {
            addCriterion("county >", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyGreaterThanOrEqualTo(String value) {
            addCriterion("county >=", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLessThan(String value) {
            addCriterion("county <", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLessThanOrEqualTo(String value) {
            addCriterion("county <=", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyLike(String value) {
            addCriterion("county like", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotLike(String value) {
            addCriterion("county not like", value, "county");
            return (Criteria) this;
        }

        public Criteria andCountyIn(List<String> values) {
            addCriterion("county in", values, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotIn(List<String> values) {
            addCriterion("county not in", values, "county");
            return (Criteria) this;
        }

        public Criteria andCountyBetween(String value1, String value2) {
            addCriterion("county between", value1, value2, "county");
            return (Criteria) this;
        }

        public Criteria andCountyNotBetween(String value1, String value2) {
            addCriterion("county not between", value1, value2, "county");
            return (Criteria) this;
        }

        public Criteria andAddrIsNull() {
            addCriterion("addr is null");
            return (Criteria) this;
        }

        public Criteria andAddrIsNotNull() {
            addCriterion("addr is not null");
            return (Criteria) this;
        }

        public Criteria andAddrEqualTo(String value) {
            addCriterion("addr =", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrNotEqualTo(String value) {
            addCriterion("addr <>", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrGreaterThan(String value) {
            addCriterion("addr >", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrGreaterThanOrEqualTo(String value) {
            addCriterion("addr >=", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrLessThan(String value) {
            addCriterion("addr <", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrLessThanOrEqualTo(String value) {
            addCriterion("addr <=", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrLike(String value) {
            addCriterion("addr like", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrNotLike(String value) {
            addCriterion("addr not like", value, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrIn(List<String> values) {
            addCriterion("addr in", values, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrNotIn(List<String> values) {
            addCriterion("addr not in", values, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrBetween(String value1, String value2) {
            addCriterion("addr between", value1, value2, "addr");
            return (Criteria) this;
        }

        public Criteria andAddrNotBetween(String value1, String value2) {
            addCriterion("addr not between", value1, value2, "addr");
            return (Criteria) this;
        }

        public Criteria andApplyerNameIsNull() {
            addCriterion("applyer_name is null");
            return (Criteria) this;
        }

        public Criteria andApplyerNameIsNotNull() {
            addCriterion("applyer_name is not null");
            return (Criteria) this;
        }

        public Criteria andApplyerNameEqualTo(String value) {
            addCriterion("applyer_name =", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameNotEqualTo(String value) {
            addCriterion("applyer_name <>", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameGreaterThan(String value) {
            addCriterion("applyer_name >", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameGreaterThanOrEqualTo(String value) {
            addCriterion("applyer_name >=", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameLessThan(String value) {
            addCriterion("applyer_name <", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameLessThanOrEqualTo(String value) {
            addCriterion("applyer_name <=", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameLike(String value) {
            addCriterion("applyer_name like", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameNotLike(String value) {
            addCriterion("applyer_name not like", value, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameIn(List<String> values) {
            addCriterion("applyer_name in", values, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameNotIn(List<String> values) {
            addCriterion("applyer_name not in", values, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameBetween(String value1, String value2) {
            addCriterion("applyer_name between", value1, value2, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerNameNotBetween(String value1, String value2) {
            addCriterion("applyer_name not between", value1, value2, "applyerName");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneIsNull() {
            addCriterion("applyer_phone is null");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneIsNotNull() {
            addCriterion("applyer_phone is not null");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneEqualTo(String value) {
            addCriterion("applyer_phone =", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneNotEqualTo(String value) {
            addCriterion("applyer_phone <>", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneGreaterThan(String value) {
            addCriterion("applyer_phone >", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("applyer_phone >=", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneLessThan(String value) {
            addCriterion("applyer_phone <", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneLessThanOrEqualTo(String value) {
            addCriterion("applyer_phone <=", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneLike(String value) {
            addCriterion("applyer_phone like", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneNotLike(String value) {
            addCriterion("applyer_phone not like", value, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneIn(List<String> values) {
            addCriterion("applyer_phone in", values, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneNotIn(List<String> values) {
            addCriterion("applyer_phone not in", values, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneBetween(String value1, String value2) {
            addCriterion("applyer_phone between", value1, value2, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerPhoneNotBetween(String value1, String value2) {
            addCriterion("applyer_phone not between", value1, value2, "applyerPhone");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardIsNull() {
            addCriterion("applyer_idcard is null");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardIsNotNull() {
            addCriterion("applyer_idcard is not null");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardEqualTo(String value) {
            addCriterion("applyer_idcard =", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardNotEqualTo(String value) {
            addCriterion("applyer_idcard <>", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardGreaterThan(String value) {
            addCriterion("applyer_idcard >", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardGreaterThanOrEqualTo(String value) {
            addCriterion("applyer_idcard >=", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardLessThan(String value) {
            addCriterion("applyer_idcard <", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardLessThanOrEqualTo(String value) {
            addCriterion("applyer_idcard <=", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardLike(String value) {
            addCriterion("applyer_idcard like", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardNotLike(String value) {
            addCriterion("applyer_idcard not like", value, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardIn(List<String> values) {
            addCriterion("applyer_idcard in", values, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardNotIn(List<String> values) {
            addCriterion("applyer_idcard not in", values, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardBetween(String value1, String value2) {
            addCriterion("applyer_idcard between", value1, value2, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andApplyerIdcardNotBetween(String value1, String value2) {
            addCriterion("applyer_idcard not between", value1, value2, "applyerIdcard");
            return (Criteria) this;
        }

        public Criteria andAccessIdIsNull() {
            addCriterion("access_id is null");
            return (Criteria) this;
        }

        public Criteria andAccessIdIsNotNull() {
            addCriterion("access_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccessIdEqualTo(Integer value) {
            addCriterion("access_id =", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdNotEqualTo(Integer value) {
            addCriterion("access_id <>", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdGreaterThan(Integer value) {
            addCriterion("access_id >", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("access_id >=", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdLessThan(Integer value) {
            addCriterion("access_id <", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdLessThanOrEqualTo(Integer value) {
            addCriterion("access_id <=", value, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdIn(List<Integer> values) {
            addCriterion("access_id in", values, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdNotIn(List<Integer> values) {
            addCriterion("access_id not in", values, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdBetween(Integer value1, Integer value2) {
            addCriterion("access_id between", value1, value2, "accessId");
            return (Criteria) this;
        }

        public Criteria andAccessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("access_id not between", value1, value2, "accessId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andResourceIsNull() {
            addCriterion("resource is null");
            return (Criteria) this;
        }

        public Criteria andResourceIsNotNull() {
            addCriterion("resource is not null");
            return (Criteria) this;
        }

        public Criteria andResourceEqualTo(String value) {
            addCriterion("resource =", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceNotEqualTo(String value) {
            addCriterion("resource <>", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceGreaterThan(String value) {
            addCriterion("resource >", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceGreaterThanOrEqualTo(String value) {
            addCriterion("resource >=", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceLessThan(String value) {
            addCriterion("resource <", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceLessThanOrEqualTo(String value) {
            addCriterion("resource <=", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceLike(String value) {
            addCriterion("resource like", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceNotLike(String value) {
            addCriterion("resource not like", value, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceIn(List<String> values) {
            addCriterion("resource in", values, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceNotIn(List<String> values) {
            addCriterion("resource not in", values, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceBetween(String value1, String value2) {
            addCriterion("resource between", value1, value2, "resource");
            return (Criteria) this;
        }

        public Criteria andResourceNotBetween(String value1, String value2) {
            addCriterion("resource not between", value1, value2, "resource");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}