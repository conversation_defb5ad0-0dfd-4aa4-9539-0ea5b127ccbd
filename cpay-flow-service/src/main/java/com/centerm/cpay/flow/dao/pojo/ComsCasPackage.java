package com.centerm.cpay.flow.dao.pojo;
/**
 * 套餐管理
 * <AUTHOR>
 *
 */
public class ComsCasPackage {
    private Integer id;

    private String name;

    private String serviceProvider;

    private Integer flowCount;

    private Float amt;

    private String startType;

    private String comment;

    private String openFlag;

    private Integer validity;

    private String packageType;
    
    private Integer operatorId;
    
	public Integer getId() {
        return id;
    }
	
    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider == null ? null : serviceProvider.trim();
    }

    public Integer getFlowCount() {
        return flowCount;
    }

    public void setFlowCount(Integer flowCount) {
        this.flowCount = flowCount;
    }

    public Float getAmt() {
        return amt;
    }

    public void setAmt(Float amt) {
        this.amt = amt;
    }

    public String getStartType() {
        return startType;
    }

    public void setStartType(String startType) {
        this.startType = startType == null ? null : startType.trim();
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment == null ? null : comment.trim();
    }

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag == null ? null : openFlag.trim();
    }

    public Integer getValidity() {
        return validity;
    }

    public void setValidity(Integer validity) {
        this.validity = validity;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType == null ? null : packageType.trim();
    }

	public Integer getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Integer operatorId) {
		this.operatorId = operatorId;
	}

	@Override
	public String toString() {
		return "ComsCasPackage [id=" + id + ", name=" + name + ", serviceProvider=" + serviceProvider + ", flowCount="
				+ flowCount + ", amt=" + amt + ", startType=" + startType + ", comment=" + comment + ", openFlag="
				+ openFlag + ", validity=" + validity + ", packageType=" + packageType + "]";
	}
}