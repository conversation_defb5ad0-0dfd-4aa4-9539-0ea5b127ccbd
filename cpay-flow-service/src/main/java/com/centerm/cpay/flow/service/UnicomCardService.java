package com.centerm.cpay.flow.service;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.flow.dao.pojo.UnicomCard;

public interface UnicomCardService {
	public UnicomCard getUincomCardByImsi(String imsi);
	
	public ResultMsg queryUincomCardByImsi(String imsi,String iccid,String imei,String certNumber);
	
	public ResultMsg updateUnicomCards() throws Exception;

	public EUDataGridResult getUincomCardfList(UnicomCard unicomCard, int page, int rows);

	public ResultMsg packageBind(Map map);

	public ResultMsg updateUnicomCardStatus(Map map);
	
	public int updateUnicomCardStatusByImsi(String imsi,String status,String comment);
	
	public ResultMsg insertOrUpdateUnicomCard(UnicomCard unicomCard);

	public int saveImportCard(UnicomCard unicomCard);

	public int updateBalanceByImsi(String imsi,float amount);
	
	public int updateUnicomCard(UnicomCard unicomCard);

	public ResultMsg startRecharge(UnicomCard unicomCard);

	public List<UnicomCard> queryUincomCard(UnicomCard unicomCard);
	
	List<UnicomCard> queryTelecomCard(long page, long rows);

	String acitveMasterPlanByPro();
	
	List<UnicomCard> queryUnicomCard(String type);
	
	Long queryCountTelecomCard();

	public ResultMsg bindingMasterPackage(UnicomCard unicomCard);

	public ResultMsg updateTelCardStatus(Map map);

	int saveImportCardList(List<UnicomCard> list);
	public void reAddMoneyByUnicomCard(Map map);

	public void reAddMainPackage(Map map);

	public UnicomCard queryUincomCardByCardNo(String cardNo);

	public void addLog(Map map2);

	public UnicomCard getUincomCardById(String id);
	
	List<UnicomCard> queryTelecomUnicomCards();

}
