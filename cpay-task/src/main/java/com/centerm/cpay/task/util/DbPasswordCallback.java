package com.centerm.cpay.task.util;

import java.util.Properties;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.druid.util.DruidPasswordCallback;
import com.centerm.cpay.common.utils.Constants;
import com.centerm.cpay.common.utils.DesUtils;

/**
 * 数据库密码回调函数
 * 
 * <AUTHOR> date 2017年11月21日
 */
@SuppressWarnings("serial")
public class DbPasswordCallback extends DruidPasswordCallback {

	@Override
	public void setProperties(Properties properties) {
		super.setProperties(properties);
		String pwd = properties.getProperty("password");
		if (StringUtils.isNotBlank(pwd)) {
			try {
				// 这里的password是将jdbc.properties配置得到的密码进行解密之后的值
				// 所以这里的代码是将密码进行解密
				// TODO 将pwd进行解密;
				String password = new String(DesUtils.strDec(pwd, Constants.DATABASE_PWD_KEY));
				setPassword(password.substring(0, password.length()-32).toCharArray());
				//setPassword(pwd.toCharArray());
			} catch (Exception e) {
				setPassword(pwd.toCharArray());
			}
		}
	}
	public static void main(String[] args) {
		String a = DesUtils.strEnc("CpayComs@Qorder123"+Constants.DATABASE_PWD_HASH, Constants.DATABASE_PWD_KEY);
		System.out.println(a);
		System.out.println(DesUtils.strDec(a, Constants.DATABASE_PWD_KEY));
		String password = DesUtils.strDec(a, Constants.DATABASE_PWD_KEY);
		System.out.println(password.substring(0, password.length()-32));
	}
}
