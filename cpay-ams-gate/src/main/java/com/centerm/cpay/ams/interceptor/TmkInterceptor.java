package com.centerm.cpay.ams.interceptor;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.centerm.cpay.ams.request.group.AGroup;
import com.centerm.cpay.ams.util.StoreServiceUtil;
import com.centerm.cpay.common.enums.ProtocolError;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.protocol.RequestHeader;
import com.centerm.cpay.common.protocol.RequestMsg;
import com.centerm.cpay.common.protocol.RequestMsgParser;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.service.ValidationBySignService;
import com.centerm.cpay.common.service.ValidationService;
import com.centerm.cpay.common.utils.CommConstant;
import com.centerm.cpay.common.utils.JsonUtils;
import com.centerm.cpay.coms.apkstore.dao.pojo.AmsAccessLog;
import com.centerm.cpay.coms.apkstore.service.AmsAccessLogService;
import com.centerm.cpay.coms.dao.pojo.Factory;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.service.FactoryService;
import com.centerm.cpay.coms.service.TerminalService;
import com.centerm.cpay.security.utils.CtUtils;
import com.fasterxml.jackson.databind.JsonNode;

public class TmkInterceptor implements HandlerInterceptor{
	private static final Logger logger = LoggerFactory.getLogger(TmkInterceptor.class);
	@Autowired
	private AmsAccessLogService amsAccessLogService;
	@Autowired
	private ValidationService validationService;
	
	@Autowired
	private TerminalService terminalService;
	
	@Autowired
	private FactoryService factoryService;
	
	@Autowired
	private ValidationBySignService validationBySignService;
	protected String message;
	@Override
	public boolean preHandle(HttpServletRequest request,HttpServletResponse response, Object handler) throws Exception {
		this.message = getRequestPostStr(request);
		JsonNode node = JsonUtils.stringToJsonNode(this.message);
		request.setAttribute("jsonNode", node);
		logger.debug("拦截器json："+this.message);
		RequestMsg<Map> requestMsg = RequestMsgParser.parse(node, Map.class);
		
		String validaString = validationService.validate(requestMsg,AGroup.class);
		if(!CommConstant.VALIDATION_RESLUT_SUCCESS.equals(validaString)){
			returnFailMessage(validaString,response,requestMsg);
			return false;
		}
		RequestHeader header = requestMsg.getHeader();
		AmsAccessLog amsAccessLog = new AmsAccessLog();
		amsAccessLog.setRandom(header.getRandom());
		amsAccessLog.setTermSeq(header.getDevMask());
		amsAccessLog.setTimestamp(header.getTimestamp());
		amsAccessLog.setUrl(request.getRequestURI());
		amsAccessLog.setRequestLength(request.getContentLength());
		try{
			if(! (amsAccessLogService.insertLog(amsAccessLog) == 1)){
				returnFailMessage("Please do not repeat the request information",response,requestMsg);
				return false;
			}
		}catch (Exception e){
			logger.debug("新增操作日志信息异常",e);
			returnFailMessage("Please do not repeat the request information",response,requestMsg);
			return false;
		}
		
		Terminal terminal = terminalService.queryTerminalInfoByTermSeq(header.getDevMask());
		if(CtUtils.isEmpty(terminal)){
			returnFailMessage("Query without this terminal",response,requestMsg);
			return false;
		}
		
		Factory factory = factoryService.getFactoryById(terminal.getTermMfrId());
		
		Map<String,String> leafMap = RequestMsgParser.parseJSON2Map(node);
		ResultMsg valideResult = validationBySignService.validtateRequestMsgBySign(leafMap,requestMsg.getSign(),factory.getPublicKeyPath());
		if(ResultMsg.SUCCESS_CODE != valideResult.getStatus()){
			returnFailMessage(valideResult.getMsg(),response,requestMsg);
			return false;
		}
		return true;
	}
	
	protected void returnFailMessage(String failMsg,HttpServletResponse response,RequestMsg<Map> requestMsg) throws Exception{
		ResponseMsg responseMsg = ResponseMsg.fail(StoreServiceUtil.responseStatus+ProtocolError.HEADER_ERROR.getCode(),failMsg,requestMsg);
		responseMsg = validationBySignService.rsponseMsgSetSign(responseMsg);
		responseOutWithJson(response,responseMsg);
	}

	@Override
	public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, Exception arg3)
			throws Exception {
		// TODO
		
	}

	@Override
	public void postHandle(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, ModelAndView arg3)
			throws Exception {
		// TODO
		
	}
	
	/**
	 * 以JSON格式输出
	 * 
	 * @param response
	 */
	protected void responseOutWithJson(HttpServletResponse response, Object responseObject) {
		// 将实体对象转换为JSON Object转换
		String json = JsonUtils.objectToJson(responseObject);
		response.setCharacterEncoding("UTF-8");
		response.setContentType("application/json; charset=utf-8");
		PrintWriter out = null;
		try {
			out = response.getWriter();
			out.append(json);
			logger.info("返回json数据=【" + json + "】");
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (out != null) {
				out.close();
			}
		}
	}

	/**
	 * 从request中获取json串
	 * 
	 * @param request
	 * @return
	 * @throws IOException
	 */
	protected static String getRequestPostStr(HttpServletRequest request) throws IOException {
		byte buffer[] = getRequestPostBytes(request);
		String charEncoding = request.getCharacterEncoding();
		if (charEncoding == null) {
			charEncoding = "UTF-8";
		}
		System.out.println("requestPostStr :" + new String(buffer, charEncoding));
		return new String(buffer, charEncoding);
	}

	/**
	 * 从request中获取byte[]
	 * 
	 * @param request
	 * @return
	 * @throws IOException
	 */
	protected static byte[] getRequestPostBytes(HttpServletRequest request) throws IOException {
		int contentLength = request.getContentLength();
		System.out.println("debug: " + contentLength);
		if (contentLength <= 0) {
			return null;
		}
		byte buffer[] = new byte[contentLength];
		for (int i = 0; i < contentLength;) {

			int readlen = request.getInputStream().read(buffer, i, contentLength - i);
			if (readlen == -1) {
				break;
			}
			i += readlen;
		}
		return buffer;
	}

}
