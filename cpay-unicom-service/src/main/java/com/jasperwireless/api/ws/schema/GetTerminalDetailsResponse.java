
package com.jasperwireless.api.ws.schema;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.w3c.dom.Element;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="terminals"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="terminal" type="{http://api.jasperwireless.com/ws/schema}TerminalType" maxOccurs="unbounded"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;any processContents='lax' maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "terminals",
    "any"
})
@XmlRootElement(name = "GetTerminalDetailsResponse")
public class GetTerminalDetailsResponse
    extends ResponseType
{

    @XmlElement(required = true)
    protected GetTerminalDetailsResponse.Terminals terminals;
    @XmlAnyElement(lax = true)
    protected List<Object> any;

    /**
     * 获取terminals属性的值。
     * 
     * @return
     *     possible object is
     *     {@link GetTerminalDetailsResponse.Terminals }
     *     
     */
    public GetTerminalDetailsResponse.Terminals getTerminals() {
        return terminals;
    }

    /**
     * 设置terminals属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link GetTerminalDetailsResponse.Terminals }
     *     
     */
    public void setTerminals(GetTerminalDetailsResponse.Terminals value) {
        this.terminals = value;
    }

    /**
     * Gets the value of the any property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the any property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAny().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Element }
     * {@link Object }
     * 
     * 
     */
    public List<Object> getAny() {
        if (any == null) {
            any = new ArrayList<Object>();
        }
        return this.any;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="terminal" type="{http://api.jasperwireless.com/ws/schema}TerminalType" maxOccurs="unbounded"/&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "terminal"
    })
    public static class Terminals {

        @XmlElement(required = true)
        protected List<TerminalType> terminal;

        /**
         * Gets the value of the terminal property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the terminal property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getTerminal().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link TerminalType }
         * 
         * 
         */
        public List<TerminalType> getTerminal() {
            if (terminal == null) {
                terminal = new ArrayList<TerminalType>();
            }
            return this.terminal;
        }

    }

}
