
package com.jasperwireless.api.ws.schema;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.w3c.dom.Element;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;group ref="{http://api.jasperwireless.com/ws/schema}AvailableRatePlansResponseParamGroup"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "availableEvents",
    "any"
})
@XmlRootElement(name = "GetAvailableEventsResponse")
public class GetAvailableEventsResponse
    extends ResponseType
{

    @XmlElement(name = "AvailableEvents", required = true)
    protected GetAvailableEventsResponse.AvailableEvents availableEvents;
    @XmlAnyElement(lax = true)
    protected List<Object> any;

    /**
     * 获取availableEvents属性的值。
     * 
     * @return
     *     possible object is
     *     {@link GetAvailableEventsResponse.AvailableEvents }
     *     
     */
    public GetAvailableEventsResponse.AvailableEvents getAvailableEvents() {
        return availableEvents;
    }

    /**
     * 设置availableEvents属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link GetAvailableEventsResponse.AvailableEvents }
     *     
     */
    public void setAvailableEvents(GetAvailableEventsResponse.AvailableEvents value) {
        this.availableEvents = value;
    }

    /**
     * Gets the value of the any property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the any property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAny().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Element }
     * {@link Object }
     * 
     * 
     */
    public List<Object> getAny() {
        if (any == null) {
            any = new ArrayList<Object>();
        }
        return this.any;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="AvailableEvent" maxOccurs="unbounded" minOccurs="0"&gt;
     *           &lt;complexType&gt;
     *             &lt;complexContent&gt;
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *                 &lt;sequence&gt;
     *                   &lt;element name="EventName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
     *                   &lt;element name="Version" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
     *                   &lt;element name="Term" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/&gt;
     *                   &lt;element name="Price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
     *                   &lt;element name="IncludedData" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
     *                   &lt;element name="DataOverageRate" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
     *                 &lt;/sequence&gt;
     *               &lt;/restriction&gt;
     *             &lt;/complexContent&gt;
     *           &lt;/complexType&gt;
     *         &lt;/element&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "availableEvent"
    })
    public static class AvailableEvents {

        @XmlElement(name = "AvailableEvent")
        protected List<GetAvailableEventsResponse.AvailableEvents.AvailableEvent> availableEvent;

        /**
         * Gets the value of the availableEvent property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the availableEvent property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getAvailableEvent().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GetAvailableEventsResponse.AvailableEvents.AvailableEvent }
         * 
         * 
         */
        public List<GetAvailableEventsResponse.AvailableEvents.AvailableEvent> getAvailableEvent() {
            if (availableEvent == null) {
                availableEvent = new ArrayList<GetAvailableEventsResponse.AvailableEvents.AvailableEvent>();
            }
            return this.availableEvent;
        }


        /**
         * <p>anonymous complex type的 Java 类。
         * 
         * <p>以下模式片段指定包含在此类中的预期内容。
         * 
         * <pre>
         * &lt;complexType&gt;
         *   &lt;complexContent&gt;
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
         *       &lt;sequence&gt;
         *         &lt;element name="EventName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         *         &lt;element name="Version" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
         *         &lt;element name="Term" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/&gt;
         *         &lt;element name="Price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         *         &lt;element name="IncludedData" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         *         &lt;element name="DataOverageRate" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         *       &lt;/sequence&gt;
         *     &lt;/restriction&gt;
         *   &lt;/complexContent&gt;
         * &lt;/complexType&gt;
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "eventName",
            "version",
            "term",
            "price",
            "includedData",
            "dataOverageRate"
        })
        public static class AvailableEvent {

            @XmlElement(name = "EventName", required = true)
            protected String eventName;
            @XmlElement(name = "Version")
            protected long version;
            @XmlElement(name = "Term")
            protected Long term;
            @XmlElement(name = "Price", required = true)
            protected BigDecimal price;
            @XmlElement(name = "IncludedData", required = true)
            protected BigDecimal includedData;
            @XmlElement(name = "DataOverageRate", required = true)
            protected BigDecimal dataOverageRate;

            /**
             * 获取eventName属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getEventName() {
                return eventName;
            }

            /**
             * 设置eventName属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setEventName(String value) {
                this.eventName = value;
            }

            /**
             * 获取version属性的值。
             * 
             */
            public long getVersion() {
                return version;
            }

            /**
             * 设置version属性的值。
             * 
             */
            public void setVersion(long value) {
                this.version = value;
            }

            /**
             * 获取term属性的值。
             * 
             * @return
             *     possible object is
             *     {@link Long }
             *     
             */
            public Long getTerm() {
                return term;
            }

            /**
             * 设置term属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link Long }
             *     
             */
            public void setTerm(Long value) {
                this.term = value;
            }

            /**
             * 获取price属性的值。
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getPrice() {
                return price;
            }

            /**
             * 设置price属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setPrice(BigDecimal value) {
                this.price = value;
            }

            /**
             * 获取includedData属性的值。
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getIncludedData() {
                return includedData;
            }

            /**
             * 设置includedData属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setIncludedData(BigDecimal value) {
                this.includedData = value;
            }

            /**
             * 获取dataOverageRate属性的值。
             * 
             * @return
             *     possible object is
             *     {@link BigDecimal }
             *     
             */
            public BigDecimal getDataOverageRate() {
                return dataOverageRate;
            }

            /**
             * 设置dataOverageRate属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link BigDecimal }
             *     
             */
            public void setDataOverageRate(BigDecimal value) {
                this.dataOverageRate = value;
            }

        }

    }

}
