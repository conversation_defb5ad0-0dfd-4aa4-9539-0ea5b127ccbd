package com.jasperwireless.api.ws.schema;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.6
 * 2016-06-01T09:34:39.685+08:00
 * Generated source version: 3.1.6
 * 
 */
@WebServiceClient(name = "SmsService", 
                  wsdlLocation = "file:wsdl/Sms.wsdl",
                  targetNamespace = "http://api.jasperwireless.com/ws/schema") 
public class SmsService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://api.jasperwireless.com/ws/schema", "SmsService");
    public final static QName SmsPort = new QName("http://api.jasperwireless.com/ws/schema", "SmsPort");
    static {
        URL url = null;
        try {
            url = new URL("file:wsdl/Sms.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(SmsService.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "file:wsdl/Sms.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public SmsService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public SmsService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public SmsService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public SmsService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public SmsService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public SmsService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns SmsPortType
     */
    @WebEndpoint(name = "SmsPort")
    public SmsPortType getSmsPort() {
        return super.getPort(SmsPort, SmsPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SmsPortType
     */
    @WebEndpoint(name = "SmsPort")
    public SmsPortType getSmsPort(WebServiceFeature... features) {
        return super.getPort(SmsPort, SmsPortType.class, features);
    }

}
