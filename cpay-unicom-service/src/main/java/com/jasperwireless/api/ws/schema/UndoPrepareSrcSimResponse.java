
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="simTransferWorkflowId" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="sourceIccid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sourceSimState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "simTransferWorkflowId",
    "sourceIccid",
    "sourceSimState"
})
@XmlRootElement(name = "UndoPrepareSrcSimResponse")
public class UndoPrepareSrcSimResponse
    extends ResponseType
{

    protected long simTransferWorkflowId;
    @XmlElement(required = true)
    protected String sourceIccid;
    @XmlElement(required = true)
    protected String sourceSimState;

    /**
     * 获取simTransferWorkflowId属性的值。
     * 
     */
    public long getSimTransferWorkflowId() {
        return simTransferWorkflowId;
    }

    /**
     * 设置simTransferWorkflowId属性的值。
     * 
     */
    public void setSimTransferWorkflowId(long value) {
        this.simTransferWorkflowId = value;
    }

    /**
     * 获取sourceIccid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceIccid() {
        return sourceIccid;
    }

    /**
     * 设置sourceIccid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceIccid(String value) {
        this.sourceIccid = value;
    }

    /**
     * 获取sourceSimState属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceSimState() {
        return sourceSimState;
    }

    /**
     * 设置sourceSimState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceSimState(String value) {
        this.sourceSimState = value;
    }

}
