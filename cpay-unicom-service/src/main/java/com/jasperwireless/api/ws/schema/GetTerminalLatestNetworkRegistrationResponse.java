
package com.jasperwireless.api.ws.schema;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;
import org.w3c.dom.Element;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;group ref="{http://api.jasperwireless.com/ws/schema}GetTerminalLatestNetworkRegistrationResponseParamGroup"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "registrationEvents",
    "any"
})
@XmlRootElement(name = "GetTerminalLatestNetworkRegistrationResponse")
public class GetTerminalLatestNetworkRegistrationResponse
    extends ResponseType
{

    @XmlElement(required = true)
    protected GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents registrationEvents;
    @XmlAnyElement(lax = true)
    protected List<Object> any;

    /**
     * 获取registrationEvents属性的值。
     * 
     * @return
     *     possible object is
     *     {@link GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents }
     *     
     */
    public GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents getRegistrationEvents() {
        return registrationEvents;
    }

    /**
     * 设置registrationEvents属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents }
     *     
     */
    public void setRegistrationEvents(GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents value) {
        this.registrationEvents = value;
    }

    /**
     * Gets the value of the any property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the any property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAny().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Element }
     * {@link Object }
     * 
     * 
     */
    public List<Object> getAny() {
        if (any == null) {
            any = new ArrayList<Object>();
        }
        return this.any;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="registrationInfo" maxOccurs="unbounded"&gt;
     *           &lt;complexType&gt;
     *             &lt;complexContent&gt;
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *                 &lt;sequence&gt;
     *                   &lt;element name="imsi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
     *                   &lt;element name="gtAddr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
     *                   &lt;element name="sgsn" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
     *                   &lt;element name="vlr" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
     *                   &lt;element name="msc" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
     *                   &lt;element name="eventDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
     *                   &lt;element name="carrierName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
     *                   &lt;element name="mme_s4sgsn" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
     *                   &lt;element name="hostName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
     *                 &lt;/sequence&gt;
     *               &lt;/restriction&gt;
     *             &lt;/complexContent&gt;
     *           &lt;/complexType&gt;
     *         &lt;/element&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "registrationInfo"
    })
    public static class RegistrationEvents {

        @XmlElement(required = true)
        protected List<GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents.RegistrationInfo> registrationInfo;

        /**
         * Gets the value of the registrationInfo property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the registrationInfo property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getRegistrationInfo().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents.RegistrationInfo }
         * 
         * 
         */
        public List<GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents.RegistrationInfo> getRegistrationInfo() {
            if (registrationInfo == null) {
                registrationInfo = new ArrayList<GetTerminalLatestNetworkRegistrationResponse.RegistrationEvents.RegistrationInfo>();
            }
            return this.registrationInfo;
        }


        /**
         * <p>anonymous complex type的 Java 类。
         * 
         * <p>以下模式片段指定包含在此类中的预期内容。
         * 
         * <pre>
         * &lt;complexType&gt;
         *   &lt;complexContent&gt;
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
         *       &lt;sequence&gt;
         *         &lt;element name="imsi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         *         &lt;element name="gtAddr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         *         &lt;element name="sgsn" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         *         &lt;element name="vlr" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         *         &lt;element name="msc" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         *         &lt;element name="eventDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         *         &lt;element name="carrierName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         *         &lt;element name="mme_s4sgsn" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         *         &lt;element name="hostName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         *       &lt;/sequence&gt;
         *     &lt;/restriction&gt;
         *   &lt;/complexContent&gt;
         * &lt;/complexType&gt;
         * </pre>
         * 
         * 
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "imsi",
            "gtAddr",
            "sgsn",
            "vlr",
            "msc",
            "eventDate",
            "carrierName",
            "mmeS4Sgsn",
            "hostName"
        })
        public static class RegistrationInfo {

            protected String imsi;
            protected String gtAddr;
            protected Boolean sgsn;
            protected Boolean vlr;
            protected Boolean msc;
            @XmlSchemaType(name = "dateTime")
            protected XMLGregorianCalendar eventDate;
            protected String carrierName;
            @XmlElement(name = "mme_s4sgsn")
            protected Boolean mmeS4Sgsn;
            protected String hostName;

            /**
             * 获取imsi属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getImsi() {
                return imsi;
            }

            /**
             * 设置imsi属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setImsi(String value) {
                this.imsi = value;
            }

            /**
             * 获取gtAddr属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getGtAddr() {
                return gtAddr;
            }

            /**
             * 设置gtAddr属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setGtAddr(String value) {
                this.gtAddr = value;
            }

            /**
             * 获取sgsn属性的值。
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isSgsn() {
                return sgsn;
            }

            /**
             * 设置sgsn属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setSgsn(Boolean value) {
                this.sgsn = value;
            }

            /**
             * 获取vlr属性的值。
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isVlr() {
                return vlr;
            }

            /**
             * 设置vlr属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setVlr(Boolean value) {
                this.vlr = value;
            }

            /**
             * 获取msc属性的值。
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isMsc() {
                return msc;
            }

            /**
             * 设置msc属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setMsc(Boolean value) {
                this.msc = value;
            }

            /**
             * 获取eventDate属性的值。
             * 
             * @return
             *     possible object is
             *     {@link XMLGregorianCalendar }
             *     
             */
            public XMLGregorianCalendar getEventDate() {
                return eventDate;
            }

            /**
             * 设置eventDate属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link XMLGregorianCalendar }
             *     
             */
            public void setEventDate(XMLGregorianCalendar value) {
                this.eventDate = value;
            }

            /**
             * 获取carrierName属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getCarrierName() {
                return carrierName;
            }

            /**
             * 设置carrierName属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setCarrierName(String value) {
                this.carrierName = value;
            }

            /**
             * 获取mmeS4Sgsn属性的值。
             * 
             * @return
             *     possible object is
             *     {@link Boolean }
             *     
             */
            public Boolean isMmeS4Sgsn() {
                return mmeS4Sgsn;
            }

            /**
             * 设置mmeS4Sgsn属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *     
             */
            public void setMmeS4Sgsn(Boolean value) {
                this.mmeS4Sgsn = value;
            }

            /**
             * 获取hostName属性的值。
             * 
             * @return
             *     possible object is
             *     {@link String }
             *     
             */
            public String getHostName() {
                return hostName;
            }

            /**
             * 设置hostName属性的值。
             * 
             * @param value
             *     allowed object is
             *     {@link String }
             *     
             */
            public void setHostName(String value) {
                this.hostName = value;
            }

        }

    }

}
