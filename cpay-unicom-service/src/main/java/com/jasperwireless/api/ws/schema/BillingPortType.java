package com.jasperwireless.api.ws.schema;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.6
 * 2016-12-14T15:41:39.510+08:00
 * Generated source version: 3.1.6
 * 
 */
@WebService(targetNamespace = "http://api.jasperwireless.com/ws/schema", name = "BillingPortType")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface BillingPortType {

    @WebMethod(operationName = "GetTerminalUsageDataDetails", action = "http://api.jasperwireless.com/ws/service/billing/GetTerminalUsageDataDetails")
    @WebResult(name = "GetTerminalUsageDataDetailsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalUsageDataDetailsResponse getTerminalUsageDataDetails(
        @WebParam(partName = "body", name = "GetTerminalUsageDataDetailsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalUsageDataDetailsRequest body
    );

    @WebMethod(operationName = "GetTerminalUsageVoiceDetails", action = "http://api.jasperwireless.com/ws/service/billing/GetTerminalUsageVoiceDetails")
    @WebResult(name = "GetTerminalUsageVoiceDetailsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalUsageVoiceDetailsResponse getTerminalUsageVoiceDetails(
        @WebParam(partName = "body", name = "GetTerminalUsageVoiceDetailsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalUsageVoiceDetailsRequest body
    );

    @WebMethod(operationName = "GetTerminalUsageSmsDetails", action = "http://api.jasperwireless.com/ws/service/billing/GetTerminalUsageSmsDetails")
    @WebResult(name = "GetTerminalUsageSmsDetailsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalUsageSmsDetailsResponse getTerminalUsageSmsDetails(
        @WebParam(partName = "body", name = "GetTerminalUsageSmsDetailsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalUsageSmsDetailsRequest body
    );

    @WebMethod(operationName = "GetTerminalUsage", action = "http://api.jasperwireless.com/ws/service/billing/GetTerminalUsage")
    @WebResult(name = "GetTerminalUsageResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalUsageResponse getTerminalUsage(
        @WebParam(partName = "body", name = "GetTerminalUsageRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalUsageRequest body
    );

    @WebMethod(operationName = "GetInvoice", action = "http://api.jasperwireless.com/ws/service/billing/GetInvoice")
    @WebResult(name = "GetInvoiceResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetInvoiceResponse getInvoice(
        @WebParam(partName = "body", name = "GetInvoiceRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetInvoiceRequest body
    );
}
