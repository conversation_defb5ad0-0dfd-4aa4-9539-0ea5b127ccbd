package com.jasperwireless.api.ws.schema;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.1.6
 * 2016-12-14T15:41:39.574+08:00
 * Generated source version: 3.1.6
 * 
 */
@WebServiceClient(name = "BillingService", 
                  wsdlLocation = "file:wsdl/Billing.wsdl",
                  targetNamespace = "http://api.jasperwireless.com/ws/schema") 
public class BillingService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://api.jasperwireless.com/ws/schema", "BillingService");
    public final static QName BillingPort = new QName("http://api.jasperwireless.com/ws/schema", "BillingPort");
    static {
        URL url = null;
        try {
            url = new URL("file:wsdl/Billing.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(BillingService.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "file:wsdl/Billing.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public BillingService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public BillingService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public BillingService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public BillingService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public BillingService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public BillingService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns BillingPortType
     */
    @WebEndpoint(name = "BillingPort")
    public BillingPortType getBillingPort() {
        return super.getPort(BillingPort, BillingPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BillingPortType
     */
    @WebEndpoint(name = "BillingPort")
    public BillingPortType getBillingPort(WebServiceFeature... features) {
        return super.getPort(BillingPort, BillingPortType.class, features);
    }

}
