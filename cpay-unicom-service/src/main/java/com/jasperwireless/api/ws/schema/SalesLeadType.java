
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>SalesLeadType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SalesLeadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="company" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="industry" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="launchTimeframe" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="device" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="numberOfDevices" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="referralCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviceDetails" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SalesLeadType", propOrder = {
    "company",
    "industry",
    "launchTimeframe",
    "device",
    "numberOfDevices",
    "referralCode",
    "deviceDetails"
})
public class SalesLeadType {

    @XmlElement(required = true)
    protected String company;
    @XmlElement(required = true)
    protected String industry;
    @XmlElement(required = true)
    protected String launchTimeframe;
    @XmlElement(required = true)
    protected String device;
    protected int numberOfDevices;
    @XmlElement(required = true)
    protected String referralCode;
    protected String deviceDetails;

    /**
     * 获取company属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompany() {
        return company;
    }

    /**
     * 设置company属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompany(String value) {
        this.company = value;
    }

    /**
     * 获取industry属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndustry() {
        return industry;
    }

    /**
     * 设置industry属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndustry(String value) {
        this.industry = value;
    }

    /**
     * 获取launchTimeframe属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLaunchTimeframe() {
        return launchTimeframe;
    }

    /**
     * 设置launchTimeframe属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLaunchTimeframe(String value) {
        this.launchTimeframe = value;
    }

    /**
     * 获取device属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDevice() {
        return device;
    }

    /**
     * 设置device属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDevice(String value) {
        this.device = value;
    }

    /**
     * 获取numberOfDevices属性的值。
     * 
     */
    public int getNumberOfDevices() {
        return numberOfDevices;
    }

    /**
     * 设置numberOfDevices属性的值。
     * 
     */
    public void setNumberOfDevices(int value) {
        this.numberOfDevices = value;
    }

    /**
     * 获取referralCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferralCode() {
        return referralCode;
    }

    /**
     * 设置referralCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferralCode(String value) {
        this.referralCode = value;
    }

    /**
     * 获取deviceDetails属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeviceDetails() {
        return deviceDetails;
    }

    /**
     * 设置deviceDetails属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeviceDetails(String value) {
        this.deviceDetails = value;
    }

}
