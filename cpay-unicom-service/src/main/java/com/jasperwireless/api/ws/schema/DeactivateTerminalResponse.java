
package com.jasperwireless.api.ws.schema;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.w3c.dom.Element;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="DeactivateTerminalStatus"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="DeactivateTerminal" type="{http://api.jasperwireless.com/ws/schema}SimTransferResponseType"/&gt;
 *                   &lt;element name="simId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;any processContents='lax' maxOccurs="unbounded" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "deactivateTerminalStatus"
})
@XmlRootElement(name = "DeactivateTerminalResponse")
public class DeactivateTerminalResponse
    extends ResponseType
{

    @XmlElement(name = "DeactivateTerminalStatus", required = true)
    protected DeactivateTerminalResponse.DeactivateTerminalStatus deactivateTerminalStatus;

    /**
     * 获取deactivateTerminalStatus属性的值。
     * 
     * @return
     *     possible object is
     *     {@link DeactivateTerminalResponse.DeactivateTerminalStatus }
     *     
     */
    public DeactivateTerminalResponse.DeactivateTerminalStatus getDeactivateTerminalStatus() {
        return deactivateTerminalStatus;
    }

    /**
     * 设置deactivateTerminalStatus属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link DeactivateTerminalResponse.DeactivateTerminalStatus }
     *     
     */
    public void setDeactivateTerminalStatus(DeactivateTerminalResponse.DeactivateTerminalStatus value) {
        this.deactivateTerminalStatus = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="DeactivateTerminal" type="{http://api.jasperwireless.com/ws/schema}SimTransferResponseType"/&gt;
     *         &lt;element name="simId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
     *         &lt;any processContents='lax' maxOccurs="unbounded" minOccurs="0"/&gt;
     *       &lt;/sequence&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "deactivateTerminal",
        "simId",
        "any"
    })
    public static class DeactivateTerminalStatus {

        @XmlElement(name = "DeactivateTerminal", required = true)
        protected SimTransferResponseType deactivateTerminal;
        @XmlElement(required = true)
        protected String simId;
        @XmlAnyElement(lax = true)
        protected List<Object> any;

        /**
         * 获取deactivateTerminal属性的值。
         * 
         * @return
         *     possible object is
         *     {@link SimTransferResponseType }
         *     
         */
        public SimTransferResponseType getDeactivateTerminal() {
            return deactivateTerminal;
        }

        /**
         * 设置deactivateTerminal属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link SimTransferResponseType }
         *     
         */
        public void setDeactivateTerminal(SimTransferResponseType value) {
            this.deactivateTerminal = value;
        }

        /**
         * 获取simId属性的值。
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getSimId() {
            return simId;
        }

        /**
         * 设置simId属性的值。
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setSimId(String value) {
            this.simId = value;
        }

        /**
         * Gets the value of the any property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the any property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getAny().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link Element }
         * {@link Object }
         * 
         * 
         */
        public List<Object> getAny() {
            if (any == null) {
                any = new ArrayList<Object>();
            }
            return this.any;
        }

    }

}
