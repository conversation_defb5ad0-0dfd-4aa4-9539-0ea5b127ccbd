
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OperationResult" type="{http://api.jasperwireless.com/ws/schema}OperationalResultType"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "operationResult"
})
@XmlRootElement(name = "MBBVehicleDeregistrationResponse")
public class MBBVehicleDeregistrationResponse
    extends ResponseType
{

    @XmlElement(name = "OperationResult", required = true)
    protected OperationalResultType operationResult;

    /**
     * 获取operationResult属性的值。
     * 
     * @return
     *     possible object is
     *     {@link OperationalResultType }
     *     
     */
    public OperationalResultType getOperationResult() {
        return operationResult;
    }

    /**
     * 设置operationResult属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link OperationalResultType }
     *     
     */
    public void setOperationResult(OperationalResultType value) {
        this.operationResult = value;
    }

}
