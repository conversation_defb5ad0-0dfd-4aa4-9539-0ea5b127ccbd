
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}ResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="simTransferWorkflowId" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="targetIccid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="targetSimState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="targetSimRatePlan" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="targetSimCommPlan" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "simTransferWorkflowId",
    "targetIccid",
    "targetSimState",
    "targetSimRatePlan",
    "targetSimCommPlan"
})
@XmlRootElement(name = "PrepareTargetSimResponse")
public class PrepareTargetSimResponse
    extends ResponseType
{

    protected long simTransferWorkflowId;
    @XmlElement(required = true)
    protected String targetIccid;
    @XmlElement(required = true)
    protected String targetSimState;
    protected String targetSimRatePlan;
    protected String targetSimCommPlan;

    /**
     * 获取simTransferWorkflowId属性的值。
     * 
     */
    public long getSimTransferWorkflowId() {
        return simTransferWorkflowId;
    }

    /**
     * 设置simTransferWorkflowId属性的值。
     * 
     */
    public void setSimTransferWorkflowId(long value) {
        this.simTransferWorkflowId = value;
    }

    /**
     * 获取targetIccid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargetIccid() {
        return targetIccid;
    }

    /**
     * 设置targetIccid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargetIccid(String value) {
        this.targetIccid = value;
    }

    /**
     * 获取targetSimState属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargetSimState() {
        return targetSimState;
    }

    /**
     * 设置targetSimState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargetSimState(String value) {
        this.targetSimState = value;
    }

    /**
     * 获取targetSimRatePlan属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargetSimRatePlan() {
        return targetSimRatePlan;
    }

    /**
     * 设置targetSimRatePlan属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargetSimRatePlan(String value) {
        this.targetSimRatePlan = value;
    }

    /**
     * 获取targetSimCommPlan属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTargetSimCommPlan() {
        return targetSimCommPlan;
    }

    /**
     * 设置targetSimCommPlan属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTargetSimCommPlan(String value) {
        this.targetSimCommPlan = value;
    }

}
