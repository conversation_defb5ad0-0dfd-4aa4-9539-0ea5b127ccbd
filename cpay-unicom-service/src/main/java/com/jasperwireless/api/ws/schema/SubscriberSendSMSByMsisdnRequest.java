
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}SubscriberRequestType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="requestKey" type="{http://api.jasperwireless.com/ws/schema}RequestKeyType"/&gt;
 *         &lt;element name="sentToMsisdn" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="messageText" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="messageTextEncoding" type="{http://api.jasperwireless.com/ws/schema}messageTextEncodingType"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "requestKey",
    "sentToMsisdn",
    "messageText",
    "messageTextEncoding"
})
@XmlRootElement(name = "SubscriberSendSMSByMsisdnRequest")
public class SubscriberSendSMSByMsisdnRequest
    extends SubscriberRequestType
{

    @XmlElement(required = true)
    protected RequestKeyType requestKey;
    @XmlElement(required = true)
    protected String sentToMsisdn;
    @XmlElement(required = true)
    protected String messageText;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected MessageTextEncodingType messageTextEncoding;

    /**
     * 获取requestKey属性的值。
     * 
     * @return
     *     possible object is
     *     {@link RequestKeyType }
     *     
     */
    public RequestKeyType getRequestKey() {
        return requestKey;
    }

    /**
     * 设置requestKey属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link RequestKeyType }
     *     
     */
    public void setRequestKey(RequestKeyType value) {
        this.requestKey = value;
    }

    /**
     * 获取sentToMsisdn属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSentToMsisdn() {
        return sentToMsisdn;
    }

    /**
     * 设置sentToMsisdn属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSentToMsisdn(String value) {
        this.sentToMsisdn = value;
    }

    /**
     * 获取messageText属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMessageText() {
        return messageText;
    }

    /**
     * 设置messageText属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMessageText(String value) {
        this.messageText = value;
    }

    /**
     * 获取messageTextEncoding属性的值。
     * 
     * @return
     *     possible object is
     *     {@link MessageTextEncodingType }
     *     
     */
    public MessageTextEncodingType getMessageTextEncoding() {
        return messageTextEncoding;
    }

    /**
     * 设置messageTextEncoding属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link MessageTextEncodingType }
     *     
     */
    public void setMessageTextEncoding(MessageTextEncodingType value) {
        this.messageTextEncoding = value;
    }

}
