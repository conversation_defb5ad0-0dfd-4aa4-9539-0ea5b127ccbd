package com.jasperwireless.api.ws.schema;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.6
 * 2016-06-01T09:32:56.352+08:00
 * Generated source version: 3.1.6
 * 
 */
@WebService(targetNamespace = "http://api.jasperwireless.com/ws/schema", name = "TerminalPortType")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface TerminalPortType {

    @WebMethod(operationName = "EditTerminal", action = "http://api.jasperwireless.com/ws/service/terminal/EditTerminal")
    @WebResult(name = "EditTerminalResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public EditTerminalResponse editTerminal(
        @WebParam(partName = "body", name = "EditTerminalRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        EditTerminalRequest body
    );

    @WebMethod(operationName = "QueueTerminalRatePlan", action = "http://api.jasperwireless.com/ws/service/terminal/QueueTerminalRatePlan")
    @WebResult(name = "QueueTerminalRatePlanResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public QueueTerminalRatePlanResponse queueTerminalRatePlan(
        @WebParam(partName = "body", name = "QueueTerminalRatePlanRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        QueueTerminalRatePlanRequest body
    );

    @WebMethod(operationName = "GetSessionInfo", action = "http://api.jasperwireless.com/ws/service/terminal/GetSessionInfo")
    @WebResult(name = "GetSessionInfoResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetSessionInfoResponse getSessionInfo(
        @WebParam(partName = "body", name = "GetSessionInfoRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetSessionInfoRequest body
    );

    @WebMethod(operationName = "UpdateSecureSimCredentials", action = "http://api.jasperwireless.com/ws/service/terminal/UpdateSecureSimCredentials")
    @WebResult(name = "UpdateSecureSimCredentialsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public UpdateSecureSimCredentialsResponse updateSecureSimCredentials(
        @WebParam(partName = "body", name = "UpdateSecureSimCredentialsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        UpdateSecureSimCredentialsRequest body
    );

    @WebMethod(operationName = "EditTerminalRating", action = "http://api.jasperwireless.com/ws/service/terminal/EditTerminalRating")
    @WebResult(name = "EditTerminalRatingResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public EditTerminalRatingResponse editTerminalRating(
        @WebParam(partName = "body", name = "EditTerminalRatingRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        EditTerminalRatingRequest body
    );

    @WebMethod(operationName = "TransferSimsToAccount", action = "http://api.jasperwireless.com/ws/service/terminal/TransferSimsToAccount")
    @WebResult(name = "TransferSimsToAccountResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public TransferSimsToAccountResponse transferSimsToAccount(
        @WebParam(partName = "body", name = "TransferSimsToAccountRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        TransferSimsToAccountRequest body
    );

    @WebMethod(operationName = "TransferTrialSimsToAccount", action = "http://api.jasperwireless.com/ws/service/terminal/TransferTrialSimsToAccount")
    @WebResult(name = "TransferTrialSimsToAccountResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public TransferTrialSimsToAccountResponse transferTrialSimsToAccount(
        @WebParam(partName = "body", name = "TransferTrialSimsToAccountRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        TransferTrialSimsToAccountRequest body
    );

    @WebMethod(operationName = "GetTerminalLatestNetworkRegistration", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalLatestNetworkRegistration")
    @WebResult(name = "GetTerminalLatestNetworkRegistrationResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalLatestNetworkRegistrationResponse getTerminalLatestNetworkRegistration(
        @WebParam(partName = "body", name = "GetTerminalLatestNetworkRegistrationRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalLatestNetworkRegistrationRequest body
    );

    @WebMethod(operationName = "GetTerminalsByImsi", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalsByImsi")
    @WebResult(name = "GetTerminalsByImsiResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalsByImsiResponse getTerminalsByImsi(
        @WebParam(partName = "body", name = "GetTerminalsByImsiRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalsByImsiRequest body
    );

    @WebMethod(operationName = "GetTerminalsBySecureSimId", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalsBySecureSimId")
    @WebResult(name = "GetTerminalsBySecureSimIdResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalsBySecureSimIdResponse getTerminalsBySecureSimId(
        @WebParam(partName = "body", name = "GetTerminalsBySecureSimIdRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalsBySecureSimIdRequest body
    );

    @WebMethod(operationName = "GetModifiedTerminals", action = "http://api.jasperwireless.com/ws/service/terminal/GetModifiedTerminals")
    @WebResult(name = "GetModifiedTerminalsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetModifiedTerminalsResponse getModifiedTerminals(
        @WebParam(partName = "body", name = "GetModifiedTerminalsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetModifiedTerminalsRequest body
    );

    @WebMethod(operationName = "GetTerminalLatestRegistration", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalLatestRegistration")
    @WebResult(name = "GetTerminalLatestRegistrationResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalLatestRegistrationResponse getTerminalLatestRegistration(
        @WebParam(partName = "body", name = "GetTerminalLatestRegistrationRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalLatestRegistrationRequest body
    );

    @WebMethod(operationName = "EditLinePayStatus", action = "http://api.jasperwireless.com/ws/service/terminal/EditLinePayStatus")
    @WebResult(name = "EditLinePayStatusResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public EditLinePayStatusResponse editLinePayStatus(
        @WebParam(partName = "body", name = "EditLinePayStatusRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        EditLinePayStatusRequest body
    );

    @WebMethod(operationName = "RemoveRatePlanFromQueue", action = "http://api.jasperwireless.com/ws/service/terminal/RemoveRatePlanFromQueue")
    @WebResult(name = "RemoveRatePlanFromQueueResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public RemoveRatePlanFromQueueResponse removeRatePlanFromQueue(
        @WebParam(partName = "body", name = "RemoveRatePlanFromQueueRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        RemoveRatePlanFromQueueRequest body
    );

    @WebMethod(operationName = "GetLinePayStatusByOpAcctId", action = "http://api.jasperwireless.com/ws/service/terminal/GetLinePayStatusByOpAcctId")
    @WebResult(name = "GetLinePayStatusByOpAcctIdResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetLinePayStatusByOpAcctIdResponse getLinePayStatusByOpAcctId(
        @WebParam(partName = "body", name = "GetLinePayStatusByOpAcctIdRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetLinePayStatusByOpAcctIdRequest body
    );

    @WebMethod(operationName = "SimRmaExchange", action = "http://api.jasperwireless.com/ws/service/terminal/SimRmaExchange")
    @WebResult(name = "SimRmaExchangeResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public SimRmaExchangeResponse simRmaExchange(
        @WebParam(partName = "body", name = "SimRmaExchangeRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        SimRmaExchangeRequest body
    );

    @WebMethod(operationName = "TransferTerminalAccountNoSimStatus", action = "http://api.jasperwireless.com/ws/service/terminal/TransferTerminalAccountNoSimStatus")
    @WebResult(name = "TransferTerminalAccountNoSimStatusResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public TransferTerminalAccountNoSimStatusResponse transferTerminalAccountNoSimStatus(
        @WebParam(partName = "body", name = "TransferTerminalAccountNoSimStatusRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        TransferTerminalAccountNoSimStatusRequest body
    );

    @WebMethod(operationName = "GetTerminalsByMsisdn", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalsByMsisdn")
    @WebResult(name = "GetTerminalsByMsisdnResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalsByMsisdnResponse getTerminalsByMsisdn(
        @WebParam(partName = "body", name = "GetTerminalsByMsisdnRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalsByMsisdnRequest body
    );

    @WebMethod(operationName = "GetTerminalRating", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalRating")
    @WebResult(name = "GetTerminalRatingResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalRatingResponse getTerminalRating(
        @WebParam(partName = "body", name = "GetTerminalRatingRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalRatingRequest body
    );

    @WebMethod(operationName = "EditLinePayStatusPerAcct", action = "http://api.jasperwireless.com/ws/service/terminal/EditLinePayStatusPerAcct")
    @WebResult(name = "EditLinePayStatusPerAcctResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public EditLinePayStatusPerAcctResponse editLinePayStatusPerAcct(
        @WebParam(partName = "body", name = "EditLinePayStatusPerAcctRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        EditLinePayStatusPerAcctRequest body
    );

    @WebMethod(operationName = "GetTerminalDetails", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalDetails")
    @WebResult(name = "GetTerminalDetailsResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalDetailsResponse getTerminalDetails(
        @WebParam(partName = "body", name = "GetTerminalDetailsRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalDetailsRequest body
    );

    @WebMethod(operationName = "AssignOrUpdateIPAddress", action = "http://api.jasperwireless.com/ws/service/terminal/AssignOrUpdateIPAddress")
    @WebResult(name = "AssignOrUpdateIPAddressResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public AssignOrUpdateIPAddressResponse assignOrUpdateIPAddress(
        @WebParam(partName = "body", name = "AssignOrUpdateIPAddressRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        AssignOrUpdateIPAddressRequest body
    );

    @WebMethod(operationName = "GetTerminalAuditTrail", action = "http://api.jasperwireless.com/ws/service/terminal/GetTerminalAuditTrail")
    @WebResult(name = "GetTerminalAuditTrailResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public GetTerminalAuditTrailResponse getTerminalAuditTrail(
        @WebParam(partName = "body", name = "GetTerminalAuditTrailRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        GetTerminalAuditTrailRequest body
    );

    @WebMethod(operationName = "SendCancelLocation", action = "http://api.jasperwireless.com/ws/service/terminal/SendCancelLocation")
    @WebResult(name = "SendCancelLocationResponse", targetNamespace = "http://api.jasperwireless.com/ws/schema", partName = "body")
    public SendCancelLocationResponse sendCancelLocation(
        @WebParam(partName = "body", name = "SendCancelLocationRequest", targetNamespace = "http://api.jasperwireless.com/ws/schema")
        SendCancelLocationRequest body
    );
}
