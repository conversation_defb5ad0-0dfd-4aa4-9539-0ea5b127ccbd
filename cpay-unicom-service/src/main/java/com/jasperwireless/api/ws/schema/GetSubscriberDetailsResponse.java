
package com.jasperwireless.api.ws.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;extension base="{http://api.jasperwireless.com/ws/schema}SubscriberResponseType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="subscriberDetails" type="{http://api.jasperwireless.com/ws/schema}SubscriberDetailsType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;anyAttribute processContents='lax'/&gt;
 *     &lt;/extension&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "subscriberDetails"
})
@XmlRootElement(name = "GetSubscriberDetailsResponse")
public class GetSubscriberDetailsResponse
    extends SubscriberResponseType
{

    protected SubscriberDetailsType subscriberDetails;

    /**
     * 获取subscriberDetails属性的值。
     * 
     * @return
     *     possible object is
     *     {@link SubscriberDetailsType }
     *     
     */
    public SubscriberDetailsType getSubscriberDetails() {
        return subscriberDetails;
    }

    /**
     * 设置subscriberDetails属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link SubscriberDetailsType }
     *     
     */
    public void setSubscriberDetails(SubscriberDetailsType value) {
        this.subscriberDetails = value;
    }

}
