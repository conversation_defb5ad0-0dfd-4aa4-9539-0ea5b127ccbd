package com.centerm.cpay.cas.unicom.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.cas.unicom.service.UnicomBillingService;
import com.centerm.cpay.common.utils.TimeUtil;
import com.jasperwireless.api.ws.schema.BillingPortType;
import com.jasperwireless.api.ws.schema.GetTerminalUsageDataDetailsRequest;
import com.jasperwireless.api.ws.schema.GetTerminalUsageDataDetailsResponse;

@Service
public class UnicomBillingServiceImpl implements UnicomBillingService {
	
	@Autowired
	private BillingPortType billingFactory;
	
	@Autowired
	private UnicomPropertyServiceImpl propertyServiceImpl;
	
	@Override
	public String getTerminalFlowUsage(String iccid) throws Exception {
		GetTerminalUsageDataDetailsRequest getTerminalUsageDataDetailRequest = new GetTerminalUsageDataDetailsRequest();
		getTerminalUsageDataDetailRequest.setIccid(iccid.substring(0,iccid.length()-1));
		getTerminalUsageDataDetailRequest.setLicenseKey(propertyServiceImpl.m2mLicense);
		getTerminalUsageDataDetailRequest.setVersion(propertyServiceImpl.m2mVersion);
		getTerminalUsageDataDetailRequest.setCycleStartDate(TimeUtil.dateToXmlDate(TimeUtil.getFristDayForMonth(new Date())));
		getTerminalUsageDataDetailRequest.setMessageId("");
		GetTerminalUsageDataDetailsResponse getTerminalUsageResponse = billingFactory.getTerminalUsageDataDetails(getTerminalUsageDataDetailRequest);
		return String.valueOf(getTerminalUsageResponse.getUsageDetails());
	}

}
