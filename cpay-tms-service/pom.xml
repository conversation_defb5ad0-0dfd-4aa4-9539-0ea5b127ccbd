<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.centerm</groupId>
		<artifactId>cpay-coms</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<groupId>com.centerm</groupId>
	<artifactId>cpay-tms-service</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>cpay-tms-service</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<!-- <version>3.8.1</version> -->
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-commons</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<!-- <dependency> <groupId>com.centerm</groupId> <artifactId>cpay-terminal-service</artifactId> 
			<version>0.0.1-SNAPSHOT</version> </dependency> -->


		<dependency>
			<groupId>com.centerm</groupId>
			<artifactId>cpay-security</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-bindings-soap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.13</version>
		</dependency>

		<!-- <dependency> <groupId>org.apache.mina</groupId> <artifactId>mina-example</artifactId> 
			<version>2.0.13</version> </dependency> -->

		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-filter-compression</artifactId>
			<version>2.0.13</version>
		</dependency>

		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-http</artifactId>
			<version>2.0.13</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>

		<!-- <dependency> <groupId>org.apache.mina</groupId> <artifactId>mina-transport-serial</artifactId> 
			<version>2.0.13</version> </dependency> <dependency> <groupId>org.apache.mina</groupId> 
			<artifactId>mina-transport-apr</artifactId> <version>2.0.13</version> </dependency> 
			<dependency> <groupId>org.apache.mina</groupId> <artifactId>mina-statemachine</artifactId> 
			<version>2.0.13</version> </dependency> -->
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-parent</artifactId>
			<version>2.0.13</version>
			<type>pom</type>
		</dependency>
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-integration-beans</artifactId>
			<version>2.0.13</version>
		</dependency>
		<!-- <dependency> <groupId>org.apache.mina</groupId> <artifactId>mina-legal</artifactId> 
			<version>2.0.13</version> </dependency> <dependency> <groupId>org.apache.mina</groupId> 
			<artifactId>mina-integration-xbean</artifactId> <version>2.0.13</version> 
			<type>xsd.html</type> </dependency> <dependency> <groupId>org.apache.mina</groupId> 
			<artifactId>mina-integration-ognl</artifactId> <version>2.0.13</version> 
			</dependency> <dependency> <groupId>org.apache.mina</groupId> <artifactId>mina-integration-jmx</artifactId> 
			<version>2.0.13</version> </dependency> <dependency> <groupId>org.apache.mina</groupId> 
			<artifactId>mina-integration-beans</artifactId> <version>2.0.13</version> 
			</dependency> -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.15</version>
			<exclusions>
				<exclusion>
					<groupId>com.sun.jmx</groupId>
					<artifactId>jmxri</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jdmk</groupId>
					<artifactId>jmxtools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.jms</groupId>
					<artifactId>jms</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<!-- https://mvnrepository.com/artifact/commons-dbcp/commons-dbcp -->
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>


		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<!-- <version>1.6.6</version> -->
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.6.6</version>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/commons-httpclient/commons-httpclient -->
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<!-- <version>4.5.2</version> -->
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.5</version>
		</dependency>

		<!-- Mybatis -->
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.jsqlparser</groupId>
			<artifactId>jsqlparser</artifactId>
		</dependency>
		<!-- MySql -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!-- 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>


		<!-- Spring -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<!-- <dependency> <groupId>org.springframework</groupId> <artifactId>spring-webmvc</artifactId> 
			</dependency> -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>

		<dependency> 
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency> 

		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.1</version>
		</dependency>

	</dependencies>
	<distributionManagement>
		<snapshotRepository>
			<id>cpay-snapshots</id>
			<name>cpay-snapshots Repository</name>
			<url>http://192.168.48.66:8081/repository/maven-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	<build>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>

			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
				</includes>
				<excludes>
						<exclude>test/*</exclude>
						<exclude>test1/*</exclude>
						<exclude>pro/*</exclude>
						<exclude>dev/*</exclude>
					</excludes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/resources/${profiles.active}</directory>
			</resource>
		</resources>
		<finalName>cpay-tms</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<classesDirectory>target/classes/</classesDirectory>
					<archive>
						<manifest>
							<mainClass>com.centerm.cpay.tms.main.TmsMain</mainClass>
							<!-- 打包时 MANIFEST.MF文件不记录的时间戳版本 -->
							<useUniqueVersions>false</useUniqueVersions>
							<addClasspath>true</addClasspath>
							<classpathPrefix></classpathPrefix>
						</manifest>
						<manifestEntries>
							<Class-Path>.</Class-Path>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>3.0.1</version>
				<extensions>true</extensions>
			</plugin>

			<!-- 解决Maven插件在Eclipse内执行了一系列的生命周期引起冲突 -->
			<!-- <plugin> <groupId>org.eclipse.m2e</groupId> <artifactId>lifecycle-mapping</artifactId> 
				<version>1.0.0</version> <configuration> <lifecycleMappingMetadata> <pluginExecutions> 
				<pluginExecution> <pluginExecutionFilter> <groupId>org.apache.maven.plugins</groupId> 
				<artifactId>maven-dependency-plugin</artifactId> <versionRange>[2.0,)</versionRange> 
				<goals> <goal>copy-dependencies</goal> </goals> </pluginExecutionFilter> 
				<action> <ignore /> </action> </pluginExecution> </pluginExecutions> </lifecycleMappingMetadata> 
				</configuration> </plugin> <plugin> <artifactId>maven-dependency-plugin</artifactId> 
				<executions> <execution> <id>unpack</id> <phase>package</phase> <goals> <goal>unpack</goal> 
				</goals> <configuration> <artifactItems> <artifactItem> <groupId>com.centerm.cpay</groupId> 
				<artifactId>cpay-tms-service</artifactId> <version>${project.version}</version> 
				<outputDirectory>${project.build.directory}/cpay-tms-service</outputDirectory> 
				<includes>assembly/**</includes> </artifactItem> </artifactItems> </configuration> 
				</execution> </executions> </plugin> -->
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<descriptor>src/main/assembly/assembly.xml</descriptor>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>


		</plugins>
	</build>
</project>