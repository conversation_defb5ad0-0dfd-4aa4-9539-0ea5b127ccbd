<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.DownloadTaskMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="job_id" property="jobId" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="terminal_type_id" property="terminalTypeId" jdbcType="INTEGER" />
    <result column="manufacturer_id" property="manufacturerId" jdbcType="INTEGER" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="app_type" property="appType" jdbcType="CHAR" />
    <result column="app_ver" property="appVer" jdbcType="VARCHAR" />
    <result column="app_file_name" property="appFileName" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="md5" property="md5" jdbcType="VARCHAR" />
    <result column="dl_flag" property="dlFlag" jdbcType="CHAR" />
    <result column="dl_begin_date" property="dlBeginDate" jdbcType="VARCHAR" />
    <result column="dl_end_date" property="dlEndDate" jdbcType="VARCHAR" />
    <result column="release_time" property="releaseTime" jdbcType="VARCHAR" />
    <result column="valid_date" property="validDate" jdbcType="VARCHAR" />
    <result column="record_create_time" property="recordCreateTime" jdbcType="VARCHAR" />
    <result column="isShow_notice" property="isShowNotice" jdbcType="CHAR" />
    <result column="result_message" property="resultMessage" jdbcType="VARCHAR" />
    <result column="size" property="size" jdbcType="INTEGER" />
    <result column="job_action" property="jobAction" jdbcType="CHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="depend_task_id" property="dependTaskId" jdbcType="VARCHAR" />
    <result column="depend_task_ids" property="dependTaskIds" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, job_id, term_seq, terminal_type_id, manufacturer_id, app_code, app_type, app_ver, 
    app_file_name, url, md5, dl_flag, dl_begin_date, dl_end_date, release_time, valid_date, 
    record_create_time,isShow_notice,result_message,size,job_action,app_name,depend_task_id,depend_task_ids
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_download_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="countByTermSeq" resultType="java.lang.Integer" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTaskCondition" >
    select 
    count(*)
    from coms_download_task
    where term_seq = #{termSeq,jdbcType=VARCHAR}
    and dl_flag = '0'
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
  </select>


  <select id="getDownloadTasks" resultMap="BaseResultMap" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTaskCondition" >
    select
    <include refid="Base_Column_List" />
    from coms_download_task
    where term_seq = #{termSeq,jdbcType=VARCHAR}
    and dl_flag = '0'
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
    order by job_id
  </select>
  <update id="stopRepeatTasks" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTaskCondition" >
        UPDATE COMS_DOWNLOAD_TASK SET DL_FLAG = '9',RESULT_MESSAGE = 'This software has new tasks available'
        WHERE term_seq = #{termSeq,jdbcType=VARCHAR}
        and dl_flag = '0'
    	and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
		and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
        AND id IN (SELECT b.id FROM
      (SELECT * FROM coms_download_task where term_seq = #{termSeq,jdbcType=VARCHAR}
        and dl_flag = '0'
        and job_action = '7'
        and app_type in ('0','1','a')
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>) b
      inner join (select
      APP_CODE, max(APP_VER) APP_VER
      from coms_download_task
      where term_seq = #{termSeq,jdbcType=VARCHAR}
      and dl_flag = '0'
      and job_action = '7'
      and app_type in ('0','1','a')
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>  group by APP_CODE ORDER BY APP_VER desc) x
      ON x.APP_CODE = b.APP_CODE AND x.APP_VER != b.APP_VER)
    </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_download_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    insert into coms_download_task (job_id, term_seq, 
      terminal_type_id, manufacturer_id, app_code, 
      app_type, app_ver, app_file_name, 
      url, md5, dl_flag, dl_begin_date, 
      dl_end_date, release_time, valid_date, 
      record_create_time,isShow_notice,result_message,size)
    values (#{jobId,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR}, 
      #{terminalTypeId,jdbcType=INTEGER}, #{manufacturerId,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, 
      #{appType,jdbcType=CHAR}, #{appVer,jdbcType=VARCHAR}, #{appFileName,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, #{dlFlag,jdbcType=CHAR}, #{dlBeginDate,jdbcType=VARCHAR}, 
      #{dlEndDate,jdbcType=VARCHAR}, #{releaseTime,jdbcType=VARCHAR}, #{validDate,jdbcType=VARCHAR}, 
      #{recordCreateTime,jdbcType=VARCHAR},#{isShowNotice,jdbcType=CHAR},#{resultMessage,jdbcType=VARCHAR},
      #{size,jdbcType=INTEGER)
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    insert into coms_download_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="jobId != null" >
        job_id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="terminalTypeId != null" >
        terminal_type_id,
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="appType != null" >
        app_type,
      </if>
      <if test="appVer != null" >
        app_ver,
      </if>
      <if test="appFileName != null" >
        app_file_name,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="md5 != null" >
        md5,
      </if>
      <if test="dlFlag != null" >
        dl_flag,
      </if>
      <if test="dlBeginDate != null" >
        dl_begin_date,
      </if>
      <if test="dlEndDate != null" >
        dl_end_date,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
      <if test="validDate != null" >
        valid_date,
      </if>
      <if test="recordCreateTime != null" >
        record_create_time,
      </if>
      <if test="isShowNotice != null" >
        isShow_notice,
      </if>
      <if test="resultMessage != null" >
        result_message,
      </if>
      <if test="size != null" >
        size,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="jobId != null" >
        #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="terminalTypeId != null" >
        #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="manufacturerId != null" >
        #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appType != null" >
        #{appType,jdbcType=CHAR},
      </if>
      <if test="appVer != null" >
        #{appVer,jdbcType=VARCHAR},
      </if>
      <if test="appFileName != null" >
        #{appFileName,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="dlFlag != null" >
        #{dlFlag,jdbcType=CHAR},
      </if>
      <if test="dlBeginDate != null" >
        #{dlBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="dlEndDate != null" >
        #{dlEndDate,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=VARCHAR},
      </if>
      <if test="validDate != null" >
        #{validDate,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        #{recordCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="isShowNotice != null" >
        #{isShowNotice,jdbcType=CHAR},
      </if>
      <if test="resultMessage != null" >
        #{resultMessage,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    update coms_download_task
    <set >
      <if test="jobId != null" >
        job_id = #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="terminalTypeId != null" >
        terminal_type_id = #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appType != null" >
        app_type = #{appType,jdbcType=CHAR},
      </if>
      <if test="appVer != null" >
        app_ver = #{appVer,jdbcType=VARCHAR},
      </if>
      <if test="appFileName != null" >
        app_file_name = #{appFileName,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="dlFlag != null" >
        dl_flag = #{dlFlag,jdbcType=CHAR},
      </if>
      <if test="dlBeginDate != null" >
        dl_begin_date = #{dlBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="dlEndDate != null" >
        dl_end_date = #{dlEndDate,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=VARCHAR},
      </if>
      <if test="validDate != null" >
        valid_date = #{validDate,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        record_create_time = #{recordCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="isShowNotice != null" >
        isShow_notice = #{isShowNotice,jdbcType=CHAR},
      </if>
      <if test="resultMessage != null" >
        result_message = #{resultMessage,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        size = #{size,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    update coms_download_task
    set job_id = #{jobId,jdbcType=INTEGER},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      terminal_type_id = #{terminalTypeId,jdbcType=INTEGER},
      manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      app_code = #{appCode,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=CHAR},
      app_ver = #{appVer,jdbcType=VARCHAR},
      app_file_name = #{appFileName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      dl_flag = #{dlFlag,jdbcType=CHAR},
      dl_begin_date = #{dlBeginDate,jdbcType=VARCHAR},
      dl_end_date = #{dlEndDate,jdbcType=VARCHAR},
      release_time = #{releaseTime,jdbcType=VARCHAR},
      valid_date = #{validDate,jdbcType=VARCHAR},
      record_create_time = #{recordCreateTime,jdbcType=VARCHAR},
      isShow_notice = #{isShowNotice,jdbcType=CHAR},
      result_message = #{resultMessage,jdbcType=VARCHAR},
      size = #{size,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByJobIdAndTermSeq" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" >
    update coms_download_task
    set dl_flag = #{dlFlag,jdbcType=CHAR},
      result_message = #{resultMessage,jdbcType=VARCHAR},
      dl_begin_date = #{dlBeginDate,jdbcType=VARCHAR},
      dl_end_date = #{dlEndDate,jdbcType=VARCHAR}
      where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKeyList" parameterType="java.util.List" >
    update coms_download_task
    set dl_flag = '1'
    where  id in 
    	<foreach item="item" index="index" collection="list" 
                 open="(" separator="," close=")">
                #{item}
        </foreach>
  </update>
  <update id="updateFlagThreeByPrimaryKeyList" parameterType="java.util.List" >
    update coms_download_task
    set dl_flag = '3'
    where id in 
    	<foreach item="item" index="index" collection="list" 
                 open="(" separator="," close=")">
                #{item}
        </foreach>
  </update>
  <select id="getDriverJobName" parameterType="java.lang.Integer" resultType="java.lang.String">
  		select job_name from coms_download_job where  id = #{id,jdbcType=INTEGER}
  </select>

  <select id="countDependWorking" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" resultType="java.lang.Integer">
  	select
    count(*)
    from coms_download_task
    where term_seq = #{termSeq,jdbcType=VARCHAR}
    and job_id = #{jobId,jdbcType=INTEGER}
    and dl_flag in ('0','1','2','3')
    and depend_task_id in
    <foreach item="item" index="index" collection="dependTaskIds.split(',')" open="(" separator="," close=")">
      #{item}
    </foreach>
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
  </select>
  <select id="countDependNotInSuccess" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" resultType="java.lang.Integer">
    select
    count(*)
    from coms_download_task
    where term_seq = #{termSeq,jdbcType=VARCHAR}
    and job_id = #{jobId,jdbcType=INTEGER}
    AND DL_FLAG not in ('4','5')
    and depend_task_id in
    <foreach item="item" index="index" collection="dependTaskIds.split(',')" open="(" separator="," close=")">
      #{item}
    </foreach>
    and <![CDATA[release_time<=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
	and <![CDATA[valid_date>=date_format(#{now,jdbcType=TIMESTAMP},'%Y%m%d%H%i%s')]]>
  </select>
  <select id="checkAppId" parameterType="com.centerm.cpay.tms.dao.pojo.DownloadTask" resultType="java.lang.Integer">
    select
    b.app_id
    from coms_download_task a
    left join COMS_DOWNLOAD_JOB_APP b
    on a.JOB_ID = b.JOB_ID and a.APP_CODE = b.APP_CODE
    where a.id = #{id,jdbcType=INTEGER}
    limit 0,1
  </select>
</mapper>