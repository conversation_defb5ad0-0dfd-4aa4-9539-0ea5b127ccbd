/*
 * Created on 2013-8-27
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.PrivateKey;
import java.util.*;

import com.centerm.cpay.tms.dao.pojo.*;
import com.centerm.cpay.tms.utils.DateUtil;
import com.centerm.cpay.tms.utils.StringUtil;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.mapper.TerminalSysdetailMapper;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.config.ConfigInfo;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;

/**
 * 终端信息上送
 * 
 */
@Service("termInfoUploadHandler")
public class TermInfoUploadHandler extends BaseCommandHandler {

	public static final String COMMAND = "0003";
	@Autowired
	private TerminalService terminalService;
	
	@Autowired
	private TerminalSysdetailMapper terminalSysdetailMapper;

	@Autowired
	private ConfigInfo configInfo;

	private static final Logger logger = LoggerFactory.getLogger(TermInfoUploadHandler.class);

	@Autowired
	private ServerPriviteKey serverPriviteKey;
	
	@Override
	public String getCommand() {
		return COMMAND;
	}
	@Override
	public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

		logger.info("TermInfoUploadHandler.doExcute:终端信息上送。。。终端序列号:" + tmsProtocol.getTerm_seq_id()+",时间戳："+tmsProtocol.getTimestamp());

		request.position(PROTOCOL_HEAD_LEN);

		// 上送类型
		String uploadTypeTemp = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_HEX);
		int uploadType = ByteUtil.hexStringToByte(uploadTypeTemp)[0];
		logger.info("uploadType=" + uploadType);
		if (TmsConstants.UPLOAD_FLAG_ALL == uploadType) {
			// 全量新增，删除旧数据
			terminalService.deleteTerminalAppRelation(tmsProtocol.getTerm_seq_id());
		}
		// 终端应用个数
		String temp = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_HEX);
		int appCount = Integer.parseInt(temp, 16);

		List<TerminalAppRelation> relationList = new ArrayList<TerminalAppRelation>();
		List<String> codeList = new ArrayList<String>();
		for (int i = 0; i < appCount; i++) {

			// app_code length
			String tempLen = CommandHandlerUtil.readRequest(request, 2, DATA_TYPE_HEX);
			//int appCdLen = ByteUtil.hexStringToByte(tempLen)[0];
			int appCdLen = Integer.parseInt(tempLen, 16);

			// app_code
			String appCd = CommandHandlerUtil.readRequest(request, appCdLen, DATA_TYPE_CHAR);

			// app_name length
			tempLen = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_HEX);
			int appNameLen = Integer.parseInt(tempLen, 16);

			// app_name
			String appName = CommandHandlerUtil.readRequest(request, appNameLen, DATA_TYPE_CHAR);
			// app_version
			String appVersion = CommandHandlerUtil.readRequest(request, 14, DATA_TYPE_CHAR);

			
			String appVersionOutsideLen = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_HEX);
			int versionOutsideCdLen = Integer.parseInt(appVersionOutsideLen, 16);
			String appVersionOutside = CommandHandlerUtil.readRequest(request, versionOutsideCdLen, DATA_TYPE_CHAR);
		
			String appUsedNum = CommandHandlerUtil.readRequest(request, 4, DATA_TYPE_HEX);
			String appUsedDuration = CommandHandlerUtil.readRequest(request, 8, DATA_TYPE_HEX);
			
			TerminalAppRelation terminalAppRelation = new TerminalAppRelation();
			terminalAppRelation.setAppCode(appCd);
			terminalAppRelation.setAppVersion(appVersion);
			terminalAppRelation.setTermSeq(tmsProtocol.getTerm_seq_id());
			terminalAppRelation.setAppName(appName);
			
			terminalAppRelation.setAppVersionOutside(appVersionOutside);

			

			terminalAppRelation.setAppUsedNum(new BigInteger(appUsedNum, 16).intValue());
			terminalAppRelation.setAppUsedDuration(new BigInteger(appUsedDuration, 16).intValue());

			relationList.add(terminalAppRelation);
			codeList.add(appCd);
		}
		if (TmsConstants.UPLOAD_FALG_PART == uploadType) {
			if (codeList != null && !codeList.isEmpty()) {
				Map map = new HashMap();
				map.put("codeList", codeList);
				map.put("termSeq", tmsProtocol.getTerm_seq_id());
				terminalService.deleteTerminaAppRelationByPart(map);
			}
		}
		if (relationList != null && !relationList.isEmpty()) {
				terminalService.insertTerminalAppRelationBatch("2", relationList);
		}

		// update terminal_dyn_inf
		TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
		terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());
		terminalDynInfo.setNewTime(CtUtils.getCurrentTime());
		terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
		terminalService.updateTerminalDynInfo(terminalDynInfo);

		IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

		// app num
		int position = response.position();
		
		String downloadUrl = terminalService.getconfigInfo("download_url");
		
		response.put((byte) 0x00);

		String termSeq = tmsProtocol.getTerm_seq_id();
		
		if(checkSpecialNetWork(termSeq)) {//公网环境或者专网环境开通了WIFI通讯许可
		
		if (checkDownloadLimit(termSeq)) {
			//待下发的任务列表	
			List<DownloadTask> downloadTasks = new ArrayList<DownloadTask>();
			DownloadTaskCondition condition = new DownloadTaskCondition();
			condition.setTermSeq(termSeq);
			//查询时区
			String timeZone = terminalService.getTimeZone(tmsProtocol.getTerm_seq_id());
			if(StringUtil.isEmpty(timeZone)){
				condition.setNow(new Date());
			}else{
				Date now = DateUtil.getTimeZoneTime(timeZone);
				if(CtUtils.isEmpty(now)){
					condition.setNow(new Date());
				}else{
					condition.setNow(now);
				}
			}
			/**
			 * 1.查找等待下发的任务
			 */
			//暂停同一软件的不必下发任务
			terminalService.stopRepeatTasks(condition);
			List<DownloadTask> downloadTaskTemps = terminalService.getDownloadTasks(condition);

			//计算此次下发的更新和回退任务的个数
			int countOfDls = 0;
			/**
			 * 2.遍历等待下发的任务
			 */

			for(DownloadTask downloadTask : downloadTaskTemps){
				//设置当前时区的时间
				downloadTask.setNow(condition.getNow());
				//如果不存在依赖软件，则该软件可下发
				if(CtUtils.isEmpty(downloadTask.getDependTaskIds())){
					switch (checkAppVersion(downloadTask,downloadTaskTemps)){
						case 0:
							downloadTasks.add(downloadTask);
							if(!downloadTask.getJobAction().equals("6")){
								countOfDls++;
							}
							break;
						case 1:
							downloadTask.setDlFlag("4");
							downloadTask.setResultMessage("The terminal has the software installed");
							terminalService.updateResultInfo(downloadTask);
							break;
						case 2:
							downloadTask.setDlFlag("9");
							downloadTask.setResultMessage("The terminal has installed the software high version");
							terminalService.updateResultInfo(downloadTask);
							break;
					}


				}else{
					//如果存在依赖，进一步进行判断
					// 如果依赖任务都成功，则该任务可下发
					int countNotInSuccess = terminalService.countDependNotInSuccess(downloadTask);
					if(countNotInSuccess == 0){
						switch (checkAppVersion(downloadTask,downloadTaskTemps)){
							case 0:
								downloadTasks.add(downloadTask);
								if(!downloadTask.getJobAction().equals("6")){
									countOfDls++;
								}
								break;
							case 1:
								downloadTask.setDlFlag("4");
								downloadTask.setResultMessage("The terminal has the software installed");
								terminalService.updateResultInfo(downloadTask);
								break;
							case 2:
								downloadTask.setDlFlag("9");
								downloadTask.setResultMessage("The terminal has installed the software high version");
								terminalService.updateResultInfo(downloadTask);
								break;
						}
					}
					//如果依赖任务中存在已失败的任务，则该任务的状态改为取消下发
					int countWorking = terminalService.countDependWorking(downloadTask);
					if(countNotInSuccess > countWorking){
						downloadTask.setDlFlag("9");
						downloadTask.setResultMessage("This dependency task is not completed");
						terminalService.updateResultInfo(downloadTask);
					}

				}
			}

			if (downloadTasks != null && downloadTasks.size() > 0) {
				int countOfTasks = downloadTasks.size();
				if(countOfDls > 0){
					DlItemsLimit dlItemsLimit = new DlItemsLimit();
					dlItemsLimit.setDlBeginDate(CtUtils.getCurrentTime());
					dlItemsLimit.setDlResNum(countOfDls);
					dlItemsLimit.setTermSeq(termSeq);

					terminalService.insertDlItemsLimit(dlItemsLimit);

					logger.info(termSeq + "获取下载的任务数[" + countOfDls + "]");
					logger.info("base downloadURL:" + downloadUrl);

				}
				logger.info(termSeq + "获取总任务数[" + countOfTasks + "]");

				response.position(position);
				response.put((byte) countOfTasks);

				List<Long> idList = new ArrayList<Long>();
				for (int i = 0; i < countOfTasks; i++) {

					idList.add(i, downloadTasks.get(i).getId());
					String jobAction = downloadTasks.get(i).getJobAction();
					String app_type = downloadTasks.get(i).getAppType();
					String app_code = downloadTasks.get(i).getAppCode();
					String app_filename = downloadTasks.get(i).getAppFileName();
					String url = verifyDownloadUrl(downloadTasks.get(i).getUrl(), downloadUrl);
					String md5 = downloadTasks.get(i).getMd5();
					String version = downloadTasks.get(i).getAppVer();
					
					
					String isInstallShield = downloadTasks.get(i).getIsShowNotice();
					String isNotice = "";
					String isRealTime = "";
					switch(isInstallShield){
					case "0":
						isNotice=TmsConstants.TASK_SILENCE_YES;
						isRealTime=TmsConstants.TASK_REALTIME_YES;
					    break;
					case "1":
						isNotice=TmsConstants.TASK_SILENCE_YES;
						isRealTime=TmsConstants.TASK_REALTIME_NO;
					    break;
					case "2":
						isNotice=TmsConstants.TASK_SILENCE_NO;
						isRealTime=TmsConstants.TASK_REALTIME_YES;
					    break;
					case "3":
						isNotice=TmsConstants.TASK_SILENCE_NO;
						isRealTime=TmsConstants.TASK_REALTIME_NO;
						break;
					}
					
					switch(app_type){
					case "9":
						app_type=TmsConstants.TASK_JOB_ACTION_NORMAL_UPDATE;
						break;
					case "a":
						app_type=TmsConstants.TASK_JOB_ACTION_NORMAL_UPDATE;
					    break;
					}
					
					int appSize = downloadTasks.get(i).getSize();
					logger.info("jobAction =[" + jobAction + "] app_type=[" + app_type + "] appcode=[" + app_code + "] name=[" + app_filename
							+ "] url=[" + url + "] md5=[" + md5 + "] version=[" + version + "] isNotice=["+ isNotice+ "] isRealTime=["+isRealTime+"]");
					if (Integer.compare(Integer.parseInt(tmsProtocol.getProtocol_ver()),
								Integer.parseInt(PROTOCOL_VERSION_06)) > 0) {
						String jobId = String.format("%032d", downloadTasks.get(i).getJobId());	
						setResponse(response, jobId, 32, DATA_TYPE_CHAR);
					}
					String taskId = String.format("%032d", downloadTasks.get(i).getId());
					setResponse(response, taskId, 32, DATA_TYPE_CHAR);
					
					String jobName = terminalService.getDriverJobName(downloadTasks.get(i).getJobId());
					int jobNameLen = 0;
					try {
						jobNameLen = jobName.getBytes("GBK").length;
					} catch (UnsupportedEncodingException e) {
						// TODO
						e.printStackTrace();
					}
					
					response.put((byte) ((jobNameLen >> 8) & 0xFF));
					response.put((byte) (jobNameLen & 0xFF));
					setResponse(response, jobName, jobNameLen, DATA_TYPE_CHAR);
					
					int appCodeLen = 0;
					try {
						appCodeLen = app_code.getBytes("GBK").length;
					} catch (UnsupportedEncodingException e) {
						// TODO
						e.printStackTrace();
					}
					
					response.put((byte) ((appCodeLen >> 8) & 0xFF));
					response.put((byte) (appCodeLen & 0xFF));
					
					setResponse(response, app_code, appCodeLen, DATA_TYPE_CHAR);
					setResponse(response, app_type, 1, DATA_TYPE_CHAR);
					
					if (Integer.compare(Integer.parseInt(tmsProtocol.getProtocol_ver()),
							Integer.parseInt(PROTOCOL_VERSION_06)) > 0) {
	
						setResponse(response, jobAction, 1, DATA_TYPE_CHAR);
					}
					
					setResponse(response, version, 14, DATA_TYPE_CHAR);
					setResponse(response, md5, 32, DATA_TYPE_CHAR);
					setResponse(response, isNotice, 1, DATA_TYPE_HEX);
					setResponse(response, isRealTime, 1, DATA_TYPE_HEX);
					setResponse(response, Integer.toHexString(appSize), 8, DATA_TYPE_HEX);
					int addrLen = 0;
					try {
						addrLen = url.getBytes("GBK").length;
					} catch (UnsupportedEncodingException e) {
						// TODO
						e.printStackTrace();
					}
					response.put((byte) ((addrLen >> 8) & 0xFF));
					response.put((byte) (addrLen & 0xFF));
					setResponse(response, url, addrLen, DATA_TYPE_CHAR);
				}
				logger.info("task id list :" + idList.toString());
				if (idList.size() > 0) {
						terminalService.updateDlFlag(idList);
					}
				}
			}
		}

		String macData = ByteUtil
				.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

		
		PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();
		
		byte[] signBytes =  MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData),TmsConstants.ALGORITHM_SHA256WITHRSA);

		response.put(signBytes);
		
		int len = response.limit() - 2;
		response.put(0, (byte) (len / 256));
		response.put(1, (byte) (len % 256));
		response.flip();

		logger.info("信息上送应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n"
				+ CommandHandlerUtil.getHexLogString(response, 0, response.limit())+",时间戳："+tmsProtocol.getTimestamp());
		recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);
		session.write(response);

		return true;
	}

	/**
	 *【验证】下载链接
	 * @param url 链接
	 * @param prefix 前缀
	 * @return 完整URL
	 */
	private String verifyDownloadUrl(String url, String prefix) {
		//【判断】链接如果为空（删除应用）
		if (CtUtils.isEmpty(url)) {
			return prefix;
		}

		//【判断】如果为三方链接：不需要前缀链接
		if (url.contains("http")) {
			return url;
		} else {
			return prefix + url;
		}
	}

	private boolean checkDownloadLimit(String termSeq) {
		int curCount = terminalService.getCurrentDownloadNum();
		int maxCount = terminalService.getMaxDownloadNum();
		
		if (curCount >= maxCount) {
			logger.info("超过最大并发数下载限制: maxCount[" + maxCount + "] DownloadConcurrent=[" + curCount
					+ "]");
			return false;
		}
		return true;
	}
	
	private boolean checkSpecialNetWork(String termSeq) {
		TerminalSysdetail terminalSysdetail = terminalSysdetailMapper.selectByTermSeq(termSeq);
		
		String networkConf = terminalSysdetailMapper.selectSysconfBynetWork();
		
		if(!CtUtils.isEmpty(terminalSysdetail) && TmsConstants.NETWORK_TYPE_SPECIAL.equals(terminalSysdetail.getNetworkType())) {
			if(!"1".equals(networkConf)) {//1、专网WIFI下为1的运行通讯，不等于1的默认关闭
				return false;
			}else {
				return true;
			}
		}
		return true;
	}

	/**
	 * 核对数数据库记录中该终端对应软件版本的信息
	 * 常规更新时，仅高版本和新软件可下发
	 * 新版本或高版本返回 0
	 * 同版本返回 1
	 * 低版本返回 2
	 * 多版本待发布 当前为低版本 3
	 * @param downloadTask
	 * @return
	 */
	private int checkAppVersion(DownloadTask downloadTask,List<DownloadTask> downloadTaskTemps){
		//返回true时，该任务可下发
		if(!downloadTask.getJobAction().equals("7") ||
				(downloadTask.getAppType().equals("2")||downloadTask.getAppType().equals("3"))){
			//非更新任务，镜像或驱动 返回0
			return 0;
		}else {

			//更新任务，检查数据库中的信息
			TerminalAppRelation terminalAppRelation = new TerminalAppRelation();
			terminalAppRelation.setTermSeq(downloadTask.getTermSeq());
			terminalAppRelation.setAppCode(downloadTask.getAppCode());
			terminalAppRelation.setAppVersion(downloadTask.getAppVer());
			return terminalService.checkDownload(terminalAppRelation);
		}
	}
	public void addTask(){

	}
}