package com.centerm.cpay.tms.service.handler;

import java.math.BigInteger;
import java.security.PublicKey;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.centerm.cpay.security.api.SecurityTools;
import com.centerm.cpay.tms.dao.pojo.TerminalInfo;
import com.centerm.cpay.tms.dao.pojo.TmsAccessLog;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.service.CommandHandler;
import com.centerm.cpay.tms.service.CommandHandlers;
import com.centerm.cpay.tms.service.ProtocolHandler;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.commandhandlers.CommandHandlerUtil;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.manager.SessionManager;
import com.centerm.cpay.tms.utils.ByteUtil;

public class TmsHandler extends IoHandlerAdapter implements ProtocolHandler,TmsConstants{
	
	
	private final Set<IoSession> sessions = Collections.synchronizedSet(new HashSet<IoSession>());

	public CommandHandlers commandHandlers;
	
	@Autowired
	private TerminalService terminalService;
	
	private static final String CURRENT_COMMAND = "command";
	
	   private static final String RSA_PUBKEY_PREFIX = "-----BEGIN PUBLIC KEY-----";
	   private static final String RSA_PUBKEY_SUFFIX = "-----END PUBLIC KEY-----";

	private static final Logger logger = LoggerFactory.getLogger(TmsHandler.class);
	
	public CommandHandlers getCommandHandlers() {
		return commandHandlers;
	}
	@Resource
	public void setCommandHandlers(CommandHandlers commandHandlers) {
		this.commandHandlers = commandHandlers;
	}
 
	
	@Override
	public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
		logger.info("sessionIdle------>");
		super.sessionIdle(session, status);
		//关闭会话
		if(!session.isClosing()) {
			session.closeNow();
		}
	}

	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		logger.info("exceptionCaught------>");
		cause.printStackTrace();
		logger.info(cause.getMessage());
		session.closeNow();
	}

	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		
		boolean isEnd = true;
		IoBuffer request = (IoBuffer)message;
		
		logger.info("message：---->"+request.limit()+"\n"+
				CommandHandlerUtil.getHexLogString(request, 0, request.limit()));
		
		
		TmsProtocol tmsProtocol =  commandParser(session,request);
		if(tmsProtocol == null){
			return;
		}
		
		
		String cmd = tmsProtocol.getCommand(); //根据请求指令获取对应的请求处理句柄
		session.setAttribute(CURRENT_COMMAND, cmd);
		SessionManager.addIoSession(tmsProtocol.getTerm_seq_id(), session);
		
		CommandHandler handler = (CommandHandler)commandHandlers.getHandlers().get(cmd);
		if(handler==null) {
			logger.info("tmsHandler:不支持‘" + cmd + "’指令!");
			IoBuffer response = 
					CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_ILLEGALDATA);
			response.flip();
//			TmsAccessLog tmsAccessLog = new TmsAccessLog();
//			tmsAccessLog.setTermSeq(tmsProtocol.getTerm_seq_id());
//			tmsAccessLog.setTimestamp(tmsProtocol.getTimestamp());
//			tmsAccessLog.setRandom(tmsProtocol.getRandom());
//			tmsAccessLog.setMessageCode(tmsProtocol.getCommand());
//			tmsAccessLog.setReponseLength(response.limit());
//			tmsAccessLog.setState(RESPONSE_CODE_ILLEGALDATA);//操作失败
//			terminalService.updateTmsAccessLog(tmsAccessLog);
			session.write(response);
			return;
		}
		//执行   
		isEnd = handler.execute(session, request,tmsProtocol);
		//don't close session	
//		//关闭会话
//		if(!session.isClosing() && isEnd) {
//			session.closeNow();
//		}
	
	}

	@Override
	public void sessionClosed(IoSession session) throws Exception {
		logger.info("sessionClosed------>");
		sessions.remove(session);
		super.sessionClosed(session);
	}

	@Override
	public void sessionCreated(IoSession session) throws Exception {
		logger.info("sessionCreated------>");
		sessions.add(session);
	}

	@Override
	public TmsProtocol commandParser(IoSession session,IoBuffer message){
				
		IoBuffer request = (IoBuffer)message;
		logger.info("报文数据："+request);
		int position = 0;
		TmsProtocol tmsProtocol = new TmsProtocol();
		
		try{
				
			//报文长度 2
			String hexLen = readRequest(request,2, TmsConstants.DATA_TYPE_HEX);
			int len = Integer.parseInt(hexLen, 16);
			
			if(len < PROTOCOL_HEAD_LEN){
				logger.error("无效的报文长度:"+len);
				session.closeOnFlush();
				return null;
			}
			
			tmsProtocol.setLength(len);
			position += 2;
			
			//接口版本号 2
			tmsProtocol.setProtocol_ver(readRequest(request,2,TmsConstants.DATA_TYPE_CHAR));
			position += 2;
			
			//终端ID mark 32
			tmsProtocol.setTerm_mark(readRequest(request,32,TmsConstants.DATA_TYPE_HEX));
			position += 32;
			
			String term_mark = tmsProtocol.getTerm_mark();
			
			String term_seq_id = SecurityTools.getTermIdFromMarkForTms(ByteUtil.hexStringToByte(term_mark));
			tmsProtocol.setTerm_seq_id(term_seq_id.trim());
			
			//IMEI 15
			tmsProtocol.setIMEI(readRequest(request,15,TmsConstants.DATA_TYPE_CHAR));
			position += 15;
			
			//IMSI 15
			tmsProtocol.setIMSI(readRequest(request,15,TmsConstants.DATA_TYPE_CHAR));
			position += 15;

			//时间戳  17
			tmsProtocol.setTimestamp(readRequest(request,17,TmsConstants.DATA_TYPE_CHAR));
			position += 17;
						
			//随机数  8
			tmsProtocol.setRandom(readRequest(request,8,TmsConstants.DATA_TYPE_HEX));
			position += 8;
			
			//消息码 4
			tmsProtocol.setCommand(readRequest(request,4,TmsConstants.DATA_TYPE_CHAR));
			position += 4;
			
			//access control
			// Accesslog注釋2024-11-25
//			TmsAccessLog tmsAccessLog = new TmsAccessLog();
//			tmsAccessLog.setTermSeq(tmsProtocol.getTerm_seq_id().trim());
//			tmsAccessLog.setTimestamp(tmsProtocol.getTimestamp());
//			tmsAccessLog.setRandom(tmsProtocol.getRandom());
//			tmsAccessLog.setMessageCode(tmsProtocol.getCommand());
//			tmsAccessLog.setRequestLength(len+2);
//			int ret = terminalService.insertTmsAccessLog(tmsAccessLog);
//			if(ret != 1){
//				logger.error("非法访问，拒绝");
//				session.closeOnFlush();
//				return null;
//			}
			//body
			logger.info("报文长度"+tmsProtocol.getLength());
			String body = readRequest(request,tmsProtocol.getLength() - position+2-256,TmsConstants.DATA_TYPE_HEX);
			tmsProtocol.setBody(ByteUtil.hexStringToByte(body));
			
			//sign 256
			tmsProtocol.setSign(readRequest(request,256,TmsConstants.DATA_TYPE_HEX));
			
			TerminalInfo terminalInfo = terminalService.getTerminalByTermSeq(tmsProtocol.getTerm_seq_id());
			if(terminalInfo == null){
				logger.error("getTerminalByTermSeq ["+tmsProtocol.getTerm_seq_id()+"] return null");
				IoBuffer response = CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_UNFINDTERM);
				response.flip();
//				tmsAccessLog.setReponseLength(response.limit());
//				tmsAccessLog.setState(RESPONSE_CODE_UNFINDTERM);
//				terminalService.updateTmsAccessLog(tmsAccessLog);
				session.write(response);
				session.closeOnFlush();
				return null;
			}
			logger.info("terminalInfo数据："+terminalInfo);
			tmsProtocol.setTerminalInfo(terminalInfo);
			
			request.position(2);
			
			String tempData= readRequest(request,tmsProtocol.getLength()-256,TmsConstants.DATA_TYPE_HEX);//终端发送报文数据

			String termPubKey = terminalInfo.getTermPubKey().replace(RSA_PUBKEY_PREFIX, "").replace(RSA_PUBKEY_SUFFIX, "");

			PublicKey publicKey = MyRSAUtils.getPublicKey(termPubKey);//厂商公钥

			boolean isRight = MyRSAUtils.verifyByPublicKey(publicKey,ByteUtil.hexStringToByte(tempData),ByteUtil.hexStringToByte(tmsProtocol.getSign()),ALGORITHM_SHA256WITHRSA);//开启验签
			if(!isRight){
				logger.error("签名验签失败");
				IoBuffer response = CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_MACEXCEP);
				response.flip();
//				tmsAccessLog.setReponseLength(response.limit());
//				tmsAccessLog.setState(RESPONSE_CODE_MACEXCEP);
//				terminalService.updateTmsAccessLog(tmsAccessLog);
				session.write(response);
				session.closeOnFlush();
				return null;
			}
			
		}catch(Exception e){
			logger.error("commandParser exception", e);
			IoBuffer response = 
					CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_REQUESTERROR);
			response.flip(); 
			session.write(response);
			session.closeOnFlush();
			return null;
		}	
		return tmsProtocol;
	}
	
	
	
	
	protected  String readRequest(IoBuffer request, int length, int dataType) throws Exception {
		
		return CommandHandlerUtil.readRequest(request, length, dataType);
	}
	
	public static void main(String[] args) throws Exception {
		String desTermSeq = SecurityTools.getTermIdFromMark(ByteUtil.hexStringToByte("44314B43304E464A303030313620202020202020202020202020202020202020"));
		System.out.println(desTermSeq);
	}

}
