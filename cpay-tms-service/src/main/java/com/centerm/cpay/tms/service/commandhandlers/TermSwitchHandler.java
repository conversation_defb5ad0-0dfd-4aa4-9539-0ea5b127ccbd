
package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.TerminalBootInfo;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;


/**
 * 终端开关机信息上送
 * 
 */
@Service("termSwitchHandler")
public class TermSwitchHandler extends BaseCommandHandler {

	@Autowired
	private TerminalService terminalService;
	
	public static final String COMMAND = "0001";
	
	Logger logger = LoggerFactory.getLogger(TermSwitchHandler.class);

	@Autowired
	private ServerPriviteKey serverPriviteKey;

	@Override
	public String getCommand() {
		return COMMAND;
	}


	@Override
	public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {
		
		
		logger.info("TermSwitchHandler.doExecute: 开关机信息上送...终端序列号:"+tmsProtocol.getTerm_seq_id()+",时间戳："+tmsProtocol.getTimestamp());
		
		request.position(PROTOCOL_HEAD_LEN);
		
		
		
		//开关机信息条数
		
		String strCount = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_HEX);
		int count = Integer.parseInt(strCount, 16);
		
		for(int i=0;i<count;i++){
			
			String termOpenTime=CommandHandlerUtil.readRequest(request, 14, DATA_TYPE_CHAR);
			String termDownTime=CommandHandlerUtil.readRequest(request, 14, DATA_TYPE_CHAR);
			
			TerminalBootInfo terminalBootInfo = new TerminalBootInfo();
			terminalBootInfo.setId(0);
			terminalBootInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
			terminalBootInfo.setLastOpenTime(termOpenTime);
			terminalBootInfo.setLastCloseTime(termDownTime);
			
			int ret = terminalService.insertTerminalBootInfo(terminalBootInfo);
			
			logger.info("insertTerminalBootInfo return "+ret);
			
		}
		
		IoBuffer response=CommandHandlerUtil.packRespHead(tmsProtocol,RESPONSE_CODE_SUCESS);
		
		String macData=ByteUtil.bytesToHexString(CommandHandlerUtil.getIoBufferData(response,2,response.limit() - 2));
		
		PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();
		
		byte[] signBytes =  MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData),TmsConstants.ALGORITHM_SHA256WITHRSA);

		response.put(signBytes);
			
		int len = response.limit() - 2;
		response.put(0, (byte)(len/256));
		response.put(1, (byte)(len%256));
		response.flip();
		
		logger.info("开关机信息上送应答报文["+tmsProtocol.getTerm_seq_id()+"]:\n"+
				CommandHandlerUtil.getHexLogString(response, 0, response.limit())+",时间戳："+tmsProtocol.getTimestamp());
		recordReponseByteLength(response,tmsProtocol,RESPONSE_CODE_SUCESS);
		session.write(response);
		
		return true;
	}

}
