package com.centerm.cpay.tms.service;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.tms.dao.pojo.*;

public interface TerminalService {

	public boolean haveDownloadTask(String termSeq);

	public List<DownloadTask> getDownloadTasks(DownloadTaskCondition condition);
	public void stopRepeatTasks(DownloadTaskCondition condition);

	String getTimeZone(String termSeq);

	public int updateDownloadTask(DownloadTask downloadTask);

	public int updateDlFlag(List<Long> idList);

	public boolean haveUploadTask(String termSeq);

	public List<UploadTask> getUploadTasks(String termSeq);

	public int updateUploadTask(UploadTask uploadTask);

	public int updateUlFlag(List<Integer> idList);

	public boolean haveCommParaDownloadTask(TmsProtocol tmsProtocol);

	public boolean termIsMove(TmsProtocol tmsProtocol);

	public InstitutionParam getInstitutionParam(InstitutionParam institutionParam);

	public InstitutionParam getInstitutionParamByInsList(int insId);

	public TerminalInfo getTerminalByTermSeq(String termSeq);

	public int updateTerminalInfo(TerminalInfo terminalInfo);

	public int updateTerminalDynInfo(TerminalDynInfo terminalDynInfo);

	public int updateTerminalDynInfo(TerminalDynInfo terminalDynInfo, String longitude, String latitude);

	public TerminalDynInfo getTerminalDynInfoByTermSeq(String termSeq);

	public int insertTerminalDynInfo(TerminalDynInfo terminalDynInfo);

	public int countTerminalDynInfo(String termSeq);

	public int insertTerminalBootInfo(TerminalBootInfo terminalBootInfo);

	public int insertTerminalAlarmInfoSelectFromDyn(String termSeq);

	public int insertTerminalPosition(TerminalPosition terminalPosition);

	public int insertTerminalAppRelationBatch(String flag, List<TerminalAppRelation> list);

	public int deleteTerminalAppRelation(String termSeq);

	public int getCurrentDownloadNum();

	public DlItemsLimit getCurrentTerminalDownloadRes(String termSeq);

	public int insertDlItemsLimit(DlItemsLimit dlItemsLimit);

	public int deleteDlItemsLimit(String termSeq);

	public int updateDlItemsLimit(DlItemsLimit dlItemsLimit);

	public int clearDlItmesLimit(ClearCondition cond);

	public List<StoreAdvert> getAdList(AdSelectCondition cond);

	public SimFlowusageDay getFlowusageDay(SimFlowusageDay cond);

	public int insertFlowusageDay(SimFlowusageDay simFlowusageDay);

	public int updateFlowusageDay(SimFlowusageDay simFlowusageDay);

	public int insertTerminalSysdetail(TerminalSysdetail record);

	public TerminalSysdetail selectTerminalSysdetailByTermSeq(String termSeq);

	public int updateTerminalSysdetail(TerminalSysdetail record);

	public int insertTmsAccessLog(TmsAccessLog record);

	public int updateTmsAccessLog(TmsAccessLog record);

	public String queryM2mMessage(String Imsi, String protocolVersion);

	public M2mCard queryM2mCardInfoByImsi(String Imsi);

	public int insertOrUpdateFlowusageMonth(SimFlowusageMonth monthFlow);

	public String querySystemMessage(String termSeq);

	int insertTerminalElectricity(String time, String termSeq, String prower);

	String querySystemConfigByKey(String key);

	int deleteAppFlowRecord(AppFlowRecord record);

	int insertAppFlowRecordByBatch(List<AppFlowRecord> records);

	TerminalMoveMonitorSwitch selectTerminalMoveMonitorSwitch(TerminalMoveMonitorCondition termCondition);

	void updateTerminalCricle(TerminalCircle terminalCircle);

	public void updateDlFlagThree(List<Integer> idList);

	public String queryIsStartEle(String termSeq,int instId);

	public boolean haveStartMonitor(TmsProtocol tmsProtocol);

	public void updateStartMonitor(TmsProtocol tmsProtocol);

	public SimFlowusageDay getLastRecord(SimFlowusageDay simFlowusageDay);

	public SimFlowusageDay getLastFlow(SimFlowusageDay simFlowusageDayWhere);

	public int insertFlowusageDayCZ(SimFlowusageDay simFlowusageDay);

	public void deleteFlowCount(SimFlowusageDay simFlowusageDayWhere2);

	public SimFlowusageDay getCurrentFlowCount(SimFlowusageDay simFlowusageDayWhere3);

	public int selectFlowCount(SimFlowusageDay simFlowusageDayWhere2);

	public int updateFlowCount(SimFlowusageDay simFlowusageDayWhere2);

	public SimFlowusageDay selectFenDuanFlowCount(SimFlowusageDay simFlowusageDay);

	public void insertFenDuanFlowCount(SimFlowusageDay simFlowusageDay);

	public void updateFenDuanFlowCount(SimFlowusageDay fenduanSimFlowusageDay);

	public boolean haveInitialPosition(TmsProtocol tmsProtocol);
	
	public int updateMerchantInfoByTermSeq(Map<String,String> param);
	
	public String getTerminalMonitorType(String instId);

	public Map<String,String> getMonitorParam(Integer insId);

	public void deleteTerminaAppRelationByPart(Map map);

	public void insertTerminalFlow(TerminalFlowDay terminalFlowDay);

	public TerminalOperation queryTerminalOperactionCommand(String term_seq_id);

	public boolean haveOperateCommandTask(String termSeq);

	public void updateOpeateCommandStatus(TerminalOperation terminalOperation);

	public String getDriverJobName(Integer jobId);

	public int getMaxDownloadNum();

	/**
	 * 计算未成功的任务数
	 * @param downloadTask
	 * @return
	 */
	public int countDependNotInSuccess(DownloadTask downloadTask);

	/**
	 * 计算待执行和执行中的任务数
	 * @param downloadTask
	 * @return
	 */
	public int countDependWorking(DownloadTask downloadTask);


	/**
	 * 更新软件结果
	 * @param downloadTask
	 * @return
	 */
	int updateResultInfo(DownloadTask downloadTask);

	public List<InformationAnnounce> getInfomation(InformationAnnounceQuery query);

	public TerminalFlowDay queryFlowExist(TerminalFlowDay terminalFlowDay);

	public void updateTerminalFlow(TerminalFlowDay terminalFlowDay);

	public String getconfigInfo(String downloadUrl);

	/**
	 * 比较终端当前版本
	 * @param relation
	 * @return
	 */
	public int checkDownload(TerminalAppRelation relation);
	/**
	 * 查找task任务中app对应的appid
	 * @param downloadTask
	 * @return
	 */
	public int checkAppId(DownloadTask downloadTask);

	public List<TerminalSystemMessage> getTerminalMessage(String term_seq_id);

	public boolean havaNewMessageNotice(TmsProtocol tmsProtocol);
}
