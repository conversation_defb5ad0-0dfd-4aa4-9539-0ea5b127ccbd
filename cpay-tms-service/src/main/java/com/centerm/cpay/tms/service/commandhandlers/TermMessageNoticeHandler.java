
package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.util.Date;
import java.util.List;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.mapper.InstitutionMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalSystemMessageMapper;
import com.centerm.cpay.tms.dao.pojo.InformationAnnounce;
import com.centerm.cpay.tms.dao.pojo.InformationAnnounceQuery;
import com.centerm.cpay.tms.dao.pojo.Institution;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.dao.pojo.TerminalOperation;
import com.centerm.cpay.tms.dao.pojo.TerminalSystemMessage;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;


/**
 * TMS短消息下发
 */
@Service("termMessageNoticeHandler")
public class TermMessageNoticeHandler extends BaseCommandHandler {

    public static final String COMMAND = "0014";

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private InstitutionMapper institutionMapper;

    @Autowired
    private TerminalSystemMessageMapper terminalSystemMessageMapper;

    private static final Logger logger = LoggerFactory.getLogger(TermMessageNoticeHandler.class);

    @Autowired
    private ServerPriviteKey serverPriviteKey;

    @Override
    public String getCommand() {
        return COMMAND;
    }


    @Override
    public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

        logger.info("TermMessageNoticeHandler.doExecute: TMS通知消息...终端序列号:" + tmsProtocol.getTerm_seq_id() + ",时间戳：" + tmsProtocol.getTimestamp());

        request.position(PROTOCOL_HEAD_LEN);
        tmsProtocol.getTerminalInfo().getInsId();

        Institution institution = institutionMapper.selectById(tmsProtocol.getTerminalInfo().getInsId());

        IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

        InformationAnnounceQuery query = new InformationAnnounceQuery();
        query.setDetail(institution.getDetail());
        query.setTermSeq(tmsProtocol.getTerm_seq_id());
        List<InformationAnnounce> infoList = terminalService.getInfomation(query);

        if (CtUtils.isEmpty(infoList)) {
            response.put((byte) 0);
        } else {
            setResponse(response, Integer.toHexString(infoList.size()), 1, DATA_TYPE_HEX);
            for (int i = 0; i < infoList.size(); i++) {
                int id = infoList.get(i).getId();
                //String stick = Byte.toString(infoList.get(i).getStick());
                String title = infoList.get(i).getTitle();
                String content = infoList.get(i).getContent();
                String idStr = String.format("%032d", id);
                setResponse(response, idStr, 32, DATA_TYPE_CHAR);
                setResponse(response, "0", 1, DATA_TYPE_HEX);
                int titleLen = 0;
                try {
                    titleLen = title.getBytes("GBK").length;
                } catch (UnsupportedEncodingException e) {
                    // TODO
                    e.printStackTrace();
                }

                response.put((byte) ((titleLen >> 8) & 0xFF));
                response.put((byte) (titleLen & 0xFF));
                setResponse(response, title, titleLen, DATA_TYPE_CHAR);

                int contentLen = 0;
                try {
                    contentLen = content.getBytes("GBK").length;
                } catch (UnsupportedEncodingException e) {
                    // TODO
                    e.printStackTrace();
                }

                response.put((byte) ((contentLen >> 8) & 0xFF));
                response.put((byte) (contentLen & 0xFF));
                setResponse(response, content, contentLen, DATA_TYPE_CHAR);
                TerminalSystemMessage terminalSystemMessage = new TerminalSystemMessage();
                terminalSystemMessage.setMessageId(infoList.get(i).getId());
                terminalSystemMessage.setTermSeq(tmsProtocol.getTerm_seq_id());
                terminalSystemMessage.setCreateTime(new Date());
                terminalSystemMessageMapper.insert(terminalSystemMessage);
            }
        }

        String macData = ByteUtil.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

        PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();

        byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData), TmsConstants.ALGORITHM_SHA256WITHRSA);

        response.put(signBytes);

        int len = response.limit() - 2;
        response.put(0, (byte) (len / 256));
        response.put(1, (byte) (len % 256));
        response.flip();

        logger.info("TMS通知消息应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n" +
                CommandHandlerUtil.getHexLogString(response, 0, response.limit()) + ",时间戳：" + tmsProtocol.getTimestamp());
        recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);

        session.write(response);

        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());

        int ret = terminalService.updateTerminalDynInfo(terminalDynInfo);

        logger.info("updateTerminalDynInfo return " + ret);

        return true;
    }

}
