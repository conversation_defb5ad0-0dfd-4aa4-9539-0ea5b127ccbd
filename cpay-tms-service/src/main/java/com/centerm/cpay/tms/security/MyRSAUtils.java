package com.centerm.cpay.tms.security;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import com.centerm.cpay.tms.utils.Base64Util;

/**
 * <AUTHOR> </br>
 * @date 2018/9/6 </br>
 */
public class MyRSAUtils {
    private final static String ALGORITHM_RSA = "RSA";

    public final static String ALGORITHM_SHA256WITHRSA = "sha256withRSA";
    public final static String RSA_ECB_PKCS1 = "RSA/ECB/PKCS1Padding";
    public final static String RSA_NONE_NOPADDING = "RSA/NONE/NoPadding";


    /**
     * 获取RSA公钥对象
     *
     * @param pubKey 公钥数据
     * @return 公钥对象
     * @throws Exception 
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static PublicKey getPublicKey(String pubKey) throws Exception {
        X509EncodedKeySpec x509 = new X509EncodedKeySpec(Base64Util.decode(pubKey));
        KeyFactory keyFactory = null;
        try {
            keyFactory = KeyFactory.getInstance(ALGORITHM_RSA);
            PublicKey pk = keyFactory.generatePublic(x509);
            return pk;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从证书中获取公钥对象
     *
     * @param certIs 证书输入流
     * @return 公钥
     * @throws CertificateException
     */
    public static PublicKey getPublicKey(InputStream certIs) {
        CertificateFactory certificatefactory = null;
        try {
            certificatefactory = CertificateFactory.getInstance("X.509");
            X509Certificate cert = (X509Certificate) certificatefactory.generateCertificate(certIs);
            PublicKey pk = cert.getPublicKey();
            //logPubkeyInfo(pk);
            return pk;
        } catch (CertificateException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从证书中获取公钥对象
     *
     * @param certData 证书文件数据
     * @return 公钥
     */
    public static PublicKey getPublicKey(byte[] certData) {
        javax.security.cert.X509Certificate cert = null;
        try {
            cert = javax.security.cert.X509Certificate.getInstance(certData);
            PublicKey pk = cert.getPublicKey();
            //logPubkeyInfo(pk);
            return pk;
        } catch (javax.security.cert.CertificateException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取私钥 </br>
     * Java只支持PKCS8格式的私钥，OpenSSL默认生成的是PKCS1格式的，需要进行转换
     *
     * @param priKey 私钥数据
     * @return 私钥对象
     * @throws Exception 
     */
    public static PrivateKey getPrivateKey(String priKey) {
        byte[] keyBytes = null;
        try {
			keyBytes = com.centerm.cpay.common.utils.Base64Util.decode(priKey);
		} catch (Exception e1) {
			// TODO
			e1.printStackTrace();
		}
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
            //logPriKeyInfo(privateKey);
            return privateKey;
        } catch (InvalidKeySpecException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取私钥 </br>
     *
     * @param fileIs 文件输入流
     * @return 私钥对象
     * @throws Exception 
     */
    public static PrivateKey getPrivateKey(InputStream fileIs) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = fileIs.read(buffer)) > 0) {
                baos.write(buffer, 0, len);
            }
            return getPrivateKey(new String(baos.toByteArray()));
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                baos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 证书链校验
     *
     * @param highLevel 高一级证书
     * @param lowLevel  低一级证书
     * @return 校验结果
     */
    public static boolean verifyCertChain(InputStream highLevel, InputStream lowLevel) {
        try {
            CertificateFactory certificatefactory = CertificateFactory.getInstance("X.509");
            X509Certificate highCert = (X509Certificate) certificatefactory.generateCertificate(highLevel);
            X509Certificate lowCert = (X509Certificate) certificatefactory.generateCertificate(lowLevel);
            lowCert.verify(highCert.getPublicKey());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 私钥签名
     *
     * @param priKey    私钥对象
     * @param content   待签名数据
     * @param algorithm 签名算法{@link Signature}
     * @return 签名信息
     */
    public static byte[] signWithPrivateKey(PrivateKey priKey, byte[] content, String algorithm) {
        try {
            Signature signature = Signature.getInstance(algorithm);
            signature.initSign(priKey);
            signature.update(content);
            byte[] signData = signature.sign();
            return signData;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (SignatureException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 私钥加密
     *
     * @param pubKey    公钥对象
     * @param algorithm 算法{@link Cipher}
     * @param data      明文
     * @return 密文
     */
    public static byte[] encryptByPrivateKey(PrivateKey privateKey, String algorithm, byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(data);
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 私钥解密
     *
     * @param privateKey 私钥对象
     * @param algorithm  算法{@link Cipher}
     * @param cipherText 密文
     * @return 明文
     */
    public static byte[] decryptByPrivateKey(PrivateKey privateKey, String algorithm, byte[] cipherText) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(cipherText);
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 公钥加密
     *
     * @param pubKey    公钥对象
     * @param algorithm 算法{@link Cipher}
     * @param data      明文
     * @return 密文
     */
    public static byte[] encryptByPublicKey(PublicKey pubKey, String algorithm, byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            byte[] result = cipher.doFinal(data);
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 公钥解密
     *
     * @param privateKey 公钥对象
     * @param algorithm  算法{@link Cipher}
     * @param cipherText 密文
     * @return 明文
     */
    public static byte[] decryptByPublicKey(PublicKey pubKey, String algorithm, byte[] cipherText) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.DECRYPT_MODE, pubKey);
            byte[] result = cipher.doFinal(cipherText);
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 公钥验签
     *
     * @param pubKey    公钥对象
     * @param content   原始数据
     * @param sign      签名数据
     * @param algorithm 算法
     * @return 验证成功返回ture，否则返回false
     */
    public static boolean verifyByPublicKey(PublicKey pubKey, byte[] content, byte[] sign, String algorithm) {
        try {
            Signature signature = Signature.getInstance(algorithm);
            signature.initVerify(pubKey);
            signature.update(content);
            boolean result = signature.verify(sign);
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (SignatureException e) {
            e.printStackTrace();
        }
        return false;
    }
}
