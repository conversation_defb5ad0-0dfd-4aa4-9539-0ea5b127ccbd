package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.centerm.cpay.tms.dao.mapper.TmsAccessLogMapper;
import com.centerm.cpay.tms.dao.pojo.TmsAccessLog;
import com.centerm.cpay.tms.service.CommandHandler;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TcpMinaException;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.ByteUtil;

public abstract class BaseCommandHandler implements CommandHandler,TmsConstants {
	
	private static final Logger logger = LoggerFactory.getLogger(BaseCommandHandler.class);
	@Autowired
	private TmsAccessLogMapper tmsAccessLogMapper;
	@Override
	public boolean execute(IoSession session, IoBuffer message, TmsProtocol rmtProtocol)
			throws TcpMinaException {
		try {
			return doExecute(session, message, rmtProtocol);
		}
		catch(TmsException e ) {
			logger.error("BaseCommandler.excute",e);
			String error = RESPONSE_CODE_SYSEXCEPTION;
			if(e.getMessage()!=null) {
				error = e.getMessage();
			}
			IoBuffer response = CommandHandlerUtil.generateErrorResponse(error);
			response.flip();
			throw new TcpMinaException(response);
		}
		catch (Exception e) {
			e.printStackTrace();
			logger.error("BaseCommandler.excute",e);
			IoBuffer response = CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_SYSEXCEPTION);
			response.flip();
			throw new TcpMinaException(response);
		}
	}

	
	public abstract boolean doExecute(IoSession session, IoBuffer request,TmsProtocol rmtProtocol) throws TmsException;
	
	
	protected void setResponse(IoBuffer response, String content, int length, int dataType) {
		content = content==null? "" : content;
				
		if(DATA_TYPE_HEX==dataType && content.length()%2 != 0) {
			content =  "0" + content;
		}
		byte[] contentBytes = new byte[0];
		try {
			contentBytes = content.getBytes("GBK");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		byte[] contentBts = dataType==DATA_TYPE_HEX ? ByteUtil.hexStringToByte(content) : contentBytes;
		
		byte[] resp = new byte[length==0 ? contentBts.length : length];
		
		if(DATA_TYPE_HEX==dataType) {
			int pos = length - contentBts.length ;
			System.arraycopy(contentBts, 0, resp, pos, contentBts.length);
			
			for(int i=0; i< pos; i++) {
				resp[i] = 0x00;
			}
		}
		
		if(DATA_TYPE_CHAR==dataType) {
			System.arraycopy(contentBts, 0, resp, 0, contentBts.length);
			int pos = contentBts.length ;
			for(int i=pos; i< length; i++) {
				resp[i] = 0x00;
			}
		}
		
		response.put(resp);
	}
	
	public void recordReponseByteLength(IoBuffer reponse,TmsProtocol rmtProtocol,String state){
//		TmsAccessLog tmsAccessLog = new TmsAccessLog();
//		tmsAccessLog.setTermSeq(rmtProtocol.getTerm_seq_id());
//		tmsAccessLog.setTimestamp(rmtProtocol.getTimestamp());
//		tmsAccessLog.setRandom(rmtProtocol.getRandom());
//		tmsAccessLog.setMessageCode(rmtProtocol.getCommand());
//		tmsAccessLog.setReponseLength(reponse.limit());
//		tmsAccessLog.setState(state);//操作失败
//		tmsAccessLogMapper.updateReponseByCondition(tmsAccessLog);
	}

}