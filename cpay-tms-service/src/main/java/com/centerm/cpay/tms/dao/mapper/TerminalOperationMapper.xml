<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.TerminalOperationMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.TerminalOperation" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_name" property="taskName" jdbcType="VARCHAR" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="operate_command" property="operateCommand" jdbcType="VARCHAR" />
    <result column="operate_status" property="operateStatus" jdbcType="CHAR" />
    <result column="record_create_time" property="recordCreateTime" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, task_name, term_seq, operate_command, operate_status, record_create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_operation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_terminal_operation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalOperation" >
    insert into coms_terminal_operation (id, task_name, term_seq, 
      operate_command, operate_status, record_create_time
      )
    values (#{id,jdbcType=INTEGER}, #{taskName,jdbcType=VARCHAR}, #{termSeq,jdbcType=VARCHAR}, 
      #{operateCommand,jdbcType=VARCHAR}, #{operateStatus,jdbcType=CHAR}, #{recordCreateTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalOperation" >
    insert into coms_terminal_operation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskName != null" >
        task_name,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="operateCommand != null" >
        operate_command,
      </if>
      <if test="operateStatus != null" >
        operate_status,
      </if>
      <if test="recordCreateTime != null" >
        record_create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskName != null" >
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="operateCommand != null" >
        #{operateCommand,jdbcType=VARCHAR},
      </if>
      <if test="operateStatus != null" >
        #{operateStatus,jdbcType=CHAR},
      </if>
      <if test="recordCreateTime != null" >
        #{recordCreateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalOperation" >
    update coms_terminal_operation
    <set >
      <if test="taskName != null" >
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="operateCommand != null" >
        operate_command = #{operateCommand,jdbcType=VARCHAR},
      </if>
      <if test="operateStatus != null" >
        operate_status = #{operateStatus,jdbcType=CHAR},
      </if>
      <if test="recordCreateTime != null" >
        record_create_time = #{recordCreateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalOperation" >
    update coms_terminal_operation
    set task_name = #{taskName,jdbcType=VARCHAR},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      operate_command = #{operateCommand,jdbcType=VARCHAR},
      operate_status = #{operateStatus,jdbcType=CHAR},
      record_create_time = #{recordCreateTime,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryTerminalOperactionCommand" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select o.*,j.content from coms_terminal_operation o left join coms_terminal_operation_job j on o.job_id = j.id
  	         where o.term_seq=#{termSeq} and o.operate_status='0' order by id desc limit 1
  </select>
</mapper>