package com.centerm.cpay.tms.dao.pojo;

public class TerminalSysdetail {
    private Integer id;

    private Integer termId;

    private String termSeq;

    private String commParaVersion;

    private String launcherParaVersion;

    private String osVersion;

    private String updateTime;

    private String tmsSDK;

    private String paySDK;

    private String emvVer;

    private String payAppCode;

    private String payAppName;

    private String payAppVersion;

    private String payAppVersionOutSide;

    private String tmsAppVersion;

    private String tmsAppVersionOutSide;

    private String safeModVer;

    private String osVer;

    private String androidVer;
    
    private String networkType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTmsAppVersion() {
        return tmsAppVersion;
    }

    public void setTmsAppVersion(String tmsAppVersion) {
        this.tmsAppVersion = tmsAppVersion;
    }

    public String getTmsAppVersionOutSide() {
        return tmsAppVersionOutSide;
    }

    public void setTmsAppVersionOutSide(String tmsAppVersionOutSide) {
        this.tmsAppVersionOutSide = tmsAppVersionOutSide;
    }

    public Integer getTermId() {
        return termId;
    }

    public void setTermId(Integer termId) {
        this.termId = termId;
    }

    public String getTermSeq() {
        return termSeq;
    }

    public void setTermSeq(String termSeq) {
        this.termSeq = termSeq == null ? null : termSeq.trim();
    }

    public String getCommParaVersion() {
        return commParaVersion;
    }

    public void setCommParaVersion(String commParaVersion) {
        this.commParaVersion = commParaVersion == null ? null : commParaVersion.trim();
    }

    public String getLauncherParaVersion() {
        return launcherParaVersion;
    }

    public void setLauncherParaVersion(String launcherParaVersion) {
        this.launcherParaVersion = launcherParaVersion == null ? null : launcherParaVersion.trim();
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion == null ? null : osVersion.trim();
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime == null ? null : updateTime.trim();
    }
    
    public String getTmsSDK() {
		return tmsSDK;
	}

	public void setTmsSDK(String tmsSDK) {
		this.tmsSDK = tmsSDK;
	}

	public String getPaySDK() {
		return paySDK;
	}

	public void setPaySDK(String paySDK) {
		this.paySDK = paySDK;
	}

	public String getEmvVer() {
        return emvVer;
    }

    public void setEmvVer(String emvVer) {
        this.emvVer = emvVer == null ? null : emvVer.trim();
    }

    public String getPayAppCode() {
        return payAppCode;
    }

    public void setPayAppCode(String payAppCode) {
        this.payAppCode = payAppCode == null ? null : payAppCode.trim();
    }

    public String getPayAppName() {
        return payAppName;
    }

    public void setPayAppName(String payAppName) {
        this.payAppName = payAppName == null ? null : payAppName.trim();
    }

    public String getPayAppVersion() {
        return payAppVersion;
    }

    public void setPayAppVersion(String payAppVersion) {
        this.payAppVersion = payAppVersion == null ? null : payAppVersion.trim();
    }

    public String getPayAppVersionOutSide() {
        return payAppVersionOutSide;
    }

    public void setPayAppVersionOutSide(String payAppVersionOutSide) {
        this.payAppVersionOutSide = payAppVersionOutSide == null ? null : payAppVersionOutSide.trim();
    }

    public String getSafeModVer() {
        return safeModVer;
    }

    public void setSafeModVer(String safeModVer) {
        this.safeModVer = safeModVer == null ? null : safeModVer.trim();
    }

    public String getOsVer() {
        return osVer;
    }

    public void setOsVer(String osVer) {
        this.osVer = osVer == null ? null : osVer.trim();
    }

    public String getAndroidVer() {
        return androidVer;
    }

    public void setAndroidVer(String androidVer) {
        this.androidVer = androidVer == null ? null : androidVer.trim();
    }

	public String getNetworkType() {
		return networkType;
	}

	public void setNetworkType(String networkType) {
		this.networkType = networkType;
	}

	@Override
	public String toString() {
		return "TerminalSysdetail [id=" + id + ", termId=" + termId + ", termSeq=" + termSeq + ", commParaVersion="
				+ commParaVersion + ", launcherParaVersion=" + launcherParaVersion + ", osVersion=" + osVersion
				+ ", updateTime=" + updateTime + ", tmsSDK=" + tmsSDK + ", paySDK=" + paySDK + ", emvVer=" + emvVer
				+ ", payAppCode=" + payAppCode + ", payAppName=" + payAppName + ", payAppVersion=" + payAppVersion
				+ ", payAppVersionOutSide=" + payAppVersionOutSide + ", tmsAppVersion=" + tmsAppVersion
				+ ", tmsAppVersionOutSide=" + tmsAppVersionOutSide + ", safeModVer=" + safeModVer + ", osVer=" + osVer
				+ ", androidVer=" + androidVer + ", networkType=" + networkType + "]";
	}
    
}