package com.centerm.cpay.tms.dao.mapper;

import com.centerm.cpay.tms.dao.pojo.TerminalSysdetail;

public interface TerminalSysdetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalSysdetail record);

    int insertSelective(TerminalSysdetail record);

    TerminalSysdetail selectByPrimaryKey(Integer id);
    TerminalSysdetail selectByTermSeq(String termSeq);

    int updateByPrimaryKeySelective(TerminalSysdetail record);

    int updateByPrimaryKey(TerminalSysdetail record);

	String selectSysconfBynetWork();
    
    
}