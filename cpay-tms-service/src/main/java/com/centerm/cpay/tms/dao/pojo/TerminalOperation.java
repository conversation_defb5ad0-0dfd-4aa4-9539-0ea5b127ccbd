package com.centerm.cpay.tms.dao.pojo;

public class TerminalOperation {
    private Integer id;

    private String taskName;

    private String termSeq;

    private String operateCommand;

    private String operateStatus;

    private String recordCreateTime;

    private String content;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    public String getTermSeq() {
        return termSeq;
    }

    public void setTermSeq(String termSeq) {
        this.termSeq = termSeq == null ? null : termSeq.trim();
    }

    public String getOperateCommand() {
        return operateCommand;
    }

    public void setOperateCommand(String operateCommand) {
        this.operateCommand = operateCommand == null ? null : operateCommand.trim();
    }

    public String getOperateStatus() {
        return operateStatus;
    }

    public void setOperateStatus(String operateStatus) {
        this.operateStatus = operateStatus == null ? null : operateStatus.trim();
    }

    public String getRecordCreateTime() {
        return recordCreateTime;
    }

    public void setRecordCreateTime(String recordCreateTime) {
        this.recordCreateTime = recordCreateTime == null ? null : recordCreateTime.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}