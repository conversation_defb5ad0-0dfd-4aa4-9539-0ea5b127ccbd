package com.centerm.cpay.tms.dao.mapper;

import com.centerm.cpay.tms.dao.pojo.SyetemMessage;

public interface SyetemMessageMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SyetemMessage record);

    int insertSelective(SyetemMessage record);

    SyetemMessage selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SyetemMessage record);

    int updateByPrimaryKeyWithBLOBs(SyetemMessage record);

    int updateByPrimaryKey(SyetemMessage record);
}