<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.TerminalInfoMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.TerminalInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="term_mfr_id" property="termMfrId" jdbcType="INTEGER" />
    <result column="term_type_code" property="termTypeCode" jdbcType="VARCHAR" />
    <result column="link_man" property="linkMan" jdbcType="VARCHAR" />
    <result column="link_phone" property="linkPhone" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="imei" property="imei" jdbcType="CHAR" />
    <result column="imsi" property="imsi" jdbcType="CHAR" />
    <result column="net_mark" property="netMark" jdbcType="CHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="county" property="county" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="production_time" property="productionTime" jdbcType="TIMESTAMP" />
    <result column="term_group_id"   property="termGroupId" jdbcType="INTEGER" />
    <result column="detail"   property="insDetail" jdbcType="VARCHAR" />
    <result column="term_pub_key"   property="termPubKey" jdbcType="VARCHAR" />
    <result column="time_zone"   property="timeZone" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="InsMap" type="HashMap">  
    <result column="lock_theme" property="lockTheme" jdbcType="VARCHAR" />  
    <result column="move_monitor_type" property="moveMonitorType" jdbcType="VARCHAR" />
    <result column="is_monitor" property="isMonitor" jdbcType="VARCHAR" />   
    <result column="is_lock" property="isLock" jdbcType="VARCHAR" /> 
</resultMap>  
  <sql id="Base_Column_List" >
    id, term_seq, ins_id, term_mfr_id, term_type_code, link_man, link_phone, longitude, 
    latitude, imei,imsi, net_mark, province, city, county, address, status, create_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_terminal_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="selectByTermSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    a.id,
    a.term_seq,
    a.ins_id,
    a.term_mfr_id,
    a.term_type_code,
    a.link_man,
    a.link_phone,
    a.longitude,
    a.latitude,
    a.imei,
    a.imsi,
    a.net_mark,
    a.province,
    a.city,
    a.county,
    a.address,
    a.status,
    a.create_time, 
    a.term_group_id,
    d.detail,
    e.pu_key_path as term_pub_key
    from coms_terminal_info a
    left join cpay_institution d on a.ins_id = d.id
    left join coms_terminal_manufacturer e on a.term_mfr_id = e.id 
    where a.term_seq = #{termSeq,jdbcType=VARCHAR}  
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_terminal_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalInfo" >
    insert into coms_terminal_info (id, term_seq, ins_id, 
      term_mfr_id, term_type_code, link_man, 
      link_phone, longitude, latitude, 
      imei, net_mark, province, 
      city, county, address, 
      status, create_time)
    values (#{id,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR}, #{insId,jdbcType=INTEGER}, 
      #{termMfrId,jdbcType=INTEGER}, #{termTypeCode,jdbcType=VARCHAR}, #{linkMan,jdbcType=VARCHAR}, 
      #{linkPhone,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, 
      #{imei,jdbcType=CHAR}, #{netMark,jdbcType=CHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalInfo" >
    insert into coms_terminal_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="insId != null" >
        ins_id,
      </if>
      <if test="termMfrId != null" >
        term_mfr_id,
      </if>
      <if test="termTypeCode != null" >
        term_type_code,
      </if>
      <if test="linkMan != null" >
        link_man,
      </if>
      <if test="linkPhone != null" >
        link_phone,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="imei != null" >
        imei,
      </if>
      <if test="netMark != null" >
        net_mark,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="county != null" >
        county,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        #{insId,jdbcType=INTEGER},
      </if>
      <if test="termMfrId != null" >
        #{termMfrId,jdbcType=INTEGER},
      </if>
      <if test="termTypeCode != null" >
        #{termTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="linkMan != null" >
        #{linkMan,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="imei != null" >
        #{imei,jdbcType=CHAR},
      </if>
      <if test="netMark != null" >
        #{netMark,jdbcType=CHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null" >
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalInfo" >
    update coms_terminal_info
    <set >
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
      <if test="termMfrId != null" >
        term_mfr_id = #{termMfrId,jdbcType=INTEGER},
      </if>
      <if test="termTypeCode != null" >
        term_type_code = #{termTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="linkMan != null" >
        link_man = #{linkMan,jdbcType=VARCHAR},
      </if>
      <if test="linkPhone != null" >
        link_phone = #{linkPhone,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null" >
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="imei != null" >
        imei = #{imei,jdbcType=CHAR},
      </if>
       <if test="imsi != null" >
        imsi = #{imsi,jdbcType=CHAR},
      </if>
      <if test="netMark != null" >
        net_mark = #{netMark,jdbcType=CHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null" >
        county = #{county,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productionTime != null" >
        production_time = #{productionTime,jdbcType=TIMESTAMP}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.TerminalInfo" >
    update coms_terminal_info
    set term_seq = #{termSeq,jdbcType=VARCHAR},
      ins_id = #{insId,jdbcType=INTEGER},
      term_mfr_id = #{termMfrId,jdbcType=INTEGER},
      term_type_code = #{termTypeCode,jdbcType=VARCHAR},
      link_man = #{linkMan,jdbcType=VARCHAR},
      link_phone = #{linkPhone,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      imei = #{imei,jdbcType=CHAR},
      imsi = #{imsi,jdbcType=CHAR},
      net_mark = #{netMark,jdbcType=CHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="haveStartMonitor" parameterType="java.lang.String" resultType="java.lang.String">
  	select monitor_status from coms_terminal_realtime_status where term_seq = #{termSeq,jdbcType=VARCHAR}
  </select>
  <update id="updateStartMonitor" parameterType="java.lang.String">
  	update coms_terminal_circle set is_change='0' where term_seq = #{termSeq,jdbcType=VARCHAR}
  </update>
  <select id="haveInitialPosition" parameterType="java.lang.String" resultType="java.lang.String">
  	select is_change as isChange from coms_terminal_circle where term_seq=#{termSeq,jdbcType=VARCHAR}
  </select>
  <update id="updateMerhcantInfoByTermSeq" parameterType="java.util.Map">
  	update coms_terminal_info set term_no = #{termNo,jdbcType=VARCHAR},pay_merchant_name = #{merchantName,jdbcType=VARCHAR},pay_merchant_no = #{merchantNo,jdbcType=VARCHAR} where term_seq=#{termSeq,jdbcType=VARCHAR}
  </update>
  <select id="selectMonitorTypeByInstId" parameterType="java.lang.String" resultType="java.lang.String">
  	select move_monitor_type from cpay_institution where id = #{insId,jdbcType=VARCHAR}
  </select>
  <select id="getMonitorParam" parameterType="java.lang.Integer" resultMap="InsMap">
  	select move_monitor_type,lock_theme,is_monitor,is_lock from cpay_institution where id = #{insId,jdbcType=INTEGER}
  </select>
  <select id="getTimeZone" parameterType="java.lang.String" resultType="java.lang.String">
  	select time_zone from coms_terminal_info where term_seq = #{termSeq,jdbcType=VARCHAR}
  </select>
</mapper>