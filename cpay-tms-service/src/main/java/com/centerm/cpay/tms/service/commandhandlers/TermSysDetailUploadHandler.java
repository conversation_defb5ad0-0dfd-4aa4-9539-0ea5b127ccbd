
package com.centerm.cpay.tms.service.commandhandlers;

import java.security.PrivateKey;
import java.util.*;

import com.centerm.cpay.tms.dao.pojo.TerminalInfo;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.dao.pojo.TerminalSysdetail;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;

/**
 * 终端系统驱动参数上送
 * 
 */
@Service("termSysDetailUploadHandler")
public class TermSysDetailUploadHandler extends BaseCommandHandler {

	public static final String COMMAND = "0011";

	@Autowired
	private TerminalService terminalService;

	Logger logger = LoggerFactory.getLogger(TermSysDetailUploadHandler.class);

	@Autowired
	private ServerPriviteKey serverPriviteKey;
	
	@Override
	public String getCommand() {
		return COMMAND;
	}

	@Override
	public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

		logger.info("TermSysDetailUploadHandler.doExecute: 终端系统驱动参数上送...终端序列号:"+tmsProtocol.getTerm_seq_id()+",时间戳："+tmsProtocol.getTimestamp());

		request.position(PROTOCOL_HEAD_LEN);

		String tempLen = CommandHandlerUtil.readRequest(request, 4, DATA_TYPE_HEX);
		int dataLen = Integer.valueOf(tempLen, 16);

		String data = CommandHandlerUtil.readRequest(request, dataLen, DATA_TYPE_CHAR);

		logger.info("系统驱动参数上送:" + data + "    len=" + dataLen);

		TerminalSysdetail oldTerminalSysdetail = terminalService
				.selectTerminalSysdetailByTermSeq(tmsProtocol.getTerm_seq_id());
		TerminalSysdetail terminalSysdetailWhere = new TerminalSysdetail();
		
		TerminalDynInfo terminalDynInfoWhere = terminalService.getTerminalDynInfoByTermSeq(tmsProtocol.getTerm_seq_id());
		
		if (oldTerminalSysdetail == null) {
			
			terminalSysdetailWhere = getTermSysDetail(data);
			terminalSysdetailWhere.setId(0);
			terminalSysdetailWhere.setTermId(tmsProtocol.getTerminalInfo().getId());
			terminalSysdetailWhere.setTermSeq(tmsProtocol.getTerm_seq_id());
			terminalSysdetailWhere.setOsVersion("00000000000000");
			terminalSysdetailWhere.setUpdateTime(CtUtils.getCurrentTime());
			if(!CtUtils.isEmpty(terminalDynInfoWhere)){
				terminalSysdetailWhere.setCommParaVersion(terminalDynInfoWhere.getParaVersion());
			}
			terminalService.insertTerminalSysdetail(terminalSysdetailWhere);
			TerminalInfo terminalInfo = new TerminalInfo();
			terminalInfo.setId(tmsProtocol.getTerminalInfo().getId());
			terminalInfo.setProductionTime(new Date());
			terminalService.updateTerminalInfo(terminalInfo);
		} else {
			
			terminalSysdetailWhere = getTermSysDetail(data);
			terminalSysdetailWhere.setTermId(tmsProtocol.getTerminalInfo().getId());
			terminalSysdetailWhere.setTermSeq(tmsProtocol.getTerm_seq_id());
			terminalSysdetailWhere.setOsVersion("00000000000000");
			terminalSysdetailWhere.setUpdateTime(CtUtils.getCurrentTime());
			terminalSysdetailWhere.setId(oldTerminalSysdetail.getId());
			if(!CtUtils.isEmpty(terminalDynInfoWhere)){
				terminalSysdetailWhere.setCommParaVersion(terminalDynInfoWhere.getParaVersion());
			}
			terminalService.updateTerminalSysdetail(terminalSysdetailWhere);
		}
		IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

		response.put(CtUtils.getCurrentTime().getBytes());

		String macData = ByteUtil
				.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));
		
		PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();
		
		byte[] signBytes =  MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData),TmsConstants.ALGORITHM_SHA256WITHRSA);

		response.put(signBytes);

		int len = response.limit() - 2;
		response.put(0, (byte) (len / 256));
		response.put(1, (byte) (len % 256));
		response.flip();

		logger.info("系统驱动参数上送应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n"
				+ CommandHandlerUtil.getHexLogString(response, 0, response.limit())+",时间戳："+tmsProtocol.getTimestamp());

		recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);
		session.write(response);

		TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
		terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
		terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());


		int updateRet = terminalService.updateTerminalDynInfo(terminalDynInfo);

		logger.info("updateTerminalDynInfo return " + updateRet);

		return true;
	}
	
	public  TerminalSysdetail getTermSysDetail(String sysDetail){
		TerminalSysdetail	termSysDetail = new TerminalSysdetail();
			if(sysDetail !=null && sysDetail != ""){
				String[] arr1 = sysDetail.split("&");

				Set<String> set = new HashSet<String>(Arrays.asList(arr1));
				logger.info(set.toString());
				Iterator<String> i = set.iterator();

				while (i.hasNext()) {

					String str = i.next();
					String val = "";
					try {
						val = str.split("=")[1];
					} catch (Exception e) {
						val = "";
					}
					if (str.split("=")[0].equals("androidVer")) {
						termSysDetail.setAndroidVer(val);
					}
					if (str.split("=")[0].equals("osVer")) {
						termSysDetail.setOsVer(val);
					}
					if (str.split("=")[0].equals("safeModVer")) {
						termSysDetail.setSafeModVer(val);
					}
					if (str.split("=")[0].equals("tmsSDK")) {
						termSysDetail.setTmsSDK(val);
					}
					if (str.split("=")[0].equals("paySDK")) {
						termSysDetail.setPaySDK(val);
					}
					if (str.split("=")[0].equals("emvVer")) {
						termSysDetail.setEmvVer(val);
					}
					if (str.split("=")[0].equals("payAppCode")) {
						termSysDetail.setPayAppCode(val);
					}
					if (str.split("=")[0].equals("payAppName")) {
						termSysDetail.setPayAppName(val);
					}
					if (str.split("=")[0].equals("payAppVersion")) {
						termSysDetail.setPayAppVersion(val);
					}
					if (str.split("=")[0].equals("payAppVersionOutSide")) {
						termSysDetail.setPayAppVersionOutSide(val);
					}
					if (str.split("=")[0].equals("tmsAppVersion")) {
						termSysDetail.setTmsAppVersion(val);
					}
					if (str.split("=")[0].equals("tmsAppVersionOutSide")) {
						termSysDetail.setTmsAppVersionOutSide(val);
					}
					if (str.split("=")[0].equals("networkType")) {
						termSysDetail.setNetworkType(val);
					}
				}
			}
		return termSysDetail;
	}
}
