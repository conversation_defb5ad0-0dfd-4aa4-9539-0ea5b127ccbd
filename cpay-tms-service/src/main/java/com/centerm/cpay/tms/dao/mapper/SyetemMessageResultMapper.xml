<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.SyetemMessageResultMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.SyetemMessageResult" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="message_id" property="messageId" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="receive_time" property="receiveTime" jdbcType="TIMESTAMP" />
    <result column="result_type" property="resultType" jdbcType="CHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, message_id, term_seq, create_time, receive_time, result_type
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_system_message_result
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_system_message_result
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.SyetemMessageResult" >
    insert into coms_system_message_result (message_id, term_seq, 
      create_time, receive_time, result_type
      )
    values (#{messageId,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP}, #{resultType,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.SyetemMessageResult" >
    insert into coms_system_message_result
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="messageId != null" >
        message_id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="receiveTime != null" >
        receive_time,
      </if>
      <if test="resultType != null" >
        result_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="messageId != null" >
        #{messageId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null" >
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultType != null" >
        #{resultType,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.SyetemMessageResult" >
    update coms_system_message_result
    <set >
      <if test="messageId != null" >
        message_id = #{messageId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null" >
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resultType != null" >
        result_type = #{resultType,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.SyetemMessageResult" >
    update coms_system_message_result
    set message_id = #{messageId,jdbcType=INTEGER},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      result_type = #{resultType,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectSendingMessageByTermSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
	  SELECT
		t.*, b.content
		FROM
			coms_system_message_result t,
			coms_system_message b
		WHERE
			t.term_seq = #{termSeq,jdbcType=VARCHAR}
		AND t.result_type = '0'
		AND t.message_id = b.id
  </select>
</mapper>