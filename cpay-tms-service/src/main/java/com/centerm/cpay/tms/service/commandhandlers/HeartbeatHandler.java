package com.centerm.cpay.tms.service.commandhandlers;

import java.security.PrivateKey;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.dao.pojo.TerminalInfo;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;
import com.centerm.cpay.tms.utils.StringUtil;

@Service("heartbeatHandler")
public class HeartbeatHandler extends BaseCommandHandler implements TmsConstants {

    public static final String COMMAND = "0000";

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private ServerPriviteKey serverPriviteKey;

    static Logger logger = LoggerFactory.getLogger(HeartbeatHandler.class);

    @Override
    public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

        logger.info("HeartbeatHandler.doExecute: 心跳包请求...终端序列号:" + tmsProtocol.getTerm_seq_id() + ",时间戳：" + tmsProtocol.getTimestamp());

        request.position(PROTOCOL_HEAD_LEN);

        TerminalInfo terminal = terminalService.getTerminalByTermSeq(tmsProtocol.getTerm_seq_id());
        terminal.setImei(tmsProtocol.getIMEI());
        if (terminal != null) {
            terminal.setImei(tmsProtocol.getIMEI());
            if (tmsProtocol.getIMSI().length() == 15) {//长度等于12的时候表示为
                terminal.setImsi(tmsProtocol.getIMSI());
            } else {
                terminal.setNetMark(tmsProtocol.getIMSI());
            }
            terminalService.updateTerminalInfo(terminal);
        }
        //报文头通讯参数版本号
        String termParamVersion = CommandHandlerUtil.readRequest(request, 14, DATA_TYPE_CHAR);

        // 下一步业务代码,默认心跳包
        String nextCmd = HeartbeatHandler.COMMAND;

        String termSeq = tmsProtocol.getTerm_seq_id();

        TerminalInfo terminalInfo = tmsProtocol.getTerminalInfo();

        if (terminalService.haveCommParaDownloadTask(tmsProtocol)) {
            //1.参数下发
            nextCmd = TermParaDownloadHandler.COMMAND;
        } else if (tmsProtocol.getTerminalInfo().getStatus() != TERM_STATE_ENABLED) {
            //2.终端激活推送
            terminalInfo.setStatus(TERM_STATE_ENABLED);
            terminalService.updateTerminalInfo(terminalInfo);
            nextCmd = TermMessageNoticeHandler.COMMAND;//同时第一次心跳下发指令消息
        } else if (terminalService.haveUploadTask(termSeq)) {
            //3.日志上送
            nextCmd = TermLogUploadReuestHandler.COMMAND;
        } else if (terminalService.haveDownloadTask(termSeq)) {
            //4.任务下载
            if (checkDownloadLimit(termSeq)) {
                nextCmd = TermInfoUploadHandler.COMMAND;
            }
        } else if (terminalService.haveOperateCommandTask(termSeq)) {
            //5.操作指令
            nextCmd = TermOperateHandler.COMMAND;

        } else if (terminalService.havaNewMessageNotice(tmsProtocol)) {
            //5.操作指令
            nextCmd = TermMessageNoticeHandler.COMMAND;

        }

        IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

        response.put(nextCmd.getBytes());

        String macData = ByteUtil
                .bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

        PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();


        byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData), TmsConstants.ALGORITHM_SHA256WITHRSA);

        response.put(signBytes);

        //String mac = CommandHandlerUtil.generateMac(tmsProtocol.getTerminalInfo().getMak(), macData);
        int len = response.limit() - 2;
        response.put(0, (byte) (len / 256));
        response.put(1, (byte) (len % 256));
        response.flip();

        logger.info("心跳包应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n"
                + CommandHandlerUtil.getHexLogString(response, 0, response.limit()) + ",时间戳：" + tmsProtocol.getTimestamp());

        recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);
        session.write(response);

        // 每次通讯之后更新动态表的通讯时间
        logger.info("nextCmd:" + nextCmd);

        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());
        terminalDynInfo.setParaVersion(termParamVersion);
        int ret = terminalService.updateTerminalDynInfo(terminalDynInfo);

        logger.info("updateTerminalDynInfo return " + ret);

        return true;
    }

    @Override
    public String getCommand() {
        return COMMAND;
    }

    private boolean checkDownloadLimit(String termSeq) {
        int curCount = terminalService.getCurrentDownloadNum();
        int maxCount = terminalService.getMaxDownloadNum();

        if (curCount >= maxCount) {
            logger.info("超过最大并发数下载限制: maxCount[" + maxCount + "] DownloadConcurrent=[" + curCount
                    + "]");
            return false;
        }
        return true;
    }
}
