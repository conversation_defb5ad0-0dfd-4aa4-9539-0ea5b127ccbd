package com.centerm.cpay.tms.dao.mapper;

import java.util.List;

import com.centerm.cpay.tms.dao.pojo.TerminalMoveMonitorCondition;
import com.centerm.cpay.tms.dao.pojo.TerminalMoveMonitorSwitch;

public interface TerminalMoveMonitorSwitchMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalMoveMonitorSwitch record);

    int insertSelective(TerminalMoveMonitorSwitch record);

    TerminalMoveMonitorSwitch selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalMoveMonitorSwitch record);

    int updateByPrimaryKey(TerminalMoveMonitorSwitch record);
    
    List<TerminalMoveMonitorSwitch> selectByCondition(TerminalMoveMonitorCondition condition);
}