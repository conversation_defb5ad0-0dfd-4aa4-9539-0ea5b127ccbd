package com.centerm.cpay.tms.dao.mapper;

import java.util.List;

import com.centerm.cpay.tms.dao.pojo.SysConfigure;

public interface SysConfigureMapper {
	List<SysConfigure> getSysConfigure(SysConfigure sysConfigure);

	int deleteById(Integer id);

	int insert(SysConfigure record);

	int insertSelective(SysConfigure record);

	SysConfigure selectByKey(String id);

	int updateByPrimaryKeySelective(SysConfigure record);

	int updateByPrimaryKey(SysConfigure record);

	SysConfigure selectById(Integer id);

	String queryIpChange(String ipChangeValidate);
}