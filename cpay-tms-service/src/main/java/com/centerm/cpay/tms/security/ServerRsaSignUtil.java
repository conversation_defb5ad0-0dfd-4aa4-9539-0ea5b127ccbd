package com.centerm.cpay.tms.security;

import java.security.PrivateKey;
import java.security.PublicKey;

import com.centerm.cpay.security.utils.BytesUtil;
import com.centerm.cpay.tms.utils.ByteUtil;

/**
 * Created by Administrator on 2015/12/25.
 */
public class ServerRsaSignUtil {
	
	public final static String ALGORITHM_SHA256WITHRSA = "sha256withRSA";
	
	private ServerPriviteKey serverPriviteKey;
	
	//终端证书
	public static String privatestr = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCKvEOm/ZE74IAyHt3NJUaPx8eJ\r\n" + 
			"0Qd+nyc113eUJoP8XFeZYqJdX8FPwcpr5NcsnGLoi0UsJlnmws1R6Z8O0BJ5d8lta4kNqLoXjh1V\r\n" + 
			"yMP/vmEvWYB4vOisb4Mz770NaoLJKeZec31ZmvNJtuR+RGbECYZC5hTHwTzvuUHHZiYlynwVGbZT\r\n" + 
			"oV86bkDOcGd6by5MdqC2hLqydUV2FSYafxrf1UBxgFK79fIiwFZi+9cHhY3W6ZNpGtDSCq5S1/5x\r\n" + 
			"LgCqJxe1i9c2vATpdYajvS2uikd/5zc8cxGLJArYvFd5FCJTcQISr78yzKzRIZMAilkEL4I/hlk1\r\n" + 
			"vlw0Mts1lplTAgMBAAECggEAKqa+VbRW1gWq473BirQPAVz151Sv7SmYsGDRjmZY5ViuRaWhLPun\r\n" + 
			"PpHhXVI5JKnfboaCeZRAx0TUQn6EO9WUGGvPLSiGNgDNVTiHpDvYpeMtlZT6fiP1lC2kNG9bu6gn\r\n" + 
			"FRkgjaELxGRyrxLfJEJ7JYGYGTEK6m5qAXj+1AJ/b9Zb1hU7LcVQLcoeVWCIT6l5MogpQU25kyNq\r\n" + 
			"lMh9fEym9IMTrTDmdQ2eVCi12zgGBfu1hxagMGB1Xj6jbcbab6RweSGCYb74vFSpuz+eWuAbldqO\r\n" + 
			"EtpVArJzNAn1ugTk0RaU3T7PsXr7JV5Oa1OmgkBPXATFJcVsgdQ/Lf4QG/Y8QQKBgQD5x1Xubb/4\r\n" + 
			"qnLd1SL6StRiwh8lIGVH+U4GMaGaQedH27O5t589i0SiLwzW2YT/bHiblz+9BmysXHspz/CBBIXe\r\n" + 
			"zXZft/8g2/dnOM+v0bjXB/LHImQ27tTyKRg8sRXQh2QcprzK75R8vmbmEGXEhJasseqAZ5UxZY2e\r\n" + 
			"Qp5kczd0yQKBgQCOMOIbUAucS3Ex4mUwzgAGfv2g+WUXVLZyccxOitss6KkFOS+PMY5AbZbO0rp5\r\n" + 
			"GTlsZNBSAHrwyayRUE4zzLptatHPrw9zYKVVRGAwpTW1QosM7SKKBdx1mXrvStaSDKHERDbSJXYS\r\n" + 
			"7g0xaVdN7SbVE/Uy6DlL6F98tUl40Tu3OwKBgQCdfiXdt8/i0D6rRxfN797pAnO26i1Sa4zZ9PNr\r\n" + 
			"m4BkW9CAGprKqIS3KqmS0wa8Mz39BSpIWsOtDocOPzHrd9mdOboY8qBorKnbqQOzXa6Jz1YyX6oG\r\n" + 
			"xSL6KhVBN3uEx8nFExFZeCUNXtTs3Yv9lIiZDXnKSwXUZq8cCe9rcQaXmQKBgHyofpJTatpriaGi\r\n" + 
			"SfSvpXYMf29RNj9uFUXgXq29LsoAb64UpI11dLEnYr+QH8trQdy1xa0enqnbJV+AeA8RvZ+PtsHN\r\n" + 
			"SE5uvIQ0neAH9z4iSKXcgBthaBXxqMWBZvxD9s6Kv0UdcpbF/OsE33WCJDlKdf1bemACKTS7+t3h\r\n" + 
			"EelhAoGAIdwCOCyl0WysGvi364hy2gKUUrtbjpJ2+B9CHACn0glBa8Q1XwkVoiW3DkZyTSdm6VBV\r\n" + 
			"kD4JqcEMjK6Ja+qiE09WpzZQX/2HX8kly0QDvDxVug36Bhle65pRNBgjoqTrlLJQ1InjNLYkCQuS\r\n" + 
			"/LoACLcPlk2jf7CGcSIKZnRFuVY=";

	public static String publicstr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAirxDpv2RO+CAMh7dzSVGj8fHidEHfp8n\r\n" + 
			"Ndd3lCaD/FxXmWKiXV/BT8HKa+TXLJxi6ItFLCZZ5sLNUemfDtASeXfJbWuJDai6F44dVcjD/75h\r\n" + 
			"L1mAeLzorG+DM++9DWqCySnmXnN9WZrzSbbkfkRmxAmGQuYUx8E877lBx2YmJcp8FRm2U6FfOm5A\r\n" + 
			"znBnem8uTHagtoS6snVFdhUmGn8a39VAcYBSu/XyIsBWYvvXB4WN1umTaRrQ0gquUtf+cS4AqicX\r\n" + 
			"tYvXNrwE6XWGo70tropHf+c3PHMRiyQK2LxXeRQiU3ECEq+/Msys0SGTAIpZBC+CP4ZZNb5cNDLb\r\n" + 
			"NZaZUwIDAQAB";
	
	
//	public static String publicstr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgxKZ4RwsHuB9YazGaMtRco2lPeiMTMHY"+
//			"9ahmqoW6gb5o7xVIXuWvG6Q4pyrOGViojfgOrtHh3Zvh7C8OVqzjc4DlfOwl2FCsNgAXQTukdpbU"+
//			"g2A7ZBCKhRz3C7Aw8qrwp6xgu75Xne3ymAf0JMbD+68qzReJdwLt4lEi8gvwpIPsLp8cO4HZ0f8G"+
//			"OabTRngFy1S0/AIl9If9RvFr7IZHV0Aeu1Df57x9CxU0xH8AR8qyHA9EU/Ex+vb9NnmcR8ubMATS"+
//			"vHlc4Wc1UWkZTl+cU7HkxDXomi3P4NOUvoCtu6Yp88C14qPtwsw/Z7Ii7Bl9Gjoxpljl1hOZZsvt"+
//			"D3aLuwIDAQAB";
//					
//					
//					
//					
//	public	static String privatestr = 
//							
//							"MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCDEpnhHCwe4H1hrMZoy1FyjaU9\r\n" + 
//							"6IxMwdj1qGaqhbqBvmjvFUhe5a8bpDinKs4ZWKiN+A6u0eHdm+HsLw5WrONzgOV87CXYUKw2ABdB\r\n" + 
//							"O6R2ltSDYDtkEIqFHPcLsDDyqvCnrGC7vled7fKYB/QkxsP7ryrNF4l3Au3iUSLyC/Ckg+wunxw7\r\n" + 
//							"gdnR/wY5ptNGeAXLVLT8AiX0h/1G8WvshkdXQB67UN/nvH0LFTTEfwBHyrIcD0RT8TH69v02eZxH\r\n" + 
//							"y5swBNK8eVzhZzVRaRlOX5xTseTENeiaLc/g05S+gK27pinzwLXio+3CzD9nsiLsGX0aOjGmWOXW\r\n" + 
//							"E5lmy+0Pdou7AgMBAAECggEAAh+9Cp7QQDfXEEwicvRIf5i4AsbP7zZ8844lC8JbAVs6jBVGNPN3\r\n" + 
//							"4PTKTjstV8/N+273nfGcvWCXSqKAkvr+PJLsJRgWjOjRwZZCyjXTNnC/RNm8XKS/+eQ8RJ44TNZk\r\n" + 
//							"S/NJJgGxnceLVNdcJ3RpHJz8DpLBEYKkpDQ4Yr+6yI+stmLtJ/ywyd9GOFC7fC2zgc74NjJ5xKvj\r\n" + 
//							"NE0W3VbUf35guXeki3F329S9uC+hlRHbZzJwdub6aoWDjGQBVEAQ3t30oWgbqIuRgkusAgI+d1eu\r\n" + 
//							"8q3P1jMnlKkdl4nIhLfX1nA8zi6CmYKx3/yH/4esEQKyJsJjmanGnfdyuLqnAQKBgQDTrBusbUCt\r\n" + 
//							"+t5jNdMbMfCUu9WnhLWfdleV9tDnAKws/1LmdDpKs+2z3VQKBmG+YlOd+r42e4J+Wy4dFYEeg02e\r\n" + 
//							"v4PdoiSuLj8WZ37zhFqV70pnB38t9vBBEwEnrVCbHMFQTE097Ifr6hlKQkiSyhX7kiXvsYIZ46DH\r\n" + 
//							"BNP3m0ioQQKBgQCehX4+aQxvMzH4cnuKiteMLUWtPrn49VpghJstEvn2G3Os/EXTnWL2Lw5prOo1\r\n" + 
//							"rLYe9u3SOP0Wk7+y1WMa36LWnC0xCcLUJZpeGlKxMhgx4bdzhojN61FgkOiKzOD+QFA6AhHIX1/L\r\n" + 
//							"2GMTWtQc5N2EQni9Bov7qczony5FcPSU+wKBgEVP0jAABnHx9+hcs6fwDmtlpmyYVDDHO57rlzRN\r\n" + 
//							"UBPb93VnEb9sc6W2D9LIjDEfWWodtTN59h0SFmOHqyQmssAMzk1AzuoKUrYvZa6v5Em8dYyPM+IF\r\n" + 
//							"Pl7QrkwjwIVKQTLeCV0BmvBxU56xXbpTWidqj+mRIpz/oKIKvq4RtxUBAoGATsmwplpV2MsMMhzM\r\n" + 
//							"dF5vpXc241WC308q/T/LVIrS+9RHGRpFpljaP9vBgRp1SfouhpNaPpseh2Ml6z+Nw2SVra7v8yiT\r\n" + 
//							"0hdicxUINpPcXKaAJIYgZqBn0OQVAC+2QAg9F6fBqOJmXY2OKKeDN101EDBtjB2943ZQpum++Lqc\r\n" + 
//							"hZMCgYBNIe9FSY+G3/dVFDtQGJ0W0lSMVI6jKjKvdhonnr/9DDdvesAhK5WNDFKx1VtmMwO2NHkU\r\n" + 
//							"5oNaci8YWeKB4xBgoDSwRFfNOIPmpzgcYC+DOKraFNljdbRGJ1qWhLOu3sY5vBxK5DS38DDHNaps\r\n" + 
//							"RjcODva6QsP+gHOUS6VkVRlElA==";	
	
	

	
	public static void main(String[] args) throws Exception {
		String strs ="3036303030303131303444315630313239393932343839202020202020202020202038363838303830333033333138393333383143344135353434333600000032303138313032363131323632383731303236393335353736303030303230313831303236313033393530";

		PrivateKey privateKey = MyRSAUtils.getPrivateKey(privatestr);
		PublicKey pubKey = MyRSAUtils.getPublicKey(publicstr);
		byte[] sign = MyRSAUtils.signWithPrivateKey(privateKey,ByteUtil.hexStringToByte(strs),ALGORITHM_SHA256WITHRSA);
		String str = ByteUtil.bytesToHexString(sign);
		System.out.println(str);
		System.out.println(MyRSAUtils.verifyByPublicKey(pubKey, ByteUtil.hexStringToByte(strs), ByteUtil.hexStringToByte(str), ALGORITHM_SHA256WITHRSA));

		
	}
}