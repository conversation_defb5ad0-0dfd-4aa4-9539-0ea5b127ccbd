
package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.util.Date;
import java.util.List;

import com.centerm.cpay.tms.utils.DateUtil;
import com.centerm.cpay.tms.utils.StringUtil;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.AdSelectCondition;
import com.centerm.cpay.tms.dao.pojo.StoreAdvert;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.config.ConfigInfo;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;


/**
 * 广告信息查询
 */
@Service("adQueryHandler")
public class AdQueryHandler extends BaseCommandHandler {

    public static final String COMMAND = "0010";

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private ConfigInfo configInfo;

    @Autowired
    private ServerPriviteKey serverPriviteKey;

    private static final Logger logger = LoggerFactory.getLogger(AdQueryHandler.class);

    @Override
    public String getCommand() {
        return COMMAND;
    }

    @Override
    public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

        logger.info("AdQueryHandler.doExecute: 广告信息查询..." + tmsProtocol.getTerm_seq_id() + ",时间戳：" + tmsProtocol.getTimestamp());

        request.position(PROTOCOL_HEAD_LEN);

        //device type  1- 横屏  2 -竖屏
        String device_type = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_CHAR);
        /**
         * 广告载体
         1-	普通桌面广告
         2-	普通应用广告
         3-	待机广告
         4-	应用商店广告
         ***/
        String AdDisplay = CommandHandlerUtil.readRequest(request, 1, DATA_TYPE_CHAR);


        //app_code
        String appCode = "";
        if (AdDisplay.equals(AD_DISPLAY_APP)) {
            appCode = CommandHandlerUtil.readRequest(request, 50, DATA_TYPE_CHAR);
        }


        int term_group_id = tmsProtocol.getTerminalInfo().getTermGroupId();
        String insDetail = tmsProtocol.getTerminalInfo().getInsDetail();

        AdSelectCondition cond = new AdSelectCondition();
        cond.setDeviceType(device_type);
        cond.setTermGroupId(term_group_id);
        cond.setInsList(insDetail);
        cond.setType(AdDisplay);
        if (AdDisplay.equals(AD_DISPLAY_APP)) {
            cond.setAppCode(appCode);
        }
        //查询时区
        String timeZone = terminalService.getTimeZone(tmsProtocol.getTerm_seq_id());
        if (StringUtil.isEmpty(timeZone)) {
            cond.setNow(new Date());
        } else {
            Date now = DateUtil.getTimeZoneTime(timeZone);
            if (CtUtils.isEmpty(now)) {
                cond.setNow(new Date());
            } else {
                cond.setNow(now);
            }
        }

        List<StoreAdvert> adList = terminalService.getAdList(cond);


        IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

        if (adList == null || adList.size() == 0) {
            response.put((byte) 0x00);
        } else {
            int count = adList.size();

            logger.info("广告查询，获取广告数量 :" + count);

            logger.info("downloadURL:" + configInfo.getDownloadUrl());

            response.put((byte) count);

            for (int i = 0; i < count; i++) {

                setResponse(response, adList.get(i).getAdFileType(), 1, DATA_TYPE_CHAR);
                String addr = "";
                if (AD_TYPE_WEB.equals(adList.get(i).getAdFileType())) {
                    addr = adList.get(i).getAdPath();
                } else {
                    addr = configInfo.getDownloadUrl() + adList.get(i).getAdPath();
                }

                int addrLen = 0;
                try {
                    addrLen = addr.getBytes("GBK").length;
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }

                response.put((byte) ((addrLen >> 8) & 0xFF));
                response.put((byte) (addrLen & 0xFF));
                setResponse(response, addr, addrLen, DATA_TYPE_CHAR);
                logger.info("广告类型:" + adList.get(i).getAdFileType() + " adPath:" + addr);
            }

        }

        String macData = ByteUtil.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

        PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();

        byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData), TmsConstants.ALGORITHM_SHA256WITHRSA);

        response.put(signBytes);

        int len = response.limit() - 2;
        response.put(0, (byte) (len / 256));
        response.put(1, (byte) (len % 256));
        response.flip();

        logger.info("广告查询应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n" +
                CommandHandlerUtil.getHexLogString(response, 0, response.limit()) + ",时间戳：" + tmsProtocol.getTimestamp());
        recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);

        session.write(response);

        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());

        int ret = terminalService.updateTerminalDynInfo(terminalDynInfo);

        logger.info("updateTerminalDynInfo return " + ret);

        return true;
    }

}
