package com.centerm.cpay.tms.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.centerm.cpay.tms.dao.pojo.*;
import com.centerm.cpay.tms.utils.DateUtil;
import com.centerm.cpay.tms.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.mina.core.buffer.IoBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.enums.MonitorType;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.tms.dao.mapper.AppFlowRecordMapper;
import com.centerm.cpay.tms.dao.mapper.DlItemsLimitMapper;
import com.centerm.cpay.tms.dao.mapper.DownloadTaskMapper;
import com.centerm.cpay.tms.dao.mapper.InformationAnnounceMapper;
import com.centerm.cpay.tms.dao.mapper.InstitutionMapper;
import com.centerm.cpay.tms.dao.mapper.InstitutionParamMapper;
import com.centerm.cpay.tms.dao.mapper.M2mCardMapper;
import com.centerm.cpay.tms.dao.mapper.M2mMessageMapper;
import com.centerm.cpay.tms.dao.mapper.SimFlowusageDayFenDuanCountMapper;
import com.centerm.cpay.tms.dao.mapper.SimFlowusageDayMapper;
import com.centerm.cpay.tms.dao.mapper.SimFlowusageMonthMapper;
import com.centerm.cpay.tms.dao.mapper.StoreAdvertMapper;
import com.centerm.cpay.tms.dao.mapper.SyetemConfigMapper;
import com.centerm.cpay.tms.dao.mapper.SyetemMessageResultMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalAlarmInfoMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalAppRelationMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalBootInfoMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalCircleMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalDynInfoMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalFlowDayMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalInfoMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalMoveMonitorSwitchMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalOperationMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalPositionMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalRealTimeStatusMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalSysdetailMapper;
import com.centerm.cpay.tms.dao.mapper.TerminalSystemMessageMapper;
import com.centerm.cpay.tms.dao.mapper.TmsAccessLogMapper;
import com.centerm.cpay.tms.dao.mapper.UploadTaskMapper;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.commandhandlers.CommandHandlerUtil;
import com.centerm.cpay.tms.service.constants.TmsConstants;

@Service("terminalService")
public class TerminalServiceImpl implements TerminalService, TmsConstants {

    Logger logger = LoggerFactory.getLogger(TerminalServiceImpl.class);

    @Autowired
    private TerminalInfoMapper terminalInfoMapper;

    @Autowired
    private TerminalPositionMapper terminalPositionMapper;

    @Autowired
    private TerminalDynInfoMapper terminalDynInfoMapper;

    @Autowired
    private TerminalBootInfoMapper terminalBootInfoMapper;

    @Autowired
    private TerminalAlarmInfoMapper terminalAlarmInfoMapper;

    @Autowired
    private TerminalAppRelationMapper terminalAppRelationMapper;

    @Autowired
    private DownloadTaskMapper downloadTaskMapper;

    @Autowired
    private UploadTaskMapper uploadTaskMapper;

    @Autowired
    private InstitutionParamMapper institutionParamMapper;

    @Autowired
    private DlItemsLimitMapper dlItemsLimitMapper;

    @Autowired
    private StoreAdvertMapper storeAdvertMapper;

    @Autowired
    private SimFlowusageDayMapper simFlowusageDayMapper;

    @Autowired
    private TerminalSysdetailMapper terminalSysdetailMapper;

    @Autowired
    private TmsAccessLogMapper tmsAccessLogMapper;

    @Autowired
    private M2mMessageMapper m2mMessageMapper;

    @Autowired
    private M2mCardMapper m2mCardMapper;

    @Autowired
    private SimFlowusageMonthMapper simFlowusageMonthMapper;

    @Autowired
    private SyetemMessageResultMapper syetemMessageResultMapper;

    @Autowired
    private SimFlowusageDayFenDuanCountMapper simFlowusageDayFenDuanCountMapper;

    @Autowired
    private SyetemConfigMapper syetemConfigMapper;

    @Autowired
    private AppFlowRecordMapper appFlowRecordMapper;

    @Autowired
    private TerminalRealTimeStatusMapper terminalRealTimeStatusMapper;

    @Autowired
    private TerminalMoveMonitorSwitchMapper terminalMoveMonitorSwitchMapper;

    @Autowired
    private TerminalCircleMapper terminalCircleMapper;

    @Autowired
    private TerminalFlowDayMapper terminalFlowDayMapper;

    @Autowired
    private TerminalOperationMapper terminalOperationMapper;

    @Autowired
    private InformationAnnounceMapper informationAnnounceMapper;

    @Autowired
    private InstitutionMapper institutionMapper;

    @Autowired
    private TerminalSystemMessageMapper terminalSystemMessageMapper;

    @Override
    public TerminalInfo getTerminalByTermSeq(String termSeq) {
        return terminalInfoMapper.selectByTermSeq(termSeq);
    }

    @Override
    public String getTimeZone(String termSeq) {
        return terminalInfoMapper.getTimeZone(termSeq);
    }

    @Override
    public int updateTerminalInfo(TerminalInfo terminalInfo) {
        return terminalInfoMapper.updateByPrimaryKeySelective(terminalInfo);
    }

    @Override
    public int insertTerminalPosition(TerminalPosition terminalPosition) {

        return terminalPositionMapper.insert(terminalPosition);
    }

    @Override
    public boolean haveUploadTask(String termSeq) {
        //设置时区时间
        DownloadTaskCondition condition = getDownloadTaskCondition(termSeq);
        return uploadTaskMapper.countByTermSeq(condition) > 0;
    }

    @Override
    public boolean haveDownloadTask(String termSeq) {

        TerminalSysdetail terminalSysdetail = terminalSysdetailMapper.selectByTermSeq(termSeq);

        String networkConf = terminalSysdetailMapper.selectSysconfBynetWork();

        if (!CtUtils.isEmpty(terminalSysdetail) && TmsConstants.NETWORK_TYPE_SPECIAL.equals(terminalSysdetail.getNetworkType())) {
            if (!"1".equals(networkConf)) {//1、专网WIFI下为1的运行通讯，不等于1的默认关闭
                return false;
            }
        }
        //设置时区时间
        DownloadTaskCondition condition = getDownloadTaskCondition(termSeq);

        //查询总体任务是否有任务需要更新
        boolean hasDownloadTask = downloadTaskMapper.countByTermSeq(condition) > 0;

        if (hasDownloadTask) {

            //获取等待下发的任务列表
            List<DownloadTask> downloadTasks = downloadTaskMapper.getDownloadTasks(condition);
            //遍历列表
            for (DownloadTask downloadTask : downloadTasks) {

                //如果不存在依赖软件，则该软件可下发，返回true。
                if (CtUtils.isEmpty(downloadTask.getDependTaskIds())) {
                    return true;
                } else {
                    //如果存在依赖，先检测依赖软件是否已完成.如果已完成，则该软件可下发
                    if (downloadTaskMapper.countDependNotInSuccess(downloadTask) == 0) {
                        return true;
                    }

                }
            }
        }
        //循环到这步,代表无可更新应用
        return false;
    }

    //设置时区时间
    private DownloadTaskCondition getDownloadTaskCondition(String termSeq) {
        DownloadTaskCondition condition = new DownloadTaskCondition();
        condition.setTermSeq(termSeq);
        //查询时区
        String timeZone = terminalInfoMapper.getTimeZone(termSeq);
        if (StringUtil.isEmpty(timeZone)) {
            condition.setNow(new Date());
        } else {
            Date now = DateUtil.getTimeZoneTime(timeZone);
            if (com.centerm.cpay.tms.utils.CtUtils.isEmpty(now)) {
                condition.setNow(new Date());
            } else {
                condition.setNow(now);
            }
        }
        return condition;
    }

    @Override
    public boolean haveCommParaDownloadTask(TmsProtocol tmsProtocol) {
        TerminalDynInfo terminalDynInfo = getTerminalDynInfoByTermSeq(tmsProtocol.getTerm_seq_id());
        if (terminalDynInfo == null) {
            return true;
        }
        if (terminalDynInfo.getParaVersion() == null) {
            return true;
        }

        InstitutionParam institutionParam = new InstitutionParam();
        institutionParam.setInsId(tmsProtocol.getTerminalInfo().getInsId());
        institutionParam.setParaVersion(tmsProtocol.getTerm_seq_id()); // 传入参数借用paraVersion字段存储termSeq
        InstitutionParam retInstitutionParam = getInstitutionParam(institutionParam);
        if (retInstitutionParam == null) {
            retInstitutionParam = getInstitutionParamByInsList(tmsProtocol.getTerminalInfo().getInsId());
            if (retInstitutionParam == null) {
                return false;
            }
        }

        if (terminalDynInfo.getParaVersion().equals(retInstitutionParam.getParaVersion())) {
            return false;
        }
        return true;
    }

    @Override
    public boolean havaNewMessageNotice(TmsProtocol tmsProtocol) {
        Institution institution = institutionMapper.selectById(tmsProtocol.getTerminalInfo().getInsId());
        InformationAnnounceQuery query = new InformationAnnounceQuery();
        query.setDetail(institution.getDetail());
        List<InformationAnnounce> infoList = informationAnnounceMapper.getInfomation(query);
        if (!CtUtils.isEmpty(infoList) && infoList.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    public int updateTerminalDynInfo(TerminalDynInfo terminalDynInfo) {
        int ret;
        ret = terminalDynInfoMapper.updateByTermSeqSelective(terminalDynInfo);
        logger.info("updateByTermSeqSelective[" + terminalDynInfo.getTermSeq() + "] return " + ret);
        if (ret == 0) {
            ret = terminalDynInfoMapper.insertSelective(terminalDynInfo);
        }
        return ret;
    }

    @Override
    public int updateTerminalDynInfo(TerminalDynInfo terminalDynInfo, String longitude, String latitude) {
        int ret;
        ret = terminalDynInfoMapper.updateByTermSeqSelective(terminalDynInfo);
        logger.info("updateByTermSeqSelective[" + terminalDynInfo.getTermSeq() + "] return " + ret);
        if (ret == 0) {
            ret = terminalDynInfoMapper.insertSelective(terminalDynInfo);
        }
        return ret;
    }

    @Override
    public int insertTerminalBootInfo(TerminalBootInfo terminalBootInfo) {
        return terminalBootInfoMapper.insert(terminalBootInfo);
    }

    @Override
    public TerminalDynInfo getTerminalDynInfoByTermSeq(String termSeq) {
        return terminalDynInfoMapper.selectByTermSeq(termSeq);
    }

    @Override
    public int insertTerminalDynInfo(TerminalDynInfo terminalDynInfo) {
        return terminalDynInfoMapper.insertSelective(terminalDynInfo);
    }

    @Override
    public int countTerminalDynInfo(String termSeq) {
        return terminalDynInfoMapper.countByTermSeq(termSeq);
    }

    @Override
    public int insertTerminalAlarmInfoSelectFromDyn(String termSeq) {

        return terminalAlarmInfoMapper.insertFromDyn(termSeq);
    }

    @Override
    public InstitutionParam getInstitutionParam(InstitutionParam institutionParam) {
        return institutionParamMapper.selectByInstIdAndGroupId(institutionParam);
    }

    @Override
    public List<UploadTask> getUploadTasks(String termSeq) {
        DownloadTaskCondition condition = getDownloadTaskCondition(termSeq);
        return uploadTaskMapper.getUploadTasks(condition);
    }

    @Override
    public int updateUploadTask(UploadTask uploadTask) {
        return uploadTaskMapper.updateByJobIdAndTermSeq(uploadTask);
    }

    @Override
    public List<DownloadTask> getDownloadTasks(DownloadTaskCondition condition) {
        return downloadTaskMapper.getDownloadTasks(condition);
    }

    @Override
    public void stopRepeatTasks(DownloadTaskCondition condition) {
        downloadTaskMapper.stopRepeatTasks(condition);
    }

    @Override
    public int updateDownloadTask(DownloadTask downloadTask) {
        return downloadTaskMapper.updateByPrimaryKeySelective(downloadTask);
    }

    @Override
    public int insertTerminalAppRelationBatch(String flag, List<TerminalAppRelation> list) {
        if ("0".equals(flag)) {
            logger.debug("insertBatchNoVersionOutside");
            return terminalAppRelationMapper.insertBatchNoVersionOutside(list);
        } else if ("1".equals(flag)) {
            logger.debug("insertBatch");
            return terminalAppRelationMapper.insertBatch(list);
        } else {
            logger.debug("insertBatchWithTime");
            return terminalAppRelationMapper.insertBatchWithTime(list);
        }
    }

    @Override
    public int deleteTerminalAppRelation(String termSeq) {
        return terminalAppRelationMapper.deleteByTermSeq(termSeq);
    }

    @Override
    public int getCurrentDownloadNum() {
        return dlItemsLimitMapper.count();
    }

    @Override
    public DlItemsLimit getCurrentTerminalDownloadRes(String termSeq) {
        return dlItemsLimitMapper.selectByTermSeq(termSeq);
    }

    @Override
    public int insertDlItemsLimit(DlItemsLimit dlItemsLimit) {

        DlItemsLimit dlItemsLimitWhere = dlItemsLimitMapper.selectByTermSeq(dlItemsLimit.getTermSeq());
        if (CtUtils.isEmpty(dlItemsLimitWhere)) {
            logger.info("dlItemsLimitWhere：为空插入");
            dlItemsLimitMapper.insert(dlItemsLimit);
        } else {
            logger.info("dlItemsLimitWhere：不为空执行更新");
            int resNum = dlItemsLimit.getDlResNum();
            dlItemsLimit.setDlResNum(resNum + dlItemsLimitWhere.getDlResNum());
            dlItemsLimitMapper.updateByTermSeq(dlItemsLimit);
        }
        return 0;
    }

    @Override
    public int deleteDlItemsLimit(String termSeq) {
        return dlItemsLimitMapper.deleteByTermSeq(termSeq);
    }

    @Override
    public int updateDlItemsLimit(DlItemsLimit dlItemsLimit) {
        return dlItemsLimitMapper.updateByPrimaryKeySelective(dlItemsLimit);
    }

    @Override
    public List<StoreAdvert> getAdList(AdSelectCondition cond) {
        return storeAdvertMapper.selectByCond(cond);
    }

    @Override
    public SimFlowusageDay getFlowusageDay(SimFlowusageDay cond) {
        return simFlowusageDayMapper.selectByCond(cond);
    }

    @Override
    public int insertFlowusageDay(SimFlowusageDay simFlowusageDay) {
        return simFlowusageDayMapper.insertSelective(simFlowusageDay);
    }

    @Override
    public int updateFlowusageDay(SimFlowusageDay simFlowusageDay) {
        return simFlowusageDayMapper.updateByPrimaryKeySelective(simFlowusageDay);
    }

    @Override
    public int insertTerminalSysdetail(TerminalSysdetail record) {
        return terminalSysdetailMapper.insertSelective(record);
    }

    @Override
    public InstitutionParam getInstitutionParamByInsList(int insId) {
        List<InstitutionParam> paramList = institutionParamMapper.selectByCond(insId);
        if (paramList == null || paramList.size() == 0) {
            return null;
        }
        return paramList.get(0);
    }

    @Override
    public int clearDlItmesLimit(ClearCondition cond) {
        return dlItemsLimitMapper.deleteByCond(cond);
    }

    @Override
    public int updateDlFlag(List<Long> idList) {
        return downloadTaskMapper.updateByPrimaryKeyList(idList);
    }

    @Override
    public int updateUlFlag(List<Integer> idList) {
        return uploadTaskMapper.updateByPrimaryKeyList(idList);
    }

    @Override
    public TerminalSysdetail selectTerminalSysdetailByTermSeq(String termSeq) {
        return terminalSysdetailMapper.selectByTermSeq(termSeq);
    }

    @Override
    public int updateTerminalSysdetail(TerminalSysdetail record) {
        return terminalSysdetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public int insertTmsAccessLog(TmsAccessLog record) {
        return tmsAccessLogMapper.insertSelective(record);
    }

    @Override
    public int updateTmsAccessLog(TmsAccessLog record) {
        return tmsAccessLogMapper.updateReponseByCondition(record);
    }

    @Override
    public String queryM2mMessage(String Imsi, String protocolVersion) {
        M2mMessageExample example = new M2mMessageExample();
        M2mMessageExample.Criteria criteria = example.createCriteria();
        criteria.andImsiEqualTo(Imsi).andStatusEqualTo("3");// 3标识发送中
        List<M2mMessage> msgList = m2mMessageMapper.selectByExample(example);
        if (msgList != null && msgList.size() > 0) {
            StringBuffer sb = new StringBuffer();
            for (M2mMessage tempMsg : msgList) {
                sb.append(TimeUtil.format(tempMsg.getSendTime(), "yyyyMMdd"));
                if (Integer.compare(Integer.parseInt(PROTOCOL_VERSION_04), Integer.parseInt(protocolVersion)) > 0) {
                    sb.append("01");// 标识物联卡短信消息
                }
                sb.append(tempMsg.getContent());
                sb.append("|");
                tempMsg.setStatus("1");
                tempMsg.setUpdateTime(new Date());
                m2mMessageMapper.updateByPrimaryKey(tempMsg);
            }
            return sb.toString().substring(0, sb.toString().length() - 1);
        }
        return null;
    }

    public M2mCard queryM2mCardInfoByImsi(String Imsi) {
        return m2mCardMapper.selectByImsi(Imsi);
    }

    public int insertOrUpdateFlowusageMonth(SimFlowusageMonth monthFlow) {
        if (simFlowusageMonthMapper.selectByMonthAndImsi(monthFlow) == null) {
            return simFlowusageMonthMapper.insert(monthFlow);
        } else {
            return simFlowusageMonthMapper.updateByPrimaryKeySelective(monthFlow);
        }
    }

    public String querySystemMessage(String termSeq) {
        List<SyetemMessageResult> msgList = syetemMessageResultMapper.selectSendingMessageByTermSeq(termSeq);
        if (msgList != null && msgList.size() > 0) {
            StringBuffer sb = new StringBuffer();
            for (SyetemMessageResult tempMsg : msgList) {
                sb.append(TimeUtil.format(tempMsg.getCreateTime(), "yyyyMMdd"));
                sb.append("02");// 标识系统消息
                sb.append(tempMsg.getContent());
                sb.append("|");
                tempMsg.setResultType("1");
                syetemMessageResultMapper.updateByPrimaryKeySelective(tempMsg);
            }
            return sb.toString().substring(0, sb.toString().length() - 1);
        }
        return null;
    }

    @Override
    public int insertTerminalElectricity(String timestamp, String termSeq, String prower) {
        TerminalRealTimeStatus terminalRealTimeStatusWhere = terminalRealTimeStatusMapper.queryByTermSeq(termSeq);
        if (terminalRealTimeStatusWhere != null) {
            terminalRealTimeStatusWhere.setTimestamp(timestamp);
            terminalRealTimeStatusWhere.setPower(prower);
            return terminalRealTimeStatusMapper.updateByPrimaryKey(terminalRealTimeStatusWhere);
        }
        TerminalRealTimeStatus prowerInfo = new TerminalRealTimeStatus();
        prowerInfo.setTermSeq(termSeq);
        prowerInfo.setTimestamp(timestamp);
        prowerInfo.setPower(prower);
        return terminalRealTimeStatusMapper.insert(prowerInfo);
    }

    @Override
    public String querySystemConfigByKey(String key) {
        SyetemConfig config = syetemConfigMapper.selectByConfigKey(key);
        if (config != null) {
            return config.getValue();
        }
        return "";
    }

    @Override
    public int deleteAppFlowRecord(AppFlowRecord record) {
        AppFlowRecord recordData = appFlowRecordMapper.selectByCond(record);
        if (recordData != null) {
            return appFlowRecordMapper.insert(record);
        }
        return appFlowRecordMapper.updateByCond(record);
    }

    @Override
    public int insertAppFlowRecordByBatch(List<AppFlowRecord> records) {
        if (records == null || records.isEmpty()) {
            return 0;
        }
        return appFlowRecordMapper.insertRecordsByBatch(records);
    }

    @Override
    public TerminalMoveMonitorSwitch selectTerminalMoveMonitorSwitch(TerminalMoveMonitorCondition termCondition) {

        List<TerminalMoveMonitorSwitch> monitorList = terminalMoveMonitorSwitchMapper.selectByCondition(termCondition);
        if (monitorList == null || monitorList.size() <= 0) {
            return null;
        }
        return monitorList.get(0);
    }

    @Override
    public void updateTerminalCricle(TerminalCircle terminalCircle) {
        TerminalCircle terminalCircleWhere = terminalCircleMapper.selectByTermSeq(terminalCircle.getTermSeq());
        if (CtUtils.isEmpty(terminalCircleWhere)) {
            terminalCircleMapper.insert(terminalCircle);
        } else {
            terminalCircleMapper.updateByPrimaryKeySelective(terminalCircle);
        }
        // int ret =
        // terminalCircleMapper.updateByPrimaryKeySelective(terminalCircle);
        // if(ret == 0){
        // return terminalCircleMapper.insert(terminalCircle);
        // }
        // return ret;
    }

    @Override
    public void updateDlFlagThree(List<Integer> idList) {
        downloadTaskMapper.updateFlagThreeByPrimaryKeyList(idList);
    }

    @Override
    public boolean termIsMove(TmsProtocol tmsProtocol) {
        TerminalRealTimeStatus terminalRealTimeStatus = terminalRealTimeStatusMapper
                .queryByTermSeq(tmsProtocol.getTerm_seq_id());
        if (terminalRealTimeStatus == null || "1".equals(terminalRealTimeStatus.getLockStatus())) {
            return false;
        }

        return true;
    }

    @Override
    public String queryIsStartEle(String termSeq, int instId) {
        String isStart = getTerminalMonitorType(String.valueOf(instId));
        TerminalRealTimeStatus terminalRealTimeStatus = terminalRealTimeStatusMapper.queryByTermSeq(termSeq);
        if (terminalRealTimeStatus != null && "1".equals(terminalRealTimeStatus.getMonitorStatus())) {
            return isStart;
        } else {
            return MonitorType.STOP_MONITOR.getCode();
        }
    }

    @Override
    public boolean haveStartMonitor(TmsProtocol tmsProtocol) {
        boolean result = false;
        String monitorStatus = terminalInfoMapper.haveStartMonitor(tmsProtocol.getTerm_seq_id());
        if (monitorStatus != null && "1".equals(monitorStatus)) {
            result = true;
            return result;
        } else {
            return result;
        }
    }

    @Override
    public void updateStartMonitor(TmsProtocol tmsProtocol) {

        terminalInfoMapper.updateStartMonitor(tmsProtocol.getTerm_seq_id());
    }

    @Override
    public SimFlowusageDay getLastRecord(SimFlowusageDay simFlowusageDay) {
        // TODO
        return simFlowusageDayMapper.getLastRecord(simFlowusageDay);
    }

    @Override
    public SimFlowusageDay getLastFlow(SimFlowusageDay simFlowusageDayWhere) {
        // TODO
        return simFlowusageDayMapper.getLastFlow(simFlowusageDayWhere);
    }

    @Override
    public int insertFlowusageDayCZ(SimFlowusageDay simFlowusageDay) {
        simFlowusageDayMapper.insertFlowusageDayCZ(simFlowusageDay);
        return 0;
    }

    @Override
    public void deleteFlowCount(SimFlowusageDay simFlowusageDayWhere2) {
        simFlowusageDayMapper.deleteFlowCount(simFlowusageDayWhere2);

    }

    @Override
    public SimFlowusageDay getCurrentFlowCount(SimFlowusageDay simFlowusageDayWhere) {
        // TODO
        return simFlowusageDayMapper.getCurrentFlowCount(simFlowusageDayWhere);
    }

    @Override
    public int selectFlowCount(SimFlowusageDay simFlowusageDayWhere) {

        simFlowusageDayMapper.selectFlowCount(simFlowusageDayWhere);
        return 0;
    }

    @Override
    public int updateFlowCount(SimFlowusageDay simFlowusageDayWhere) {

        simFlowusageDayMapper.updateFlowCount(simFlowusageDayWhere);
        return 0;
    }

    @Override
    public SimFlowusageDay selectFenDuanFlowCount(SimFlowusageDay simFlowusageDay) {
        // TODO
        return simFlowusageDayMapper.selectFenDuanFlowCount(simFlowusageDay);
    }

    @Override
    public void insertFenDuanFlowCount(SimFlowusageDay simFlowusageDay) {
        // TODO
        simFlowusageDayFenDuanCountMapper.insert(simFlowusageDay);
    }

    @Override
    public void updateFenDuanFlowCount(SimFlowusageDay fenduanSimFlowusageDay) {
        // TODO
        simFlowusageDayFenDuanCountMapper.updateFenDuanFlowCount(fenduanSimFlowusageDay);
    }

    @Override
    public boolean haveInitialPosition(TmsProtocol tmsProtocol) {
        String isChange = terminalInfoMapper.haveInitialPosition(tmsProtocol.getTerm_seq_id());
        if (!"0".equals(querySystemConfigByKey("isStartEle")) && (StringUtils.isEmpty(isChange) || "1".equals(isChange))) {

            return true;
        } else {
            return false;
        }
    }

    @Override
    public int updateMerchantInfoByTermSeq(Map<String, String> param) {
        return terminalInfoMapper.updateMerhcantInfoByTermSeq(param);
    }

    @Override
    public String getTerminalMonitorType(String instId) {
        return terminalInfoMapper.selectMonitorTypeByInstId(instId);
    }

    @Override
    public Map<String, String> getMonitorParam(Integer instId) {
        return terminalInfoMapper.getMonitorParam(instId);
    }

    @Override
    public void deleteTerminaAppRelationByPart(Map map) {
        terminalAppRelationMapper.terminalAppRelationMapper(map);
    }

    @Override
    public void insertTerminalFlow(TerminalFlowDay terminalFlowDay) {
        // TODO
        terminalFlowDayMapper.insert(terminalFlowDay);
    }

    @Override
    public TerminalOperation queryTerminalOperactionCommand(String termSeq) {
        // TODO
        return terminalOperationMapper.queryTerminalOperactionCommand(termSeq);
    }

    @Override
    public boolean haveOperateCommandTask(String termSeq) {
        TerminalOperation terminalOperation = terminalOperationMapper.queryTerminalOperactionCommand(termSeq);
        if (!CtUtils.isEmpty(terminalOperation)) {
            return true;
        }
        return false;
    }

    @Override
    public void updateOpeateCommandStatus(TerminalOperation terminalOperation) {
        // TODO
        terminalOperationMapper.updateByPrimaryKeySelective(terminalOperation);
    }

    @Override
    public String getDriverJobName(Integer jobId) {
        // TODO
        return downloadTaskMapper.getDriverJobName(jobId);
    }

    @Override
    public int getMaxDownloadNum() {

        return Integer.parseInt(dlItemsLimitMapper.getMaxDownloadNum());
    }


    @Override
    public int updateResultInfo(DownloadTask downloadTask) {
        return downloadTaskMapper.updateByPrimaryKeySelective(downloadTask);
    }

    @Override
    public int countDependWorking(DownloadTask downloadTask) {
        return downloadTaskMapper.countDependWorking(downloadTask);
    }

    @Override
    public int countDependNotInSuccess(DownloadTask downloadTask) {
        return downloadTaskMapper.countDependNotInSuccess(downloadTask);
    }

    @Override
    public List<InformationAnnounce> getInfomation(InformationAnnounceQuery query) {
        // TODO
        return informationAnnounceMapper.getInfomation(query);
    }

    @Override
    public TerminalFlowDay queryFlowExist(TerminalFlowDay terminalFlowDay) {
        // TODO
        return terminalFlowDayMapper.queryFlowExist(terminalFlowDay);
    }

    @Override
    public void updateTerminalFlow(TerminalFlowDay terminalFlowDay) {
        // TODO
        terminalFlowDayMapper.updateByPrimaryKey(terminalFlowDay);
    }

    @Override
    public String getconfigInfo(String downloadUrl) {
        // TODO
        return dlItemsLimitMapper.getconfigInfo(downloadUrl);
    }

    @Override
    public int checkDownload(TerminalAppRelation relation) {

        TerminalAppRelation terminalAppRelation = terminalAppRelationMapper.selectByDownTask(relation);
        try {
            //获取数据库中记录的终端软件信息
            //无记录时，可下发
            if (CtUtils.isEmpty(terminalAppRelation)) {
                return 0;
            } else {
                int appVersionCur = Integer.parseInt(terminalAppRelation.getAppVersion());
                int recordVersion = Integer.parseInt(relation.getAppVersion());
                logger.info("sn:"+terminalAppRelation.getTermSeq()+" appcode:"+terminalAppRelation.getAppCode()+" appVersionCur:"+appVersionCur);
                logger.info("sn:"+relation.getTermSeq()+" appcode:"+relation.getAppCode()+" recordVersion:"+recordVersion);
                //下发的版本大于记录版本，可下发
                if (appVersionCur < recordVersion) {
                    return 0;
                } else if (appVersionCur == recordVersion) {
                    //下发的版本等于记录版本，返回 1
                    return 1;
                } else {
                    return 2;
                }
            }
        } catch (Exception e) {
            logger.info("checkDownload： " + "软件版本检测出错！");
        }
        //报错不可下发
        return 4;
    }

    @Override
    public int checkAppId(DownloadTask downloadTask) {
        return downloadTaskMapper.checkAppId(downloadTask);
    }

    @Override
    public List<TerminalSystemMessage> getTerminalMessage(String termSeq) {
        // TODO
        return terminalSystemMessageMapper.getTerminalMessage(termSeq);
    }
}
