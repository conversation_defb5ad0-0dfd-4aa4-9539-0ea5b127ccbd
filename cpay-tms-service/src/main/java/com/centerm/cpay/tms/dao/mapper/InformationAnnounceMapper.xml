<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.InformationAnnounceMapper">
	<resultMap id="BaseResultMap"
		type="com.centerm.cpay.tms.dao.pojo.InformationAnnounce">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="ins_id" property="insId" jdbcType="INTEGER" />
		<result column="title" property="title" jdbcType="VARCHAR" />
		<result column="content" property="content" jdbcType="VARCHAR" />
		<result column="stick" property="stick" jdbcType="TINYINT" />
	</resultMap>
	<sql id="Base_Column_List">
		a.id, a.content,a.stick,
		a.ins_id,a.title
	</sql>
	<select id="getInfomation" resultMap="BaseResultMap"
		parameterType="com.centerm.cpay.tms.dao.pojo.InformationAnnounceQuery">
		select
		<include refid="Base_Column_List" />
		,b.name as roleName
		from cpay_information_announcement a LEFT JOIN
		cpay_institution b on
		a.ins_id = b.id where 1=1 
		and a.type='2'
		and (a.ins_id IN (
			SELECT
				t2.id
			FROM
				cpay_institution t2
			WHERE
				t2. CODE IN (${detail})
		) or a.ins_id is null)
		and a.id not in (
			select c.message_id from COMS_TERMINAL_SYSTEM_MESSAGE c where c.term_seq=#{termSeq}
		)
	</select>
	</mapper>