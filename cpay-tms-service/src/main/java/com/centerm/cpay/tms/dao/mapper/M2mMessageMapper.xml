<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.M2mMessageMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.M2mMessage" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="card_no" property="cardNo" jdbcType="VARCHAR" />
    <result column="msg_id" property="msgId" jdbcType="VARCHAR" />
    <result column="iccid" property="iccid" jdbcType="CHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="remind_month" property="remindMonth" jdbcType="VARCHAR" />
    <result column="imsi" property="imsi" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, card_no, msg_id, iccid, content, type, send_time, update_time, status, remind_month,imsi
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.centerm.cpay.tms.dao.pojo.M2mMessageExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from coms_cas_m2m_msg
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_cas_m2m_msg
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_cas_m2m_msg
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.M2mMessage" >
    insert into coms_cas_m2m_msg (card_no, msg_id, 
      iccid, content, type, 
      send_time, update_time, status, 
      remind_month,imsi)
    values (#{cardNo,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR}, 
      #{iccid,jdbcType=CHAR}, #{content,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=CHAR}, 
      #{remindMonth,jdbcType=VARCHAR},#{imsi,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.M2mMessage" >
    insert into coms_cas_m2m_msg
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cardNo != null" >
        card_no,
      </if>
      <if test="msgId != null" >
        msg_id,
      </if>
      <if test="iccid != null" >
        iccid,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="sendTime != null" >
        send_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remindMonth != null" >
        remind_month,
      </if>
      <if test="imsi != null" >
        imsi,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="cardNo != null" >
        #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null" >
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null" >
        #{iccid,jdbcType=CHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=CHAR},
      </if>
      <if test="remindMonth != null" >
        #{remindMonth,jdbcType=VARCHAR},
      </if>
       <if test="imsi != null" >
        #{imsi,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.M2mMessage" >
    update coms_cas_m2m_msg
    <set >
      <if test="cardNo != null" >
        card_no = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null" >
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null" >
        iccid = #{iccid,jdbcType=CHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="remindMonth != null" >
        remind_month = #{remindMonth,jdbcType=VARCHAR},
      </if>
      <if test="imsi != null" >
        imsi = #{imsi,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.M2mMessage" >
    update coms_cas_m2m_msg
    set card_no = #{cardNo,jdbcType=VARCHAR},
      msg_id = #{msgId,jdbcType=VARCHAR},
      iccid = #{iccid,jdbcType=CHAR},
      content = #{content,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=CHAR},
      remind_month = #{remindMonth,jdbcType=VARCHAR},
      imsi = #{imsi,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>