package com.centerm.cpay.tms.dao.mapper;

import java.util.Map;

import com.centerm.cpay.tms.dao.pojo.TerminalInfo;

public interface TerminalInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TerminalInfo record);

    int insertSelective(TerminalInfo record);

    TerminalInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TerminalInfo record);

    int updateByPrimaryKey(TerminalInfo record);

	TerminalInfo selectByTermSeq(String termSeq);

	String haveStartMonitor(String termSeq);

	void updateStartMonitor(String termSeq);

	String haveInitialPosition(String term_seq_id);
	
	int updateMerhcantInfoByTermSeq(Map<String,String> params);
	
	String selectMonitorTypeByInstId(String instId);

	Map<String,String> getMonitorParam(Integer instId);

	String getTimeZone(String termSeq);
}