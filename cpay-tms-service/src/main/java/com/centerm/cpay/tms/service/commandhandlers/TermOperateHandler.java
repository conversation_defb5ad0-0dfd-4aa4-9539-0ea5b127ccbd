package com.centerm.cpay.tms.service.commandhandlers;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.dao.pojo.TerminalOperation;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;


/**
 * 终端远程运维
 */
@Service("termOperateHandler")
public class TermOperateHandler extends BaseCommandHandler {

    public static final String COMMAND = "0013";

    @Autowired
    private TerminalService terminalService;

    private static final Logger logger = LoggerFactory.getLogger(TermOperateHandler.class);

    @Autowired
    private ServerPriviteKey serverPriviteKey;

    @Override
    public String getCommand() {
        return COMMAND;
    }


    @Override
    public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

        logger.info("TermOperateHandler.doExecute: 远程运维指令...终端序列号:" + tmsProtocol.getTerm_seq_id() + ",时间戳：" + tmsProtocol.getTimestamp());

        request.position(PROTOCOL_HEAD_LEN);


        IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, RESPONSE_CODE_SUCESS);

        TerminalOperation terminalOperation = terminalService.queryTerminalOperactionCommand(tmsProtocol.getTerm_seq_id());

        String command = terminalOperation.getOperateCommand();

        setResponse(response, command, 2, DATA_TYPE_CHAR);

        // kiosk mode
        if ("19".equals(command)){
            JSONObject contenJson = JSONUtil.parseObj(terminalOperation.getContent());
            int appPackageLen = 0;
            try {
                appPackageLen = contenJson.getStr("appPackage").getBytes("GBK").length;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            response.put((byte) ((appPackageLen >> 8) & 0xFF));
            response.put((byte) (appPackageLen & 0xFF));
            setResponse(response, contenJson.getStr("appPackage"), appPackageLen, DATA_TYPE_CHAR);

            int appActivityLen = 0;
            try {
                appActivityLen = contenJson.getStr("appActivity").getBytes("GBK").length;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            response.put((byte) ((appActivityLen >> 8) & 0xFF));
            response.put((byte) (appActivityLen & 0xFF));
            setResponse(response, contenJson.getStr("appActivity"), appActivityLen, DATA_TYPE_CHAR);

            setResponse(response, contenJson.getStr("appPwd"), 4, DATA_TYPE_CHAR);
        }

        terminalOperation.setOperateStatus("1");

        terminalService.updateOpeateCommandStatus(terminalOperation);


        String macData = ByteUtil.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

        PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();

        byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData), TmsConstants.ALGORITHM_SHA256WITHRSA);

        response.put(signBytes);

        int len = response.limit() - 2;
        response.put(0, (byte) (len / 256));
        response.put(1, (byte) (len % 256));
        response.flip();

        logger.info("远程运维指令应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n" +
                CommandHandlerUtil.getHexLogString(response, 0, response.limit()) + ",时间戳：" + tmsProtocol.getTimestamp());
        recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);

        session.write(response);

        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());

        int ret = terminalService.updateTerminalDynInfo(terminalDynInfo);

        logger.info("updateTerminalDynInfo return " + ret);

        return true;
    }

}
