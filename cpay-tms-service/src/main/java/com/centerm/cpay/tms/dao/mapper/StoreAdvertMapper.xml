<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.tms.dao.mapper.StoreAdvertMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.tms.dao.pojo.StoreAdvert" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ad_path" property="adPath" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="md5" property="md5" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="pub_time" property="pubTime" jdbcType="TIMESTAMP" />
    <result column="cr_time" property="crTime" jdbcType="TIMESTAMP" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="DATE" />
    <result column="end_time" property="endTime" jdbcType="DATE" />
    <result column="type" property="type" jdbcType="CHAR" />
    <result column="factory_id" property="factoryId" jdbcType="INTEGER" />
    <result column="term_type_id" property="termTypeId" jdbcType="INTEGER" />
    <result column="ad_audit" property="adAudit" jdbcType="VARCHAR" />
    <result column="term_group_id" property="termGroupId" jdbcType="INTEGER" />
    <result column="ad_file_type" property="adFileType" jdbcType="CHAR" />
    <result column="device_type" property="deviceType" jdbcType="CHAR" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="is_disabled" property="isDisabled" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, ad_path, file_name, md5, name, ins_id, pub_time, cr_time, description, start_time,
    end_time, type, factory_id, term_type_id, ad_audit, term_group_id, ad_file_type,
    device_type, app_code,is_disabled
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from coms_store_advert
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByCond" resultMap="BaseResultMap" parameterType="com.centerm.cpay.tms.dao.pojo.AdSelectCondition" >
    SELECT * FROM (
    SELECT
    <include refid="Base_Column_List" />,
    (SELECT COUNT(*) FROM coms_store_advert AS csa WHERE csa.pub_time > coms_store_advert.pub_time) + 1 AS ROWNUM
    FROM coms_store_advert
    WHERE ins_id IN (SELECT id FROM cpay_institution WHERE code IN (${insList}))
    AND device_type IN ('3', #{deviceType,jdbcType=CHAR})
    AND ad_audit = '1'
    AND is_disabled = '0'
    AND (term_group_id = #{termGroupId,jdbcType=INTEGER} OR term_group_id = 0)
    AND type = #{type,jdbcType=VARCHAR}
    AND start_time <![CDATA[ <= ]]> #{now,jdbcType=TIMESTAMP}
    AND end_time <![CDATA[>=]]> #{now,jdbcType=TIMESTAMP}
    <if test="appCode != null">
      AND app_code = #{appCode,jdbcType=VARCHAR}
    </if>
    ) AS subquery
    WHERE  ROWNUM &gt;=1 AND ROWNUM &lt;=5
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_store_advert
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.tms.dao.pojo.StoreAdvert" >
    insert into coms_store_advert (ad_path, file_name,
                                   md5, name, ins_id, pub_time,
                                   cr_time, description, start_time,
                                   end_time, type, factory_id,
                                   term_type_id, ad_audit, term_group_id,
                                   ad_file_type, device_type, app_code,is_disabled
    )
    values (#{adPath,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
            #{md5,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{insId,jdbcType=INTEGER}, #{pubTime,jdbcType=TIMESTAMP},
            #{crTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR}, #{startTime,jdbcType=DATE},
            #{endTime,jdbcType=DATE}, #{type,jdbcType=CHAR}, #{factoryId,jdbcType=INTEGER},
            #{termTypeId,jdbcType=INTEGER}, #{adAudit,jdbcType=VARCHAR}, #{termGroupId,jdbcType=INTEGER},
            #{adFileType,jdbcType=CHAR}, #{deviceType,jdbcType=CHAR}, #{appCode,jdbcType=VARCHAR},#{isDisabled,jdbcType=CHAR}
           )
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.tms.dao.pojo.StoreAdvert" >
    insert into coms_store_advert
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="adPath != null" >
        ad_path,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="md5 != null" >
        md5,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="insId != null" >
        ins_id,
      </if>
      <if test="pubTime != null" >
        pub_time,
      </if>
      <if test="crTime != null" >
        cr_time,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="factoryId != null" >
        factory_id,
      </if>
      <if test="termTypeId != null" >
        term_type_id,
      </if>
      <if test="adAudit != null" >
        ad_audit,
      </if>
      <if test="termGroupId != null" >
        term_group_id,
      </if>
      <if test="adFileType != null" >
        ad_file_type,
      </if>
      <if test="deviceType != null" >
        device_type,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="isDisabled != null" >
        is_disabled,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="adPath != null" >
        #{adPath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        #{insId,jdbcType=INTEGER},
      </if>
      <if test="pubTime != null" >
        #{pubTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crTime != null" >
        #{crTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=DATE},
      </if>
      <if test="type != null" >
        #{type,jdbcType=CHAR},
      </if>
      <if test="factoryId != null" >
        #{factoryId,jdbcType=INTEGER},
      </if>
      <if test="termTypeId != null" >
        #{termTypeId,jdbcType=INTEGER},
      </if>
      <if test="adAudit != null" >
        #{adAudit,jdbcType=VARCHAR},
      </if>
      <if test="termGroupId != null" >
        #{termGroupId,jdbcType=INTEGER},
      </if>
      <if test="adFileType != null" >
        #{adFileType,jdbcType=CHAR},
      </if>
      <if test="deviceType != null" >
        #{deviceType,jdbcType=CHAR},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="isDisabled != null" >
        #{isDisabled,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.tms.dao.pojo.StoreAdvert" >
    update coms_store_advert
    <set >
      <if test="adPath != null" >
        ad_path = #{adPath,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
      <if test="pubTime != null" >
        pub_time = #{pubTime,jdbcType=TIMESTAMP},
      </if>
      <if test="crTime != null" >
        cr_time = #{crTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=DATE},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=CHAR},
      </if>
      <if test="factoryId != null" >
        factory_id = #{factoryId,jdbcType=INTEGER},
      </if>
      <if test="termTypeId != null" >
        term_type_id = #{termTypeId,jdbcType=INTEGER},
      </if>
      <if test="adAudit != null" >
        ad_audit = #{adAudit,jdbcType=VARCHAR},
      </if>
      <if test="termGroupId != null" >
        term_group_id = #{termGroupId,jdbcType=INTEGER},
      </if>
      <if test="adFileType != null" >
        ad_file_type = #{adFileType,jdbcType=CHAR},
      </if>
      <if test="deviceType != null" >
        device_type = #{deviceType,jdbcType=CHAR},
      </if>
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="isDisabled != null" >
        is_disabled = #{isDisabled,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.tms.dao.pojo.StoreAdvert" >
    update coms_store_advert
    set ad_path = #{adPath,jdbcType=VARCHAR},
        file_name = #{fileName,jdbcType=VARCHAR},
        md5 = #{md5,jdbcType=VARCHAR},
        name = #{name,jdbcType=VARCHAR},
        ins_id = #{insId,jdbcType=INTEGER},
        pub_time = #{pubTime,jdbcType=TIMESTAMP},
        cr_time = #{crTime,jdbcType=TIMESTAMP},
        description = #{description,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=DATE},
        end_time = #{endTime,jdbcType=DATE},
        type = #{type,jdbcType=CHAR},
        factory_id = #{factoryId,jdbcType=INTEGER},
        term_type_id = #{termTypeId,jdbcType=INTEGER},
        ad_audit = #{adAudit,jdbcType=VARCHAR},
        term_group_id = #{termGroupId,jdbcType=INTEGER},
        ad_file_type = #{adFileType,jdbcType=CHAR},
        device_type = #{deviceType,jdbcType=CHAR},
        app_code = #{appCode,jdbcType=VARCHAR},
        is_disabled = #{isDisabled,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>