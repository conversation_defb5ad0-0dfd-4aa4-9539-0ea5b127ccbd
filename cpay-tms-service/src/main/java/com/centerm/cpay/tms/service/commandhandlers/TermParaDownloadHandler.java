package com.centerm.cpay.tms.service.commandhandlers;

import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.security.utils.ByteUtil;
import com.centerm.cpay.tms.dao.pojo.InstitutionParam;
import com.centerm.cpay.tms.dao.pojo.TerminalDynInfo;
import com.centerm.cpay.tms.dao.pojo.TerminalSysdetail;
import com.centerm.cpay.tms.security.MyRSAUtils;
import com.centerm.cpay.tms.security.ServerPriviteKey;
import com.centerm.cpay.tms.service.TerminalService;
import com.centerm.cpay.tms.service.TmsProtocol;
import com.centerm.cpay.tms.service.constants.TmsConstants;
import com.centerm.cpay.tms.service.exception.TmsException;
import com.centerm.cpay.tms.utils.CtUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service("termParaDownloadHandler")
public class TermParaDownloadHandler extends BaseCommandHandler implements TmsConstants {

    public static final String COMMAND = "0005";

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private ServerPriviteKey serverPriviteKey;

    Logger logger = LoggerFactory.getLogger(TermParaDownloadHandler.class);

    @Override
    public boolean doExecute(IoSession session, IoBuffer request, TmsProtocol tmsProtocol) throws TmsException {

        logger.info("TermParaDownloadHandler.doExecute: 参数下载...终端序列号:" + tmsProtocol.getTerm_seq_id() + ",时间戳：" + tmsProtocol.getTimestamp());

        request.position(PROTOCOL_HEAD_LEN);

        //参数版本号
        String paraVersion = CommandHandlerUtil.readRequest(request, 14, DATA_TYPE_CHAR);

        logger.info("paraVersion=" + paraVersion);

        InstitutionParam institutionParam = new InstitutionParam();
        institutionParam.setInsId(tmsProtocol.getTerminalInfo().getInsId());
        institutionParam.setParaVersion(tmsProtocol.getTerm_seq_id());  //传入参数借用paraVersion字段存储termSeq
        InstitutionParam retInstitutionParam = terminalService.getInstitutionParam(institutionParam);

        String respCode = RESPONSE_CODE_SUCESS;

        if (retInstitutionParam == null || retInstitutionParam.getParaContent() == null) {

            retInstitutionParam = terminalService.getInstitutionParamByInsList(tmsProtocol.getTerminalInfo().getInsId());

            if (retInstitutionParam == null || retInstitutionParam.getParaContent() == null) {
                logger.error("termSeq[" + tmsProtocol.getTerm_seq_id() + "] ins_id[" + tmsProtocol.getTerminalInfo().getInsId() + "] 参数不存在");
                IoBuffer response = CommandHandlerUtil.generateErrorResponse(RESPONSE_CODE_NOPARAM);
                response.flip();
                recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_NOPARAM);
                session.write(response);
                return true;
            }
        }


        IoBuffer response = CommandHandlerUtil.packRespHead(tmsProtocol, respCode);

        //参数更新版本号
        response.put(retInstitutionParam.getParaVersion().getBytes());

        //参数长度
        int paraLen = 0;
        String paramStr = retInstitutionParam.getParaContent();
        paramStr = (Integer.compare(Integer.parseInt(PROTOCOL_VERSION_02), Integer.parseInt(tmsProtocol.getProtocol_ver())) <= 0 ? paramStr : (paramStr.contains("#") ? paramStr.substring(0, paramStr.indexOf("#")) : paramStr));
        try {
            paraLen = paramStr.getBytes("GBK").length;

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.put((byte) (paraLen >> 8));
        response.put((byte) (paraLen & 0xFF));

        //参数
        try {
            response.put(paramStr.getBytes("GBK"));
        } catch (UnsupportedEncodingException e) {
            // TODO
            e.printStackTrace();
        }

        String macData = ByteUtil.bytesToHexString(CommandHandlerUtil.getIoBufferData(response, 2, response.limit() - 2));

        PrivateKey PrivateKey = serverPriviteKey.getServerPrivateKey();

        byte[] signBytes = MyRSAUtils.signWithPrivateKey(PrivateKey, ByteUtil.hexStringToByte(macData), TmsConstants.ALGORITHM_SHA256WITHRSA);

        response.put(signBytes);


        int len = response.limit() - 2;
        response.put(0, (byte) (len / 256));
        response.put(1, (byte) (len % 256));
        response.flip();


        logger.info("参数下载应答报文[" + tmsProtocol.getTerm_seq_id() + "]:\n" +
                CommandHandlerUtil.getHexLogString(response, 0, response.limit()) + ",时间戳：" + tmsProtocol.getTimestamp());
        recordReponseByteLength(response, tmsProtocol, RESPONSE_CODE_SUCESS);
        session.write(response);

        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setTermSeq(tmsProtocol.getTerm_seq_id());
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());
        terminalDynInfo.setParaVersion(retInstitutionParam.getParaVersion());

        int ret = terminalService.updateTerminalDynInfo(terminalDynInfo);

        logger.info("updateTerminalDynInfo return " + ret);

        return true;
    }

    @Override
    public String getCommand() {
        return COMMAND;
    }

}
