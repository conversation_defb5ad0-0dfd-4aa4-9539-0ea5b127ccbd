<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
	http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd">

	<!-- 扫描包加载Service实现类 -->
	<context:component-scan base-package="com.centerm.cpay"></context:component-scan>
	
	<import resource="applicationContext-dao.xml"/>
	<import resource="applicationContext-trans.xml"/>
	<!-- <import resource="applicationContext-cxf.xml"/> -->
    <!--  about mina -->
    <bean id="cacheUtil" class="com.centerm.cpay.common.utils.CacheUtil" init-method="init" destroy-method="destroy">
	
		<property name="threadPoolSize" value="${thread.pool.size}"></property>

	</bean>
    <bean class="org.springframework.beans.factory.config.CustomEditorConfigurer">
	    <property name="customEditors">
	      <map>
	        <entry key="java.net.SocketAddress" value="org.apache.mina.integration.beans.InetSocketAddressEditor" />
	      </map>
	    </property>
  	</bean>
  	
  <!-- 	<bean id="timeUnit" class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">    
        <property name="staticField" value="java.util.concurrent.TimeUnit.SECONDS" />    
    </bean>  
  	
  	<bean id="workQueue" class="java.util.concurrent.SynchronousQueue" /> -->
  	
  	
  	<bean id="tmsHandler" class="com.centerm.cpay.tms.service.handler.TmsHandler" />
  	
  	
  	<!-- the IoFilters -->

	<bean id="executorFilter" class="org.apache.mina.filter.executor.ExecutorFilter" >
		<constructor-arg value="32"/>
	</bean>

	<bean id="mdcInjectionFilter" class="org.apache.mina.filter.logging.MdcInjectionFilter" />
	
	<bean id="codecFilter" class="org.apache.mina.filter.codec.ProtocolCodecFilter">
	  <constructor-arg>
	    <bean class="com.centerm.cpay.tms.service.message.MessageCodecFactory" />
	  </constructor-arg>
	</bean>
	
	<bean id="loggingFilter" class="org.apache.mina.filter.logging.LoggingFilter">
		<constructor-arg value="com.centerm"/>
	</bean>
  
    
	<bean id="filterChainBuilder" class="org.apache.mina.core.filterchain.DefaultIoFilterChainBuilder">
	  <property name="filters">
	    <map>
	      <entry key="executor" value-ref="executorFilter"/>
	      <entry key="mdcInjectionFilter" value-ref="mdcInjectionFilter"/>
	      <entry key="codecFilter" value-ref="codecFilter"/>
	      <entry key="loggingFilter" value-ref="loggingFilter"/>
	    </map>
	  </property>
	</bean>
    
    <!-- The IoAcceptor -->
	<bean id="ioAcceptor" class="org.apache.mina.transport.socket.nio.NioSocketAcceptor" init-method="bind" destroy-method="unbind">
	  <property name="defaultLocalAddress" value=":${tms.port}" />
	  <property name="handler" ref="tmsHandler" />
	  <property name="reuseAddress" value="true" />
	  <property name="filterChainBuilder" ref="filterChainBuilder" />
	</bean>
	
	 <bean id="sessionConfig" factory-bean="ioAcceptor" factory-method="getSessionConfig" >
        <property name="readerIdleTime" value="300"/>
        <property name="minReadBufferSize" value="512"/>
        <property name="maxReadBufferSize" value="10240"/>
     </bean>
     
     <!-- the command handlers -->
     <bean id="commandHandlers" class="com.centerm.cpay.tms.service.CommandHandlers">
     	<property name="handlers">
     		<map>
     			<entry key="0000" value-ref="heartbeatHandler"></entry>  <!-- 心跳包 -->
     			<entry key="0001" value-ref="termSwitchHandler"></entry>   <!--终端开关机信息上送 -->
     			<entry key="0002" value-ref="termStatusUploadHandler"></entry> <!--终端状态上送 -->
     			<entry key="0003" value-ref="termInfoUploadHandler"></entry> <!--终端信息上送 -->
     			<entry key="0004" value-ref="dlResultUploadHandler"></entry> <!-- 下载结果上送 -->
     			<entry key="0005" value-ref="termParaDownloadHandler"></entry> <!-- 应用参数下载 -->
     			<entry key="0006" value-ref="termLogUploadReuestHandler"></entry><!--请求上传日志文件 -->
     			<entry key="0007" value-ref="ulResultUploadHandler"></entry> <!-- 上传结果上送 -->
     			<entry key="0008" value-ref="termFlowUploadHandler"></entry> <!-- 终端流量统计 -->
    			<entry key="0009" value-ref="terminalFaultUploadHandler"></entry><!-- 终端故障信息上送接口 -->
 				<entry key="0010" value-ref="adQueryHandler"></entry> <!-- 广告查询 -->
 				<entry key="0011" value-ref="termSysDetailUploadHandler"></entry> <!-- 终端系统驱动参数 -->
 				<!-- <entry key="0012" value-ref="coordinateUploadHandler"></entry> 终端经纬度信息上送 -->
 				<entry key="0012" value-ref="termMonitorStatusUploadHandler"></entry><!-- 终端移机监控位置上送 -->
 				<entry key="0013" value-ref="termOperateHandler"></entry><!-- 终端远程操作指令下发 -->
 				<entry key="0014" value-ref="termMessageNoticeHandler"></entry><!-- 终端远程操作指令下发 -->
     		</map>
     	</property>
     </bean>
     
     
     <!-- spring redis -->
    <!-- <bean id="jedisConnFactory" 
    	class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory" 
    		p:use-pool="true"/> -->

	<!-- redis template definition -->
	<!-- <bean id="redisTemplate" 
   		class="org.springframework.data.redis.core.RedisTemplate" 
    	p:connection-factory-ref="jedisConnFactory"/> -->
    	
    
    <bean name="startQuertz" lazy-init="false" autowire="no"
        class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="triggers">
            <list>
                <ref bean="clearJobTrigger" />
            </list>
        </property>
        <property name="quartzProperties"> 
			<props>
			<prop key="org.quartz.scheduler.skipUpdateCheck">true</prop> 
			</props>
		</property>
    </bean>
    
    <bean id="clearJobTrigger"
        class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail">
            <ref bean="clearJobDetail" />
        </property>
        <property name="cronExpression">
            <value>0 */5 * * * ?</value>
        </property>
    </bean>
    
    <bean id="clearJobDetail"
        class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <property name="targetObject">
            <ref bean="clearDlItemsLimitJob" />
        </property>
        <property name="targetMethod">
            <value>work</value>
        </property>
    </bean>
    <!-- <bean id="securityCer" class="com.centerm.cpay.tms.security.ServerPriviteKey"
		init-method="initPrivateKey" /> -->
	<bean id="serverPriviteKey" class="com.centerm.cpay.tms.security.ServerPriviteKey"
		init-method="initPrivateKey" />
    <bean id="clearDlItemsLimitJob" class="com.centerm.cpay.tms.job.ClearDlItemsLimitJob" />
    
    
    <!-- <bean id="serviceExporter" class="org.springframework.remoting.rmi.RmiServiceExporter">  
        <property name="serviceName" value="MobileAccountService" />  
        <property name="service" ref="pushService" />  
        <property name="serviceInterface"  
            value="com.centerm.cpay.tms.service.PushService" />  
        <property name="registryPort" value="9527" />  
    </bean>  
  
    <bean id="pushService" class="com.centerm.cpay.tms.service.impl.PushServiceImpl" /> -->
    <!-- http file upload -->
<!--     <import resource="applicationContext-http.xml"/>
 -->    
</beans>