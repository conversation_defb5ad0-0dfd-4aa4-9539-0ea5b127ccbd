package com.centerm.cpay.param.section;

import java.util.HashMap;
import java.util.Map;

import com.centerm.cpay.coms.dao.pojo.TermParamAccessLog;
import com.centerm.cpay.coms.service.TermParamAccessLogService;
import com.centerm.cpay.security.api.SecurityTools;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.utils.JsonUtils;

import net.sf.json.JSONObject;

@Component
@Aspect
public class ResponseSection {

	private static final Logger logger = LoggerFactory.getLogger(ResponseSection.class);

	@Autowired
	private TermParamAccessLogService termParamAccessLogService;

	@AfterReturning(value = "execution(* com.centerm.cpay.param.controller.*.*(..)) ",returning="response")
	public void beforeMethod(JoinPoint point,ResponseMsg response) throws Exception {
		String contentStr = JsonUtils.objectToJson(response);
		JSONObject json = JSONObject.fromObject(contentStr);
		json = json.getJSONObject("header");
		Map<String,String> returnMap = new HashMap<String,String>();
		JsonUtils.jsonToMap(returnMap,json);
		TermParamAccessLog termParamAccessLog = new TermParamAccessLog();
		termParamAccessLog.setRandom(returnMap.get("token"));
		termParamAccessLog.setTermSeq(SecurityTools.getTermIdFromMarkBase64(returnMap.get("devMask")));
		termParamAccessLog.setTimestamp(returnMap.get("timestamp"));
		termParamAccessLog.setReponseLength(contentStr.getBytes("utf8").length);
		termParamAccessLog.setResult(returnMap.get("status"));
		termParamAccessLogService.updateTermParamAccessLog(termParamAccessLog);
	}
}
