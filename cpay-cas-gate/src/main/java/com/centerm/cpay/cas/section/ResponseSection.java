package com.centerm.cpay.cas.section;

import java.util.HashMap;
import java.util.Map;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.centerm.cpay.cas.dao.pojo.CasAccessLog;
import com.centerm.cpay.cas.service.CasAccessLogService;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.utils.JsonUtils;

import net.sf.json.JSONObject;

@Component
@Aspect
public class ResponseSection {
	
	private static final Logger logger = LoggerFactory.getLogger(ResponseSection.class);
	
	@Autowired
	private CasAccessLogService casAccessLogService;
	
	@AfterReturning(value = "execution(* com.centerm.cpay.cas.controller.*.*(..)) ",returning="response")
    public void beforeMethod(JoinPoint point,ResponseMsg response) throws Exception {
		String contentStr = JsonUtils.objectToJson(response);
		JSONObject json = JSONObject.fromObject(contentStr);
		json = json.getJSONObject("header");
		Map<String,String> returnMap = new HashMap<String,String>();
		JsonUtils.jsonToMap(returnMap,json);
		CasAccessLog casAccessLog = new CasAccessLog();
		casAccessLog.setRandom(returnMap.get("random"));
		casAccessLog.setTermSeq(returnMap.get("devMask"));
		casAccessLog.setTimestamp(returnMap.get("timestamp"));
		casAccessLog.setReponseLength(contentStr.getBytes("utf8").length);
		casAccessLog.setResult(returnMap.get("status"));
		casAccessLogService.updateCasAccessLog(casAccessLog);
    }
}
