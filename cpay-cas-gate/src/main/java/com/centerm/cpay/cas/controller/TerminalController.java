package com.centerm.cpay.cas.controller;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.cas.dao.pojo.MerchantInfo;
import com.centerm.cpay.cas.dao.pojo.TerminalManufacturer;
import com.centerm.cpay.cas.request.ActiveBody;
import com.centerm.cpay.cas.request.AutoActivBody;
import com.centerm.cpay.cas.request.group.AGroup;
import com.centerm.cpay.cas.request.group.BGroup;
import com.centerm.cpay.cas.service.MerchantService;
import com.centerm.cpay.cas.service.TerminalManufacturerService;
import com.centerm.cpay.cas.service.TerminalMerchantRelService;
import com.centerm.cpay.cas.util.CasTool;
import com.centerm.cpay.cas.util.ServiceUtil;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.protocol.RequestMsg;
import com.centerm.cpay.common.protocol.RequestMsgParser;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.service.ValidationService;
import com.centerm.cpay.common.utils.CommConstant;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.service.TerminalService;
import com.fasterxml.jackson.databind.JsonNode;

@Controller
@RequestMapping("/cas/terminal")
public class TerminalController {
	private static final Logger logger = LoggerFactory.getLogger(AuthCodeController.class);
	
	@Autowired
	private ValidationService validationService;
	
	@Autowired
	private TerminalMerchantRelService terminalMerchantRelService;
	
	@Autowired
	private TerminalManufacturerService terminalManufacturerService;
	
	@Autowired
	private InstitutionService institutionService;
	@Autowired
	private TerminalService terminalService;
	@Autowired
	private MerchantService merchantService;
	
	@RequestMapping(value = "/active", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMsg active(HttpServletRequest request) throws Exception{
		String functionId = "05";
		logger.debug("【云POS助手】0305-终端激活模块开始运行");
		JsonNode node = (JsonNode)request.getAttribute("jsonNode");
		RequestMsg<ActiveBody> requestMsg = RequestMsgParser.parse(node, ActiveBody.class);
		ActiveBody body = requestMsg.getBody();
		String validaString = validationService.validate(body,AGroup.class);
		if(!CommConstant.VALIDATION_RESLUT_SUCCESS.equals(validaString)){
			logger.debug("【云POS助手】0305-终端激活模块输入报文校验失败："+validaString);
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.fail(ServiceUtil.responseStatus+functionId,validaString,requestMsg));
		}
		logger.debug("【云POS助手】0305-终端激活模块输入报文校验成功");
		ResultMsg result = null;
		try {
			result = terminalMerchantRelService.updateTerminalStatus(requestMsg.getHeader().getDevMask(), body.getActivateCode(), body.getRandomKey());
		} catch (Exception e) {
			result = ResultMsg.fail(e.getMessage());
		}
		logger.debug("【云POS助手】0305-终端激活模块激活结果："+result.getStatus()+"-"+result.getMsg()+result.getData());
		return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));
	}
	
	@RequestMapping(value = "/autoActive", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMsg AutoActive(HttpServletRequest request) throws Exception{
		String functionId = "17";
		logger.debug("【云POS助手】0317-终端自助激活模块开始运行");
		JsonNode node = (JsonNode)request.getAttribute("jsonNode");
		RequestMsg<AutoActivBody> requestMsg = RequestMsgParser.parse(node, AutoActivBody.class);
		AutoActivBody body = requestMsg.getBody();
		String validaString = validationService.validate(body,BGroup.class);
		if(!CommConstant.VALIDATION_RESLUT_SUCCESS.equals(validaString)){
			logger.debug("【云POS助手】0317-终端自助激活模块输入报文校验失败："+validaString);
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.fail(ServiceUtil.responseStatus+functionId,validaString,requestMsg));
		}
		logger.debug("【云POS助手】0317-终端自助激活模块输入报文校验成功");
		TerminalManufacturer terminalFactory = terminalManufacturerService.queryTerminalManufacturerByCode(body.getTermMfrId());
		if(terminalFactory ==  null){
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.fail(ServiceUtil.responseStatus+functionId,"无此终端厂商信息",requestMsg));
		}
		ResultMsg result = null;
		Institution org = institutionService.selectByInsCode(body.getAccessId());
		if(org == null){
			logger.error("【云POS助手】0317-商户注册模块,机构信息不存在");
			result = ResultMsg.fail("机构编号不存在");
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));
		}
		if(CommConstant.TERMINAL_AUTO_ACTIVE != org.getAuto()){
			Terminal terminalInfoWhere = terminalService.queryTerminalInfoByTermSeq(requestMsg.getHeader().getDevMask());
			if(!CtUtils.isEmpty(terminalInfoWhere)){
				
			}else{
				logger.error("【云POS助手】0317-非自助激活机构");
				result = ResultMsg.fail("非自助激活机构");
				return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));
			}
		}
		if(org.getDefaultMerchantId() == null){
			logger.error("【云POS助手】0317-自助激活机构商户ID为空");
			result = ResultMsg.fail("系统错误-自助激活机构商户ID为空");
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));
		}
		MerchantInfo merchantInfo = merchantService.selectByPrimaryKey(org.getDefaultMerchantId());
		if(merchantInfo == null){
			logger.error("【云POS助手】0317-默认机构信息不存在");
			result = ResultMsg.fail("默认机构信息不存在");
			return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));

		}
		Terminal terminalInfo = CasTool.autoActivBodyToTerminalInfo(requestMsg.getHeader(), body);
		terminalInfo.setTermMfrId(terminalFactory.getId());
		terminalInfo.setInsId(org.getId());
		try {
			result = merchantService.createTermMerchantRel(terminalInfo, merchantInfo, body.getRandomKey(),true);
		} catch (Exception e) {
			logger.error("云POS助手】0317-终端激活模块异常",e);
			result = ResultMsg.fail(e.getMessage());
		}
		logger.debug("【云POS助手】0317-终端激活模块激活结果："+result.getStatus()+"-"+result.getMsg()+result.getData());
		return validationService.rsponseMsgSetMacByRandomKey(body.getRandomKey(),ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg));
	}
}
