package com.centerm.cpay.cas.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.centerm.cpay.cas.service.TerminalFaultService;
import com.centerm.cpay.cas.util.ServiceUtil;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.protocol.RequestMsg;
import com.centerm.cpay.common.protocol.RequestMsgParser;
import com.centerm.cpay.common.protocol.ResponseMsg;
import com.centerm.cpay.common.service.ValidationService;
import com.fasterxml.jackson.databind.JsonNode;

@Controller
@RequestMapping("/cas/fault")
public class TerminalFaultController {
private static final Logger logger = LoggerFactory.getLogger(TerminalFaultController.class);
	
	@Autowired
	private ValidationService validationService;
	
	@Autowired
	private TerminalFaultService terminalFaultService;
	
	@RequestMapping(value = "/record", method = { RequestMethod.POST })
	@ResponseBody
	public ResponseMsg recordFault(HttpServletRequest request) throws Exception{
		String functionId = "21";
		logger.debug("【云POS助手】0321-故障申报模块开始运行");
		JsonNode node = (JsonNode)request.getAttribute("jsonNode");
		RequestMsg<Map> requestMsg = RequestMsgParser.parse(node, Map.class);
		Map body = requestMsg.getBody();
//		String validaString = validationService.validate(body,AGroup.class);
//		if(!CommConstant.VALIDATION_RESLUT_SUCCESS.equals(validaString)){
//			return validationService.rsponseMsgSetMacByTmk(ResponseMsg.fail(ServiceUtil.responseStatus+functionId,validaString,requestMsg));
//		}
		ResultMsg result = terminalFaultService.recordTerminalFault(requestMsg.getHeader().getDevMask(),(String)body.get("faultMsg"));
		logger.debug("【云POS助手】0321-故障申报模块，请求结果："+result.getStatus()+"-"+result.getMsg());
		ResponseMsg responseMsg = ResponseMsg.build(ServiceUtil.responseStatus+functionId, result, requestMsg);
		return validationService.rsponseMsgSetMacByTmk(responseMsg);
	}
}
