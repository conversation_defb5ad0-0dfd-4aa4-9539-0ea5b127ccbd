#---------------------ResultMsg---------------------------#

ResultMsg.SUCCESS_CODE = 操作成功
ResultMsg.ERROR_CODE = 操作失败
ResultMsg.FAIL_CODE = 系统异常
#---------------------ApkInfoController---------------------------#

ApkInfoController.pubApk.module = 应用管理
ApkInfoController.pubApk.action = 应用发布
ApkInfoController.pubApk.opDesc = 应用进行发布操作

ApkInfoController.afreshPubApk.module = 应用管理
ApkInfoController.afreshPubApk.action = 应用发布
ApkInfoController.afreshPubApk.opDesc = 应用进行发布操作

ApkInfoController.addApkType.resuletMsg1 = 类型编号或名称已存在。

ApkInfoController.getApkTypeById.resuletMsg1 = 查询成功

ApkInfoController.updateApkType.resuletMsg1 = 查询成功

ApkInfoController.deleteApkTypeById.resuletMsg1 = 改分类正在被使用中，不可删除。
ApkInfoController.deleteApkTypeById.resuletMsg2 = 删除成功

ApkInfoController.deleteApkTypeByIds.resuletMsg1 = 部分分类正在被使用中，删除失败。
ApkInfoController.deleteApkTypeByIds.resuletMsg2 = 删除成功


ApkInfoController.offLineApk.module = 应用管理
ApkInfoController.offLineApk.action = 应用发布
ApkInfoController.offLineApk.opDesc = 应用进行下架操作

#---------------------StoreApkController---------------------------#

StoreApkController.insert.resuletMsg1 = 请上传3-5张图片
StoreApkController.insert.resuletMsg2 = 应用签名失败
StoreApkController.insert.module = 应用管理
StoreApkController.insert.action = 应用上传
StoreApkController.insert.opDesc = 进行应用上传操作
StoreApkController.insert.resuletMsg3 = 应用上传成功

StoreApkController.auditApk.resuletMsg1 = 上传失败，用户未登录
StoreApkController.auditApk.resuletMsg2  = 审核失败
StoreApkController.auditApk.module = 应用管理
StoreApkController.auditApk.action = 应用审核
StoreApkController.auditApk.opDesc = 应用进行审核操作
StoreApkController.auditApk.resuletMsg3 = 审核成功

StoreApkController.changeImg.resuletMsg1 = 未选择图片
StoreApkController.changeImg.resuletMsg2 = 请选择png或jpg格式的图片文件
StoreApkController.changeImg.resuletMsg3 = 图片上传成功
StoreApkController.changeImg.resuletMsg4 = 图片保存失败

StoreApkController.changeStoredImg.resuletMsg1 = 未选择图片
StoreApkController.changeStoredImg.resuletMsg2 = 请选择png或jpg格式的图片文件。
StoreApkController.changeStoredImg.resuletMsg3 = 图片保存失败
StoreApkController.changeStoredImg.resuletMsg4 = 图片上传成功

StoreApkController.uploadImg.resuletMsg1 = 选择的图片文件太大！
StoreApkController.uploadImg.resuletMsg2 = 图片上传成功
StoreApkController.uploadImg.resuletMsg3 = 图片保存失败

StoreApkController.updateApkTempInfo.resuletMsg1 = 修改失败

StoreApkController.uploadIcon.resuletMsg1 = 文件太大
StoreApkController.uploadIcon.resuletMsg2 = 图片的尺寸大于128px × 128px，请重新选择图片！
StoreApkController.uploadIcon.resuletMsg3 = 图标上传成功

StoreApkController.uploadApk.resuletMsg1 = 登录超时或未登录。
StoreApkController.uploadApk.resuletMsg2 = 该文件不是apk格式
StoreApkController.uploadApk.resuletMsg3 = 应用包大小不超过50M
StoreApkController.uploadApk.resuletMsg4 = 上传失败，该应用的当前版本或更高版本已被上传!
StoreApkController.uploadApk.resuletMsg5 = 系统异常：解析应用包失败!
StoreApkController.uploadApk.resuletMsg6 = 上传成功

StoreApkController.deleteApkById.resuletMsg1 = 删除失败
StoreApkController.deleteApkById.resuletMsg2 = 删除成功

StoreApkController.deleteApks.resuletMsg1 = 应用删除成功！

StoreApkController.toAuditApks.resuletMsg1 = 提交审核成功
StoreApkController.toAuditApks.resuletMsg2 = 请选择记录


#------------------------------------ApkInfoServiceImpl--------------------------------#


ApkInfoServiceImpl.addApkType.resuletMsg1 = 各个机构最多只能有10个分类
ApkInfoServiceImpl.addApkType.resuletMsg2 = 分类新增成功



#-----------------------------------ApkTemporaryServiceImpl------------------------------#

ApkTemporaryServiceImpl.updateApkTempInfoById.resuletMsg1 = 修改成功


#-----------------------------------ApkTypeServiceImpl------------------------------#

ApkTypeServiceImpl.queryApkTpesContainAppInfo.fail1 = 查询终端信息异常
ApkTypeServiceImpl.queryApkTpesContainAppInfo.fail2 = 终端所属机构不存在
ApkTypeServiceImpl.queryApkTpesContainAppInfo.fail3 = 查无应用类别信息


#-----------------------------------HotWordsServiceImpl------------------------------#

HotWordsServiceImpl.queryHotWords.fail = 系统暂无热词信息

HotWordsServiceImpl.updateHotWordsDegree.fail1 = 热词新增失败
HotWordsServiceImpl.updateHotWordsDegree.fail2 = 热词更新失败


#-----------------------------------StoreApkServiceImpl------------------------------#

StoreApkServiceImpl.pubApk.resultMsg1 = 发布失败，该应用已被指定机构使用，不得发布给其他机构!
StoreApkServiceImpl.pubApk.resultMsg2 = 发布失败，该应用当前或更高版本已被发布!
StoreApkServiceImpl.pubApk.resultMsg3 = 发布成功

StoreApkServiceImpl.queryStoreApkByAppCode.fail = 该应用已下架

StoreApkServiceImpl.selectPageByApkType.fail1 =查询终端信息异常
StoreApkServiceImpl.selectPageByApkType.fail2 = 终端所属机构不存在
StoreApkServiceImpl.selectPageByApkType.fail3 = 无此类别应用列表

StoreApkServiceImpl.queryListOrderByRecommended.fail1 = 查询终端信息异常
StoreApkServiceImpl.queryListOrderByRecommended.fail2 = 终端所属机构不存在
StoreApkServiceImpl.queryListOrderByRecommended.fail3 = 无此类别应用列表

StoreApkServiceImpl.queryListOrderByDownload.fail1 = 查询终端信息异常
StoreApkServiceImpl.queryListOrderByDownload.fail2 = 终端所属机构不存在
StoreApkServiceImpl.queryListOrderByDownload.fail3 = 无此类别应用列表

StoreApkServiceImpl.queryListByApkIdString.fail = 无此类别应用列表

StoreApkServiceImpl.queryListByKeyWord.fail1 = 查询终端信息异常
StoreApkServiceImpl.queryListByKeyWord.fail2 = 终端所属机构不存在
StoreApkServiceImpl.queryListByKeyWord.fail3 = 无此类别应用列表

StoreApkServiceImpl.updateApkInfo.resultMsg1 = 修改成功。

StoreApkServiceImpl.afreshPubApk.resultMsg1 = 发布失败，该应用已被指定机构使用，不得发布给其他机构!
StoreApkServiceImpl.afreshPubApk.resultMsg2 = 发布失败，该应用的更高版本已被发布!
StoreApkServiceImpl.afreshPubApk.resultMsg3 = 发布成功



TerminalController_contactBinding_msg1=终端管理
TerminalController_contactBinding_msg2=终端解绑
TerminalController_contactBinding_msg3=终端进行解绑操作
TerminalController_contactBinding_msg4=云POS管理平台通知：您终端为
TerminalController_contactBinding_msg5=已经解绑成功，请重启后重新激活

TerminalController_uploadFile_msg1=第
TerminalController_uploadFile_msg2=行:终端序列号为
TerminalController_uploadFile_msg3=手机号码不正确
TerminalController_uploadFile_msg4=的终端已经存在
TerminalController_uploadFile_msg5=的数据不正确
TerminalController_uploadFile_msg6=成功导入
TerminalController_uploadFile_msg7=条记录

TerminalServiceImpl_reContactBinding_msg1=当前终端所属机构未设置为自动激活,请手动激活
TerminalServiceImpl_reContactBinding_msg2=当前终端所属机构未设置默认的商户信息,无法重绑
TerminalServiceImpl_reContactBinding_msg3=当前终端不存在

TerminalTypeServiceImpl_insert_msg1=型号代码已存在
TerminalTypeServiceImpl_insert_msg2=型号名称已存在

TerminalTypeServiceImpl_updateByPrimaryKey_msg1=型号代码已存在
TerminalTypeServiceImpl_updateByPrimaryKey_msg2=型号名称已存在

TerminalGroupServiceImpl_insert_msg1=组别名称已存在
TerminalGroupServiceImpl_updateByPrimaryKey_msg1=组别名称已存在

FactoryServiceImpl_deleteById_msg1=当前厂商已经有终端型号挂靠，不得删除

FactoryServiceImpl_insertFactory_msg1=厂商代码已存在
FactoryServiceImpl_insertFactory_msg2=厂商名称已存在

FactoryServiceImpl_updateFactory_msg1=厂商代码已存在
FactoryServiceImpl_updateFactory_msg2=厂商名称已存在

TerminalParamServiceImpl_insert_msg1=当前这套参数已存在
TerminalParamServiceImpl_updateByPrimaryKey_msg1=当前这套参数已存在
#广告管理
notFindTerminal=无此终端信息
notFindInstitution=无此机构信息
notFindAD=无符合条件的广告信息


UserController_changePwd_msg1=密码修改失败,输入的原密码错误。
UserController_changePwd_msg2=密码修改成功。
UserController_updateUserInfo_msg1=修改失败，该手机号码已被注册使用。
UserController_updateUserInfo_msg2=修改失败，未发送手机验证码。
UserController_updateUserInfo_msg3=修改失败，未输入手机验证码。
UserController_updateUserInfo_msg4=修改失败，短信验证码错误。
UserController_updateUserInfo_msg5=修改失败，该邮箱已被注册使用。
UserController_updateUserInfo_msg6=修改失败，未发送邮箱验证码。
UserController_updateUserInfo_msg7=修改失败，未输入邮箱验证码。
UserController_updateUserInfo_msg8=修改失败，邮箱验证码错误。
UserController_updateUserInfo_msg9=个人信息修改成功。
UserController_sendCode_msg1=验证码发送失败，手机号为空。
UserController_sendCode_msg2=该手机号码已被注册使用。
UserController_sendCode_msg3=您的验证码为:
UserController_sendCode_msg4=验证码发送成功
UserController_sendEmail_msg1=邮箱不能为空。
UserController_sendEmail_msg2=该邮箱已被注册使用。
UserController_sendEmail_msg3=您本次修改邮箱操作的验证码为：
UserController_sendEmail_msg4=，请及时处理！
UserController_sendEmail_msg5=升腾资讯邮箱验证码
UserController_sendEmail_msg6=异常：
UserController_sendEmail_msg7=邮箱验证码发送失败
UserController_sendEmail_msg8=邮箱验证码发送成功

