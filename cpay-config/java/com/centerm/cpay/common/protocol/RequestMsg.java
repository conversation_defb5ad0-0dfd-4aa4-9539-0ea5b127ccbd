package com.centerm.cpay.common.protocol;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.centerm.cpay.common.utils.CommConstant;

/**
 * 客户端请求消息封装类
 * <p>Title: RequestMsg</p>
 * <p>Description: </p>
 * <p>Company: www.centerm.com</p> 
 * <AUTHOR> 赖煌生
 * @date	2016年5月23日 上午10:30:25 
 * @version 1.0
 */
public class RequestMsg<T> {
	
	private RequestHeader header;
	private T body;
	private String MAC;
	@NotNull(message=CommConstant.MESSAGE_REQUEST_MAC)
	public String getMAC() {
		return MAC;
	}
	public void setMAC(String mAC) {
		MAC = mAC;
	}
	@NotNull(message=CommConstant.MESSAGE_REQUEST_HEADER)
	@Valid
	public RequestHeader getHeader() {
		return header;
	}
	public void setHeader(RequestHeader header) {
		this.header = header;
	}
	public T getBody() {
		return body;
	}
	public void setBody(T body) {
		this.body = body;
	}
	@Override
	public String toString() {
		return "RequestMsg [header=" + header + ", body=" + body + ", MAC=" + MAC + "]";
	}
	
}
