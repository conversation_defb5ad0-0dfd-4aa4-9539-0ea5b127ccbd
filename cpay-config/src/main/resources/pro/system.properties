########################## linux环境配置 ###################
#fastDFS开关配置
isFastDFS=false
#文件下载（显示）地址,  isFastDFS为false时：http://172.30.20.100:6922/
#downloadUrl=http://172.30.20.100:58888/
downloadUrl=https://47.88.214.136/files/
#百度地图Api地址   外网114：http://api.map.baidu.com/geocoder/v2/ 
baiduApiUrl=http://api.map.baidu.com/geocoder/v2/ 
#文件保存头路径
headerpath=/data/CPAY2.0/korea/files/
#文件上传路径（日志上送）
uploadloadpath=upload/
#软件管理文件保存路径头
appsystempath=appsystem/
#广告保存路径
adpath=ad/

keypath=keys/

#launcher保存路径
launcherpath=launcher/
#appstore保存路径
appstorepath=appstore/
#服务器秘钥路径
PKey.path=keys/cpay.p12
#秘钥密码
PKey.password=hf445566

#appt保存路径
apptpath=/data/CPAY2.0/aapt/aapt
#appt保存的临时路径
apptTempFilePath = /work/Catalina/temp


########################## 业务代码配置  ####################
#初始化线程池数量
thread.pool.size=50
#终端移机最大距离(单位/米)
task.max.distance=10000
#终端离线最大时长(单位/分钟)
task.max.minute=10
#终端开机启动配置xml文件命名
boot.conf.xml.name=_auto_app
#新增用户默认密码
user.default.password=e99a18c428cb38d5f260853678922e03
#默认有效期月数
warranty.month=3
#ams热词搜索功能
ams.hotwords.search=false

##########################     短信    ######################
#短信网关账号
sms.account=fj_stzx
#短信网关密码
sms.password=Centerm123!
#短信网关公司ID
company.code=235386
#短信验证码过期时间 单位:分钟
authcode.expired=5
#同一类型短信重发次数
send.max.time=5

##########################    tms_gate连接tms配置  ######################
tms.server.ip=127.0.0.1
tms.server.port=6990
tms.connect.timeout=60
job.task.length = 500

server.privatekeys.path=keys/server_priv.pem
file.upload.temp = /tmp/prms
file.setting.header= settings.xml
dlresetnum=3

##########################  Cloudflare R2存储数据  ######################
r2.url = https://131eb954f37dbeb24f6ad55be71e0af5.r2.cloudflarestorage.com
r2.download.url = https://pub-6ead58f7861245739eb8e439593ee761.r2.dev
r2.access.key = 31e5d61cd9d0b29c16b867606cd65f22
r2.secret.key = 859c76ee15f7c52443cc421797e7ac469f20d22ac5e865af656776b4250b24f9
r2.bucket = cpay-singapore