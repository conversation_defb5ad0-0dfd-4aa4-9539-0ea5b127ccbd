package com.centerm.cpay.sms.dao.mapper;

import com.centerm.cpay.sms.dao.pojo.SmsHistory;
import com.centerm.cpay.sms.dao.pojo.SmsHistoryExample;
import java.util.List;

public interface SmsHistoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SmsHistory record);

    int insertSelective(SmsHistory record);

    List<SmsHistory> selectByExample(SmsHistoryExample example);

    SmsHistory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SmsHistory record);

    int updateByPrimaryKey(SmsHistory record);
}