<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.DriverJobMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="job_name" property="jobName" jdbcType="VARCHAR" />
    <result column="manufacturer_id" property="manufacturerId" jdbcType="INTEGER" />
    <result column="release_time" property="releaseTime" jdbcType="VARCHAR" />
    <result column="valid_date" property="validDate" jdbcType="VARCHAR" />
    <result column="release_status" property="releaseStatus" jdbcType="CHAR" />
    <result column="release_type" property="releaseType" jdbcType="VARCHAR" />
    <result column="release_ins" property="releaseIns" jdbcType="INTEGER" />
    <result column="record_create_time" property="recordCreateTime" jdbcType="VARCHAR" />
    <result column="record_update_time" property="recordUpdateTime" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="group_id" property="groupId" jdbcType="INTEGER" />
    <result column="is_group_update" property="isGroupUpdate" jdbcType="CHAR" />
    <result column="group_ids" property="groupIds" jdbcType="VARCHAR" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="collection" property="collection" jdbcType="LONGVARCHAR" />
    <result column="isShow_notice" property="isShowNotice" jdbcType="CHAR" />
    <result column="number" property="number" jdbcType="INTEGER" />
    <result column="success" property="success" jdbcType="INTEGER" />
    <result column="failure_down" property="failureDown" jdbcType="INTEGER" />
    <result column="failure_update" property="failureUpdate" jdbcType="INTEGER" />
    <result column="success_down" property="successDown" jdbcType="INTEGER" />
    <result column="wait_send" property="waitSend" jdbcType="INTEGER" />
      <result column="start_time" property="startTime" jdbcType="VARCHAR" />
      <result column="end_time" property="endTime" jdbcType="VARCHAR" />
      <result column="term_type_arr" property="termTypeArr" jdbcType="VARCHAR" />
    <result column="activate_type" property="activateType" jdbcType="CHAR" />
    <result column="activate_types" property="activateTypes" jdbcType="VARCHAR" />
    <result column="dcc_sup_flag" property="dccSupFlag" jdbcType="CHAR" />
    <result column="cup_conn_mode" property="cupConnMode" jdbcType="CHAR" />
    <result column="buss_type" property="bussType" jdbcType="VARCHAR" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
    <result column="valid_date_show" property="validDateShow" jdbcType="CHAR" />
  </resultMap>
    <resultMap id="dlItemsLimit" type="com.centerm.cpay.coms.dao.pojo.DlItemsLimit" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
        <result column="dl_res_num" property="dlResNum" jdbcType="INTEGER" />
        <result column="dl_begin_date" property="dlBeginDate" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="TerminalResultMap" type="com.centerm.cpay.coms.dao.pojo.Terminal" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="term_mfr_id" property="termMfrId" jdbcType="INTEGER" />
    <result column="term_type_code" property="termTypeCode" jdbcType="VARCHAR" />
    <result column="link_man" property="linkMan" jdbcType="VARCHAR" />
    <result column="link_phone" property="linkPhone" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="VARCHAR" />
    <result column="imei" property="imei" jdbcType="CHAR" />
    <result column="net_mark" property="netMark" jdbcType="CHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="county" property="county" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="applyer_name" property="applyerName" jdbcType="VARCHAR" />
    <result column="applyer_phone" property="applyerPhone" jdbcType="VARCHAR" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
    <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
    <result column="factory_name" property="termMfrName" jdbcType="VARCHAR" />
    <result column="term_type_name" property="termTypeName" jdbcType="VARCHAR" />
   	<result column="status_detail" property="statusDetail" jdbcType="VARCHAR" />
   	<result column="term_group_id" property="terminalGroupId" jdbcType="VARCHAR" />
   	<result column="group_name" property="groupName" jdbcType="VARCHAR" />
   	<result column="type" property="industryType" jdbcType="VARCHAR" />
  </resultMap>
    <resultMap id="AppVersionResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="factory_id" property="factoryId" jdbcType="INTEGER" />
    <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="md5" property="md5" jdbcType="VARCHAR" />
    <result column="app_path" property="appPath" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="CHAR" />
    <result column="app_id" property="appId" jdbcType="INTEGER" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    id, job_name, app_code, manufacturer_id, terminal_type_id, app_version, release_time,
    valid_date, release_status, release_type, release_ins, record_create_time, record_update_time, 
    user_id, ins_id,collection,isShow_notice,term_type_arr,activate_type,activate_types,
    dcc_sup_flag,cup_conn_mode,buss_type,valid_date_show
  </sql>

    <select id="selectForIndex" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        SELECT
        a.id, job_name,manufacturer_id, release_time,
        valid_date, release_status, release_type, release_ins, record_create_time, record_update_time,
        user_id,collection,isShow_notice,term_type_arr,ACTIVATE_TYPE,GROUP_IDS,IS_GROUP_UPDATE,valid_date_show,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id ) AS number,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('4','5')) AS success,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('6','7','8')) AS failure_update,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('1','2','3')) AS success_down,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '9') AS pause,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '0') AS wait_send
        FROM
        coms_download_job a
        where 1=1

        <if test="releaseIns != null and releaseIns != 0" >
            AND (a.release_ins IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 ) OR a.INS_ID IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 )
            )
        </if>
        order by a.record_create_time desc
        limit 0,10
    </select>
    <select id="selectJobList" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        SELECT m.* from (select t.*,
        CASE
        when t.release_status_tmp != 5 then t.release_status_tmp
        WHEN t.number = (t.success+t.failure_update+t.pause) THEN 7
        WHEN  <![CDATA[ t.VALID_DATE <= CURRENT_TIMESTAMP ]]> THEN 7
        ELSE t.release_status_tmp END AS release_status from
        (SELECT
        a.id, job_name,manufacturer_id, release_time,
        valid_date,ins.NAME as ins_name,release_status AS release_status_tmp, release_type, release_ins, record_create_time, record_update_time,
        user_id,isShow_notice,term_type_arr,ACTIVATE_TYPE,GROUP_IDS,IS_GROUP_UPDATE,valid_date_show,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id ) AS number,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('4','5')) AS success,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('6','7','8')) AS failure_update,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('1','2','3')) AS success_down,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '9') AS pause,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '0') AS wait_send
        FROM
        coms_download_job a
        left join CPAY_INSTITUTION ins
        on ins.ID = a.RELEASE_INS
        where 1=1
        <if test="jobName != null">
            and a.job_name like '%${jobName}%'
        </if>

        <if test="releaseIns != null and releaseIns != 0">
            AND (a.release_ins IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 ) OR a.INS_ID IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 )
            )
        </if>

        <choose>
            <when test="timeType == 1">
                and TO_DAYS(a.record_create_time) = TO_DAYS(now())
            </when>
            <when test="timeType == 7">
                AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
            </when>
            <when test="timeType == 30">
                AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
            </when>
        </choose>
        ) t) m where 1=1
        <if test="releaseStatus != null and releaseStatus != ''">
            and m.release_status = #{releaseStatus,jdbcType=CHAR}
        </if>
        order by m.record_create_time desc
    </select>
    <select id="judgeJobUnFinish" resultType="java.lang.Integer" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        select count(*) from (select t.*,
        CASE
        when t.release_status_tmp != 5 then t.release_status_tmp
        WHEN t.number = (t.success+t.failure_update+t.pause) THEN 7
        WHEN  <![CDATA[ t.VALID_DATE <= CURRENT_TIMESTAMP ]]> THEN 7
        ELSE t.release_status_tmp END AS release_status from
        (SELECT
        a.id, job_name,manufacturer_id, release_time,
        valid_date,ins.NAME as ins_name,release_status AS release_status_tmp, release_type, release_ins, record_create_time, record_update_time,
        user_id,isShow_notice,term_type_arr,ACTIVATE_TYPE,GROUP_IDS,IS_GROUP_UPDATE,valid_date_show,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id ) AS number,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('4','5')) AS success,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('6','7','8')) AS failure_update,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag in ('1','2','3')) AS success_down,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '9') AS pause,
        (SELECT count(b.id) FROM COMS_DOWNLOAD_TASK b WHERE b.JOB_ID = a.id and b.dl_flag = '0') AS wait_send
        FROM
        coms_download_job a
        left join CPAY_INSTITUTION ins
        on ins.ID = a.RELEASE_INS
        where 1=1
        and FIND_IN_SET(a.id,#{collection})>0
        <choose>
            <when test="timeType == 1">
                and TO_DAYS(a.record_create_time) = TO_DAYS(now())
            </when>
            <when test="timeType == 7">
                AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
            </when>
            <when test="timeType == 30">
                AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
            </when>
        </choose>
        ) t where 1=1

        order by t.record_create_time desc) r where r.release_status != '7'
    </select>
    <select id="selectForNewTerm" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        select * from
        (SELECT
        id, job_name,manufacturer_id, release_time,RECORD_UPDATE_TIME,
        valid_date,
        CASE
        WHEN  <![CDATA[ a.VALID_DATE <= CURRENT_TIMESTAMP ]]> THEN 7
        ELSE release_status END AS release_status, release_type,INS_ID, release_ins,
        user_id,isShow_notice,term_type_arr,GROUP_IDS,IS_GROUP_UPDATE,ACTIVATE_TYPES,valid_date_show
        FROM
        coms_download_job a
        where RELEASE_TYPE = '1'
        and ACTIVATE_TYPE = '1'
        <if test="releaseIns != null and releaseIns != 0">
            AND (a.release_ins IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 ) OR a.INS_ID IN (SELECT s1.id FROM cpay_institution s1 WHERE
            locate(
            (
            SELECT
            s2.detail
            FROM
            cpay_institution s2
            WHERE
            s2.id = #{releaseIns}
            ),
            s1.detail
            ) > 0 )
            )
        </if>
        <if test="releaseStatus != null and releaseStatus != ''">
            and release_status=#{releaseStatus,jdbcType=CHAR}
        </if>
        <if test="jobName != null">
            and a.job_name like '%${jobName}%'
        </if>
        ) b where release_status in ('2','5')
    </select>
    <select id="updateReleaseStatus" parameterType="java.util.Map" >
      update coms_download_job set release_status = #{releaseStatus,jdbcType=VARCHAR}
      where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    	SELECT
    	a.id, job_name,manufacturer_id, release_time,
    	valid_date, release_status, release_type, release_ins, record_create_time, record_update_time, 
    	user_id,collection,isShow_notice,term_type_arr,ACTIVATE_TYPE,GROUP_IDS,IS_GROUP_UPDATE,
        dcc_sup_flag,cup_conn_mode,buss_type,valid_date_show,
		e.*
        FROM
		coms_download_job a
        LEFT JOIN
        (SELECT c.job_id,
        COUNT(c.DL_FLAG) as number,
        sum(case when c.DL_FLAG='4' OR c.DL_FLAG='5' then 1 else 0 end) as success,
        sum(case when c.DL_FLAG='6' or c.DL_FLAG='7' or c.DL_FLAG='8' then 1 else 0 end) as failure_update,
        sum(case when c.DL_FLAG='1' OR c.DL_FLAG='2' OR c.DL_FLAG='3' then 1 else 0 end) as success_down,
        sum(case when c.DL_FLAG='0' then 1 else 0 end) as wait_send FROM COMS_DOWNLOAD_TASK c GROUP BY job_id) e
        ON e.job_id = a.id
		where 1=1
		<if test="jobName != null" >
        	and a.job_name like  '%${jobName}%'
      	</if>

      	<if test="releaseStatus != null and releaseStatus != ''" >
        	and a.release_status=#{releaseStatus,jdbcType=CHAR}
      	</if>
      	<if test="releaseIns != null and releaseIns != 0" >
        	AND a.release_ins IN (
		SELECT
			s1.id
		FROM
			cpay_institution s1
		WHERE
			locate(
				(
					SELECT
						s2.detail
					FROM
						cpay_institution s2
					WHERE
						s2.id = #{releaseIns}
				),
				s1.detail
			) > 0
		)
     	</if>
     	<choose>
     		<when test="timeType == 1">
     			and TO_DAYS(a.record_create_time) = TO_DAYS(now())
     		</when>
     		<when test="timeType == 7">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
     		</when>
     		<when test="timeType == 30">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
     		</when>
     	</choose>
      	order by a.record_create_time desc
  </select>
    <select id="selectJobListReport" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        SELECT
        a.id, job_name, manufacturer_id, release_time,
        valid_date, release_status, release_type, release_ins, record_create_time, record_update_time,
        user_id,collection,isShow_notice,valid_date_show, ci.name AS insName,
        e.*
        FROM
        coms_download_job a
        LEFT JOIN cpay_institution ci ON a.release_ins = ci.id
        LEFT JOIN
        (SELECT c.job_id,
        COUNT(c.DL_FLAG) as number,
        sum(case when c.DL_FLAG='4' OR c.DL_FLAG='5' then 1 else 0 end) as success,
        sum(case when c.DL_FLAG='6' then 1 else 0 end) as failure_down,
        sum(case when c.DL_FLAG='7' OR c.DL_FLAG='8' then 1 else 0 end) as failure_update,
        sum(case when c.DL_FLAG='3' then 1 else 0 end) as success_down,
        sum(case when c.DL_FLAG='0' then 1 else 0 end) as wait_send FROM COMS_DOWNLOAD_TASK c 
        
        LEFT JOIN COMS_TERMINAL_INFO k ON c.TERM_SEQ = k.TERM_SEQ where 1=1
        <if test="insId != null and insId != 0">
			and k.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
        GROUP BY job_id ) e
        ON e.job_id = a.id
        where 1=1 and e.number>0
        <if test="id == 1">
            AND a.release_ins IN (
			SELECT
				s1.id
			FROM
				cpay_institution s1
			WHERE
				locate((SELECT s2.detail FROM cpay_institution s2 WHERE s2.id = #{insId,jdbcType=INTEGER} ), s1.detail ) > 0
				or
				locate(s1.detail,(SELECT s2.detail FROM cpay_institution s2 WHERE s2.id = #{insId,jdbcType=INTEGER} ) ) > 0
			)   
        </if>
        <if test="id == 0">
            and a.release_ins = #{insId,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            and  <![CDATA[ date_format(a.record_create_time,'%Y%m' )>= #{startTime,jdbcType=VARCHAR}]]>
        </if>
        <if test="endTime != null">
            and  <![CDATA[ date_format(a.record_create_time,'%Y%m') <= #{endTime,jdbcType=VARCHAR}]]>
        </if>
        order by a.record_create_time desc
    </select>
  
  <select id="selectBySwichCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    	SELECT
		a.*, b.NAME AS app_name
		FROM
		coms_download_job a
		LEFT JOIN coms_app_inf b ON a.app_code = b. CODE
		where a.job_Type = 'a' or a.job_Type = 'b' 
		<if test="jobName != null" >
        	and a.job_name like '%${jobName}%'
      	</if>
      	<if test="appName != null" >
        	and b.name like  '%${appName}%'
      	</if>
      	<if test="releaseStatus != null and releaseStatus != ''" >
        	and a.release_status=#{releaseStatus,jdbcType=CHAR}
      	</if>
      	<if test="appVersion != null" >
        	and a.app_version like concat '%${appVersion}%'
      	</if>
      	<if test="releaseIns != null and releaseIns != 0" >
        	and a.release_ins in(SELECT s1.id from cpay_institution s1 where 
        	locate((SELECT s2.detail from cpay_institution s2 where s2.id=${releaseIns}),s1.detail)>0)
     	</if>
      	order by a.record_create_time desc
  </select>
 
  <select id="selectByConditionForLauncher" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    	SELECT
    	a.id, job_name, app_code, manufacturer_id, terminal_type_id, app_version, release_time, 
    	valid_date, release_status, release_type, release_ins, record_create_time, record_update_time, 
    	user_id, a.ins_id,collection,isShow_notice, b. NAME AS app_name,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id) as number,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id and dl_flag = 4) as success
		FROM
		coms_download_job a 
		LEFT JOIN coms_launcher_info b ON a.app_code = b.launcher_code
		where 1=1 and a.job_type='2'
		<if test="jobName != null" >
        	and a.job_name like  '%${jobName}%'
      	</if>
      	<if test="appName != null" >
        	and b.name like  '%${appName}%'
      	</if>
      	<if test="releaseStatus != null and releaseStatus !=''" >
        	and a.release_status=#{releaseStatus,jdbcType=CHAR}
      	</if>
      	<if test="appVersion != null" >
        	and a.app_version like '%${appVersion}%'
      	</if>
      	<if test="releaseIns != null and releaseIns != 0" >
        	and a.release_ins in(SELECT s1.id from cpay_institution s1 where   
        	locate((SELECT s2.detail from cpay_institution s2 where s2.id=${releaseIns}),s1.detail )>0)
     	</if>
     	<choose>
     		<when test="timeType == 1">
     			and TO_DAYS(a.record_create_time) = TO_DAYS(now())
     		</when>
     		<when test="timeType == 7">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
     		</when>
     		<when test="timeType == 30">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
     		</when>
     	</choose>
      	order by a.record_create_time desc
  </select>
  
  
  <select id="selectByConditionForApkPush" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    	SELECT
		a.*,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id) as number,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id and dl_flag = 4) as success
		FROM
		coms_download_job a
		where 1=1 and a.job_type='0'
		<if test="jobName != null" >
        	and a.job_name like  '%${jobName}%'
      	</if>
      	<if test="appName != null" >
        	and a.app_name like  '%${appName}%'
      	</if>
      	<if test="releaseStatus != null and releaseStatus !=''" >
        	and a.release_status=#{releaseStatus,jdbcType=CHAR}
      	</if>
      	<if test="appVersion != null" >
        	and a.app_version like '%${appVersion}%'
      	</if>
      	<if test="releaseIns != null and releaseIns != 0" >
        	and a.release_ins in(SELECT s1.id from cpay_institution s1 where  
        	locate((SELECT s2.detail from cpay_institution s2 where s2.id=${releaseIns}),s1.detail)>0)
     	</if>
     	<choose>
     		<when test="timeType == 1">
     			and TO_DAYS(a.record_create_time) = TO_DAYS(now())
     		</when>
     		<when test="timeType == 7">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
     		</when>
     		<when test="timeType == 30">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
     		</when>
     	</choose>
      	order by a.record_create_time desc
  </select>
  <select id="selectByConditionForApkPushDel" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    	SELECT
		a.*,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id) as number,(SELECT COUNT(*) FROM coms_download_task c where a.id = c.job_id and dl_flag = 5) as success
		FROM
		coms_download_job a
		where 1=1 and a.job_type='4'
		<if test="jobName != null" >
        	and a.job_name like '%${jobName}%'
      	</if>
      	<if test="appName != null" >
        	and a.app_name like  '%${appName}%'
      	</if>
      	<if test="releaseStatus != null and releaseStatus !=''" >
        	and a.release_status=#{releaseStatus,jdbcType=CHAR}
      	</if>
      	<if test="appVersion != null" >
        	and a.app_version like concat '%${appVersion}%'
      	</if>
      	<if test="releaseIns != null and releaseIns != 0" >
        	and a.release_ins in(SELECT s1.id from cpay_institution s1 where   
        	locate((SELECT s2.detail from cpay_institution s2 where s2.id=${releaseIns}), s1.detail)>0)
     	</if>
     	<choose>
     		<when test="timeType == 1">
     			and TO_DAYS(a.record_create_time) = TO_DAYS(now())
     		</when>
     		<when test="timeType == 7">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>7
     		</when>
     		<when test="timeType == 30">
     			AND TO_DAYS(now())-TO_DAYS(a.record_create_time) <![CDATA[ <= ]]>30
     		</when>
     	</choose>
      	order by a.record_create_time desc
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select a.*,c.name as group_name,b.name as ins_name
    from coms_download_job a left join cpay_institution b on a.ins_id = b.id
    left join coms_terminal_group c on a.group_id = c.id
    where a.id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_download_job
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="delayByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
        update COMS_DOWNLOAD_JOB
        set VALID_DATE = #{validDate,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" useGeneratedKeys="true" keyProperty="id">
    insert into COMS_DOWNLOAD_JOB ( JOB_NAME, MANUFACTURER_ID,
      RELEASE_TIME, VALID_DATE, RELEASE_STATUS,
      RELEASE_TYPE, RELEASE_INS, GROUP_ID,
      RECORD_CREATE_TIME, RECORD_UPDATE_TIME, USER_ID,
      INS_ID, ISSHOW_NOTICE, COLLECTION,TERM_TYPE_ARR,ACTIVATE_TYPES,ACTIVATE_TYPE,valid_date_show,
      GROUP_IDS,IS_GROUP_UPDATE,cup_conn_mode,buss_type
      <if test="dccSupFlag != null and dccSupFlag != ''">
          ,dcc_sup_flag
      </if>
      )
    values ( #{jobName,jdbcType=VARCHAR}, #{manufacturerId,jdbcType=INTEGER},
      #{releaseTime,jdbcType=VARCHAR}, #{validDate,jdbcType=VARCHAR}, #{releaseStatus,jdbcType=CHAR},
      #{releaseType,jdbcType=VARCHAR}, #{releaseIns,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER},
      #{recordCreateTime,jdbcType=VARCHAR}, #{recordUpdateTime,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER},
      #{insId,jdbcType=INTEGER}, #{isShowNotice,jdbcType=CHAR}, #{collection,jdbcType=CLOB},#{termTypeArr,jdbcType=CHAR},
      #{activateTypes,jdbcType=VARCHAR},#{activateType,jdbcType=CHAR},#{validDateShow,jdbcType=CHAR},
      #{groupIds,jdbcType=VARCHAR},#{isGroupUpdate,jdbcType=CHAR},
      #{cupConnMode,jdbcType=CHAR},#{bussType,jdbcType=VARCHAR}
        <if test="dccSupFlag != null and dccSupFlag != ''">
            ,#{dccSupFlag,jdbcType=CHAR}
        </if>
      )
  </insert>
    <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" useGeneratedKeys="true" keyProperty="id">
        insert into COMS_DOWNLOAD_JOB
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="jobName != null" >
                JOB_NAME,
            </if>
            <if test="manufacturerId != null" >
                MANUFACTURER_ID,
            </if>
            <if test="releaseTime != null" >
                RELEASE_TIME,
            </if>
            <if test="validDate != null" >
                VALID_DATE,
            </if>
            <if test="releaseStatus != null" >
                RELEASE_STATUS,
            </if>
            <if test="releaseType != null" >
                RELEASE_TYPE,
            </if>
            <if test="releaseIns != null" >
                RELEASE_INS,
            </if>
            <if test="groupId != null" >
                GROUP_ID,
            </if>
            <if test="recordCreateTime != null" >
                RECORD_CREATE_TIME,
            </if>
            <if test="recordUpdateTime != null" >
                RECORD_UPDATE_TIME,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="insId != null" >
                INS_ID,
            </if>
            <if test="isShowNotice != null" >
                ISSHOW_NOTICE,
            </if>
            <if test="collection != null" >
                COLLECTION,
            </if>
            <if test="termTypeArr != null" >
                term_type_arr,
            </if>
            <if test="activateTypes != null" >
                ACTIVATE_TYPES,
            </if>
            <if test="groupIds != null" >
                GROUP_IDS,
            </if>
            <if test="isGroupUpdate != null" >
                IS_GROUP_UPDATE,
            </if>
            <if test="dccSupFlag != null " >
                dcc_sup_flag,
            </if>
            <if test="cupConnMode != null " >
                cup_conn_mode,
            </if>
            <if test="bussType != null " >
                buss_type,
            </if>
            <if test="activateType != null and activateType != ''" >
                activate_type,
            </if>
            <if test="validDateShow != null and validDateShow != ''" >
                valid_date_show
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="jobName != null" >
                #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="manufacturerId != null" >
                #{manufacturerId,jdbcType=INTEGER},
            </if>
            <if test="releaseTime != null" >
                #{releaseTime,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null" >
                #{validDate,jdbcType=VARCHAR},
            </if>
            <if test="releaseStatus != null" >
                #{releaseStatus,jdbcType=CHAR},
            </if>
            <if test="releaseType != null" >
                #{releaseType,jdbcType=VARCHAR},
            </if>
            <if test="releaseIns != null" >
                #{releaseIns,jdbcType=INTEGER},
            </if>
            <if test="groupId != null" >
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="recordCreateTime != null" >
                #{recordCreateTime,jdbcType=VARCHAR},
            </if>
            <if test="recordUpdateTime != null" >
                #{recordUpdateTime,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="insId != null" >
                #{insId,jdbcType=INTEGER},
            </if>
            <if test="isShowNotice != null" >
                #{isShowNotice,jdbcType=CHAR},
            </if>
            <if test="collection != null" >
                #{collection,jdbcType=CLOB},
            </if>
            <if test="termTypeArr != null" >
                #{termTypeArr,jdbcType=VARCHAR},
            </if>
            <if test="activateTypes != null" >
                #{activateTypes,jdbcType=VARCHAR},
            </if>
            <if test="groupIds != null" >
                #{groupIds,jdbcType=VARCHAR},
            </if>
            <if test="isGroupUpdate != null" >
                #{isGroupUpdate,jdbcType=CHAR},
            </if>
            <if test="dccSupFlag != null" >
                #{dccSupFlag,jdbcType=CHAR},
            </if>
            <if test="cupConnMode != null" >
                #{cupConnMode,jdbcType=CHAR},
            </if>
            <if test="bussType != null" >
                #{bussType,jdbcType=VARCHAR},
            </if>
            <if test="activateType != null and activateType != ''" >
                #{activateType,jdbcType=CHAR},
            </if>
            <if test="validDateShow != null and validDateShow != ''" >
                #{validDateShow,jdbcType=CHAR}
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
        update COMS_DOWNLOAD_JOB
        <set >
            <if test="jobName != null" >
                JOB_NAME = #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="manufacturerId != null" >
                MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER},
            </if>
            <if test="releaseTime != null" >
                RELEASE_TIME = #{releaseTime,jdbcType=VARCHAR},
            </if>
            <if test="validDate != null" >
                VALID_DATE = #{validDate,jdbcType=VARCHAR},
            </if>
            <if test="releaseStatus != null" >
                RELEASE_STATUS = #{releaseStatus,jdbcType=CHAR},
            </if>
            <if test="releaseType != null" >
                RELEASE_TYPE = #{releaseType,jdbcType=VARCHAR},
            </if>
            <if test="releaseIns != null" >
                RELEASE_INS = #{releaseIns,jdbcType=INTEGER},
            </if>
            <if test="groupId != null" >
                GROUP_ID = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="recordCreateTime != null" >
                RECORD_CREATE_TIME = #{recordCreateTime,jdbcType=VARCHAR},
            </if>
            <if test="recordUpdateTime != null" >
                RECORD_UPDATE_TIME = #{recordUpdateTime,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=INTEGER},
            </if>
            <if test="insId != null" >
                INS_ID = #{insId,jdbcType=INTEGER},
            </if>
            <if test="isShowNotice != null" >
                ISSHOW_NOTICE = #{isShowNotice,jdbcType=CHAR},
            </if>
            <if test="collection != null" >
                COLLECTION = #{collection,jdbcType=CLOB},
            </if>
            <if test="termTypeArr != null" >
                TERM_TYPE_ARR = #{termTypeArr,jdbcType=VARCHAR},
            </if>
            <if test="groupIds != null" >
                GROUP_IDS = #{groupIds,jdbcType=VARCHAR},
            </if>
            <if test="isGroupUpdate != null" >
                IS_GROUP_UPDATE = #{isGroupUpdate,jdbcType=CHAR},
            </if>
            <if test="activateTypes != null" >
                ACTIVATE_TYPES = #{activateTypes,jdbcType=VARCHAR},
            </if>
            <if test="activateType != null" >
                activate_type = #{activateType,jdbcType=CHAR},
            </if>
            <if test="validDateShow != null" >
                valid_date_show = #{validDateShow,jdbcType=CHAR},
            </if>
            <if test="cupConnMode != null " >
                cup_conn_mode = #{cupConnMode,jdbcType=CHAR},
            </if>
            <if test="bussType != null" >
                buss_type = #{bussType,jdbcType=VARCHAR}
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" >
    update COMS_DOWNLOAD_JOB
    set JOB_NAME = #{jobName,jdbcType=VARCHAR},
      MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER},
      RELEASE_TIME = #{releaseTime,jdbcType=VARCHAR},
      COLLECTION = #{collection,jdbcType=CLOB},
      VALID_DATE = #{validDate,jdbcType=VARCHAR},
      RELEASE_STATUS = #{releaseStatus,jdbcType=CHAR},
      RELEASE_TYPE = #{releaseType,jdbcType=VARCHAR},
      RELEASE_INS = #{releaseIns,jdbcType=INTEGER},
      GROUP_ID = #{groupId,jdbcType=INTEGER},
      RECORD_CREATE_TIME = #{recordCreateTime,jdbcType=VARCHAR},
      RECORD_UPDATE_TIME = #{recordUpdateTime,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      INS_ID = #{insId,jdbcType=INTEGER},
      ISSHOW_NOTICE = #{isShowNotice,jdbcType=CHAR},
      TERM_TYPE_ARR = #{termTypeArr,jdbcType=VARCHAR},
      ACTIVATE_TYPES = #{activateTypes,jdbcType=VARCHAR},
      GROUP_IDS = #{groupIds,jdbcType=VARCHAR},
      IS_GROUP_UPDATE = #{isGroupUpdate,jdbcType=CHAR},
      activate_type = #{activateType,jdbcType=CHAR},
      valid_date_show = #{validDateShow,jdbcType=CHAR},
      <if test="dccSupFlag != null and dccSupFlag != ''">
          dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR},
      </if>
      cup_conn_mode = #{cupConnMode,jdbcType=CHAR},
      buss_type = #{bussType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
    <select id="countTerminal" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob"  resultType="java.lang.Integer">
        select count(*) from coms_terminal_info a
        left join coms_terminal_type c on c.code = a.term_type_code
        left join COMS_MERCHANT_INFO d on d.MERCHANT_NO = a.PAY_MERCHANT_NO
        where 1=1
        <if test="manufacturerId != null" >
            and  a.term_mfr_id = #{manufacturerId,jdbcType=INTEGER}
        </if>
        <if test="cupConnMode != null and cupConnMode != ''">
            and d.CUP_CONN_MODE = #{cupConnMode,jdbcType=CHAR}
        </if>
        <if test="bussType != null and bussType != ''">
            and d.BUSS_TYPE = #{bussType,jdbcType=VARCHAR}
        </if>
        <if test="dccSupFlag != null and dccSupFlag != ''" >
            and  d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
        </if>
        <if test="termTypeArr != null" >
            and c.code in
            <foreach item="item" index="index" collection="termTypeArr.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activateTypes != null and activateTypes != ''" >
            and  a.activate_type in
            <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insId != null" >
            and  a.ins_id in (SELECT s1.id from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insId}),s1.detail)>0)
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '0'.toString()">
            and  a.term_group_id in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '1'.toString()" >
            and  a.term_group_id not in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
    </select>
    <select id="queryTerminal" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob"  resultMap="TerminalResultMap">
        select a.TERM_SEQ from coms_terminal_info a
        left join coms_terminal_type c on c.code = a.term_type_code
        left join COMS_MERCHANT_INFO d on d.MERCHANT_NO = a.PAY_MERCHANT_NO
        where 1=1
        <if test="manufacturerId != null" >
            and  a.term_mfr_id = #{manufacturerId,jdbcType=INTEGER}
        </if>
        <if test="cupConnMode != null and cupConnMode != ''">
            and d.CUP_CONN_MODE = #{cupConnMode,jdbcType=CHAR}
        </if>
        <if test="bussType != null and bussType != ''">
            and d.BUSS_TYPE = #{bussType,jdbcType=VARCHAR}
        </if>
        <if test="dccSupFlag != null and dccSupFlag != ''" >
            and  d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
        </if>
        <if test="termTypeArr != null" >
            and c.code in
            <foreach item="item" index="index" collection="termTypeArr.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activateTypes != null and activateTypes != ''" >
            and  a.activate_type in
            <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insId != null" >
            and  a.ins_id in (SELECT s1.id from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insId}),s1.detail)>0)
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '0'.toString()">
            and  a.term_group_id in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '1'.toString()" >
            and  a.term_group_id not in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
  </select>
    <select id="queryTerminal2" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob"  resultMap="TerminalResultMap">
        select a.TERM_SEQ from coms_terminal_info a
        left join coms_terminal_type c on c.code = a.term_type_code
        left join COMS_MERCHANT_INFO d on d.MERCHANT_NO = a.PAY_MERCHANT_NO
        where 1=1
        and <![CDATA[ a.PRODUCTION_TIME >= #{recordUpdateTime,jdbcType=TIMESTAMP} ]]>
        <if test="manufacturerId != null" >
            and  a.term_mfr_id = #{manufacturerId,jdbcType=INTEGER}
        </if>
        <if test="cupConnMode != null and cupConnMode != ''">
            and d.CUP_CONN_MODE = #{cupConnMode,jdbcType=CHAR}
        </if>
        <if test="bussType != null and bussType != ''">
            and d.BUSS_TYPE = #{bussType,jdbcType=VARCHAR}
        </if>
        <if test="dccSupFlag != null and dccSupFlag != ''" >
            and  d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
        </if>
        <if test="termTypeArr != null" >
            and c.code in
            <foreach item="item" index="index" collection="termTypeArr.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activateTypes != null and activateTypes != ''" >
            and  a.activate_type in
            <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insId != null" >
            and  a.ins_id in (SELECT s1.id from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insId}),s1.detail)>0)
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '0'.toString()">
            and  a.term_group_id in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '1'.toString()" >
            and  a.term_group_id not in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
    </select>
  <update id="updateRelease" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
  	  update coms_download_job set release_status=#{releaseStatus,jdbcType=CHAR},
  	  record_update_time=#{recordUpdateTime,jdbcType=VARCHAR} 
  	  where id=#{id,jdbcType=INTEGER}
  </update>
  <select id="selectAppVersionByJob" resultType="java.util.HashMap" parameterType="java.util.Map">
    SELECT
	c.term_type_id,c.factory_id,d.code,d.type,c.app_version,c.file_name,c.app_path,c.md5,c.size
	FROM coms_app_version c
	LEFT JOIN coms_app_inf d ON c.app_id = d.id
	WHERE CODE=#{appCode}
	AND app_version= #{appVersion}
	AND factory_id=#{manufacturerId}
	AND term_type_id=#{termTypeId}
	<if test="insId != null and insId != 0">
			and c.ins_id in(SELECT s1.id from cpay_institution s1
			where 
			locate((SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=VARCHAR}),s1.detail)>0)
	</if>
  </select>
  <update id="updateSizeAppByJob" parameterType="java.util.Map">
  	update coms_app_version set size=#{size} WHERE
	app_id = (SELECT id FROM coms_app_inf a WHERE CODE=#{appCode})
	AND app_version= #{appVersion}
	AND factory_id=#{manufacturerId}
	AND term_type_id=#{termTypeId}
  </update>
  <select id="getAppVersion" resultMap="AppVersionResultMap" parameterType="java.lang.String">
  		select * from coms_app_version where app_id = 
		(select distinct id from coms_app_inf where code=#{appCode,jdbcType=VARCHAR})
  </select>
  <select id="getDriverAppVersion" resultMap="AppVersionResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
  		select * from coms_app_version where app_id =#{appId,jdbcType=INTEGER}
		 and term_type_id=#{terminalTypeId,jdbcType=INTEGER} 
		 <if test="insId != null and insId != 0" >
        	and ins_id in(SELECT s1.id from cpay_institution s1 where 
        	locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0)
     	</if>
  </select>
  
  <select id="getTermAppVersion" resultMap="AppVersionResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
  		select  * from coms_app_version where app_id =#{appId,jdbcType=INTEGER}
  </select>
  
  <select id="selectLauncherParam" resultType="java.util.HashMap" parameterType="java.util.Map">
  	SELECT c. launcher_code,4 as type, c.version,c.file_name,c.res_path,c.md5,c.size FROM
	coms_launcher_info c where c.launcher_code=#{appCode}
  </select>

    <select id="queryTermSeqList" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob"  resultType="java.lang.String">
        select a.term_seq from coms_terminal_info a
        left join coms_terminal_type c on c.code = a.term_type_code
        where 1=1
        <if test="manufacturerId != null" >
            and  a.term_mfr_id = #{manufacturerId,jdbcType=INTEGER}
        </if>
        <if test="termTypeArr != null" >
            and c.code in
            <foreach item="item" index="index" collection="termTypeArr.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activateTypes != null and activateTypes != ''" >
            and  a.activate_type in
            <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insId != null" >
            and  a.ins_id in (SELECT s1.id from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insId}),s1.detail)>0)
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '0'.toString()">
            and  a.term_group_id in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '1'.toString()" >
            and  a.term_group_id not in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>


    </select>
    <!--查找和当前任务相似的任务，条件限制：
        1.软件限1个，包名相同(appId相同)，操作类型相同
        2.厂商相同，型号相同
        3.终端类型相同或当前任务不区分
        4.间直连、业务类型、dcc相同或当前任务不区分
        5.发布类型、发布机构相同
        6.组别过滤方式、组别相同
        7.任务状态为 启动；非当前任务-->
    <select id="selectSameDriverJob" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob" resultMap="BaseResultMap">
      select a.id,a.job_name,b.APP_CODE,b.APP_VERSION from COMS_DOWNLOAD_JOB a
      left join COMS_DOWNLOAD_JOB_APP b
      on a.id = b.JOB_ID
      where  (SELECT count(*) FROM COMS_DOWNLOAD_JOB_APP c WHERE c.JOB_ID = a.id)=1
      and b.APP_CODE = #{appCode,jdbcType=VARCHAR}
      and b.JOB_ACTION = #{jobAction,jdbcType=VARCHAR}
      and b.APP_ID = #{appId,jdbcType=INTEGER}
      and a.MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
      and a.TERM_TYPE_ARR = #{termTypeArr,jdbcType=VARCHAR}
        <if test="activateTypes != null and activateTypes != ''">
            and a.ACTIVATE_TYPES = #{activateTypes,jdbcType=VARCHAR}
        </if>
        <if test="cupConnMode != null and cupConnMode != ''">
            and a.cup_conn_mode = #{cupConnMode,jdbcType=CHAR}
        </if>

        <if test="bussType != null and bussType != ''">
            and a.buss_type = #{bussType,jdbcType=VARCHAR}
        </if>
      <if test="dccSupFlag != null and dccSupFlag != ''">
          and a.DCC_SUP_FLAG = #{dccSupFlag,jdbcType=CHAR}
      </if>
      and a.RELEASE_TYPE = #{releaseType,jdbcType=VARCHAR}
      and a.INS_ID = #{insId,jdbcType=INTEGER}
      and a.IS_GROUP_UPDATE = #{isGroupUpdate,jdbcType=CHAR}
      <if test="groupIds != null and groupIds != ''">
          and a.GROUP_IDS = #{groupIds,jdbcType=VARCHAR}
      </if>
      and <![CDATA[ a.VALID_DATE >= CURRENT_TIMESTAMP ]]>
      and a.RELEASE_STATUS = '5'
      <if test="id != null and id != ''">
          and a.id != #{id,jdbcType=INTEGER}
      </if>
    </select>
    <select id="countWorkingJob" resultType="java.lang.Integer"
            parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
        SELECT count(a.id) FROM COMS_DOWNLOAD_JOB a
        LEFT JOIN COMS_DOWNLOAD_JOB_APP b ON a.id = b.JOB_ID
        WHERE b.APP_CODE = #{appCode,jdbcType=VARCHAR}
        and b.APP_ID = #{appId,jdbcType=INTEGER}
        AND b.APP_VERSION = #{appVersion,jdbcType=VARCHAR}
        AND a.RELEASE_STATUS != '7'
        AND <![CDATA[ a.VALID_DATE > CURRENT_TIMESTAMP ]]>
    </select>
    <select id="calculateWorkingJob" resultType="java.lang.Integer"
            parameterType="com.centerm.cpay.coms.dao.pojo.DriverJob">
        SELECT a.id FROM COMS_DOWNLOAD_JOB a
        LEFT JOIN COMS_DOWNLOAD_JOB_APP b ON a.id = b.JOB_ID
        WHERE b.APP_CODE = #{appCode,jdbcType=VARCHAR}
        and b.APP_ID = #{appId,jdbcType=INTEGER}
        AND b.APP_VERSION = #{appVersion,jdbcType=VARCHAR}
        AND a.RELEASE_STATUS != '7'
        AND <![CDATA[ a.VALID_DATE > CURRENT_TIMESTAMP ]]>
    </select>
    <select id="selectdlItems" resultMap="dlItemsLimit"
    parameterType="com.centerm.cpay.coms.dao.pojo.DlItemsLimit">
        select * from COMS_DL_ITERMS_LIMIT
        where 1=1
        <if test="termSeq != null and termSeq !=''">
		  and term_seq LIKE '%${termSeq}%'
		</if>
        <if test="dlBeginDate != null" >
          and <![CDATA[dl_begin_date > #{dlBeginDate,jdbcType=VARCHAR}]]>
        </if>

    </select>
</mapper>