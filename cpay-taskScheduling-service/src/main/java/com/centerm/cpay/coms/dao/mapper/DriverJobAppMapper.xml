<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.DriverJobAppMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.DriverJobApp" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="JOB_ID" property="jobId" jdbcType="INTEGER" />
    <result column="JOB_TYPE" property="jobType" jdbcType="CHAR" />
    <result column="APP_ID" property="appId" jdbcType="INTEGER" />
    <result column="APP_CODE" property="appCode" jdbcType="VARCHAR" />
    <result column="JOB_ACTION" property="jobAction" jdbcType="CHAR" />
    <result column="TERMINAL_TYPE_ID" property="terminalTypeId" jdbcType="INTEGER" />
    <result column="APP_VERSION" property="appVersion" jdbcType="VARCHAR" />
    <result column="SIZE" property="size" jdbcType="INTEGER" />
    <result column="APP_NAME" property="appName" jdbcType="VARCHAR" />
    <result column="APP_VERSION_ID" property="appVersionId" jdbcType="INTEGER" />
    <result column="number" property="number" jdbcType="INTEGER" />
    <result column="success" property="success" jdbcType="INTEGER" />
    <result column="failure_update" property="failureUpdate" jdbcType="INTEGER" />
    <result column="success_down" property="successDown" jdbcType="INTEGER" />
    <result column="wait_send" property="waitSend" jdbcType="INTEGER" />
    <result column="depend_app_id" property="dependAppId" jdbcType="VARCHAR" />
    <result column="depend_app_ids" property="dependAppIds" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, JOB_ID, JOB_TYPE, APP_ID, APP_CODE, JOB_ACTION, TERMINAL_TYPE_ID, APP_VERSION, 
    SIZE, APP_NAME, APP_VERSION_ID,DEPEND_APP_ID,DEPEND_APP_IDS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from COMS_DOWNLOAD_JOB_APP
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByJobId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from COMS_DOWNLOAD_JOB_APP
    where JOB_ID = #{jobId,jdbcType=INTEGER}
  </select>
  <select id="selectInfoByJobId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    a.*,
    e.number,e.success,e.failure_update,e.success_down,e.wait_send
    from COMS_DOWNLOAD_JOB_APP a
    LEFT JOIN
    (SELECT
    c.DEPEND_TASK_ID,
    COUNT(c.DL_FLAG) as number,
    sum(case when c.DL_FLAG='4' OR c.DL_FLAG='5' then 1 else 0 end) as success,
    sum(case when c.DL_FLAG='6' or c.DL_FLAG='7' or c.DL_FLAG='8' then 1 else 0 end) as failure_update,
    sum(case when c.DL_FLAG='1' OR c.DL_FLAG='2' OR c.DL_FLAG='3' then 1 else 0 end) as success_down,
    sum(case when c.DL_FLAG='0' then 1 else 0 end) as wait_send FROM COMS_DOWNLOAD_TASK c
    WHERE c.JOB_ID =#{jobId,jdbcType=INTEGER} GROUP BY c.DEPEND_TASK_ID) e
    ON e.DEPEND_TASK_ID = a.DEPEND_APP_ID
    where a.JOB_ID = #{jobId,jdbcType=INTEGER}
    order by a.ID
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from COMS_DOWNLOAD_JOB_APP
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByJobId" parameterType="java.lang.Integer" >
    delete from COMS_DOWNLOAD_JOB_APP
    where JOB_ID = #{jobId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobApp" >
    insert into COMS_DOWNLOAD_JOB_APP (JOB_ID, JOB_TYPE,
      APP_ID, APP_CODE, JOB_ACTION, 
      TERMINAL_TYPE_ID, APP_VERSION, SIZE, 
      APP_NAME, APP_VERSION_ID)
    values (#{jobId,jdbcType=INTEGER}, #{jobType,jdbcType=CHAR},
      #{appId,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, #{jobAction,jdbcType=CHAR}, 
      #{terminalTypeId,jdbcType=INTEGER}, #{appVersion,jdbcType=VARCHAR}, #{size,jdbcType=INTEGER}, 
      #{appName,jdbcType=VARCHAR}, #{appVersionId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobApp" useGeneratedKeys="true" keyProperty="id">
    insert into COMS_DOWNLOAD_JOB_APP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        JOB_ID,
      </if>
      <if test="jobType != null" >
        JOB_TYPE,
      </if>
      <if test="appId != null" >
        APP_ID,
      </if>
      <if test="appCode != null" >
        APP_CODE,
      </if>
      <if test="jobAction != null" >
        JOB_ACTION,
      </if>
      <if test="terminalTypeId != null" >
        TERMINAL_TYPE_ID,
      </if>
      <if test="appVersion != null" >
        APP_VERSION,
      </if>
      <if test="size != null" >
        SIZE,
      </if>
      <if test="appName != null" >
        APP_NAME,
      </if>
      <if test="appVersionId != null" >
        APP_VERSION_ID,
      </if>
      <if test="dependAppId != null" >
        depend_app_id,
      </if>
      <if test="dependAppIds != null" >
        depend_app_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        #{jobId,jdbcType=INTEGER},
      </if>
      <if test="jobType != null" >
        #{jobType,jdbcType=CHAR},
      </if>
      <if test="appId != null" >
        #{appId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="jobAction != null" >
        #{jobAction,jdbcType=CHAR},
      </if>
      <if test="terminalTypeId != null" >
        #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="appVersion != null" >
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=INTEGER},
      </if>
      <if test="appName != null" >
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersionId != null" >
        #{appVersionId,jdbcType=INTEGER},
      </if>
      <if test="dependAppId != null" >
        #{dependAppId,jdbcType=INTEGER},
      </if>
      <if test="dependAppIds != null" >
        #{dependAppIds,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobApp" >
    update COMS_DOWNLOAD_JOB_APP
    <set >
      <if test="jobId != null" >
        JOB_ID = #{jobId,jdbcType=INTEGER},
      </if>
      <if test="jobType != null" >
        JOB_TYPE = #{jobType,jdbcType=CHAR},
      </if>
      <if test="appId != null" >
        APP_ID = #{appId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        APP_CODE = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="jobAction != null" >
        JOB_ACTION = #{jobAction,jdbcType=CHAR},
      </if>
      <if test="terminalTypeId != null" >
        TERMINAL_TYPE_ID = #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="appVersion != null" >
        APP_VERSION = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        SIZE = #{size,jdbcType=INTEGER},
      </if>
      <if test="appName != null" >
        APP_NAME = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appVersionId != null" >
        APP_VERSION_ID = #{appVersionId,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobApp" >
    update COMS_DOWNLOAD_JOB_APP
    set JOB_ID = #{jobId,jdbcType=INTEGER},
      JOB_TYPE = #{jobType,jdbcType=CHAR},
      APP_ID = #{appId,jdbcType=INTEGER},
      APP_CODE = #{appCode,jdbcType=VARCHAR},
      JOB_ACTION = #{jobAction,jdbcType=CHAR},
      TERMINAL_TYPE_ID = #{terminalTypeId,jdbcType=INTEGER},
      APP_VERSION = #{appVersion,jdbcType=VARCHAR},
      SIZE = #{size,jdbcType=INTEGER},
      APP_NAME = #{appName,jdbcType=VARCHAR},
      APP_VERSION_ID = #{appVersionId,jdbcType=INTEGER},
      JOB_PRIORITY = #{jobPriority,jdbcType=CHAR},
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>