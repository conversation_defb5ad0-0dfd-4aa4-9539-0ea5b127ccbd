package com.centerm.cpay.coms.service.impl;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.PropertyUtil;
import com.centerm.cpay.coms.dao.mapper.*;
import com.centerm.cpay.coms.dao.pojo.*;
import com.centerm.cpay.coms.service.DriverJobService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Service
public class DriverJobServiceImpl implements DriverJobService{

	private Logger logger = LoggerFactory.getLogger(DriverJobServiceImpl.class);

	@Autowired
	private DriverJobMapper driverJobMapper;
	
	@Autowired
	private TerminalCertMapper terminalCertMapper;

	@Autowired
	private ComsAppInfMapper comsAppInfMapper;
	
	@Autowired
	private DriverJobTaskMapper driverJobTaskMapper;
	@Autowired
	private DriverJobAppMapper driverJobAppMapper;

	@Autowired
	 private ComsLauncherInfoMapper comsLauncherInfoMapper;
	@Autowired
	 private ComsAppVersionMapper comsAppVersionMapper;

	private Integer rows = Integer.valueOf(PropertyUtil.getValue("job.task.length"));
	@Override
	public List<ComsAppVersion> getTermType(ComsAppVersion entity){
		ComsAppVersion entity1=comsAppVersionMapper.selectByPrimaryKey(Integer.valueOf(entity.getAppVersion()));
		entity.setAppVersion(entity1.getAppVersion());
		List<ComsAppVersion> list = comsAppVersionMapper.selectTermType(entity);
		return list;	
	}
	
	@Override
	public EUDataGridResult getdlItems(DlItemsLimit dlItemsLimit, Integer page, Integer rows) {
		
		// 分页处理
		PageHelper.startPage(page, rows);
		List<DriverJob> list = driverJobMapper.selectdlItems(dlItemsLimit);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJob> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	@Override
	public EUDataGridResult getDriverJobList(DriverJob driverJob, Integer page, Integer rows) {

		// 分页处理
		PageHelper.startPage(page, rows);
		List<DriverJob> list = driverJobMapper.selectJobList(driverJob);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJob> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	@Override
	public List<DriverJob> getApkUpdateReport(DriverJob driverJob) {
		List<DriverJob> list = driverJobMapper.selectJobListReport(driverJob);

		return list;
	}
	@Override
	public List<DriverJob> getJobListEcharts(DriverJob driverJob) {
		List<DriverJob> list = driverJobMapper.selectForIndex(driverJob);

		return list;
	}
	@Override
	public ResultMsg deleteByPrimaryKey(Integer id) {
		final ExecutorService exec = Executors.newSingleThreadExecutor();
		//原基数上乘2
		int deleteRows =rows * 2;
		//删除当前任务的时候，如果当前任务为下发成功和下载成功的时候这个任务不能删除
//		int countNotEnd = driverJobTaskMapper.queryTaskStatus(id);
//		if (countNotEnd > 0) {
//			return ResultMsg.build(ResultMsg.ERROR_CODE, "The task has not been completed, which cannot delete it!");
//		}
		exec.submit(()-> {
			//删除当前作业
			driverJobMapper.deleteByPrimaryKey(id);
			driverJobAppMapper.deleteByJobId(id);
			int countAllTask = driverJobTaskMapper.queryCountTasks(id);
			Map paramMap = new HashMap(3);
			paramMap.put("jobId", id);
			//每次删除前rows * 2条
			paramMap.put("rows", deleteRows);
			paramMap.put("offset", 0);
			for (int offset = 0; offset < countAllTask; offset = offset + deleteRows) {
				try {
					//删除当前属于这个作业的任务
					driverJobTaskMapper.deleteTaskBatch(paramMap);
					driverJobTaskMapper.submitTran();
				} catch (Exception e) {
					logger.error("任务id" + id + "分批删除失败");
					logger.error("任务删除失败偏移量：" + offset);
					logger.error(e.toString());
				}

			}
		});
		exec.shutdown();

		return ResultMsg.success();
	}

	@Override
	public DriverJob selectByPrimaryKey(Integer id) {
		String reg = "(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})";
		DriverJob driverJob = driverJobMapper.selectByPrimaryKey(id);
		List<DriverJobApp> list = driverJobAppMapper.selectInfoByJobId(id);
		driverJob.setDriverJobApps(list);
		String releaseTime = driverJob.getReleaseTime();
		String validDate = driverJob.getValidDate();
		driverJob.setReleaseTime(releaseTime.replaceAll(reg, "$1-$2-$3 $4:$5:$6"));
		driverJob.setValidDate(validDate.replaceAll(reg, "$1-$2-$3 $4:$5:$6"));
		return driverJob;
	}

	@Override
	public ResultMsg insert(DriverJob driverJob) {
		String termSeqs="";
		String releaseTime = driverJob.getReleaseTime().replace(" ", "").replace(":", "").replace("-", "");
		String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
		if(Long.parseLong(releaseTime)>=Long.parseLong(validDate)){
			return ResultMsg.build(1, "起始日期不得大于或等于结束日期");
		}
		driverJob.setReleaseTime(releaseTime);
		driverJob.setValidDate(validDate);

		if(driverJob.getReleaseType().equals("1")){
			int countTerminal = driverJobMapper.countTerminal(driverJob);
			if(countTerminal == 0){
				return ResultMsg.build(1, "当前条件下无满足条件的终端可供选择");
			}
		}
		/*driverJob.setId(null);
        if(!checkOldTask(driverJob)){
            return ResultMsg.build(1, "任务列表中已发布当前软件的高版本或同版本");
        }
*/
		driverJobMapper.insert(driverJob);
        int jobId = driverJob.getId();
        for(DriverJobApp driverJobApp : driverJob.getDriverJobApps()){
			driverJobApp.setJobId(jobId);
			driverJobAppMapper.insertSelective(driverJobApp);
		}
		return ResultMsg.success();
	}


	@Override
	public ResultMsg insertClearCache(DriverJob driverJob) {
		String termSeqs="";
		List<Terminal> list = null;
		String releaseTime = driverJob.getReleaseTime().replace(" ", "").replace(":", "").replace("-", "");
		String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
		if(Long.parseLong(releaseTime)>=Long.parseLong(validDate)){
			return ResultMsg.build(ResultMsg.ERROR_CODE, "起始日期不得大于结束日期");
		}
		driverJob.setReleaseTime(releaseTime);
		driverJob.setValidDate(validDate);
		if(driverJob.getReleaseType().equals("1")){
				list = driverJobMapper.queryTerminal(driverJob);
		}
		if(list !=null && !list.isEmpty()){
			for(int i = 0;i<list.size();i++){
				termSeqs=termSeqs+list.get(i).getTermSeq()+",";
			}
			driverJob.setCollection(termSeqs.substring(0,termSeqs.length()-1));
		}
		driverJobMapper.insert(driverJob);
		return ResultMsg.success();
	}
	@Override
	public ResultMsg updateClearCache(DriverJob driverJob) {
		String termSeqs="";
		List<Terminal> list = null;
		String releaseTime = driverJob.getReleaseTime().replace(" ", "").replace(":", "").replace("-", "");
		String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
		if(Long.parseLong(releaseTime)>=Long.parseLong(validDate)){
			return ResultMsg.build(ResultMsg.ERROR_CODE, "起始日期不得大于结束日期");
		}
		driverJob.setReleaseTime(releaseTime);
		driverJob.setValidDate(validDate);
		if(driverJob.getReleaseType().equals("1")){
			list = driverJobMapper.queryTerminal(driverJob);
		}
		if(list !=null && !list.isEmpty()){
			for(int i = 0;i<list.size();i++){
				termSeqs=termSeqs+list.get(i).getTermSeq()+",";
			}
			driverJob.setCollection(termSeqs.substring(0,termSeqs.length()-1));
		}
		driverJobMapper.updateByPrimaryKey(driverJob);
		return ResultMsg.success();
	}
	@Override
	public ResultMsg updateByPrimaryKey(DriverJob driverJob) {
		String releaseTime = driverJob.getReleaseTime().replace(" ", "").replace(":", "").replace("-", "");
		String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
		if(Long.parseLong(releaseTime)>=Long.parseLong(validDate)){
			return ResultMsg.build(1, "起始日期不得大于或等于结束日期");
		}
		driverJob.setReleaseTime(releaseTime);
		driverJob.setValidDate(validDate);
		if(driverJob.getReleaseType().equals("1")){
			int countTerminal = driverJobMapper.countTerminal(driverJob);
			if(countTerminal == 0){
				return ResultMsg.build(1, "当前条件下无满足条件的终端可供选择");
			}
		}

        /*if(!checkOldTask(driverJob)){
            return ResultMsg.build(1, "任务列表中已发布当前软件的高版本或同版本");
        }*/
		driverJobMapper.updateByPrimaryKey(driverJob);
		driverJobAppMapper.deleteByJobId(driverJob.getId());
		int jobId = driverJob.getId();
		for(DriverJobApp driverJobApp : driverJob.getDriverJobApps()){
			driverJobApp.setJobId(jobId);
			driverJobAppMapper.insertSelective(driverJobApp);
		}
		return ResultMsg.success();
	}
	@Override
	public ResultMsg delayByPrimaryKey(DriverJob driverJob) {
		final ExecutorService exec = Executors.newSingleThreadExecutor();
		exec.submit(()->{
			String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
			driverJob.setValidDate(validDate);
			driverJobMapper.delayByPrimaryKey(driverJob);
			int countAllTask = driverJobTaskMapper.queryCountTasks(driverJob.getId());
			Map paramMap = new HashMap(4);
			paramMap.put("jobId",driverJob.getId());
			paramMap.put("rows",rows);
			paramMap.put("validDate",validDate);
			for(int offset = 0;offset < countAllTask;offset = offset+rows ){
				paramMap.put("offset",offset);
				try{
					//延时当前属于这个作业的任务
					driverJobTaskMapper.delayTaskBatch(paramMap);
					driverJobTaskMapper.submitTran();
				}catch (Exception e){
					logger.info("任务id"+driverJob.getId()+"分批延时失败");
					logger.info("任务延时失败偏移量：" + offset);
				}
			}
		});
		exec.shutdown();

		return ResultMsg.success();
	}
	@Override
	public List<ComsAppInf> selectDriverApp(Integer insId) {
		ComsAppInf comsAppInf = new ComsAppInf();
		comsAppInf.setInsId(insId);
		return comsAppInfMapper.selectDriverApp(comsAppInf);
	}

	@Override
	public ResultMsg subJob(Integer id) {
		DriverJob driverJob = driverJobMapper.selectByPrimaryKey(id);
		driverJob.setDriverJobApps(driverJobAppMapper.selectByJobId(id));
		driverJob.setReleaseStatus("2");
		driverJobMapper.updateRelease(driverJob);//修改状态为发布中
		//当软件的类型中有镜像时，使用method1进行发布
		boolean subMethodTemp = false;
		for(DriverJobApp driverJobApp : driverJob.getDriverJobApps()){
			if(driverJobApp.getJobType().equals("3")||driverJobApp.getJobType().equals("2")){
				subMethodTemp = true;
				break;
			}
		}
		final ExecutorService exec = Executors.newSingleThreadExecutor();

		if(driverJob.getReleaseType().equals("2") || subMethodTemp){
			//method1 按条件发布、软件镜像发布时，使用单条插入
			exec.submit(new UploadDriverTask(driverJob));
			//new Thread(new UploadDriverTask(driverJob)).start();
		}else{
			exec.submit(new UploadDriverBatchTask(driverJob));
			//method2 按组别发布时，批量提交
			//new Thread(new UploadDriverBatchTask(driverJob)).start();
		}
		exec.shutdown();
		return ResultMsg.success();
	}

	@Override
	public List<DriverJobTask> getTaskListExcel(DriverJobTask driverJobTask) {
		List<DriverJobTask> list = driverJobTaskMapper.selectExcelByCondition(driverJobTask);
		return list;
	}
	@Override
	public EUDataGridResult getTaskList(DriverJobTask driverJobTask, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);

		List<DriverJobTask> list = driverJobTaskMapper.selectByCondition(driverJobTask);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg issuedAgain(List<String> idsList) {
		Map<String,Integer> map = new HashMap<>(2);
		map.put("releaseStatus",5);
		map.put("id",Integer.parseInt(idsList.get(0)));
		driverJobMapper.updateReleaseStatus(map);
		driverJobTaskMapper.issuedAgain(idsList.subList(1,idsList.size()));
		return ResultMsg.success();
	}
	@Override
	public ResultMsg issuedAgainAll(Integer id) {
		Map<String,Integer> map = new HashMap<>(2);
		map.put("releaseStatus",5);
		map.put("id",id);
		driverJobMapper.updateReleaseStatus(map);
		driverJobTaskMapper.issuedAgainAll(id);
		return ResultMsg.success();
	}
	@Override
	public ResultMsg issuedCancel(List<String> idsList) {
		driverJobTaskMapper.issuedCancel(idsList);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg issuedCancelAll(Integer id) {
		Map<String,Integer> map = new HashMap<>(2);
		map.put("releaseStatus",6);
		map.put("id",id);
		driverJobMapper.updateReleaseStatus(map);
		driverJobTaskMapper.issuedCancelAll(id);
		return ResultMsg.success();
	}

	@Override
	public List<ComsAppVersion> getAppVersion(String appCode) {
		return driverJobMapper.getAppVersion(appCode);
	}

	@Override
	public List<ComsLauncherInfo> selectLauncher(ComsLauncherInfo comsLauncherInf) {
		comsLauncherInf.setPubStatus("1");
		return comsLauncherInfoMapper.selectByCondition(comsLauncherInf);
	}

	@Override
	public int publish(DriverJob driverJob,Integer jobId,String appType) {
		String termSeqs="";
		List<Terminal> list = null;
		DriverJobTask driverJobTask = new DriverJobTask();
		driverJobTask.setSize(driverJob.getSize());
		driverJobTask.setJobId(driverJob.getId());
		driverJobTask.setTerminalTypeId(0);
		driverJobTask.setManufacturerId(0);
		driverJobTask.setAppCode(driverJob.getAppCode());
		driverJobTask.setAppVer(driverJob.getAppVersion());
		driverJobTask.setAppFileName(driverJob.getAppName());
		driverJobTask.setMd5(driverJob.getMd5());
		driverJobTask.setAppVer(driverJob.getAppVersion());
		driverJobTask.setDlFlag("0");
		driverJobTask.setAppType(appType);
		driverJobTask.setUrl(driverJob.getUrl());
		String termSeqCollection="";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		driverJobTask.setReleaseTime(driverJob.getReleaseTime());
		driverJobTask.setValidDate(driverJob.getValidDate());
		driverJobTask.setRecordCreateTime(sdf.format(new Date()));
		driverJobTask.setIsShowNotice(driverJob.getIsShowNotice());
		if(driverJob.getReleaseType().equals("1")){
			list = driverJobMapper.queryTerminal(driverJob);
		}
		if(list !=null && !list.isEmpty()){
			for(int i = 0;i<list.size();i++){
				termSeqs=termSeqs+list.get(i).getTermSeq()+",";
			}
			driverJob.setCollection(termSeqs.substring(0,termSeqs.length()-1));
		}
		termSeqCollection=driverJob.getCollection();
		if(termSeqCollection==null){
			return 1;
		}
		if(termSeqCollection !=null && termSeqCollection !=""){
			String[] arr1 = termSeqCollection.split(",");
			if(arr1.length>0){
				try{
					for(int k =0;k<arr1.length;k++){
						driverJobTask.setTermSeq(arr1[k]);
						
						driverJobTaskMapper.insert(driverJobTask);
					}
				}catch(Exception e){
					e.printStackTrace();
				}
			}
		}
		return 2;
	}
	@Override
	public int publishApkOrDel(DriverJob driverJob,Integer jobId,String appType,String jobType) {
		String releaseTime = driverJob.getReleaseTime().replace(" ", "").replace(":", "").replace("-", "");
		String validDate = driverJob.getValidDate().replace(" ", "").replace(":", "").replace("-", "");
		driverJob.setReleaseTime(releaseTime);
		driverJob.setValidDate(validDate);
		driverJob.setReleaseStatus("2");
		driverJob.setJobType(jobType);//4表示卸载
		driverJobMapper.insert(driverJob);
		String termSeqs="";
		List<Terminal> list = null;
		DriverJobTask driverJobTask = new DriverJobTask();
		driverJobTask.setJobId(driverJob.getId());
		driverJobTask.setSize(driverJob.getSize());
		driverJobTask.setJobId(driverJob.getId());
		driverJobTask.setTerminalTypeId(0);
		driverJobTask.setManufacturerId(0);
		driverJobTask.setAppCode(driverJob.getAppCode());
		driverJobTask.setAppVer(driverJob.getAppVersion());
		driverJobTask.setAppFileName(driverJob.getAppName());
		driverJobTask.setMd5(driverJob.getMd5());
		driverJobTask.setAppVer(driverJob.getAppVersion());
		driverJobTask.setDlFlag("0");
		driverJobTask.setAppType(appType);
		driverJobTask.setUrl(driverJob.getUrl());
		String termSeqCollection="";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		driverJobTask.setReleaseTime(driverJob.getReleaseTime());
		driverJobTask.setValidDate(driverJob.getValidDate());
		driverJobTask.setRecordCreateTime(sdf.format(new Date()));
		driverJobTask.setIsShowNotice(driverJob.getIsShowNotice());
		if(driverJob.getReleaseType().equals("1")){
			list = driverJobMapper.queryTerminal(driverJob);
		}
		if(list !=null && !list.isEmpty()){
			for(int i = 0;i<list.size();i++){
				termSeqs=termSeqs+list.get(i).getTermSeq()+",";
			}
			driverJob.setCollection(termSeqs.substring(0,termSeqs.length()-1));
		}
		termSeqCollection=driverJob.getCollection();
		if(termSeqCollection==null){
			return 1;
		}
		if(termSeqCollection !=null && termSeqCollection !=""){
			String[] arr1 = termSeqCollection.split(",");
			if(arr1.length>0){
				try{
					for(int k =0;k<arr1.length;k++){
						driverJobTask.setTermSeq(arr1[k]);
						
						driverJobTaskMapper.insert(driverJobTask);
					}
				}catch(Exception e){
					e.printStackTrace();
				}
			}
		}
		return 2;
	}
	@Override
    public Integer getMaxJobId(){
    	return driverJobTaskMapper.getMaxJobId();
    }
	@Override
	public EUDataGridResult getApkPushDelList(Map map, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<DriverJobTask> list = new ArrayList();
		list = driverJobTaskMapper.getApkPushDelList(map);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	
	@Override
	public List<ComsAppVersion> getDriverAppVersion(DriverJob driverJob) {
		return driverJobMapper.getDriverAppVersion(driverJob);
	}
	
	@Override
	public List<ComsAppVersion> getTermAppVersion(DriverJob driverJob) {
		return driverJobMapper.getTermAppVersion(driverJob);
	}
	
	@Override
	public EUDataGridResult getBootConfList(Map map, Integer page, Integer rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<DriverJobTask> list = new ArrayList();
		list = driverJobTaskMapper.getBootConfList(map);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg deleteApkPush(List<String> ids) {
		driverJobTaskMapper.deleteApkPush(ids);
		return ResultMsg.success();
	}

	@Override
	public EUDataGridResult getLauncherAppPushList(Map map, Integer page, Integer rows) {
			// 分页处理
			PageHelper.startPage(page, rows);
			List<DriverJobTask> list = new ArrayList();
			list = driverJobTaskMapper.getLauncherAppPushList(map);
			// 创建一个返回值对象
			EUDataGridResult result = new EUDataGridResult();
			result.setRows(list);
			// 取记录总条数
			PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
			result.setTotal(pageInfo.getTotal());
			return result;
	}

	@Override
	public EUDataGridResult getThemeAppPushList(Map map, Integer page, Integer rows) {
			// 分页处理
			PageHelper.startPage(page, rows);
			List<DriverJobTask> list = new ArrayList();
			list = driverJobTaskMapper.getThemeAppPushList(map);
			// 创建一个返回值对象
			EUDataGridResult result = new EUDataGridResult();
			result.setRows(list);
			// 取记录总条数
			PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
			result.setTotal(pageInfo.getTotal());
			return result;
	}
	
	@Override
	public EUDataGridResult getDriverJobSwichList(DriverJob driverJob, Integer page, Integer rows) {
		
		// 分页处理
		PageHelper.startPage(page, rows);
   	    List<DriverJob> list = driverJobMapper.selectBySwichCondition(driverJob);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJob> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	
	@Override
	public ResultMsg subSwichJob(Integer id) {
		DriverJob driverJob = driverJobMapper.selectByPrimaryKey(id);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		driverJob.setRecordCreateTime(sdf.format(new Date()));
		driverJob.setReleaseStatus("2");
		driverJobMapper.updateRelease(driverJob);//修改状态为发布中
		
		String collection[] = driverJob.getCollection().split(",");
		DriverJobTask driverJobTask = new DriverJobTask();
		
		for(int i=0;i<collection.length;i++){
		driverJobTask.setAppCode(driverJob.getAppCode());
		driverJobTask.setJobId(driverJob.getId());
		driverJobTask.setTermSeq(collection[i]);
		driverJobTask.setManufacturerId(99);
		driverJobTask.setTerminalTypeId(99);
		driverJobTask.setAppVer("99");
		driverJobTask.setAppFileName("99");
		driverJobTask.setDlFlag("0");
		driverJobTask.setAppType(driverJob.getJobType());
		driverJobTask.setReleaseTime(driverJob.getReleaseTime());
		driverJobTask.setValidDate(driverJob.getValidDate());
		driverJobTask.setRecordCreateTime(sdf.format(new Date()));
		driverJobTask.setJobAction(driverJob.getJobAction());
		driverJobTaskMapper.insert(driverJobTask);
		}
		return ResultMsg.success();
	}
	
	@Override
	public EUDataGridResult getDriverJobCentList(DriverJob driverJob, Integer page, Integer rows) {
		
		// 分页处理
		PageHelper.startPage(page, rows);
   	    List<DriverJob> list = driverJobMapper.selectByCondition(driverJob);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<DriverJob> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}
	
	@Override
	public ResultMsg subCertJob(Integer id) {
		DriverJob driverJob = driverJobMapper.selectByPrimaryKey(id);
		
		Integer centId = Integer.parseInt(driverJob.getAppCode());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		driverJob.setRecordCreateTime(sdf.format(new Date()));
		driverJob.setReleaseStatus("2");
		driverJobMapper.updateRelease(driverJob);//修改状态为发布中
		
		TerminalCert terminalCert =  terminalCertMapper.selectByPrimaryKey(centId);
		
		String collection[] = driverJob.getCollection().split(",");
		DriverJobTask driverJobTask = new DriverJobTask();
		
		for(int i=0;i<collection.length;i++){
		driverJobTask.setAppCode(driverJob.getAppCode());
		driverJobTask.setJobId(driverJob.getId());
		driverJobTask.setTermSeq(collection[i]);
		driverJobTask.setManufacturerId(99);
		driverJobTask.setTerminalTypeId(99);
		driverJobTask.setAppVer("99");
		driverJobTask.setAppFileName("99");
		driverJobTask.setDlFlag("0");
		driverJobTask.setMd5(terminalCert.getPrivatekeyMd5());
		driverJobTask.setUrl(terminalCert.getPrivatekeyPath());
		driverJobTask.setAppType(driverJob.getJobType());
		driverJobTask.setReleaseTime(driverJob.getReleaseTime());
		driverJobTask.setValidDate(driverJob.getValidDate());
		driverJobTask.setRecordCreateTime(sdf.format(new Date()));
	
		driverJobTaskMapper.insert(driverJobTask);
		}
		return ResultMsg.success();
	}

	@Override
	public EUDataGridResult taskListAboutReport(DriverJobTask driverJobTask, Integer page, Integer rows) {
		// 分页处理
				PageHelper.startPage(page, rows);

				List<DriverJobTask> list = driverJobTaskMapper.taskListAboutReport(driverJobTask);
				// 创建一个返回值对象
				EUDataGridResult result = new EUDataGridResult();
				result.setRows(list);
				// 取记录总条数
				PageInfo<DriverJobTask> pageInfo = new PageInfo<>(list);
				result.setTotal(pageInfo.getTotal());
				return result;
	}

	@Override
	public List<DriverJobTask> exportExcelAboutReport(DriverJobTask driverJobTask) {
		List<DriverJobTask> list = driverJobTaskMapper.exportExcelAboutReport(driverJobTask);
		return list;
	}

	@Override
	public int countWorkingJob(DriverJob driverJob) {

		List<Integer> driverJobIds = driverJobMapper.calculateWorkingJob(driverJob);
		StringBuffer idsStr = new StringBuffer();
		for (int i = 0; i < driverJobIds.size(); i++) {
			if (i > 0) {
				idsStr.append(",");
			}
			idsStr.append(driverJobIds.get(i));
		}
		driverJob.setCollection(idsStr.toString());
		int result = driverJobMapper.judgeJobUnFinish(driverJob);
		return result;
	}
    /**
     * 判断待发布任务是否有旧任务，若有，判断新任务的版本，比旧任务高或相同，停掉旧任务；
     * 限定：高版本任务和低版本任务的终端筛选条件相同，单个任务中仅单个软件。
     *
     * @return 返回 true，新任务可保存；返回false，新任务不可保存
     */
    public boolean checkOldTask(DriverJob driverJob) {


        /**
         * 仅处理软件个数为一个，并且该单个软件是常规更新的任务
         */
        //软件个数为1
        if (driverJob.getDriverJobApps().size() == 1){
			DriverJobApp driverJobAppNew = driverJob.getDriverJobApps().get(0);
			//软件为更新，且不为驱动或系统
			if(driverJobAppNew.getJobAction().equals("7") &&
					!driverJobAppNew.getJobType().equals("2")&& !driverJobAppNew.getJobType().equals("3")){
				driverJob.setAppCode(driverJobAppNew.getAppCode());
				driverJob.setJobAction(driverJobAppNew.getJobAction());
                driverJob.setAppId(driverJobAppNew.getAppId());
				//查找和该单个软件类似的任务
				try {
					List<DriverJob> driverJobOldList = driverJobMapper.selectSameDriverJob(driverJob);
					//存在相似任务
					if (!CtUtils.isEmpty(driverJobOldList)) {
						DriverJob driverJobOld = driverJobOldList.get(0);
						//相似任务软件的版本
						int appVersion = Integer.parseInt(driverJobOld.getAppVersion());
						//当前任务软件的版本
						int appVersionCur = Integer.parseInt(driverJobAppNew.getAppVersion());
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						if (appVersion < appVersionCur) {
							return true;
						} else {
							//结束新创建的任务
							logger.info("【开始当前新任务的版本过低或重复，取消保存】");
							return false;
						}
					}
				}catch (Exception e){
					e.printStackTrace();
				}

			}
		}
        return true;
    }
}