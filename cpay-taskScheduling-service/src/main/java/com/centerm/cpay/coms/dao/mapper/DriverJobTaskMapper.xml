<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.DriverJobTaskMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="job_id" property="jobId" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="terminal_type_id" property="terminalTypeId" jdbcType="INTEGER" />
    <result column="manufacturer_id" property="manufacturerId" jdbcType="INTEGER" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="app_type" property="appType" jdbcType="CHAR" />
    <result column="app_ver" property="appVer" jdbcType="VARCHAR" />
    <result column="app_file_name" property="appFileName" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="md5" property="md5" jdbcType="VARCHAR" />
    <result column="dl_flag" property="dlFlag" jdbcType="CHAR" />
    <result column="dl_begin_date" property="dlBeginDate" jdbcType="VARCHAR" />
    <result column="dl_end_date" property="dlEndDate" jdbcType="VARCHAR" />
    <result column="release_time" property="releaseTime" jdbcType="VARCHAR" />
    <result column="valid_date" property="validDate" jdbcType="VARCHAR" />
    <result column="record_create_time" property="recordCreateTime" jdbcType="VARCHAR" />
    <result column="job_name" property="jobName" jdbcType="VARCHAR" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="isShow_notice" property="isShowNotice" jdbcType="CHAR" />
    <result column="job_action" property="jobAction" jdbcType="CHAR" />
    <result column="result_message" property="resultMessage" jdbcType="CHAR" />
    <result column="size" property="size" jdbcType="INTEGER"/>
    <result column="down_time" property="downTime" jdbcType="TIMESTAMP"/>
    <result column="depend_task_id" property="dependTaskId" jdbcType="VARCHAR" />
    <result column="depend_task_ids" property="dependTaskIds" jdbcType="VARCHAR" />
    <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
    <result column="applyer_name" property="applyerName" jdbcType="VARCHAR" />
    <result column="applyer_phone" property="applyerPhone" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="county" property="county" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="term_no" property="termNo" jdbcType="VARCHAR" />
    <result column="pay_merchant_no" property="payMerchantNo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, job_id, term_seq, terminal_type_id, manufacturer_id, app_code, app_type, app_ver, 
    app_file_name, url, md5, dl_flag, dl_begin_date, dl_end_date, release_time, valid_date, 
    record_create_time,isShow_notice,down_time,job_action,app_name,depend_task_id,depend_task_ids
  </sql>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    	select a.*,
    	b.job_name
    	from coms_download_task a
        left join coms_download_job b on a.job_id =  b.id
    	where job_id = #{jobId,jdbcType=INTEGER}
		<if test="termSeq != null and termSeq != ''" >
        	and term_seq like  '%${termSeq}%'
      	</if>
      <if test="appName != null and appName != ''" >
          and app_name like  '%${appName}%'
      </if>
      <if test="appVer != null and appVer != ''" >
          and app_ver like  '%${appVer}%'
      </if>
      <if test="jobAction != null and jobAction != ''" >
          and job_action like  '%${jobAction}%'
      </if>
      <if test="dlFlag != null and dlFlag != ''" >
      	and dl_flag=#{dlFlag,jdbcType=CHAR}
      </if>
      <if test="optCmdStatus != null">
		and opt_cmd_status = #{optCmdStatus,jdbcType=INTEGER}
	  </if>
  </select>
  <select id="taskListAboutReport" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask">
  		select a.term_seq,a.app_name,a.app_ver,a.dl_flag,a.result_message,
  		d.name as merchant_name,d.applyer_name,d.APPLYER_PHONE,c.province,c.city,c.county,c.address,
  		c.term_no,c.pay_merchant_no
    	from coms_download_task a
        left join coms_terminal_info c on a.term_seq = c.term_seq
        left join coms_merchant_info d on c.PAY_MERCHANT_NO = d.merchant_no
        LEFT JOIN cpay_institution e ON c.ins_id = e.id
    	where job_id = #{jobId,jdbcType=INTEGER}
    	<if test="termSeq != null and termSeq != ''" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
	    <if test="appName != null and appName != ''" >
	          and a.app_name like  '%${appName}%'
	    </if>
	    <if test="appVer != null and appVer != ''" >
	          and a.app_ver like  '%${appVer}%'
	    </if>
	    <if test="dlFlag != null and dlFlag != ''" >
	      	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
	    </if>
    	<if test="insId != null and insId != 0">
			and c.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
  </select>
  <select id="selectExcelByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
        select a.term_seq, a.app_ver,
        c.name as JOB_ACTION, a.dl_begin_date, a.dl_end_date,a.result_message,
        d.NAME as DL_FLAG,a.app_name
        from coms_download_task a
        left join (SELECT * FROM CPAY_DICT WHERE PARENT_ID = (SELECT id FROM CPAY_DICT WHERE TYPE = 'job_action')) c
        on c.type = a.JOB_ACTION
        left join (SELECT * FROM CPAY_DICT WHERE PARENT_ID = (SELECT id FROM CPAY_DICT WHERE TYPE = 'taskStatus')) d
        on d.type = a.DL_FLAG
        where job_id = #{jobId,jdbcType=INTEGER}
        <if test="termSeq != null and termSeq != ''" >
            and term_seq like  '%${termSeq}%'
        </if>
        <if test="appName != null and appName != ''" >
            and app_name like  '%${appName}%'
        </if>
        <if test="appVer != null and appVer != ''" >
            and app_ver like  '%${appVer}%'
        </if>
        <if test="jobAction != null and jobAction != ''" >
            and job_action like  '%${jobAction}%'
        </if>
        <if test="dlFlag != null and dlFlag != ''" >
            and dl_flag=#{dlFlag,jdbcType=CHAR}
        </if>
    </select>
    <select id="exportExcelAboutReport" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
        select a.term_seq,a.app_name,a.app_ver,f.name AS dl_flag,a.result_message,
        d.name as merchant_name,d.applyer_name,d.APPLYER_PHONE,c.province,c.city,c.county,c.address,c.term_no,c.pay_merchant_no
    	from coms_download_task a
        left join coms_terminal_info c on a.term_seq = c.term_seq
        left join coms_merchant_info d on c.PAY_MERCHANT_NO = d.merchant_no
        LEFT JOIN cpay_institution e ON c.ins_id = e.id
        left join (SELECT * FROM CPAY_DICT WHERE PARENT_ID = (SELECT id FROM CPAY_DICT WHERE TYPE = 'taskStatus')) f
        on f.type = a.DL_FLAG
    	where job_id = #{jobId,jdbcType=INTEGER}
    	<if test="termSeq != null and termSeq != ''" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
	    <if test="appName != null and appName != ''" >
	          and a.app_name like  '%${appName}%'
	    </if>
	    <if test="appVer != null and appVer != ''" >
	          and a.app_ver like  '%${appVer}%'
	    </if>
	    <if test="dlFlag != null and dlFlag != ''" >
	      	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
	    </if>
    	<if test="insId != null and insId != 0">
			and c.ins_id in(SELECT s1.id from cpay_institution s1
			where locate((SELECT s2.detail from cpay_institution
			s2 where
			s2.id=#{insId}),s1.detail)>0)
		</if>
    </select>
    
    
    
  <select id="getMaxJobId" resultType="java.lang.Integer">
     SELECT MAX(job_id) FROM coms_download_task
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_download_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_download_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    insert into coms_download_task ( job_id, term_seq, 
      terminal_type_id, manufacturer_id, app_code, 
      app_type, app_ver, app_file_name, 
      url, md5, dl_flag, dl_begin_date, 
      dl_end_date, release_time, valid_date, 
      record_create_time,isShow_notice,size,job_action,app_name)
    values ( #{jobId,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR}, 
      #{terminalTypeId,jdbcType=INTEGER}, #{manufacturerId,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, 
      #{appType,jdbcType=CHAR}, #{appVer,jdbcType=VARCHAR}, #{appFileName,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, #{dlFlag,jdbcType=CHAR}, #{dlBeginDate,jdbcType=VARCHAR}, 
      #{dlEndDate,jdbcType=VARCHAR}, #{releaseTime,jdbcType=VARCHAR}, #{validDate,jdbcType=VARCHAR}, 
      #{recordCreateTime,jdbcType=VARCHAR},#{isShowNotice,jdbcType=CHAR},#{size,jdbcType=INTEGER}
      ,#{jobAction,jdbcType=CHAR},#{appName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    insert into coms_download_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        job_id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="terminalTypeId != null" >
        terminal_type_id,
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="appType != null" >
        app_type,
      </if>
      <if test="appVer != null" >
        app_ver,
      </if>
      <if test="appFileName != null" >
        app_file_name,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="md5 != null" >
        md5,
      </if>
      <if test="dlFlag != null" >
        dl_flag,
      </if>
      <if test="dlBeginDate != null" >
        dl_begin_date,
      </if>
      <if test="dlEndDate != null" >
        dl_end_date,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
      <if test="validDate != null" >
        valid_date,
      </if>
      <if test="recordCreateTime != null" >
        record_create_time,
      </if>
      <if test="isShowNotice != null" >
        isShow_notice,
      </if>
      <if test="size != null" >
        size,
      </if>
      <if test="jobAction != null">
          job_action,
      </if>
      <if test="appName != null">
          app_name,
      </if>
      <if test="dependTaskId != null">
          depend_task_id,
      </if>
      <if test="dependTaskIds != null">
          depend_task_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="terminalTypeId != null" >
        #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="manufacturerId != null" >
        #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appType != null" >
        #{appType,jdbcType=CHAR},
      </if>
      <if test="appVer != null" >
        #{appVer,jdbcType=VARCHAR},
      </if>
      <if test="appFileName != null" >
        #{appFileName,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="dlFlag != null" >
        #{dlFlag,jdbcType=CHAR},
      </if>
      <if test="dlBeginDate != null" >
        #{dlBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="dlEndDate != null" >
        #{dlEndDate,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=VARCHAR},
      </if>
      <if test="validDate != null" >
        #{validDate,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        #{recordCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="isShowNotice != null" >
        #{isShowNotice,jdbcType=CHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=INTEGER},
      </if>
      <if test="jobAction != null">
        #{jobAction,jdbcType=CHAR},
      </if>
      <if test="appName != null" >
          #{appName,jdbcType=VARCHAR},
      </if>
      <if test="dependTaskId != null" >
          #{dependTaskId,jdbcType=VARCHAR},
      </if>
      <if test="dependTaskIds != null" >
          #{dependTaskIds,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    update coms_download_task
    <set >
      <if test="jobId != null" >
        job_id = #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="terminalTypeId != null" >
        terminal_type_id = #{terminalTypeId,jdbcType=INTEGER},
      </if>
      <if test="manufacturerId != null" >
        manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="appType != null" >
        app_type = #{appType,jdbcType=CHAR},
      </if>
      <if test="appVer != null" >
        app_ver = #{appVer,jdbcType=VARCHAR},
      </if>
      <if test="appFileName != null" >
        app_file_name = #{appFileName,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null" >
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="dlFlag != null" >
        dl_flag = #{dlFlag,jdbcType=CHAR},
      </if>
      <if test="dlBeginDate != null" >
        dl_begin_date = #{dlBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="dlEndDate != null" >
        dl_end_date = #{dlEndDate,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=VARCHAR},
      </if>
      <if test="validDate != null" >
        valid_date = #{validDate,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        record_create_time = #{recordCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="isShowNotice != null" >
        isShow_notice = #{isShowNotice,jdbcType=CHAR},
      </if>
      <if test="jobAction != null" >
        job_action = #{jobAction,jdbcType=CHAR},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR}
      </if>
      <if test="optCmdStatus != null" >
        opt_cmd_status = #{optCmdStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" >
    update coms_download_task
    set job_id = #{jobId,jdbcType=INTEGER},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      terminal_type_id = #{terminalTypeId,jdbcType=INTEGER},
      manufacturer_id = #{manufacturerId,jdbcType=INTEGER},
      app_code = #{appCode,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=CHAR},
      app_ver = #{appVer,jdbcType=VARCHAR},
      app_file_name = #{appFileName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      dl_flag = #{dlFlag,jdbcType=CHAR},
      dl_begin_date = #{dlBeginDate,jdbcType=VARCHAR},
      dl_end_date = #{dlEndDate,jdbcType=VARCHAR},
      release_time = #{releaseTime,jdbcType=VARCHAR},
      valid_date = #{validDate,jdbcType=VARCHAR},
      record_create_time = #{recordCreateTime,jdbcType=VARCHAR},
      isShow_notice = #{isShowNotice,jdbcType=CHAR},
      job_action = #{jobAction,jdbcType=CHAR},
      app_name = #{appName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="issuedAgain" parameterType="java.util.List">
		update  coms_download_task set dl_flag = '0',down_time=null,DL_BEGIN_DATE=null,DL_END_DATE=null where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
        and dl_flag in ('9','6')
  </update>
    <update id="issuedAgainAll" parameterType="java.lang.Integer">
        update  coms_download_task set dl_flag = '0',down_time=null,DL_BEGIN_DATE=null,DL_END_DATE=null where job_id=#{id,jdbcType=INTEGER}
        and dl_flag in ('9','6')
    </update>
  <update id="issuedCancel" parameterType="java.util.List">
		update  coms_download_task set dl_flag = '9', down_time=null where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>

  </update>
  <update id="issuedCancelAll" parameterType="java.lang.Integer" >
		update coms_download_task set dl_flag = '9', down_time=null where job_id=#{id,jdbcType=INTEGER} and dl_flag in ('0','1','6')
  </update>
  <select id="getApkPushList" parameterType="java.util.Map" resultMap="BaseResultMap"> 
  	select a.*,a.app_file_name as app_name from coms_download_task a  
	where a.app_type != 7
	  <![CDATA[
        and job_id >= 300000000 and job_id <=600000000
      ]]>
	 and term_seq in (
	select term_seq from coms_terminal_info c where 1=1 
    and c.ins_id in(SELECT s1.id from cpay_institution s1 where    
    locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0))
    	<if test="termSeq != null" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
    	<if test="appName != null" >
        	and b.app_name like  '%${appName}%'
      	</if>
      	<if test="appVer != null" >
        	and a.app_ver like  concat '%${appVer}%'
      	</if>
      	<if test="dlFlag != null and dlFlag !=''" >
        	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
      	</if>
	</select>

   <select id="getApkPushDelList" parameterType="java.util.Map" resultMap="BaseResultMap"> 
  	select a.*,a.app_file_name as app_name from coms_download_task a 
	where 1=1 
	  <![CDATA[
        and job_id >= 300000000 and job_id <=600000000
      ]]>
	and term_seq in (
	select term_seq from coms_terminal_info c where 1=1 
    and c.ins_id in(SELECT s1.id from cpay_institution s1 where  
    locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0))
    	<if test="termSeq != null" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
    	<if test="appName != null" >
        	and b.app_name like '%${appName}%'
      	</if>
      	<if test="appVer != null" >
        	and a.app_ver like   '%${appVer}%'
      	</if>
      	<if test="dlFlag != null and dlFlag !=''" >
        	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
      	</if>
      	<if test="appType != null and appType !=''" >
        	and a.app_type=#{appType,jdbcType=CHAR}
      	</if>
	</select>

    

	 <select id="getBootConfList" parameterType="java.util.Map" resultMap="BaseResultMap"> 
  	    select a.* from coms_download_task a where 1=1 
  	    <![CDATA[
        and job_id >= 600000000 and job_id <=999999999
        ]]>
    	<if test="termSeq != null" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
    	<if test="appCode != null" >
        	and a.app_code like  '%${appCode}%'
      	</if>
      	<if test="dlFlag != null and dlFlag != ''" >
        	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
      	</if>
      	and term_seq in (
				select term_seq from coms_terminal_info c where 1=1 
    			and c.ins_id in(SELECT s1.id from cpay_institution s1 where    
    			locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0))
	</select>
	<select id="queryTaskStatus"  resultType="java.lang.Integer" parameterType="java.lang.Integer">
		select count(*) from coms_download_task where job_id=#{id,jdbcType=INTEGER} and dl_flag  in ('3','2')
	</select>
    <select id="queryCountTasks"  resultType="java.lang.Integer" parameterType="java.lang.Integer">
		select count(*) from coms_download_task where job_id=#{id,jdbcType=INTEGER}
	</select>
	<delete id="deleteApkPush" parameterType="java.util.List">
		delete from  coms_download_task where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
	<delete id="deleteByTaskByJobId" parameterType="java.lang.Integer" >
		delete from coms_download_task where job_id=#{id,jdbcType=INTEGER}
  	</delete>
    <delete id="deleteTaskBatch" parameterType="java.util.Map" >
		delete from coms_download_task where job_id=#{jobId,jdbcType=INTEGER} limit #{rows}
  	</delete>
    <delete id="delayTaskBatch" parameterType="java.util.Map">
        update coms_download_task set VALID_DATE = #{validDate,jdbcType=VARCHAR}
        where job_id=#{jobId,jdbcType=INTEGER}
		limit #{offset},#{rows}
    </delete>
    <select id="getLauncherAppPushList" parameterType="java.util.Map" resultMap="BaseResultMap">
		select a.* from coms_download_task a where 1=1 and a.app_type=6 or a.app_type=8
    	<if test="termSeq != null" >
        	and a.term_seq like  '%${termSeq}%'
      	</if>
    	<if test="appCode != null" >
        	and a.app_code like  '%${appCode}%'
      	</if>
      	<if test="dlFlag != null and dlFlag != ''" >
        	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
      	</if>
      	and a.term_seq in (
				select term_seq from coms_terminal_info c where 1=1 
    			and c.ins_id in(SELECT s1.id from cpay_institution s1 where    
    			locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0))
	</select>
	<select id="getThemeAppPushList" parameterType="java.util.Map" resultMap="BaseResultMap">
		select a.* from coms_download_task a where 1=1 and a.app_type='8'
    	<if test="termSeq != null" >
        	and a.term_seq like '%${termSeq}%'
      	</if>
    	<if test="appCode != null" >
        	and a.app_code like  '%${appCode}%'
      	</if>
      	<if test="dlFlag != null and dlFlag != ''" >
        	and a.dl_flag=#{dlFlag,jdbcType=CHAR}
      	</if>
      	and a.term_seq in (
				select term_seq from coms_terminal_info c where 1=1 
    			and c.ins_id in(SELECT s1.id from cpay_institution s1 where  
    			locate((SELECT s2.detail from cpay_institution s2 where s2.id=${insId}),s1.detail)>0))
	</select>
	<select id="queryTaskUniqueByTermSeq" parameterType="com.centerm.cpay.coms.dao.pojo.DriverJobTask" resultType="java.lang.Integer">
		select count(*) from coms_download_task where term_seq=#{termSeq,jdbcType=VARCHAR} and job_id=#{jobId,jdbcType=INTEGER}
		and depend_task_id = #{dependTaskId,jdbcType=VARCHAR}
	</select>

    <!-- 任务插入 (表转移方式)-->
    <insert id="insertTras" parameterType="java.util.Map" >
        insert into COMS_DOWNLOAD_TASK (
        job_id,
        terminal_type_id, manufacturer_id, app_code,
        app_type, app_ver, app_file_name,
        url, md5, dl_flag, dl_begin_date,
        dl_end_date, release_time, valid_date,
        record_create_time,isShow_notice,size,job_action,app_name,depend_task_id,depend_task_ids,term_seq)
        (select #{jobId,jdbcType=INTEGER},
        #{terminalTypeId,jdbcType=INTEGER}, #{manufacturerId,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR},
        #{appType,jdbcType=CHAR}, #{appVer,jdbcType=VARCHAR}, #{appFileName,jdbcType=VARCHAR},
        #{url,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, #{dlFlag,jdbcType=CHAR}, #{dlBeginDate,jdbcType=VARCHAR},
        #{dlEndDate,jdbcType=VARCHAR}, #{releaseTime,jdbcType=VARCHAR}, #{validDate,jdbcType=VARCHAR},
        #{recordCreateTime,jdbcType=VARCHAR},#{isShowNotice,jdbcType=CHAR},#{size,jdbcType=INTEGER}
        ,#{jobAction,jdbcType=CHAR},#{appName,jdbcType=VARCHAR},#{dependTaskId,jdbcType=VARCHAR},#{dependTaskIds,jdbcType=VARCHAR},
        TERM_SEQ
        from coms_terminal_info a
        left join coms_terminal_type c on c.code = a.term_type_code
        left join COMS_MERCHANT_INFO d on d.MERCHANT_NO = a.PAY_MERCHANT_NO
        where 1=1
        <if test="manufacturerId != null" >
            and  a.term_mfr_id = #{manufacturerId,jdbcType=INTEGER}
        </if>
        <if test="dccSupFlag != null and dccSupFlag != ''" >
            and  d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
        </if>
        <if test="cupConnMode != null and cupConnMode != ''">
            and d.CUP_CONN_MODE = #{cupConnMode,jdbcType=CHAR}
        </if>
        <if test="bussType != null and bussType != ''">
            and d.BUSS_TYPE = #{bussType,jdbcType=VARCHAR}
        </if>
        <if test="termTypeArr != null">
            and c.code in
            <foreach item="item" index="index" collection="termTypeArr.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="activateTypes != null and activateTypes != ''" >
            and  a.activate_type in
            <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insId != null">
            and a.ins_id in (SELECT s1.id from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insId}),s1.detail)>0)
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '0'.toString()">
            and a.term_group_id in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        <if test="groupIds != null and groupIds != '' and isGroupUpdate == '1'.toString()">
            and a.term_group_id not in
            <foreach item="item" index="index" collection="groupIds.split(',')" open="(" separator="," close=")">
                '${item}'
            </foreach>
        </if>
        limit #{offset},#{rows}
        )

    </insert>


    <!--批量任务sql提交-->
    <update id="submitTran">
  	commit
  </update>
</mapper>