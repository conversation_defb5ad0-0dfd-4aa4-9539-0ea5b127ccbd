
package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.RoleFunctionMapper;
import com.centerm.cpay.coms.dao.mapper.RoleMenuMapper;
import com.centerm.cpay.coms.dao.pojo.RoleFunction;
import com.centerm.cpay.coms.dao.pojo.RoleMenu;
import com.centerm.cpay.coms.service.RoleFunctionService;
import com.centerm.cpay.coms.service.RoleMenuService;

/**
 * @Title: RoleFunctionServiceImpl.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年6月7日 下午5:26:48
 * @version V1.0
 */
@Service
public class RoleMenuServiceImpl implements RoleMenuService {
	@Autowired
	private RoleMenuMapper roleMenuMapper;

	@Override
	public ResultMsg deleteRoleMenu(Integer roleId) {
		roleMenuMapper.deleteByRoleId(roleId);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg insertRoleMenu(RoleMenu roleMenus) {
		roleMenuMapper.deleteByRoleId(roleMenus.getRoleId());
		String[] functionArray = roleMenus.getFunctions().split(",");
		for (String menuId : functionArray) {
			if (menuId == "") {
				continue;
			}
			RoleMenu roleMenu = new RoleMenu();
			roleMenu.setMenuId(Integer.valueOf(menuId));
			roleMenu.setRoleId(roleMenus.getRoleId());
			roleMenuMapper.insert(roleMenu);
		}
		return ResultMsg.success();
	}

	@Override
	public List<RoleMenu> getRoleMenuList(Integer roleId) {
		return roleMenuMapper.getRoleMenuList(roleId);
	}

}
