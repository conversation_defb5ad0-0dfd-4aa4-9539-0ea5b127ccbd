package com.centerm.cpay.coms.dao.pojo;

import lombok.ToString;

@ToString
public class ComsAppVersion {
    private Integer id;

    private Integer appId;

    private String appName;

    private Integer factoryId;

    private String factoryName;

    //内部版本
    private String appVersion;
    //外部版本
    private String appVersionName;
    //上传文件名称
    private String uploadFileName;

    private String fileName;

    private String versionStatus;

    private String md5;

    private String appPath;

    private String remark;

    private String operFlag;
    
    private String type;
    
    private String appCode;
    
    private Integer size;
    
    private Integer insId;
    
    private String insName;
    
    private String iconPath;
    
    private String mainActivity;

    private String uploadStrategy;

    private String uploadStatus;

    //临时字段
    private String code;
    
    private String ngsPicPath;

    private String termTypeCodeStr;
    private String termTypeNameStr;
    //是否支持DCC
    private String dccSupFlag;

    //银联间直连
    private String cupConnMode;

    public String getUploadStrategy() {
        return uploadStrategy;
    }

    public void setUploadStrategy(String uploadStrategy) {
        this.uploadStrategy = uploadStrategy;
    }

    public String getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(String uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public String getCupConnMode() {
        return cupConnMode;
    }

    public void setCupConnMode(String cupConnMode) {
        this.cupConnMode = cupConnMode;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }

    public String getUploadFileName() {
        return uploadFileName;
    }

    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName;
    }

    public String getDccSupFlag() {
        return dccSupFlag;
    }

    public void setDccSupFlag(String dccSupFlag) {
        this.dccSupFlag = dccSupFlag;
    }

    public String getTermTypeCodeStr() {
        return termTypeCodeStr;
    }

    public void setTermTypeCodeStr(String termTypeCodeStr) {
        this.termTypeCodeStr = termTypeCodeStr;
    }

    public String getTermTypeNameStr() {
        return termTypeNameStr;
    }

    public void setTermTypeNameStr(String termTypeNameStr) {
        this.termTypeNameStr = termTypeNameStr;
    }

    public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public Integer getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Integer factoryId) {
        this.factoryId = factoryId;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName == null ? null : factoryName.trim();
    }


    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion == null ? null : appVersion.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getVersionStatus() {
        return versionStatus;
    }

    public void setVersionStatus(String versionStatus) {
        this.versionStatus = versionStatus == null ? null : versionStatus.trim();
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5 == null ? null : md5.trim();
    }

    public String getAppPath() {
        return appPath;
    }

    public void setAppPath(String appPath) {
        this.appPath = appPath == null ? null : appPath.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getOperFlag() {
        return operFlag;
    }

    public void setOperFlag(String operFlag) {
        this.operFlag = operFlag == null ? null : operFlag.trim();
    }

	public String getAppCode() {
		return appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public Integer getSize() {
		return size;
	}

	public void setSize(Integer size) {
		this.size = size;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getNgsPicPath() {
		return ngsPicPath;
	}

	public void setNgsPicPath(String ngsPicPath) {
		this.ngsPicPath = ngsPicPath;
	}

	public Integer getInsId() {
		return insId;
	}

	public void setInsId(Integer insId) {
		this.insId = insId;
	}

	public String getInsName() {
		return insName;
	}

	public void setInsName(String insName) {
		this.insName = insName;
	}

	public String getIconPath() {
		return iconPath;
	}

	public void setIconPath(String iconPath) {
		this.iconPath = iconPath;
	}

	public String getMainActivity() {
		return mainActivity;
	}

	public void setMainActivity(String mainActivity) {
		this.mainActivity = mainActivity;
	}

    
}