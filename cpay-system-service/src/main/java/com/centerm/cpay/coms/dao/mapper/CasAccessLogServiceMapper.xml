<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.CasAccessLogServiceMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.CasAccessLog" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="timestamp" property="timestamp" jdbcType="CHAR" />
    <result column="random" property="random" jdbcType="CHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="CHAR" />
    <result column="request_length" property="requestLength" jdbcType="INTEGER" />
    <result column="reponse_length" property="reponseLength" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, term_seq, timestamp, random, url, result, request_length, reponse_length
  </sql>
  
  <select id="selectByCondition" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog" resultMap="BaseResultMap">
  	select 
  		<include refid="Base_Column_List"/> 
  	from (select * from coms_cas_access_log where 1=1
  	<if test="termSeq != null and termSeq !='' " >
     	and term_seq =  #{termSeq,jdbcType=VARCHAR}
   	</if> 
   	order by id desc limit 1000) t
  	where 1=1 
	<if test="termSeq != null and termSeq !='' " >
     	and term_seq =  #{termSeq,jdbcType=VARCHAR}
   	</if>
    ORDER BY id desc
  </select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_cas_access_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_cas_access_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog" >
    insert into coms_cas_access_log ( term_seq, timestamp, 
      random, url, result, request_length, 
      reponse_length)
    values ( #{termSeq,jdbcType=VARCHAR}, #{timestamp,jdbcType=CHAR}, 
      #{random,jdbcType=CHAR}, #{url,jdbcType=VARCHAR}, #{result,jdbcType=CHAR}, #{requestLength,jdbcType=INTEGER}, 
      #{reponseLength,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog" >
    insert into coms_cas_access_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="timestamp != null" >
        timestamp,
      </if>
      <if test="random != null" >
        random,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="requestLength != null" >
        request_length,
      </if>
      <if test="reponseLength != null" >
        reponse_length,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="timestamp != null" >
        #{timestamp,jdbcType=CHAR},
      </if>
      <if test="random != null" >
        #{random,jdbcType=CHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=CHAR},
      </if>
      <if test="requestLength != null" >
        #{requestLength,jdbcType=INTEGER},
      </if>
      <if test="reponseLength != null" >
        #{reponseLength,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog" >
    update coms_cas_access_log
    <set >
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="timestamp != null" >
        timestamp = #{timestamp,jdbcType=CHAR},
      </if>
      <if test="random != null" >
        random = #{random,jdbcType=CHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=CHAR},
      </if>
      <if test="requestLength != null" >
        request_length = #{requestLength,jdbcType=INTEGER},
      </if>
      <if test="reponseLength != null" >
        reponse_length = #{reponseLength,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog" >
    update coms_cas_access_log
    set term_seq = #{termSeq,jdbcType=VARCHAR},
      timestamp = #{timestamp,jdbcType=CHAR},
      random = #{random,jdbcType=CHAR},
      url = #{url,jdbcType=VARCHAR},
      result = #{result,jdbcType=CHAR},
      request_length = #{requestLength,jdbcType=INTEGER},
      reponse_length = #{reponseLength,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="clearCasLog1" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog">
  	insert into coms_cas_access_log_temp select * from coms_cas_access_log where timestamp &lt; #{timestamp};
  </insert>
  <delete id="clearCasLog2" parameterType="com.centerm.cpay.coms.dao.pojo.CasAccessLog">
  	delete from coms_cas_access_log where timestamp &lt; #{timestamp};
  </delete> 
</mapper>