
package com.centerm.cpay.coms.service;


import java.util.List;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.ComsCasWarrantyOrder;
/**
 * 
 * <AUTHOR>
 * @version $Id: CasWarrantOrderService.java, v 0.1 2016年7月10日 下午4:28:04 sup Exp $
 */
public interface CasWarrantOrderService {
	EUDataGridResult getGridList(ComsCasWarrantyOrder entity, int page, int rows);

	ResultMsg deleteByIds(List<String> ids);
	
	public ResultMsg createOrder(String termSeq,String orderNo,String termId );
	
	public ResultMsg queryOrderList(String termSeq);
	
	public ResultMsg updateOrder(String orderNo, String payType,String payId,String status) throws Exception;

	ComsCasWarrantyOrder getWarrantyOrder(Integer id);

	EUDataGridResult getGridBuDingList(ComsCasWarrantyOrder entity, Integer page, Integer rows);

}
