package com.centerm.cpay.coms.dao.mapper;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.coms.dao.pojo.SystemClean;
import org.apache.ibatis.annotations.Param;

public interface SystemCleanMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SystemClean record);

    int insertSelective(SystemClean record);

    SystemClean selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SystemClean record);

    int updateByPrimaryKey(SystemClean record);

	List<SystemClean> selectByCondition(SystemClean systemClean);

	List<SystemClean> queryTableInfoById(Integer id);

	void deleteInfo(Map map);

	void deleteSystemClean(List<String> idsList);

	Integer getCleanAmount(String insCd);

	void cleanTerm(@Param("string") String string);

	void cleanOrg(@Param("id") Integer insId);

	List<Map> queryAllIdByinsId(Integer insId);

	void deleteOrgbyId(Integer id);
}