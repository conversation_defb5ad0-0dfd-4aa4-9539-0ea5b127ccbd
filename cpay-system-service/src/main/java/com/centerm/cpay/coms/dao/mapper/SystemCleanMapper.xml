<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.SystemCleanMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.SystemClean" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="module" property="module" jdbcType="VARCHAR" />
    <result column="table_column" property="tableColumn" jdbcType="VARCHAR" />
    <result column="table_id" property="tableId" jdbcType="INTEGER" />
    <result column="table_name" property="tableName" jdbcType="VARCHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, module, table_column, table_id, table_name, ins_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from cpay_system_clean
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="getCleanAmount" parameterType="java.lang.String" resultType="java.lang.Integer">
       select COUNT(*) from (select a.id from cpay_system_clean a LEFT JOIN cpay_institution b on a.ins_id=b.id WHERE b.detail like concat(concat('%',#{insCd}),'%'))t
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from cpay_system_clean
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.SystemClean" >
    insert into cpay_system_clean ( module, table_column, 
      table_id, table_name, ins_id
      )
    values ( #{module,jdbcType=VARCHAR}, #{tableColumn,jdbcType=VARCHAR}, 
      #{tableId,jdbcType=INTEGER}, #{tableName,jdbcType=VARCHAR}, #{insId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.SystemClean" >
    insert into cpay_system_clean
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="module != null" >
        module,
      </if>
      <if test="tableColumn != null" >
        table_column,
      </if>
      <if test="tableId != null" >
        table_id,
      </if>
      <if test="tableName != null" >
        table_name,
      </if>
      <if test="insId != null" >
        ins_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="module != null" >
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="tableColumn != null" >
        #{tableColumn,jdbcType=VARCHAR},
      </if>
      <if test="tableId != null" >
        #{tableId,jdbcType=INTEGER},
      </if>
      <if test="tableName != null" >
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        #{insId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.SystemClean" >
    update cpay_system_clean
    <set >
      <if test="module != null" >
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="tableColumn != null" >
        table_column = #{tableColumn,jdbcType=VARCHAR},
      </if>
      <if test="tableId != null" >
        table_id = #{tableId,jdbcType=INTEGER},
      </if>
      <if test="tableName != null" >
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="insId != null" >
        ins_id = #{insId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.SystemClean" >
    update cpay_system_clean
    set module = #{module,jdbcType=VARCHAR},
      table_column = #{tableColumn,jdbcType=VARCHAR},
      table_id = #{tableId,jdbcType=INTEGER},
      table_name = #{tableName,jdbcType=VARCHAR},
      ins_id = #{insId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByCondition" parameterType="com.centerm.cpay.coms.dao.pojo.SystemClean" resultMap="BaseResultMap">
  		 select a.*,b.name as ins_name
    	 from cpay_system_clean a left join cpay_institution b on a.ins_id = b.id
    	 where 1=1 
    	 <if test="module != null" >
         and a.module LIKE concat (concat('%',#{module}),'%')
      	 </if>
      	 <if test="insId != null">
			and a.ins_id IN (
			SELECT s1.id FROM cpay_institution s1 WHERE
				s1.detail LIKE CONCAT(concat('%',
					(SELECT s2.detail FROM cpay_institution s2
						WHERE s2.id = #{insId,jdbcType=INTEGER}
					)),'%')
			)
	  </if>
  </select>
  <select id="queryTableInfoById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
 	select a.* from  cpay_system_clean a where id = #{id,jdbcType=INTEGER}
  </select>
    <delete id="deleteInfo" parameterType="java.util.Map">
 		delete  from  ${tableName}  where id = #{id,jdbcType=INTEGER}
  	</delete>
  	<delete id="deleteSystemClean" parameterType="java.util.List">
		delete from cpay_system_clean where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
	<delete id="cleanTerm" parameterType="java.lang.String">
        delete from coms_download_task where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_alarm_info where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_app_relation where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_boot_info where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_dyn_info where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_info where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_status where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_sysdetail where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_terminal_card_issue_param where term_seq=#{string,jdbcType=VARCHAR};
        delete from coms_upload_task where term_seq=#{string,jdbcType=VARCHAR};
  	</delete>
  	<delete id="cleanOrg" parameterType="java.lang.Integer">
        DELETE FROM coms_download_task WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_terminal_app_relation WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_terminal_boot_info WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_terminal_dyn_info WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_terminal_status WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_terminal_sysdetail WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_upload_task WHERE term_seq IN (SELECT a.term_seq FROM coms_terminal_info a WHERE a.ins_id = #{id,jdbcType=INTEGER});
        DELETE FROM coms_download_job WHERE release_ins = #{id,jdbcType=INTEGER};
        DELETE FROM coms_store_advert WHERE ins_id = #{id,jdbcType=INTEGER};
        DELETE FROM coms_store_apk_info WHERE ins_id = #{id,jdbcType=INTEGER};
        DELETE FROM coms_store_apk_info_temporary WHERE ins_id = #{id,jdbcType=INTEGER};
        DELETE FROM coms_store_apk_type_info WHERE ins_id = #{id,jdbcType=INTEGER};
        DELETE FROM coms_upload_job WHERE ins_id = #{id,jdbcType=INTEGER};
        DELETE FROM coms_terminal_info WHERE ins_id = #{id,jdbcType=INTEGER};
  	</delete>
  	<select id="queryAllIdByinsId" parameterType="java.lang.Integer" resultType="java.util.HashMap">
  		select s1.id from 
		cpay_institution s1
		WHERE
		s1.detail LIKE CONCAT(
		concat('%',
		(
			SELECT
				s2.detail
			FROM
				cpay_institution s2
			WHERE
				s2.id = #{insId,jdbcType=INTEGER}
		)),
		'%'
	)
  	</select>
  	<delete id="deleteOrgbyId" parameterType="java.lang.Integer">
  		delete from cpay_institution where id=#{id,jdbcType=INTEGER}
  	</delete>
</mapper>