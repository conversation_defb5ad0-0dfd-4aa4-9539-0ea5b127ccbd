package com.centerm.cpay.coms.enums;

import java.util.Objects;

/**
 *【文件类型】
 */
public enum DriverTypeEnum {

    //【文件类型】收单应用
    ACQUIRING_APP("a"),

    //【文件类型】通用应用
    GENERAL_APP("0"),

    //【文件类型】系统应用
    SYSTEM_APP("1"),

    //【文件类型】系统驱动
    SYSTEM_DRIVER("2"),

    //【文件类型】系统镜像
    SYSTEM_MIRROR("3"),

    //【文件类型】应用商店
    APP_STORE("9");

    private final String code;

    DriverTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    /**
     *【判断】文件类型(APK)
     * @param fileType 文件类型
     * @return 是否为系统问题
     */
    public static Boolean checkApkType(String fileType) {
        if (Objects.equals(ACQUIRING_APP.code, fileType) ||
                Objects.equals(GENERAL_APP.code, fileType) ||
                Objects.equals(SYSTEM_APP.code, fileType) ||
                Objects.equals(APP_STORE.code, fileType)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    /**
     *【判断】文件类型(系统文件)
     * @param fileType 文件类型
     * @return 是否为系统问题
     */
    public static Boolean checkSystemFile(String fileType) {
        if (Objects.equals(SYSTEM_DRIVER.code, fileType) ||
                Objects.equals(SYSTEM_MIRROR.code, fileType)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}