
package com.centerm.cpay.coms.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.date.DateUtil;
import com.centerm.cpay.coms.dao.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;

import com.centerm.cpay.common.enums.JobReleaseStatus;
import com.centerm.cpay.common.enums.TakUploadStatus;
import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.Config;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.OperationCmdEnum;
import com.centerm.cpay.common.utils.OperationResponseStatusEnum;
import com.centerm.cpay.common.utils.OperationTypeEnum;
import com.centerm.cpay.common.utils.SignUtil;
import com.centerm.cpay.common.utils.TimeUtil;
import com.centerm.cpay.coms.dao.pojo.ComsUploadJob;
import com.centerm.cpay.coms.dao.pojo.ComsUploadTask;
import com.centerm.cpay.coms.dao.pojo.Log;
import com.centerm.cpay.coms.dao.pojo.OperationRequest;
import com.centerm.cpay.coms.dao.pojo.OperationResponse;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalOperationJob;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.UploadJobService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 
 * <AUTHOR>
 * @version $Id: UploadJobServiceImpl.java, v 0.1 2016年7月6日 下午1:59:36 sup Exp $
 */
@Service
public class UploadJobServiceImpl implements UploadJobService {

	private static final Logger logger = LoggerFactory.getLogger(UploadJobServiceImpl.class);

	@Autowired
	private ComsUploadJobMapper comsUploadJobMapper;

	@Autowired
	private ComsUploadTaskMapper comsUploadTaskMapper;

	@Autowired
	private LogMapper logMapper;
	
    @Autowired
    private SysConfigureMapper sysconfigureMapper;

	@Autowired
	private TerminalMapper terminalMapper;

	@Override
	public EUDataGridResult getUploadJobList(ComsUploadJob entity, int page, int rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<ComsUploadJob> list = comsUploadJobMapper.selectByCondition(entity);
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<ComsUploadJob> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg deleteById(Integer id) {
		// TODO
		ComsUploadJob comsUploadJob = comsUploadJobMapper.selectByPrimaryKey(Integer.valueOf(id));
		if (StringUtils.equals(comsUploadJob.getReleaseStatus(), JobReleaseStatus.JOB_POST_DONING.getCode()))
			return ResultMsg.build(ResultMsg.ERROR_CODE, "作业发布中无法删除");
		comsUploadJobMapper.deleteByPrimaryKey(id);
		comsUploadTaskMapper.deleteByJobId(id);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg deleteJobs(List<String> ids) {
		for (int i = 0; i < ids.size(); i++) {
			ComsUploadJob comsUploadJob = comsUploadJobMapper.selectByPrimaryKey(Integer.valueOf(ids.get(i)));
			if (StringUtils.equals(comsUploadJob.getReleaseStatus(), JobReleaseStatus.JOB_POST_DONING.getCode()))
				return ResultMsg.build(ResultMsg.ERROR_CODE, "作业发布中无法删除");
			if (i == ids.size() - 1)
				comsUploadJobMapper.deleteByIds(ids);
		}
		comsUploadTaskMapper.deleteByJobIds(ids);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg insertEntity(ComsUploadJob entity) {
		//【判断】是否为日志抓取功能(日志抓取需要判断时间间隔)
		if (!Objects.equals(entity.getType(), "03")) {
			if (entity.getGetEndDate().getTime() < entity.getGetBeginDate().getTime()) {
				return ResultMsg.error("起始日期不得大于截止日期");
			}
		} else {
			//【配置】抓取时间（远程截图）
			entity.setGetBeginDate(DateUtil.date());
			entity.setGetEndDate(DateUtil.endOfYear(new Date()));
		}

		//【判断】任务有效期
		if (entity.getReleaseTime().getTime() > entity.getValidDate().getTime()) {
			return ResultMsg.error("有效期起始日期不得大于截止日期");
		}

		//【配置】时间参数
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String beginTime = sdf1.format(entity.getGetBeginDate()) + " 00:00:00";
		String endTime = sdf1.format(entity.getGetEndDate()) + " 23:59:59";
		try {
			entity.setGetBeginDate(sdf2.parse(beginTime));
			entity.setGetEndDate(sdf2.parse(endTime));
		} catch (ParseException e) {

			e.printStackTrace();
		}
		entity.setReleaseStatus(JobReleaseStatus.JOB_POST_PENDING.getCode()); // 等待发布
		entity.setReleaseType("0"); // 暂不使用
		entity.setRecordStatus("0"); // 暂不使用
		entity.setOperateSign("1"); // 暂不使用
		entity.setRecordCreateTime(new Date());
		entity.setRecordUpdateTime(new Date());
		comsUploadJobMapper.insert(entity);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg updateByEntity(ComsUploadJob entity) {
		// TODO
		ComsUploadJob comsUploadJob = comsUploadJobMapper.selectByPrimaryKey(Integer.valueOf(entity.getId()));
		switch (JobReleaseStatus.getEnums(comsUploadJob.getReleaseStatus())) {
		case JOB_POST_DONING:
			return ResultMsg.build(ResultMsg.ERROR_CODE, "作业发布中无法修改");
		case JOB_POST_SUCESS:
			return ResultMsg.build(ResultMsg.ERROR_CODE, "作业发布成功无法修改");
		default:
			//【判断】是否为日志抓取功能(日志抓取需要判断时间间隔)
			if (!Objects.equals(entity.getType(), "03")) {
				if (entity.getGetEndDate().getTime() < entity.getGetBeginDate().getTime()) {
					return ResultMsg.error("起始日期不得大于截止日期");
				}
			} else {
				//【配置】抓取时间（远程截图）
				entity.setGetBeginDate(DateUtil.date());
				entity.setGetEndDate(DateUtil.endOfYear(new Date()));
			}
			if(entity.getReleaseTime().getTime() > entity.getValidDate().getTime()){
				return ResultMsg.error("有效期起始日期不得大于截止日期");
			}
			SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String beginTime = sdf1.format(entity.getGetBeginDate()) + " 00:00:00";
			String endTime = sdf1.format(entity.getGetEndDate()) + " 23:59:59";
			try {
				entity.setGetBeginDate(sdf2.parse(beginTime));
				entity.setGetEndDate(sdf2.parse(endTime));
			} catch (ParseException e) {
				e.printStackTrace();
			}
			comsUploadJobMapper.updateByPrimaryKeySelective(entity);
			return ResultMsg.success();
		}
	}

	@Override
	public ComsUploadJob getJob(Integer id) {
		ComsUploadJob entity = comsUploadJobMapper.selectByPrimaryKey(id);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String beginTime = sdf.format(entity.getGetBeginDate());
		String endTime = sdf.format(entity.getGetEndDate());
		try {
			entity.setGetBeginDate(sdf.parse(beginTime));
			entity.setGetEndDate(sdf.parse(endTime));
		} catch (ParseException e) {

			e.printStackTrace();
		}
		return entity;
	}

	@Override
	public ResultMsg updateJobToTask(ComsUploadJob comsUploadJob) {
		ComsUploadJob job = comsUploadJobMapper.selectByPrimaryKey(Integer.valueOf(comsUploadJob.getId()));
		switch (JobReleaseStatus.getEnums(job.getReleaseStatus())) {
		case JOB_POST_DONING:
			return ResultMsg.build(ResultMsg.ERROR_CODE, "作业发布中");
		case JOB_POST_SUCESS:
			return ResultMsg.build(ResultMsg.ERROR_CODE, "作业已发布");
		default:
			// 1. 设置任务状态为发布中
			generationResult(comsUploadJob, JobReleaseStatus.JOB_POST_DONING);
			// 2. 任务发布、设置发布结果
			generationResult(comsUploadJob, processTask(comsUploadJob));
			return ResultMsg.success();
		}
	}

	private void generationResult(ComsUploadJob comsUploadJob, JobReleaseStatus status) {
		comsUploadJob.setReleaseStatus(status.getCode());
		comsUploadJob.setRecordUpdateTime(new Date());
		comsUploadJobMapper.updateByPrimaryKeySelective(comsUploadJob);
	}
	

	private JobReleaseStatus processTask(ComsUploadJob comsUploadJob) {
		List<ComsUploadTask> comsUploadTasks = new ArrayList<>();
		Stream.of(StringUtils.split(comsUploadJob.getCollection(), ",")).collect(Collectors.toList()).stream()
				.forEach(e -> {
					ComsUploadTask comsUploadTask = new ComsUploadTask();
					comsUploadTask.setJobId(comsUploadJob.getId());
					comsUploadTask.setTermSeq(e);
					comsUploadTask.setAppCode(comsUploadJob.getAppCode());
					comsUploadTask.setType(comsUploadJob.getType());
					comsUploadTask.setGetBeginDate(comsUploadJob.getGetBeginDate());
					comsUploadTask.setGetEndDate(comsUploadJob.getGetEndDate());
					comsUploadTask.setUploadFlag(TakUploadStatus.TASK_PENDING_DOWN.getCode());
					comsUploadTask.setReleaseTime(comsUploadJob.getReleaseTime());
					comsUploadTask.setValidDate(comsUploadJob.getValidDate());
					comsUploadTask.setOptCmdStatus(0);
					comsUploadTasks.add(comsUploadTask);

					//【推送】IoT任务
					Terminal terminal = terminalMapper.selectByTermSeq(e);
					if (terminal != null && "1".equals(terminal.getNetworkStatus())) {
						try {
							boolean result = sendOperationToIot(comsUploadTask);
							if (result) comsUploadTask.setOptCmdStatus(1);
						} catch (Exception exception) {
							logger.error("【IoT推送】发生异常原因为：{}", exception.getMessage());
						}
					}
				});
		try {
			comsUploadTaskMapper.batchInsertSelective(comsUploadTasks);
		} catch (Exception e) {
			return JobReleaseStatus.JOB_POST_FAILURE;
		}
		return JobReleaseStatus.JOB_POST_SUCESS;
	}

	@Override
	public ResultMsg insertSysLog(User user, String optCode) {
		Log log = new Log();
		log.setUserName(user.getUsername());
		log.setAction(optCode);
		log.setModule("O&M Management");
		log.setOpTime(TimeUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
		log.setOpDesc("Terminal Remote Extract-" + optCode);
		logMapper.insert(log);
		return ResultMsg.success();
	}

	@Override
	public List<Map> queryTerminalExist(String termSeq) {
		return comsUploadTaskMapper.queryTerminalExist(termSeq);
	}

	@Override
	public List<Map> queryTerminalForExist(Terminal terminal) {
		return comsUploadTaskMapper.queryTerminalForExist(terminal);
	}

	/**
	 *【IoT推送】下载任务请求发送
	 * @param task 任务
	 * @return 推送结果
	 */
	private boolean sendOperationToIot(ComsUploadTask task) {
		//【配置】IoT请求体
		OperationRequest operationRequest = new OperationRequest();
		operationRequest.setVersion("1.0.0");
		operationRequest.setCid(Config.CID);
		operationRequest.setSn(task.getTermSeq());
		operationRequest.setCommand("00");
		operationRequest.setRandom(CtUtils.getRandom(6));
		operationRequest.setType(OperationTypeEnum.DOWNLOAD.getValue());

		//【签名】IoT请求体
		Map requetMap = JSONUtil.toBean(JSONUtil.toJsonStr(operationRequest), Map.class);
		String sign = null;
		try {
			sign = SignUtil.generateSignature(requetMap, Config.IOT_KEY, "HMAC-SHA256");
		} catch (Exception e) {
			logger.error("【IoT推送】下载任务签名异常，详细原因为：「{}」", e.getMessage());
		}
		operationRequest.setSign(sign);

		//【发生】请求体
		String iotUrl = sysconfigureMapper.selectByKey("iotUrl").getValue();
		String request = JSONUtil.toJsonStr(operationRequest);
		logger.info("【IoT推送】下载任务请求报文体为：{}", request);
		String response = HttpUtil.post(iotUrl , request);
		logger.info("【IoT推送】下载任务响应报文体为：{}", response);

		//【处理】响应参数
		if (response == null || "".equals(response)) {
			return false;
		}
		OperationResponse operationResponse = JSONUtil.toBean(response, OperationResponse.class);
		if (operationResponse == null) {
			return false;
		}
		return OperationResponseStatusEnum.SUCCESS.getCode().equals(operationResponse.getStatus());
	}
}