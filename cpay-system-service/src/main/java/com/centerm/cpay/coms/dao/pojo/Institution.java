package com.centerm.cpay.coms.dao.pojo;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

public class Institution {
	private Integer id;

	private String code;

	@NotNull(message = "机构名称不能为空")
	@Max(value = 50, message = "长度不能超过50")
	private String name;

	private Integer parentId;
	private String parentName;

	@Max(value = 50, message = "长度不能超过100")
	private String detail;

	@NotNull(message = "机构状态不能为空")
	private Byte enabled;

	private Byte auto;

	private Integer defaultMerchantId;
	
	private String elements;
	
	private String publickeyPath;//公钥证书文件存放地址
	
	private String privatekeyPath;//签名私钥证书存放地址
	
	private String publickeyPathName;//公钥证书名称
	
	private String privatekeyPathName;//私钥证书名称
	
	private String publickeyMd5;//公钥文件的MD5值
	
	private String privatekeyMd5;//私钥文件的MD5值
	
	private String startLimitIp;//是否打开IP登陆限制
	
	private String limitLoginIp;//限制登陆IP
	
	private String moveMonitorType; //移机监控类型
	
	private String indexType;//设置机构进入显示的标识，0，不显示进销存相关信息页面，2，显示进销存相关信息页面
	
	private String lockTheme;//设定锁机主题
	
	private Byte isMonitor;
	private Byte isLock;
	
	
	public Byte getIsMonitor() {
		return isMonitor;
	}

	public void setIsMonitor(Byte isMonitor) {
		this.isMonitor = isMonitor;
	}

	public Byte getIsLock() {
		return isLock;
	}

	public void setIsLock(Byte isLock) {
		this.isLock = isLock;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code == null ? null : code.trim();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail == null ? null : detail.trim();
	}

	public Byte getEnabled() {
		return enabled;
	}

	public void setEnabled(Byte enabled) {
		this.enabled = enabled;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public Byte getAuto() {
		return auto;
	}

	public void setAuto(Byte auto) {
		this.auto = auto;
	}

	public Integer getDefaultMerchantId() {
		return defaultMerchantId;
	}

	public void setDefaultMerchantId(Integer defaultMerchantId) {
		this.defaultMerchantId = defaultMerchantId;
	}

	public String getElements() {
		return elements;
	}

	public void setElements(String elements) {
		this.elements = elements;
	}

	public String getPublickeyPath() {
		return publickeyPath;
	}

	public void setPublickeyPath(String publickeyPath) {
		this.publickeyPath = publickeyPath;
	}

	public String getPrivatekeyPath() {
		return privatekeyPath;
	}

	public String getMoveMonitorType() {
		return moveMonitorType;
	}

	public void setMoveMonitorType(String moveMonitorType) {
		this.moveMonitorType = moveMonitorType;
	}

	public void setPrivatekeyPath(String privatekeyPath) {
		this.privatekeyPath = privatekeyPath;
	}

	public String getPublickeyPathName() {
		return publickeyPathName;
	}

	public void setPublickeyPathName(String publickeyPathName) {
		this.publickeyPathName = publickeyPathName;
	}

	public String getPrivatekeyPathName() {
		return privatekeyPathName;
	}

	public void setPrivatekeyPathName(String privatekeyPathName) {
		this.privatekeyPathName = privatekeyPathName;
	}

	public String getPublickeyMd5() {
		return publickeyMd5;
	}

	public void setPublickeyMd5(String publickeyMd5) {
		this.publickeyMd5 = publickeyMd5;
	}

	public String getPrivatekeyMd5() {
		return privatekeyMd5;
	}

	public void setPrivatekeyMd5(String privatekeyMd5) {
		this.privatekeyMd5 = privatekeyMd5;
	}

	public String getStartLimitIp() {
		return startLimitIp;
	}

	public void setStartLimitIp(String startLimitIp) {
		this.startLimitIp = startLimitIp;
	}

	public String getLimitLoginIp() {
		return limitLoginIp;
	}

	public void setLimitLoginIp(String limitLoginIp) {
		this.limitLoginIp = limitLoginIp;
	}

	public String getIndexType() {
		return indexType;
	}

	public void setIndexType(String indexType) {
		this.indexType = indexType;
	}

	public String getLockTheme() {
		return lockTheme;
	}

	public void setLockTheme(String lockTheme) {
		this.lockTheme = lockTheme;
	}
	
}