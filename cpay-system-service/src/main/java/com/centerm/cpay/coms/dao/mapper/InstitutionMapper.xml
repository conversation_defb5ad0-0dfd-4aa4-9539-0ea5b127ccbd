<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.InstitutionMapper">
	<resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.Institution">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="code" property="code" jdbcType="VARCHAR" />
		<result column="name" property="name" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="INTEGER" />
		<result column="parent_name" property="parentName" jdbcType="VARCHAR" />
		<result column="detail" property="detail" jdbcType="VARCHAR" />
		<result column="enabled" property="enabled" jdbcType="INTEGER" />
		<result column="auto" property="auto" jdbcType="INTEGER" />
		<result column="default_merchant_id" property="defaultMerchantId"
			jdbcType="INTEGER" />
		<result column="elements" property="elements" jdbcType="VARCHAR" />
		<result column="start_limit_ip" property="startLimitIp" jdbcType="VARCHAR" />
		<result column="limit_login_ip" property="limitLoginIp" jdbcType="VARCHAR" />
		<result column="move_monitor_type" property="moveMonitorType" jdbcType="VARCHAR" />
		<result column="index_type" property="indexType" jdbcType="VARCHAR" />
		<result column="lock_theme" property="lockTheme" jdbcType="VARCHAR" />
		<result column="is_monitor" property="isMonitor" jdbcType="INTEGER" />
		<result column="is_lock" property="isLock" jdbcType="INTEGER" />
		
	</resultMap>
	<sql id="Base_Column_List">
		id, code, name, parent_id, detail,
		enabled,auto,default_merchant_id,elements,start_limit_ip,limit_login_ip,
		move_monitor_type,index_type,lock_theme,is_monitor,is_lock
	</sql>
	<select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from cpay_institution
		where id = #{id,jdbcType=INTEGER}
	</select>

	<select id="selectBelowByCodeList" resultMap="BaseResultMap"
		parameterType="com.centerm.cpay.coms.dao.mapper.InstitutionMapper">
		select
		<include refid="Base_Column_List" />
		from cpay_institution
		where code like concat (concat('%',#{code}),'%')
	</select>

	<select id="getInstitutionTree" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List"></include>
		from cpay_institution
		<if test="_parameter!=null">
			WHERE locate((
			SELECT
			code
			FROM
			cpay_institution
			WHERE
			id =
			${_parameter}
			),detail)>0
		</if>
	</select>
	<select id="getInstitutionLimitTree" resultMap="BaseResultMap"
			parameterType="java.util.Map">
		select
		<include refid="Base_Column_List"/>
		from cpay_institution a
		WHERE 1=1
		<if test="pId!=null">
			and locate((
			SELECT
			DETAIL
			FROM
			cpay_institution
			WHERE
			id =
			'${pId}'
			),a.detail)>0
		</if>
		<if test="insId != null">
			and (locate((
			SELECT
			DETAIL
			FROM
			cpay_institution
			WHERE
			id =
			'${insId}'
			),a.detail)>0
			or
			locate(a.detail,(
			SELECT
			DETAIL
			FROM
			cpay_institution
			WHERE
			id =
			'${insId}'
			))>0)
		</if>
	</select>
	<select id="institutionList" resultMap="BaseResultMap"
		parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		select a.id,a.code,a.name,a.detail,a.parent_id,a.auto,b.name as
		parent_name,a.elements,a.is_monitor,a.is_lock 
		from cpay_institution a
		LEFT JOIN cpay_institution b on
		a.parent_id=
		b.id where 1=1
		<if test="id!=null">
			and a.id = #{id}
		</if>
		<if test="parentId!=null">
			and a.parent_id = #{parentId}
		</if>
		<if test="code!=null">
			and a.code like concat (concat('%',#{code}),'%')
		</if>
		<if test="name!=null">
			and a.name LIKE concat (concat('%',#{name}),'%')
		</if>
		order by code asc
	</select>

	<insert id="insertInsInf" parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		insert into
		cpay_institution(code,name,parent_id,detail,
		auto,start_limit_ip,limit_login_ip,move_monitor_type,index_type,lock_theme,is_monitor,is_lock)
		values
		(#{code,jdbcType=VARCHAR},
		#{name,jdbcType=VARCHAR},
		#{parentId,jdbcType=VARCHAR},
		#{detail,jdbcType=VARCHAR},
		#{auto,jdbcType=INTEGER},
		#{startLimitIp,jdbcType=CHAR},
		#{limitLoginIp,jdbcType=VARCHAR},
		#{moveMonitorType,jdbcType=VARCHAR},
		#{indexType,jdbcType=VARCHAR},
		#{lockTheme,jdbcType=VARCHAR},
		#{isMonitor,jdbcType=INTEGER},
		#{isLock,jdbcType=INTEGER}
		)
	</insert>

	<update id="updateInstitution" parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		update cpay_institution
		<set>
			<if test="code != null">
				code = #{code,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="parentId != null">
				parent_id = #{parentId,jdbcType=VARCHAR},
			</if>
			<if test="detail != null">
				detail = #{detail,jdbcType=VARCHAR},
			</if>
			<if test="enabled != null">
				enabled = #{enabled,jdbcType=INTEGER},
			</if>
			<if test="auto != null">
				auto = #{auto,jdbcType=INTEGER},
			</if>
			<if test="startLimitIp != null">
				start_limit_ip = #{startLimitIp,jdbcType=CHAR},
			</if>
			<if test="limitLoginIp != null">
				limit_login_ip = #{limitLoginIp,jdbcType=VARCHAR},
			</if>
			
			<if test="moveMonitorType != null">
				move_monitor_type = #{moveMonitorType,jdbcType=VARCHAR},
			</if>
			<if test="indexType != null">
				index_type = #{indexType,jdbcType=VARCHAR},
			</if>
			<if test="lockTheme != null">
				lock_theme = #{lockTheme,jdbcType=VARCHAR},
			</if>
			<if test="isMonitor != null">
				is_monitor = #{isMonitor,jdbcType=INTEGER},
			</if>
			<if test="isLock != null">
				is_lock = #{isLock,jdbcType=INTEGER},
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateOrganizationElements" parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		update cpay_institution
		<set>
			elements = #{elements,jdbcType=VARCHAR},
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	<select id="getOrganizationElements"
		resultType="java.lang.Integer">
		select id from (select id,type from cpay_dict where parent_id in
		(select id
		from cpay_dict where type ='institution_element')) a
		where
		a.type in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="item">
			#{item}
		</foreach>
	</select>
	<delete id="deleteById" parameterType="java.lang.Integer">
		delete from
		cpay_institution
		where id =
		#{id,jdbcType=INTEGER}
	</delete>

	<select id="selectByInsCode" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		select
		<include refid="Base_Column_List" />
		from cpay_institution
		where code = #{insCode,jdbcType=VARCHAR}
	</select>

	<select id="selectSuperCode" resultType="java.lang.String"
		parameterType="java.lang.Integer">
		select detail
		from cpay_institution
		where id =
		#{id,jdbcType=INTEGER}
	</select>
	<select id="validate" resultType="java.lang.Integer"
		parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		select count(*)
		from cpay_institution
		where 1=1
		<if test="code!=null">
			and code = #{code}
		</if>
		<if test="name!=null">
			and name = #{name}
		</if>
		<if test="id!=null">
			and id != #{id}
		</if>
	</select>
	<select id="validateByInsert" resultType="java.lang.Integer"
		parameterType="com.centerm.cpay.coms.dao.pojo.Institution">
		select count(*)
		from cpay_institution
		where 1=1
		<if test="code!=null">
			and code = #{code}
		</if>
		<if test="name!=null">
			or name = #{name}
		</if>
		<if test="id!=null">
			and id != #{id}
		</if>
	</select>
	<select id="querySignType" resultType="java.lang.String"
		parameterType="java.lang.Integer">
		select enabled from cpay_institution where id = #{id}
	</select>
	<select id="selectDetailId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT s1.id from cpay_institution s1
			where locate(
		(
		SELECT
		s2.detail
		FROM
		cpay_institution s2
		WHERE
		s2.id=#{insId}),s1.detail)>0
	</select>
	<select id="selectValid" resultType="java.lang.Integer" parameterType="java.util.Map">
		SELECT count(s1.id) from cpay_institution s1
            where locate((SELECT s2.detail from cpay_institution
            s2 where
            s2.id=#{insIdLogin}),s1.detail)>0
			and s1.id = #{insIdQuery}
	</select>
</mapper>