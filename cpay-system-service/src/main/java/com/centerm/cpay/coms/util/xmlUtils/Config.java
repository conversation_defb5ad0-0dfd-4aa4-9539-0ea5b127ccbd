package com.centerm.cpay.coms.util.xmlUtils;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name="config")
@XmlAccessorType(XmlAccessType.FIELD)
public class Config {

	@XmlElement(name = "name",required=true)
    private String name;
	@XmlElement(name = "version",required=true)
    private String version;
	@XmlElement(name = "versionCode",required=true)
    private String versionCode;

	@XmlElement(name = "items",required=true)
    private items items;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getVersionCode() {
		return versionCode;
	}

	public void setVersionCode(String versionCode) {
		this.versionCode = versionCode;
	}

	public items getItems() {
		return items;
	}

	public void setItems(items items) {
		this.items = items;
	}
}