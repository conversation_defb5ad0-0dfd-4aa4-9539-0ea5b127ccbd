package com.centerm.cpay.coms.factory;

import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.enums.FileUploadStrategyEnum;
import com.centerm.cpay.coms.service.FileUploadService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 *【工厂模式】文件上传模式
 * <AUTHOR>
 * @date 2024/4/15 14:00
 */
@Component
public class StorageModeFactory {

    //【环境变量】判断是否使用FastFDS
    private static final String ENABLE_FAST_DFS = ConfigInfo.IS_FASTDFS;

    //【环境变量】使用FastFDS
    private static final String ENABLE = "true";

    //【环境变量】使用FastFDS
    private static final String FILE_HEAD_PATH =  ConfigInfo.HEADER_PATH;

    @Resource
    private Map<String, FileUploadService> strategyMap = new ConcurrentHashMap<>(4);

    /**
     *【返回】文件上传模式
     * @param uploadType 上传模式
     * @return 上传方法
     */
    public FileUploadService getUploadStrategy(String uploadType) {
        //【判断】是否使用FastDFS
        if (Objects.equals(ENABLE_FAST_DFS, ENABLE)) {
            return strategyMap.get(FileUploadStrategyEnum.FASTDFS_STORAGE.getName());
        }
        return strategyMap.get(FileUploadStrategyEnum.getNameFromCode(uploadType));
    }

    /**
     *【删除】已存在文件
     * @param comsAppVersion 文件信息
     */
    public void deleteExistedFile(ComsAppVersion comsAppVersion) {
        //【FastDFS】文件删除
        if (Objects.equals(ENABLE_FAST_DFS, ENABLE)) {
            String appPath = FILE_HEAD_PATH + comsAppVersion.getAppPath();
            strategyMap.get(FileUploadStrategyEnum.FASTDFS_STORAGE.getName()).deleteDriverFile(appPath);
        }

        //【本地存储】文件删除
        if (Objects.equals(comsAppVersion.getUploadStrategy(), FileUploadStrategyEnum.LOCAL_STORAGE.getCode())) {
            String appPath = comsAppVersion.getAppPath();
            strategyMap.get(FileUploadStrategyEnum.LOCAL_STORAGE.getName()).deleteDriverFile(appPath);
        }

        //【R2存储】文件删除
        if (Objects.equals(comsAppVersion.getUploadStrategy(), FileUploadStrategyEnum.CLOUD_STORAGE.getCode())) {
            String appName = comsAppVersion.getFileName();
            strategyMap.get(FileUploadStrategyEnum.CLOUD_STORAGE.getName()).deleteDriverFile(appName);
        }
    }
}