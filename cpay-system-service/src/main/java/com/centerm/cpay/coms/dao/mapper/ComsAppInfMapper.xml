<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsAppInfMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="CHAR" />
    <result column="ins_id" property="insId" jdbcType="INTEGER" />
    <result column="ins_name" property="insName" jdbcType="CHAR" />
    <result column="icon_path" property="iconPath" jdbcType="VARCHAR" />
    <result column="dcc_sup_flag" property="dccSupFlag" jdbcType="CHAR" />
    <result column="cup_conn_mode" property="cupConnMode" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, code, name, type,ins_id,dcc_sup_flag,cup_conn_mode
  </sql>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    select 
    a.id, a.code, a.name, a.type,a.dcc_sup_flag,a.cup_conn_mode,b.name as ins_name,CONCAT(#{ngsPicPath},icon_path) as icon_path
    from coms_app_inf a
    left join cpay_institution b on a.ins_id = b.id
    where 1=1
    <if test="id != null">
			and a.id = #{id,jdbcType=INTEGER}
	</if>
   <if test="name != null">
			and a.name like concat (concat('%',#{name}),'%')
	</if>
	<if test="type != null and type !=''">
			and a.type = #{type,jdbcType=CHAR}
	</if>
	<if test="code != null">
			and a.code like concat (concat('%',#{code}),'%')
	</if>
    <if test="dccSupFlag != null and dccSupFlag !=''">
        and a.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
    </if>
    <if test="cupConnMode != null and cupConnMode !=''">
        and a.cup_conn_mode = #{cupConnMode,jdbcType=CHAR}
    </if>
	<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail
			like
			CONCAT(concat('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=${insId})),'%'))
	</if>
	order by a.id desc
  </select>
  
  <select id="selectByInsId" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    select 
    a.id, a.code, a.name, a.type,b.name as ins_name
    from coms_app_inf a left join cpay_institution b on a.ins_id = b.id 
    left join coms_app_version c on a.id = c.app_id
    where 1=1
    <if test="id != null">
			and a.id = #{id,jdbcType=INTEGER}
	</if>
	<if test="type != null and type !=''">
			and a.type = #{type,jdbcType=CHAR}
	</if>
	<if test="insId != null and insId != 0">
			and a.ins_id =#{insId,jdbcType=INTEGER}
	</if>
      <if test="dccSupFlag != null and dccSupFlag !=''">
          and a.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
      </if>
      <if test="cupConnMode != null and cupConnMode !=''">
          and a.cup_conn_mode = #{cupConnMode,jdbcType=CHAR}
      </if>
	<if test="factoryId != null and insId != 0">
			and c.factory_id =#{factoryId,jdbcType=INTEGER}
	</if>
    AND c.upload_status = '2'
	and  c.id in (select max(id) from coms_app_version where FACTORY_ID = c.factory_id group by app_id)
	order by a.id desc
  </select>
  
  
  <select id="validate" resultType="java.lang.Integer" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    select 
    count(*)
    from coms_app_inf
    where 1=1
   <if test="name != null">
			and name = #{name}
	</if>
	<if test="code != null">
			and code = #{code}
	</if>
  </select>
   <delete id="deleteByIds" parameterType="java.util.List">
		delete from coms_app_inf where id in
		<foreach collection="list" index="index" open="("  separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
    <select id="selectByAppInf" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
        select
        id, code, name, type,ins_id,CONCAT(#{ngsPicPath},icon_path) as icon_path,dcc_sup_flag,cup_conn_mode
        from coms_app_inf
        where id = #{id,jdbcType=INTEGER}
    </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_app_inf
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_app_inf
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" useGeneratedKeys="true" keyProperty="id">
    insert into coms_app_inf ( code, name, 
      type,ins_id,icon_path,dcc_sup_flag,cup_conn_mode)
    values ( #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=CHAR},#{insId,jdbcType=INTEGER},#{iconPath,jdbcType=VARCHAR},#{dccSupFlag,jdbcType=CHAR},#{cupConnMode,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    insert into coms_app_inf
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="code != null" >
        code,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="type != null" >
        type,
      </if>
        <if test="dccSupFlag != null" >
            dcc_sup_flag,
        </if>
        <if test="cupConnMode != null" >
            cup_conn_mode,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=CHAR},
      </if>
        <if test="dccSupFlag != null" >
            #{dccSupFlag,jdbcType=CHAR},
        </if>
        <if test="cupConnMode != null" >
            #{cupConnMode,jdbcType=CHAR},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    update coms_app_inf
    <set >
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=CHAR},
      </if>
        <if test="insId != null" >
            ins_id = #{insId,jdbcType=INTEGER},
        </if>
        <if test="iconPath != null" >
            icon_path = #{iconPath,jdbcType=VARCHAR},
        </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf" >
    update coms_app_inf
    set code = #{code,jdbcType=VARCHAR},
        name = #{name,jdbcType=VARCHAR}
        <if test="type != null" >
        ,type = #{type,jdbcType=CHAR}
        </if>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <select id="selectDriverApp" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
  	select  id, a.name
    from coms_app_inf a
    where 1=1 and a.type !='d'
  	<if test="insId != null and insId != 0">
			and a.ins_id in(SELECT s1.id from cpay_institution s1
			where s1.detail like
			CONCAT(concat('%',(SELECT s2.detail from cpay_institution
			s2 where s2.id=#{insId,jdbcType=VARCHAR})),'%'))
	</if> 
  </select>
   <resultMap id="AppVisionMap" type="com.centerm.cpay.coms.dao.pojo.ComsAppInf" extends="BaseResultMap" >
  		<association property="comsAppVersion" javaType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion">
			<result column="app_id" property="appId" jdbcType="INTEGER" />
		    <result column="app_name" property="appName" jdbcType="VARCHAR" />
		    <result column="factory_id" property="factoryId" jdbcType="INTEGER" />
		    <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
		    <result column="term_type_id" property="termTypeId" jdbcType="INTEGER" />
		    <result column="term_type_name" property="termTypeName" jdbcType="VARCHAR" />
		    <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
		    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
		    <result column="version_status" property="versionStatus" jdbcType="CHAR" />
		    <result column="md5" property="md5" jdbcType="VARCHAR" />
		    <result column="app_path" property="appPath" jdbcType="VARCHAR" />
		    <result column="remark" property="remark" jdbcType="VARCHAR" />
		    <result column="oper_flag" property="operFlag" jdbcType="CHAR" />
		</association>
  </resultMap>
  <select id="selectByCode" resultMap="AppVisionMap" parameterType="java.lang.String" >
    select t.* ,b.app_id, b.app_name, b.factory_id, b.factory_name, b.term_type_id, b.term_type_name, b.app_version, 
    b.file_name, b.version_status, b.md5, b.app_path, b.remark, b.oper_flag
    from coms_app_inf t ,coms_app_version b
    where t.code = #{code,jdbcType=VARCHAR}
    and t.id = b.app_id
    order by CAST(b.app_version as SIGNED) desc limit 0,1
  </select>
  <select id="queryDriverCodeExist" resultType="java.util.HashMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
  	select * from coms_app_inf
    where code = #{code,jdbcType=VARCHAR} and name = #{name,jdbcType=VARCHAR} and ins_id=#{insId,jdbcType=INTEGER}
  	 <if test="id != null" >
        and id != #{id,jdbcType=INTEGER}
    </if>
      <if test="type != null" >
          and type = #{type,jdbcType=CHAR}
      </if>
  </select>
  <select id="queryDriverCodeExistByOtherIns" resultType="java.lang.Integer" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
  	select count(*) from coms_app_inf where code = #{code,jdbcType=VARCHAR} 
  	and name = #{name,jdbcType=VARCHAR} 
  	and ins_id !=#{insId,jdbcType=INTEGER} 
  </select>
  <select id="queryDriverNameExist" resultType="java.util.HashMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
  	select * from coms_app_inf where name = #{name,jdbcType=VARCHAR}
  	 <if test="id != null" >
        and id != #{id,jdbcType=INTEGER}
    </if>
  </select>
  <select id="queryIdByCode" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
  	select * from coms_app_inf where
      code = #{code,jdbcType=VARCHAR}
      and ins_id=#{insId,jdbcType=INTEGER}
      <if test="type != null" >
          and type = #{type,jdbcType=CHAR}
      </if>
</select>
  <update id="updateByCode" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppInf">
      update coms_app_inf set 
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=CHAR},
      icon_path = #{iconPath,jdbcType=VARCHAR}
      where code = #{code,jdbcType=VARCHAR} and ins_id=#{insId,jdbcType=INTEGER }
  </update>
  <delete id="deleteAppVersion" parameterType="java.lang.Integer">
  	  delete from coms_app_version where app_id=#{id,jdbcType=INTEGER}
  </delete>
  <select id="selectByCodeReName" parameterType="java.lang.String" resultType="java.lang.String">
  	select name from coms_app_inf where code = #{code,jdbcType=VARCHAR} limit 0,1
  </select>
</mapper>