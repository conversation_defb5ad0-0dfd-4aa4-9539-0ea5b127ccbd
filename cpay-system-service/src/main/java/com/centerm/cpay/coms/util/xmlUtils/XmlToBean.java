package com.centerm.cpay.coms.util.xmlUtils;


import com.centerm.cpay.common.utils.CtUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.io.IOException;

public class XmlToBean {
    
    /**
     * xml文件配置转换为对象
     * @param xmlPath  xml文件路径
     * @param load    java对象.Class
     * @return    java对象
     * @throws JAXBException    
     * @throws IOException
     */
    public static Object xmlToBean(String xmlPath,Class<?> load) throws JAXBException, IOException{
        File xmlFile = new File(xmlPath);
        if(CtUtils.isEmpty(xmlFile)){
            return null;
        }
        JAXBContext context = JAXBContext.newInstance(load);
        Unmarshaller unmarshaller = context.createUnmarshaller(); 
        Object object = unmarshaller.unmarshal(xmlFile);
        return object;
    }
    public static void main(String[] args) throws JAXBException, IOException {
        String xmlPath = "C:\\Users\\<USER>\\Desktop\\settings.xml";
        Config config  = (Config)XmlToBean.xmlToBean(xmlPath, Config.class);
    }
    public static Config getConfig(String xmlPath)throws IOException, JAXBException{
        Config config  = (Config)XmlToBean.xmlToBean(xmlPath, Config.class);
        return config;
    }
}