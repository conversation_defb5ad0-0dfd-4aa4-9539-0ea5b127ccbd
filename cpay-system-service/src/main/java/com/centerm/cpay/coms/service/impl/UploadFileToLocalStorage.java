package com.centerm.cpay.coms.service.impl;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.FileMD5;
import com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.enums.DriverTypeEnum;
import com.centerm.cpay.coms.enums.FileUploadStatusEnum;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.service.FileUploadService;
import com.centerm.cpay.coms.util.storage.ApkUtil;
import com.centerm.cpay.coms.util.xmlUtils.Config;
import com.centerm.cpay.coms.util.xmlUtils.Item;
import com.centerm.cpay.coms.util.xmlUtils.XmlToBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 *【文件上传】本地存储
 * <AUTHOR>
 * @date 2024/3/12 10:14
 */
@Slf4j
@Service(value = "local")
public class UploadFileToLocalStorage implements FileUploadService {

    @Resource
    private DriverUploadService driverUploadService;

    @Override
    public ResultMsg uploadDriverFile(ComsAppVersion entity) {
        String allPath = entity.getAppPath();
        String fileNamePre = entity.getFileName().substring(0,entity.getFileName().lastIndexOf("."));
        log.info("appPath:" + allPath);
        String iconPath = entity.getIconPath();
        Boolean invalid = validIconPath(entity.getIconPath());
        if (invalid) return new ResultMsg(3, "Image save failed", null);

        ResultMsg result;
        String unzipDir = "";
        if (DriverTypeEnum.checkApkType(entity.getType())) {
            FileInfo fileInfo;
            log.info("保存路径："+iconPath);
            try {
                fileInfo = ApkUtil.getSignedFileCustomer(FILE_HEAD_PATH + FILE_SAVE_PATH, entity.getAppPath(),entity.getFileName());
                if(fileInfo.getFileSize() == 0){
                    return new ResultMsg(3, "File upload failed. Please upload again", null);
                }
            } catch (Exception e) {
                log.info("异常:" + e);
                return new ResultMsg(3, "Application upload failed", null);
            }
            try {
                entity.setMd5(FileMD5.getFileMD5String(fileInfo.getFile_absolute_path()));
                entity.setAppPath(FILE_SAVE_PATH + fileInfo.getFileSavePath());
                entity.setFileName(fileInfo.getFile_save_name());
                entity.setSize(fileInfo.getFileSize());
                entity.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());
            } catch (IOException e) {
                e.printStackTrace();
            }

            log.info("存储路径:" + entity.getAppPath());
            result = driverUploadService.driverUploadAdd(entity);
        } else {
            log.debug("oldFilePath:" + allPath);
            entity.setMd5(FileMD5.MD5File(allPath));
            String uuid = CtUtils.uuid();

            String newFilePath = FILE_SAVE_PATH + CtUtils.getCurrentTime("yyyyMMdd") + "/";
            String newFileName = uuid + ".zip";
            log.debug("newFilePath:" + FILE_HEAD_PATH + newFilePath + "newFileName: " + newFileName);
            ApkUtil.fileCopy(FILE_HEAD_PATH +newFilePath,newFileName, allPath);
            log.info("文件复制完成");
            File file = new File(FILE_HEAD_PATH + newFilePath + newFileName);
            log.info("获取到复制的文件：" + FILE_HEAD_PATH + newFilePath + newFileName + "文件大小为：" + file.length());
            entity.setSize(Integer.parseInt(String.valueOf(file.length())));
            entity.setAppPath(newFilePath+newFileName);
            entity.setFileName(newFileName);
            entity.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());
            if(Integer.parseInt(String.valueOf(file.length())) == 0){
                return new ResultMsg(3, "File upload failed. Please upload again", null);
            }

            result =  driverUploadService.driverUploadAdd(entity);

            if(result.getStatus() == ResultMsg.SUCCESS_CODE && DriverTypeEnum.checkSystemFile(entity.getType())) {
                Config config = null;
                List<Item> items = new ArrayList<>();
                unzipDir = unzipRootDir+"/" +fileNamePre;
                log.info("文件解压路径：" + unzipDir);
                try {
                    //获取解压文件的settings信息
                    config = XmlToBean.getConfig(unzipDir+"/"+settingsHeader);
                    items = config.getItems().getItems();
                } catch (IOException | JAXBException e) {
                    e.printStackTrace();
                }
                int appVersionId = (int) result.getData();
                ComsAppVerDetail comsAppVerDetail = new ComsAppVerDetail();
                comsAppVerDetail.setAppVersionId(appVersionId);

                String newOsFilePath = FILE_SAVE_PATH + CtUtils.getCurrentTime("yyyyMMdd") + "/";
                for(Item item : items) {
                    String uuidOs = CtUtils.uuid();
                    String newOsFileName =  uuidOs + ".zip";
                    log.debug("newFilePath:" + FILE_HEAD_PATH + newOsFilePath + "newFileName: " + newOsFileName);
                    ApkUtil.fileCopy(FILE_HEAD_PATH + newOsFilePath, newOsFileName, unzipDir+"/" + item.getFilename());
                    log.info("文件复制完成");
                    File fileTemp = new File(FILE_HEAD_PATH +newOsFilePath+newOsFileName);
                    log.info("获取到复制的文件：" + FILE_HEAD_PATH + newOsFilePath+newOsFileName + "文件大小为：" + fileTemp.length());
                    String[] versionArr = item.getVersion().split(",");
                    for(String version : versionArr) {
                        comsAppVerDetail.setAppVersion(version);
                        comsAppVerDetail.setFileName(newOsFileName);
                        comsAppVerDetail.setAppPath(newOsFilePath + newOsFileName);
                        comsAppVerDetail.setMd5(item.getMd5());
                        comsAppVerDetail.setSize(Integer.parseInt(String.valueOf(fileTemp.length())));
                        driverUploadService.insertAppVerDetail(comsAppVerDetail);
                    }
                }
                log.info("系统镜像及其差分包保存完成");
            }
        }

        //【删除】临时文件
        deleteTempFile(result, allPath, unzipDir, iconPath);
        return result;
    }

    @Override
    @SuppressWarnings("ResultOfMethodCallIgnored")
    public void deleteDriverFile(String appPath) {
        log.info("【开始删除】文件：路径「{}」", appPath);
        File appFile = new File(appPath);
        if (appFile.exists()) {
            appFile.delete();
            log.info("【删除成功】文件：路径「{}」", appPath);
        }
    }
}