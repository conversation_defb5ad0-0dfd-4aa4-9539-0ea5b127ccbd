package com.centerm.cpay.coms.service.impl;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.enums.DriverTypeEnum;
import com.centerm.cpay.coms.enums.FileUploadStatusEnum;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.service.FileUploadService;
import com.centerm.cpay.coms.util.storage.ApkUtil;
import com.centerm.cpay.coms.util.storage.R2StorageAPI;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.centerm.cpay.coms.util.xmlUtils.Config;
import com.centerm.cpay.coms.util.xmlUtils.Item;
import com.centerm.cpay.coms.util.xmlUtils.XmlToBean;

/**
 *【文件上传】R2 Storage
 * <AUTHOR>
 * @date 2024/3/12 10:13
 */
@Slf4j
@Service(value = "r2")
public class UploadFileToR2Storage implements FileUploadService {

    @Resource
    private DriverUploadService driverUploadService;

    @Override
    public ResultMsg uploadDriverFile(ComsAppVersion entity) {
        //【验证】文件图标链接
        log.info("【上传】图标路径：「{}」", entity.getIconPath());
        Boolean invalid = validIconPath(entity.getIconPath());
        if (invalid) return new ResultMsg(3, "Image save failed", null);

        //【新增】上传文件信息（开始上传）
        entity.setUploadStatus(FileUploadStatusEnum.PENDING.getCode());
        ResultMsg result = driverUploadService.driverUploadAdd(entity);

        //【上传】APK文件/系统文件（异步）
        CompletableFuture.runAsync(() -> insertFileInfo(entity));
        return result;
    }

    @Override
    public void deleteDriverFile(String appPath) {
        log.info("【开始删除】文件：路径「{}」", appPath);
        R2StorageAPI.getInstance().deleteFile(appPath);
        log.info("【删除成功】文件：路径「{}」", appPath);
    }

    /**
     *【新增】App信息
     * @param entity App信息
     */
    private void insertFileInfo(ComsAppVersion entity) {
        //【上传】App文件到远程服务(Cloudflare R2)
        log.info("【异步】上传文件");
        String fullPath = entity.getAppPath();
        String fileNamePrefix = entity.getFileName().substring(0, entity.getFileName().lastIndexOf("."));

        //【上传】主体文件
        ResultMsg result = uploadFile(entity);

        //【上传】系统文件（差分包）
        String unzipDir = null;
        if (DriverTypeEnum.checkSystemFile(entity.getType()) &&
                Objects.equals(result.getStatus(), ResultMsg.SUCCESS_CODE)) {

            //【获取】解压文件settings信息
            XMLInfo xmlInfo = getItemsFromXML(fileNamePrefix);
            List<Item> items = xmlInfo.getItems();
            unzipDir = xmlInfo.getUnzipDir();

            //【读取】差分包信息
            ComsAppVerDetail comsAppVerDetail = new ComsAppVerDetail();
            comsAppVerDetail.setAppVersionId(entity.getId());
            log.info("【上传】系统镜像差分包：差分包个数「{}」", items.size());

            //【更新】差分包版本详细信息
            for (Item item : items) {
                String currentPath = unzipDir + "/" + item.getFilename();
                log.info("【准备上传】差分包文件路径:「{}」文件名称「{}」", currentPath, entity.getFileName());
                FileInfo osFile = ApkUtil.getSignedFileCustomer(currentPath, entity.getFileName());
                log.info("【上传成功】差分包文件路径:「{}」文件名称「{}」", osFile.getFile_absolute_path(), entity.getFileName());
                String[] versionArr = item.getVersion().split(",");
                for (String version : versionArr) {
                    comsAppVerDetail.setAppVersion(version);
                    comsAppVerDetail.setFileName(osFile.getFile_save_name());
                    comsAppVerDetail.setAppPath(osFile.getFile_absolute_path());
                    comsAppVerDetail.setMd5(item.getMd5());
                    comsAppVerDetail.setSize(osFile.getFileSize());
                    driverUploadService.insertAppVerDetail(comsAppVerDetail);
                }
            }
            log.info("【上传】系统镜像及其差分包完成");
        }

        //【更新】文件上传状态(成功）
        ComsAppVersion updateStatus = new ComsAppVersion();
        updateStatus.setId(entity.getId());
        updateStatus.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());
        driverUploadService.driverUploadupdate(updateStatus);

        //【删除】临时存储文件
        deleteTempFile(result, fullPath, unzipDir, entity.getIconPath());
    }

    /**
     *【读取】差分包配置文件
     * @param fileNamePrefix 文件路径前缀
     * @return 配置信息
     */
    private XMLInfo getItemsFromXML(String fileNamePrefix) {
        Config config;
        List<Item> items = new ArrayList<>();
        String unzipDir = unzipRootDir + "/" + fileNamePrefix;
        log.info("【文件解压】路径:「{}」", unzipDir);
        try {
            config = XmlToBean.getConfig(unzipDir + "/" + settingsHeader);
            items = config.getItems().getItems();
        } catch (Exception e) {
            log.error("【解析文件】发生异常：原因「{}」", e.toString());
        }
        return new XMLInfo().setItems(items).setUnzipDir(unzipDir);
    }

    @Data
    @Accessors(chain = true)
    protected static class XMLInfo {

        //【差分包】信息
        private List<Item> items;

        //【路径】解压缩
        private String unzipDir;
    }

    /**
     *【上传】文件信息
     * @param entity 文件信息
     * @return 更新结果
     */
    private ResultMsg uploadFile(ComsAppVersion entity) {
        //【更新】文件上传状态
        entity.setUploadStatus(FileUploadStatusEnum.ON_GOING.getCode());
        driverUploadService.driverUploadupdate(entity);

        //【上传】文件信息
        FileInfo fileInfo = null;
        try {
            fileInfo = ApkUtil.getSignedFileCustomer(entity.getAppPath(), entity.getFileName());
        } catch (Exception e) {
            log.error("【远端文件上传】发生异常：原因「{}」", e.toString());
        }

        //【验证】文件上传结果(失败-》更新标志位)
        if (CtUtils.isEmpty(fileInfo) || CtUtils.isEmpty(fileInfo.getFileSize())) {
            updateAppUploadFailure(entity);
            return new ResultMsg(3, "File uploads failure", null);
        }

        //【更新】APK/系统文件
        entity.setMd5(fileInfo.getFileMd5());
        entity.setFileName(fileInfo.getFile_save_name());
        entity.setSize(fileInfo.getFileSize());
        entity.setAppPath(fileInfo.getFile_absolute_path());
        log.info("【远端文件存储】完整信息:「{}」", entity);
        return driverUploadService.driverUploadupdate(entity);
    }

    /**
     *【更新】文件上传失败结果
     * @param entity 文件信息
     */
    private void updateAppUploadFailure(ComsAppVersion entity) {
        //【配置】更新失败标志位
        entity.setUploadStatus(FileUploadStatusEnum.FAILED.getCode());
        driverUploadService.driverUploadupdate(entity);
    }
}