<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsAppVersionMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="app_id" property="appId" jdbcType="INTEGER" />
    <result column="app_name" property="appName" jdbcType="VARCHAR" />
    <result column="factory_id" property="factoryId" jdbcType="INTEGER" />
    <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
    <result column="app_version" property="appVersion" jdbcType="VARCHAR" />
    <result column="app_version_name" property="appVersionName" jdbcType="VARCHAR" />
    <result column="upload_file_name" property="uploadFileName" jdbcType="VARCHAR" />
    <result column="file_name" property="fileName" jdbcType="VARCHAR" />
    <result column="version_status" property="versionStatus" jdbcType="CHAR" />
    <result column="md5" property="md5" jdbcType="VARCHAR" />
    <result column="app_path" property="appPath" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="oper_flag" property="operFlag" jdbcType="CHAR" />
    <result column="upload_status" property="uploadStatus" jdbcType="CHAR" />
    <result column="upload_strategy" property="uploadStrategy" jdbcType="CHAR" />
    <result column="size" property="size" jdbcType="INTEGER" />
    <result column="appCode" property="appCode" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="ins_name" property="insName" jdbcType="VARCHAR" />
    <result column="icon_path" property="iconPath" jdbcType="VARCHAR"/>
    <result column="main_activity" property="mainActivity" jdbcType="VARCHAR" />
    <result column="TERM_TYPE_CODESTR" property="termTypeCodeStr" jdbcType="VARCHAR" />
    <result column="TERM_TYPE_NAMESTR" property="termTypeNameStr" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, app_id, app_name, factory_id, factory_name,app_version,app_version_name,upload_file_name,
    file_name, version_status, md5, app_path, remark, oper_flag,size,ins_id,main_activity,icon_path,
    TERM_TYPE_CODESTR,TERM_TYPE_NAMESTR, upload_strategy
  </sql>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    select
    a.id, app_id, app_name, factory_id,app_version,app_version_name,upload_file_name,
    file_name, version_status, md5, a.remark, oper_flag,size,ins_id,main_activity,icon_path,
    TERM_TYPE_CODESTR,TERM_TYPE_NAMESTR, b.name as ins_name, c.name as factory_name, a.upload_status,
    IF(a.upload_strategy = '1', CONCAT(#{ngsPicPath}, a.app_path), a.app_path) as app_path, a.upload_strategy
      from coms_app_version a left join cpay_institution b on a.ins_id = b.id
      left join COMS_TERMINAL_MANUFACTURER c on c.id = a.factory_id
    where 1=1

    <if test="appVersionName != null" >
      and  app_version_name like concat (concat('%',#{appVersionName}),'%')
    </if>
     <if test="factoryId != null" >
      and  factory_id = #{factoryId,jdbcType=INTEGER}
      </if>
      <if test="appId != null" >
      and  app_id = #{appId,jdbcType=INTEGER}
      </if>
      <if test="appVersion != null" >
      and  app_version like concat (concat('%',#{appVersion}),'%')
      </if>
      <if test="remark != null" >
      and  remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="insId != null and insId != 0">
		and ins_id = #{insId,jdbcType=INTEGER}
		</if>
      order by app_version desc
  </select>
  
  <select id="selectTermType" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    select 
    a.id, app_id, app_name, factory_id, factory_name, app_version,app_version_name,upload_file_name,
    version_status,b.name as ins_name
    from coms_app_version a
      left join cpay_institution b on a.ins_id = b.id
    where 1=1
     <if test="factoryId != null" >
      and  factory_id = #{factoryId,jdbcType=INTEGER}
      </if>
      <if test="appId != null" >
      and  app_id = #{appId,jdbcType=INTEGER}
      </if>
      <if test="appVersion != null" >
      and  app_version = #{appVersion,jdbcType=VARCHAR}
      </if>
      <if test="insId != null and insId != 0">
		and ins_id =#{insId,jdbcType=INTEGER}
		</if>
      order by app_version desc
  </select>
  
  


  <delete id="deleteByIds" parameterType="java.util.List">
		delete from coms_app_version where id in
		<foreach collection="list" index="index" open="("  separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_app_version
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_app_version
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" useGeneratedKeys="true" keyProperty="id">
    insert into coms_app_version ( app_id, app_name, 
      factory_id, factory_name, app_version, app_version_name,upload_file_name,file_name,
      version_status, md5, app_path, 
      remark, oper_flag,size,ins_id,icon_path,main_activity,term_type_codestr,term_type_namestr, upload_strategy, upload_status)
    values ( #{appId,jdbcType=INTEGER}, #{appName,jdbcType=VARCHAR}, 
      #{factoryId,jdbcType=INTEGER}, #{factoryName,jdbcType=VARCHAR},#{appVersion,jdbcType=VARCHAR},
       #{appVersionName,jdbcType=VARCHAR},#{uploadFileName,jdbcType=VARCHAR},#{fileName,jdbcType=VARCHAR},
      #{versionStatus,jdbcType=CHAR}, #{md5,jdbcType=VARCHAR}, #{appPath,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{operFlag,jdbcType=CHAR},#{size,jdbcType=INTEGER},#{insId,jdbcType=INTEGER},
      #{iconPath,jdbcType=VARCHAR},#{mainActivity,jdbcType=VARCHAR},#{termTypeCodeStr,jdbcType=VARCHAR},#{termTypeNameStr,jdbcType=VARCHAR},
      #{uploadStrategy,jdbcType=VARCHAR}, #{uploadStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    insert into coms_app_version
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="appId != null" >
        app_id,
      </if>
      <if test="appName != null" >
        app_name,
      </if>
      <if test="factoryId != null" >
        factory_id,
      </if>
      <if test="factoryName != null" >
        factory_name,
      </if>
      <if test="appVersion != null" >
        app_version,
      </if>
      <if test="appVersionName != null" >
        app_version_name,
      </if>
      <if test="uploadFileName != null" >
        upload_file_name,
      </if>
      <if test="fileName != null" >
        file_name,
      </if>
      <if test="versionStatus != null" >
        version_status,
      </if>
      <if test="md5 != null" >
        md5,
      </if>
      <if test="appPath != null" >
        app_path,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="operFlag != null" >
        oper_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="appId != null" >
        #{appId,jdbcType=INTEGER},
      </if>
      <if test="appName != null" >
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null" >
        #{factoryId,jdbcType=INTEGER},
      </if>
      <if test="factoryName != null" >
        #{factoryName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="uploadFileName != null" >
        #{uploadFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="versionStatus != null" >
        #{versionStatus,jdbcType=CHAR},
      </if>
      <if test="md5 != null" >
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="appPath != null" >
        #{appPath,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operFlag != null" >
        #{operFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    update coms_app_version
    <set >
      <if test="appId != null" >
        app_id = #{appId,jdbcType=INTEGER},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null" >
        factory_id = #{factoryId,jdbcType=INTEGER},
      </if>
      <if test="factoryName != null" >
        factory_name = #{factoryName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        app_version_name = #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="uploadFileName != null" >
        upload_file_name = #{uploadFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="versionStatus != null" >
        version_status = #{versionStatus,jdbcType=CHAR},
      </if>
      <if test="md5 != null" >
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="appPath != null" >
        app_path = #{appPath,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operFlag != null" >
        oper_flag = #{operFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    update coms_app_version
    set app_id = #{appId,jdbcType=INTEGER},
      app_name = #{appName,jdbcType=VARCHAR},
      factory_id = #{factoryId,jdbcType=INTEGER},
      factory_name = #{factoryName,jdbcType=VARCHAR},
      app_version = #{appVersion,jdbcType=VARCHAR},
      app_version_name = #{appVersionName,jdbcType=VARCHAR},
      upload_file_name = #{uploadFileName,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      version_status = #{versionStatus,jdbcType=CHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      app_path = #{appPath,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      oper_flag = #{operFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
<select id="queryAppInfo" resultType="java.util.HashMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion">
	select code from coms_app_inf where id=#{appId,jdbcType=INTEGER}
</select>
<select id="queryAppVersionExist" resultType="java.util.HashMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion">
	select * from coms_app_version where 1=1
	and app_id=#{appId,jdbcType=INTEGER} 
	and factory_id = #{factoryId,jdbcType=INTEGER}
	and app_version = #{appVersion,jdbcType=VARCHAR}
	<if test="id != null" >
		and id != #{id,jdbcType=INTEGER}
	</if>
</select>
<select id="selectByAppId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_app_version
    where app_id = #{id,jdbcType=INTEGER}
  </select>
  <select id="getAppVersions" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    select
    <include refid="Base_Column_List" />
    from coms_app_version
    where app_id = #{appId,jdbcType=INTEGER}
    and factory_id = #{factoryId,jdbcType=INTEGER}
    and upload_status = '2'
  </select>
    <update id="updateDriverUpload" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" >
    update coms_app_version
    <set >
      <if test="appId != null" >
        app_id = #{appId,jdbcType=INTEGER},
      </if>
      <if test="appName != null" >
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="factoryId != null" >
        factory_id = #{factoryId,jdbcType=INTEGER},
      </if>
      <if test="factoryName != null" >
        factory_name = #{factoryName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null" >
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="appVersionName != null" >
        app_version_name = #{appVersionName,jdbcType=VARCHAR},
      </if>
      <if test="uploadFileName != null" >
        upload_file_name = #{uploadFileName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="versionStatus != null" >
        version_status = #{versionStatus,jdbcType=CHAR},
      </if>
      <if test="md5 != null" >
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        SIZE = #{size,jdbcType=INTEGER},
      </if>
      <if test="appPath != null" >
        app_path = #{appPath,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operFlag != null" >
        oper_flag = #{operFlag,jdbcType=CHAR},
      </if>
      <if test="termTypeCodeStr != null" >
        term_type_codestr = #{termTypeCodeStr,jdbcType=VARCHAR},
      </if>
      <if test="termTypeNameStr != null" >
        term_type_namestr = #{termTypeNameStr,jdbcType=VARCHAR},
      </if>
      <if test="uploadStatus != null" >
        upload_status = #{uploadStatus,jdbcType=VARCHAR},
      </if>
      <if test="uploadStrategy != null" >
        upload_strategy = #{uploadStrategy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectAppVersionUnique" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" resultType="java.lang.Integer">
  	select count(*) from coms_app_version where 1=1
  	  <if test="factoryId != null" >
      and  factory_id = #{factoryId,jdbcType=INTEGER}
      </if>
      <if test="appId != null" >
      and  app_id = #{appId,jdbcType=INTEGER}
      </if>
      <if test="appVersion != null" >
      and app_version = #{appVersion,jdbcType=VARCHAR}
      </if>
      and id != #{id,jdbcType=INTEGER}
  </select>
    <select id="selectAppVersion2" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" resultType="java.lang.Integer">
        select count(*) from coms_app_version where 1=1
        and APP_ID = #{appId,jdbcType=INTEGER}
        and app_version = #{appVersion,jdbcType=VARCHAR}
        and FACTORY_ID  = #{factoryId,jdbcType=INTEGER}
        and id != #{id,jdbcType=INTEGER}
    </select>
  <select id="selectAppVersion" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVersion" resultType="java.lang.Integer">
        select count(*) from coms_app_version where 1=1
        and APP_ID = #{appId,jdbcType=INTEGER}
        and app_version = #{appVersion,jdbcType=VARCHAR}
        and FACTORY_ID  = #{factoryId,jdbcType=INTEGER}
    </select>
    <select id="selectAppFileCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*) from coms_app_version where md5 = #{md5,jdbcType=VARCHAR}
    </select>
</mapper>