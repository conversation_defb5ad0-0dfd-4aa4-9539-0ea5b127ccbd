package com.centerm.cpay.coms.dao.pojo;

public class FileInfo {
    private Integer id;

    private String uuid;

    private String fileOldName;

    private String fileSavePath;

    private Integer fileSize;

    private String fileMd5;

    private String recCrtTm;
    //非数据库字段
    private String file_absolute_path;
    
    private String file_save_name;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    public String getFileOldName() {
        return fileOldName;
    }

    public void setFileOldName(String fileOldName) {
        this.fileOldName = fileOldName == null ? null : fileOldName.trim();
    }

    public String getFileSavePath() {
        return fileSavePath;
    }

    public void setFileSavePath(String fileSavePath) {
        this.fileSavePath = fileSavePath == null ? null : fileSavePath.trim();
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileMd5() {
        return fileMd5;
    }

    public void setFileMd5(String fileMd5) {
        this.fileMd5 = fileMd5 == null ? null : fileMd5.trim();
    }

    public String getRecCrtTm() {
        return recCrtTm;
    }

    public void setRecCrtTm(String recCrtTm) {
        this.recCrtTm = recCrtTm == null ? null : recCrtTm.trim();
    }

	public String getFile_absolute_path() {
		return file_absolute_path;
	}

	public void setFile_absolute_path(String file_absolute_path) {
		this.file_absolute_path = file_absolute_path;
	}

	public String getFile_save_name() {
		return file_save_name;
	}

	public void setFile_save_name(String file_save_name) {
		this.file_save_name = file_save_name;
	}

    @Override
    public String toString() {
        return "FileInfo{" +
                "id=" + id +
                ", uuid='" + uuid + '\'' +
                ", fileOldName='" + fileOldName + '\'' +
                ", fileSavePath='" + fileSavePath + '\'' +
                ", fileSize=" + fileSize +
                ", fileMd5='" + fileMd5 + '\'' +
                ", recCrtTm='" + recCrtTm + '\'' +
                ", file_absolute_path='" + file_absolute_path + '\'' +
                ", file_save_name='" + file_save_name + '\'' +
                '}';
    }
}