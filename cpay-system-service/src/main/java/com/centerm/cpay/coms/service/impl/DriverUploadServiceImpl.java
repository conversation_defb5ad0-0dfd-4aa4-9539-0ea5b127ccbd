
package com.centerm.cpay.coms.service.impl;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.coms.dao.mapper.ComsAppVersionDetailMapper;
import com.centerm.cpay.coms.dao.pojo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.mapper.ComsAppInfMapper;
import com.centerm.cpay.coms.dao.mapper.ComsAppVersionMapper;
import com.centerm.cpay.coms.service.DriverService;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.service.FactoryService;
import com.centerm.cpay.coms.service.TerminalTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class DriverUploadServiceImpl implements DriverUploadService {

    @Autowired
    private ComsAppVersionMapper comsAppVersionMapper;
    @Autowired
    private ComsAppVersionDetailMapper detailMapper;
    @Autowired
    private ComsAppInfMapper comsAppInfMapper;
    @Autowired
    private FactoryService factoryService;
    @Autowired
    private TerminalTypeService terminalTypeService;
    @Autowired
    private DriverService driverService;

    @Override
    public EUDataGridResult getGridList(ComsAppVersion entity, int page, int rows) {
        // 分页处理
        PageHelper.startPage(page, rows);
        List<ComsAppVersion> list = comsAppVersionMapper.selectByCondition(entity);

        // 创建一个返回值对象
        EUDataGridResult result = new EUDataGridResult();
        result.setRows(list);
        // 取记录总条数
        PageInfo<ComsAppVersion> pageInfo = new PageInfo<>(list);
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    @Override
    public ResultMsg deleteById(Integer id) {
        // TODO
        comsAppVersionMapper.deleteByPrimaryKey(id);
        return ResultMsg.success();
    }

    /**
     * 查找表中文件 相同md5的个数
     * @param md5
     * @return
     */
    @Override
    public int appFileCount(String md5){
        return comsAppVersionMapper.selectAppFileCount(md5);
    }

    @Override
    public ResultMsg insertEntity(ComsAppVersion entity) {
        if (entity.getType().equals("1") || entity.getType().equals("0") || entity.getType().equals("6")) {
            List<Map> list1 = comsAppVersionMapper.queryAppInfo(entity);// 查询当前解析出来的包名是否和填入的信息是否相同
            if (!list1.get(0).get("code").equals(entity.getAppCode())) {
                /*return ResultMsg.build(ResultMsg.ERROR_CODE, "包名:" + entity.getAppCode() + ",与定义的不同");*/
                return ResultMsg.build(ResultMsg.ERROR_CODE, "The package name" + entity.getAppCode() + ",It's different from the definition");
            } else {
                List<Map> list2 = comsAppVersionMapper.queryAppVersionExist(entity);// 查询当前包名的版本是否已经存在(根据厂商型号包名版本号)
                if (list2 != null && !list2.isEmpty()) {
                    /*return ResultMsg.build(ResultMsg.ERROR_CODE, "当前版本已经存在,重新上传");*/
                    return ResultMsg.build(ResultMsg.ERROR_CODE, "The current version already exists, re-upload");
                } else {
                    comsAppVersionMapper.insertSelective(entity);
                }
            }
        } else {
            comsAppVersionMapper.insertSelective(entity);
        }
        return ResultMsg.success();
    }

    @Override
    public ResultMsg updateByEntity(ComsAppVersion entity) {
        if (entity.getType().equals("1") || entity.getType().equals("0") || entity.getType().equals("6")) {
            List<Map> list1 = comsAppVersionMapper.queryAppInfo(entity);// 查询当前解析出来的包名是否和填入的信息是否相同
            if (!list1.get(0).get("code").equals(entity.getAppCode())) {
                /*return ResultMsg.build(ResultMsg.ERROR_CODE, "包名" + entity.getAppCode() + ",与定义的不同");*/
                return ResultMsg.build(ResultMsg.ERROR_CODE, "The package name" + entity.getAppCode() + ",It's different from the definition");
            } else {
                List<Map> list2 = comsAppVersionMapper.queryAppVersionExist(entity);// 查询当前包名的版本是否已经存在(根据厂商型号包名版本号)
                if (list2 != null && !list2.isEmpty()) {
                    /*return ResultMsg.build(ResultMsg.ERROR_CODE, "当前版本已经存在,重新上传");*/
                    return ResultMsg.build(ResultMsg.ERROR_CODE, "The current version already exists, re-upload");
                } else {
                    comsAppVersionMapper.updateByPrimaryKeySelective(entity);
                }
            }
        } else {
            comsAppVersionMapper.updateByPrimaryKeySelective(entity);
        }
        return ResultMsg.success();
    }

    @Override
    public int insertAppVerDetail(ComsAppVerDetail record) {
        return detailMapper.insert(record);
    }

    @Override
    public ResultMsg driverUploadAdd(ComsAppVersion entity) {
        //【配置】App信息
        ComsAppInf comsAppInf = new ComsAppInf();
        comsAppInf.setCode(entity.getAppCode());
        comsAppInf.setType(entity.getType());
        comsAppInf.setInsId(entity.getInsId());
        comsAppInf.setIconPath(entity.getIconPath());
        comsAppInf.setDccSupFlag(entity.getDccSupFlag());
        comsAppInf.setCupConnMode(entity.getCupConnMode());
        if (!entity.getType().equals("5")) {
        	entity.setMainActivity(null);
        }

        //【判断】当前软件是否存在
        ComsAppInf preExisted = comsAppInfMapper.queryIdByCode(comsAppInf);
        if (!CtUtils.isEmpty(preExisted)) {
            entity.setAppId(preExisted.getId());
            int count = comsAppVersionMapper.selectAppVersion(entity);
            if (count != 0) {
                return ResultMsg.build(ResultMsg.ERROR_CODE, "The software version already exists under the current organization's current terminal firm");
            }
            //【修改】图标 + 更新应用名称
            preExisted.setIconPath(entity.getIconPath());
            preExisted.setName(entity.getAppName());
            comsAppInfMapper.updateByPrimaryKeySelective(preExisted);
        } else {
            comsAppInf.setName(entity.getAppName());
            comsAppInfMapper.insert(comsAppInf);
            entity.setAppId(comsAppInf.getId());
        }

        //【新增】版本信息
        comsAppVersionMapper.insert(entity);
        return ResultMsg.success(entity.getId());
    }

    @Override
    public ResultMsg deleteByIds(List<String> ids) {
        // TODO
        comsAppVersionMapper.deleteByIds(ids);
        return ResultMsg.success();
    }
    @Override
    public List<ComsAppVersion> selectByPrimaryKey(Integer id) {

        return comsAppVersionMapper.selectByAppId(id);
    }
    @Override
    public List<ComsAppVersion> getAppVersions(ComsAppVersion entity) {

        return comsAppVersionMapper.getAppVersions(entity);
    }
    @Override
    public ResultMsg driverUploadupdate(ComsAppVersion entity) {
        comsAppVersionMapper.updateDriverUpload(entity);
        return ResultMsg.success(entity.getId());
    }

    @Override
    public ComsAppVersion queryByJobId(Integer id) {

        return comsAppVersionMapper.selectByPrimaryKey(id);
    }

    @Override
    public ResultMsg updateDriverUpload(ComsAppVersion entity) {
        int count = comsAppVersionMapper.selectAppVersionUnique(entity);//判断当前修改的APP版本是否已经存在了
        if (count > 0) {
            /*return ResultMsg.build(ResultMsg.ERROR_CODE, "当前终端型号的此版本已存在");*/
            return ResultMsg.build(ResultMsg.ERROR_CODE, "This version of the current terminal model already exists");
        }
        try {
            comsAppVersionMapper.updateDriverUpload(entity);
            return ResultMsg.success();
        } catch (Exception e) {
            /*return ResultMsg.build(ResultMsg.ERROR_CODE, "信息修改异常，请重试");*/
            return ResultMsg.build(ResultMsg.ERROR_CODE, "Message modifies exception, please try again");
        }

    }

    @Override
    public List<ComsAppVersion> selectConditions(ComsAppVersion entity) {

        return comsAppVersionMapper.selectByCondition(entity);
    }

    @Override
    public List<ComsAppVersion> selectRom(ComsAppVersion entity) {

        return comsAppVersionMapper.selectByRom(entity);
    }
}