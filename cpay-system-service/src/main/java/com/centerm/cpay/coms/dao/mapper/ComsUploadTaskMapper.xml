<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsUploadTaskMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="job_id" property="jobId" jdbcType="INTEGER" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="upload_flag" property="uploadFlag" jdbcType="CHAR" />
    <result column="upload_begin_date" property="uploadBeginDate" jdbcType="TIMESTAMP" />
    <result column="upload_end_date" property="uploadEndDate" jdbcType="TIMESTAMP" />
    <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP" />
    <result column="valid_date" property="validDate" jdbcType="TIMESTAMP" />
    <result column="get_begin_date" property="getBeginDate" jdbcType="TIMESTAMP" />
    <result column="get_end_date" property="getEndDate" jdbcType="TIMESTAMP" />
    <result column="record_status" property="recordStatus" jdbcType="VARCHAR" />
    <result column="log_name" property="logName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, job_id, term_seq, type, app_code, upload_flag, upload_begin_date, upload_end_date, 
    release_time, valid_date, get_begin_date, get_end_date, record_status, log_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_upload_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    select 
    b.job_name,
    a.id, job_id, a.term_seq, a.type, a.app_code, a.upload_flag, 
    a.upload_begin_date, a.upload_end_date,a.release_time, 
    a.valid_date, a.get_begin_date, a.get_end_date, a.record_status, a.log_name
    from coms_upload_task a
    LEFT JOIN coms_upload_job b ON b.id = a.job_id
    where 1=1
    <if test="termSeq != null">
		and a.term_seq like concat (concat('%',#{termSeq}),'%')
	</if>
	<if test="uploadFlag != null and uploadFlag != ''">
		and a.upload_flag = #{uploadFlag}
	</if>
	<if test="jobId != null">
		and a.job_id = #{jobId}
	</if>
	<if test="optCmdStatus != null" >
        and a.opt_cmd_status = #{optCmdStatus,jdbcType=INTEGER}
     </if>
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_upload_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <delete id="deleteByJobIds" parameterType="java.util.List" >
    delete from coms_upload_task where job_id in
	<foreach collection="list" index="index" open="(" separator=","
		close=")" item="jobId">
		#{jobId}
	</foreach>
  </delete>
  
  <delete id="deleteByJobId" parameterType="java.lang.Integer" >
    delete from coms_upload_task where job_id = #{jobId,jdbcType=INTEGER}
  </delete>
  
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    insert into coms_upload_task ( job_id, term_seq, 
      type, app_code, upload_flag, 
      upload_begin_date, upload_end_date, release_time, 
      valid_date, get_begin_date, get_end_date, 
      record_status, log_name)
    values ( #{jobId,jdbcType=INTEGER}, #{termSeq,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{appCode,jdbcType=VARCHAR}, #{uploadFlag,jdbcType=CHAR}, 
      #{uploadBeginDate,jdbcType=TIMESTAMP}, #{uploadEndDate,jdbcType=TIMESTAMP}, #{releaseTime,jdbcType=TIMESTAMP}, 
      #{validDate,jdbcType=TIMESTAMP}, #{getBeginDate,jdbcType=TIMESTAMP}, #{getEndDate,jdbcType=TIMESTAMP}, 
      #{recordStatus,jdbcType=VARCHAR}, #{logName,jdbcType=VARCHAR})
  </insert>
  
  <update id="batchUpdateFlag" parameterType="java.util.Map">
      update coms_upload_task
      set upload_flag = #{uploadFlag}
      where id in
     <foreach item="item" index="index" collection="ids.split(',')" open="(" separator="," close=")">
      '${item}'
     </foreach>
 </update>
  
  
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    insert into coms_upload_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="jobId != null" >
        job_id,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="uploadFlag != null" >
        upload_flag,
      </if>
      <if test="uploadBeginDate != null" >
        upload_begin_date,
      </if>
      <if test="uploadEndDate != null" >
        upload_end_date,
      </if>
      <if test="releaseTime != null" >
        release_time,
      </if>
      <if test="validDate != null" >
        valid_date,
      </if>
      <if test="getBeginDate != null" >
        get_begin_date,
      </if>
      <if test="getEndDate != null" >
        get_end_date,
      </if>
      <if test="recordStatus != null" >
        record_status,
      </if>
      <if test="logName != null" >
        log_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="jobId != null" >
        #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="uploadFlag != null" >
        #{uploadFlag,jdbcType=CHAR},
      </if>
      <if test="uploadBeginDate != null" >
        #{uploadBeginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="uploadEndDate != null" >
        #{uploadEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validDate != null" >
        #{validDate,jdbcType=TIMESTAMP},
      </if>
      <if test="getBeginDate != null" >
        #{getBeginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="getEndDate != null" >
        #{getEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordStatus != null" >
        #{recordStatus,jdbcType=VARCHAR},
      </if>
      <if test="logName != null" >
        #{logName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <insert id="batchInsertSelective" parameterType="java.util.List">
   	insert into coms_upload_task
    (
        job_id,
        term_seq,
        type,
        app_code,
        upload_flag,
        release_time,
        valid_date,
        get_begin_date,
        get_end_date,
        opt_cmd_status
	) values    
	<foreach collection="list" item="item" index="index" separator="," >
	(	  
		  #{item.jobId,jdbcType=INTEGER},
		  #{item.termSeq,jdbcType=VARCHAR},
		  #{item.type,jdbcType=VARCHAR},
		  #{item.appCode,jdbcType=VARCHAR},
		  #{item.uploadFlag,jdbcType=CHAR},
		  #{item.releaseTime,jdbcType=TIMESTAMP},
		  #{item.validDate,jdbcType=TIMESTAMP},
		  #{item.getBeginDate,jdbcType=TIMESTAMP},
		  #{item.getEndDate,jdbcType=TIMESTAMP},
	      #{item.optCmdStatus,jdbcType=INTEGER}
	)
   	</foreach>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    update coms_upload_task
    <set >
      <if test="jobId != null" >
        job_id = #{jobId,jdbcType=INTEGER},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="uploadFlag != null" >
        upload_flag = #{uploadFlag,jdbcType=CHAR},
      </if>
      <if test="uploadBeginDate != null" >
        upload_begin_date = #{uploadBeginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="uploadEndDate != null" >
        upload_end_date = #{uploadEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="releaseTime != null" >
        release_time = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validDate != null" >
        valid_date = #{validDate,jdbcType=TIMESTAMP},
      </if>
      <if test="getBeginDate != null" >
        get_begin_date = #{getBeginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="getEndDate != null" >
        get_end_date = #{getEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordStatus != null" >
        record_status = #{recordStatus,jdbcType=VARCHAR},
      </if>
      <if test="logName != null" >
        log_name = #{logName,jdbcType=VARCHAR},
      </if>
      <if test="optCmdStatus != null" >
        opt_cmd_status = #{optCmdStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.ComsUploadTask" >
    update coms_upload_task
    set job_id = #{jobId,jdbcType=INTEGER},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      app_code = #{appCode,jdbcType=VARCHAR},
      upload_flag = #{uploadFlag,jdbcType=CHAR},
      upload_begin_date = #{uploadBeginDate,jdbcType=TIMESTAMP},
      upload_end_date = #{uploadEndDate,jdbcType=TIMESTAMP},
      release_time = #{releaseTime,jdbcType=TIMESTAMP},
      valid_date = #{validDate,jdbcType=TIMESTAMP},
      get_begin_date = #{getBeginDate,jdbcType=TIMESTAMP},
      get_end_date = #{getEndDate,jdbcType=TIMESTAMP},
      record_status = #{recordStatus,jdbcType=VARCHAR},
      log_name = #{logName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryTerminalExist" parameterType="java.lang.String" resultType="java.util.HashMap">
  	select * from coms_terminal_info where term_seq=#{termSeq}
  </select>
  <select id="queryTerminalForExist" parameterType="com.centerm.cpay.coms.dao.pojo.Terminal" resultType="java.util.HashMap">
  	select a.* from coms_terminal_info a
    left join COMS_MERCHANT_INFO d
    on a.PAY_MERCHANT_NO = d.MERCHANT_NO
    where a.term_seq=#{termSeq,jdbcType=VARCHAR}
  	<if test="insId != null and insId != 0">
			and a.ins_id IN (
			SELECT s1.id FROM cpay_institution s1 WHERE
				locate((SELECT s2.detail FROM cpay_institution s2
						WHERE s2.id = #{insId,jdbcType=INTEGER}
					),s1.detail )>0
			)
	  </if>
    <if test="termMfrId != null">
      and a.term_mfr_id=#{termMfrId,jdbcType=INTEGER}
    </if>
    <if test="activateType != null and activateType != ''">
      and a.activate_type= #{activateType,jdbcType=CHAR}
    </if>
    <if test="activateTypes != null and activateTypes != ''">
      and a.activate_type in
      <foreach item="item" index="index" collection="activateTypes.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="termTypeCodes != null and termTypeCodes != ''">
      and a.term_type_code in
      <foreach item="item" index="index" collection="termTypeCodes.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="dccSupFlag != null and dccSupFlag != ''">
      and d.dcc_sup_flag = #{dccSupFlag,jdbcType=CHAR}
    </if>
    <if test="cupConnMode != null and cupConnMode != ''">
      and d.cup_conn_mode = #{cupConnMode,jdbcType=CHAR}
    </if>
    <if test="bussType != null and bussType != ''">
      and d.buss_type = #{bussType,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>