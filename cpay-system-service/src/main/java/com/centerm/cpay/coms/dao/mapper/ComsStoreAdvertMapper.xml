<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsStoreAdvertMapper" >
    <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="ad_path" property="adPath" jdbcType="VARCHAR" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="md5" property="md5" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="ins_id" property="insId" jdbcType="INTEGER" />
        <result column="ins_name" property="insName" jdbcType="VARCHAR" />
        <result column="pub_time" property="pubTime" jdbcType="TIMESTAMP" />
        <result column="cr_time" property="crTime" jdbcType="TIMESTAMP" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="DATE" />
        <result column="end_time" property="endTime" jdbcType="DATE" />
        <result column="type" property="type" jdbcType="CHAR" />
        <result column="factory_id" property="factoryId" jdbcType="INTEGER" />
        <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
        <result column="term_type_id" property="termTypeId" jdbcType="INTEGER" />
        <result column="term_type_name" property="termTypeName" jdbcType="VARCHAR" />
        <result column="ad_audit" property="adAudit" jdbcType="CHAR" />
        <result column="term_group_id" property="termGroupId" jdbcType="INTEGER" />
        <result column="ad_file_type" property="adFileType" jdbcType="CHAR" />
        <result column="device_type" property="deviceType" jdbcType="CHAR" />
        <result column="app_code" property="appCode" jdbcType="VARCHAR" />
        <result column="app_name" property="appName" jdbcType="VARCHAR" />
        <result column="ad_pic_path" property="adPicPath" jdbcType="VARCHAR" />
        <result column="is_disabled" property="isDisabled" jdbcType="CHAR" />
        <result column="termGroupName" property="termGroupName" jdbcType="VARCHAR" />
        <result column="audit_opinion" property="auditOpinion" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, ad_path, file_name, md5, name, ins_id, ins_name, pub_time, cr_time, description,
    start_time, end_time, type, factory_id, factory_name, term_type_id, term_type_name, 
    ad_audit, term_group_id, ad_file_type, device_type, app_code, app_name, ad_pic_path,audit_opinion
    </sql>
    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        SELECT
        A.id,
        A.ad_path,
        A.file_name,
        A.md5,
        A.name,
        A.ins_id,
        A.ins_name,
        A.pub_time,
        A.cr_time,
        A.description,
        A.start_time,
        A.end_time,
        A.type,
        A.factory_id,
        A.factory_name,
        A.term_type_id,
        A.term_type_name,
        A.ad_audit,
        A.term_group_id,
        A.ad_file_type,
        A.device_type,
        A.app_code,
        A.app_name,
        A.ad_pic_path,
        A.is_disabled,
        B.name termGroupName,
        aft.name AS adFileTypeName
        FROM
        coms_store_advert A
        LEFT JOIN coms_terminal_group B
        ON A.term_group_id = B.id
        LEFT JOIN
        (SELECT
        a.*
        FROM
        cpay_dict a
        LEFT JOIN cpay_dict b
        ON a.parent_id = b.id
        WHERE b.type = 'ad_file_type') aft
        ON aft.type = A.ad_file_type
        where 1=1
        <if test="id != null">
            and A.id = #{id,jdbcType=INTEGER}
        </if>
        <if test="name != null">
            and A.name like concat( concat ('%',#{name}),'%')
        </if>
        <if test="type != null and type != '' ">
            and A.type = '${type}'
        </if>
        <if test="adFileType != null and adFileType != '' ">
            and A.ad_file_type = '${adFileType}'
        </if>
        <if test="factoryId != null" >
            and  A.factory_id = #{factoryId,jdbcType=INTEGER}
        </if>
        <if test="termTypeId != null" >
            and  A.term_type_id = #{termTypeId,jdbcType=INTEGER}
        </if>
        <if test="insId != null">
            and A.ins_id IN (
            SELECT s1.id FROM cpay_institution s1 WHERE
            locate((SELECT s2.detail FROM cpay_institution s2
            WHERE s2.id = #{insId,jdbcType=INTEGER}
            ),s1.detail )>0
            )
        </if>
        <if test="adAudit != null and adAudit!= '' " >
            and  A.ad_audit = #{adAudit,jdbcType=CHAR}
        </if>
        order by A.id desc
    </select>

    <select id="selectADAuditByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        SELECT
        A.id,
        A.ad_path,
        A.file_name,
        A.md5,
        A.name,
        A.ins_id,
        A.ins_name,
        A.pub_time,
        A.cr_time,
        A.description,
        A.start_time,
        A.end_time,
        A.type,
        A.factory_id,
        A.factory_name,
        A.term_type_id,
        A.term_type_name,
        A.ad_audit,
        A.term_group_id,
        A.ad_file_type,
        A.device_type,
        A.app_code,
        A.app_name,
        A.ad_pic_path,
        B.name termGroupName,
        aft.name AS adFileTypeName
        FROM
        coms_store_advert A
        LEFT JOIN coms_terminal_group B
        ON A.term_group_id = B.id
        LEFT JOIN
        (SELECT
        a.*
        FROM
        cpay_dict a
        LEFT JOIN cpay_dict b
        ON a.parent_id = b.id
        WHERE b.type = 'ad_file_type') aft
        ON aft.type = A.ad_file_type
        where 1=1 and A.ad_audit = 0
        <if test="name != null">
            and A.name like concat( concat ('%',#{name}),'%')
        </if>
        <if test="type != null and type != '' ">
            and A.type = '${type}'
        </if>
        <if test="adFileType != null and adFileType != '' ">
            and A.ad_file_type = '${adFileType}'
        </if>
        <if test="factoryId != null" >
            and  A.factory_id = #{factoryId,jdbcType=INTEGER}
        </if>
        <if test="termTypeId != null" >
            and  A.term_type_id = #{termTypeId,jdbcType=INTEGER}
        </if>
        <if test="insId != null">
            and A.ins_id IN (
            SELECT s1.id FROM cpay_institution s1 WHERE
            locate ((SELECT s2.detail FROM cpay_institution s2
            WHERE s2.id = #{insId,jdbcType=INTEGER}
            ),s1.detail)>0
            )
        </if>
        order by A.id desc

    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete from coms_store_advert where id in
        <foreach collection="list" index="index" open="("  separator=","
                 close=")" item="id">
            #{id}
        </foreach>
    </delete>
    <delete id="passByIds" parameterType="java.util.List">
        update coms_store_advert set ad_audit=1,is_disabled=0,pub_time=NOW() where id in
        <foreach collection="list" index="index" open="("  separator=","
                 close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="rejectByIds" parameterType="java.util.List">
        update coms_store_advert set ad_audit=2 where id in
        <foreach collection="list" index="index" open="("  separator=","
                 close=")" item="id">
            #{id}
        </foreach>
    </delete>
    <select id="getAdAuditAmount" parameterType="java.lang.Integer"
            resultType="java.lang.Integer">
        select count(*) from (select a.id
                              from coms_store_advert a
                              WHERE a.ad_audit ='0' and a.ins_id in
                                                        (SELECT s1.id from
                                                            cpay_institution s1 where
                                                                 locate((SELECT s2.detail
                                                                         from cpay_institution s2 where
                                                                             s2.id=#{_parameter}),s1.detail)>0))t

    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from coms_store_advert
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from coms_store_advert
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        insert into coms_store_advert ( ad_path, file_name,
                                        md5, name, ins_id, ins_name,
                                        pub_time, cr_time, description,
                                        start_time, end_time, type, factory_id,
                                        factory_name, term_type_id, term_type_name,
                                        ad_audit, term_group_id, ad_file_type,
                                        device_type, app_code, app_name,
                                        ad_pic_path)
        values ( #{adPath,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
                 #{md5,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{insId,jdbcType=INTEGER}, #{insName,jdbcType=VARCHAR},
                 #{pubTime,jdbcType=TIMESTAMP}, #{crTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR},
                 #{startTime,jdbcType=DATE}, #{endTime,jdbcType=DATE}, #{type,jdbcType=CHAR}, #{factoryId,jdbcType=INTEGER},
                 #{factoryName,jdbcType=VARCHAR}, #{termTypeId,jdbcType=INTEGER}, #{termTypeName,jdbcType=VARCHAR},
                 #{adAudit,jdbcType=CHAR}, #{termGroupId,jdbcType=INTEGER}, #{adFileType,jdbcType=CHAR},
                 #{deviceType,jdbcType=CHAR}, #{appCode,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR},
                 #{adPicPath,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        insert into coms_store_advert
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="adPath != null" >
                ad_path,
            </if>
            <if test="fileName != null" >
                file_name,
            </if>
            <if test="md5 != null" >
                md5,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="insId != null" >
                ins_id,
            </if>
            <if test="insName != null" >
                ins_name,
            </if>
            <if test="pubTime != null" >
                pub_time,
            </if>
            <if test="crTime != null" >
                cr_time,
            </if>
            <if test="description != null" >
                description,
            </if>
            <if test="startTime != null" >
                start_time,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="factoryId != null" >
                factory_id,
            </if>
            <if test="factoryName != null" >
                factory_name,
            </if>
            <if test="termTypeId != null" >
                term_type_id,
            </if>
            <if test="termTypeName != null" >
                term_type_name,
            </if>
            <if test="adAudit != null" >
                ad_audit,
            </if>
            <if test="termGroupId != null" >
                term_group_id,
            </if>
            <if test="adFileType != null" >
                ad_file_type,
            </if>
            <if test="deviceType != null" >
                device_type,
            </if>
            <if test="appCode != null" >
                app_code,
            </if>
            <if test="appName != null" >
                app_name,
            </if>
            <if test="adPicPath != null" >
                ad_pic_path,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="adPath != null" >
                #{adPath,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null" >
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null" >
                #{md5,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="insId != null" >
                #{insId,jdbcType=INTEGER},
            </if>
            <if test="insName != null" >
                #{insName,jdbcType=VARCHAR},
            </if>
            <if test="pubTime != null" >
                #{pubTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crTime != null" >
                #{crTime,jdbcType=TIMESTAMP},
            </if>
            <if test="description != null" >
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                #{startTime,jdbcType=DATE},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=DATE},
            </if>
            <if test="type != null" >
                #{type,jdbcType=CHAR},
            </if>
            <if test="factoryId != null" >
                #{factoryId,jdbcType=INTEGER},
            </if>
            <if test="factoryName != null" >
                #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="termTypeId != null" >
                #{termTypeId,jdbcType=INTEGER},
            </if>
            <if test="termTypeName != null" >
                #{termTypeName,jdbcType=VARCHAR},
            </if>
            <if test="adAudit != null" >
                #{adAudit,jdbcType=CHAR},
            </if>
            <if test="termGroupId != null" >
                #{termGroupId,jdbcType=INTEGER},
            </if>
            <if test="adFileType != null" >
                #{adFileType,jdbcType=CHAR},
            </if>
            <if test="deviceType != null" >
                #{deviceType,jdbcType=CHAR},
            </if>
            <if test="appCode != null" >
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="appName != null" >
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="adPicPath != null" >
                #{adPicPath,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        update coms_store_advert
        <set >
            <if test="adPath != null" >
                ad_path = #{adPath,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null" >
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null" >
                md5 = #{md5,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="insId != null" >
                ins_id = #{insId,jdbcType=INTEGER},
            </if>
            <if test="insName != null" >
                ins_name = #{insName,jdbcType=VARCHAR},
            </if>
            <if test="pubTime != null" >
                pub_time = #{pubTime,jdbcType=TIMESTAMP},
            </if>
            <if test="crTime != null" >
                cr_time = #{crTime,jdbcType=TIMESTAMP},
            </if>
            <if test="description != null" >
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                start_time = #{startTime,jdbcType=DATE},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=DATE},
            </if>
            <if test="type != null" >
                type = #{type,jdbcType=CHAR},
            </if>
            <if test="factoryId != null" >
                factory_id = #{factoryId,jdbcType=INTEGER},
            </if>
            <if test="factoryName != null" >
                factory_name = #{factoryName,jdbcType=VARCHAR},
            </if>
            <if test="termTypeId != null" >
                term_type_id = #{termTypeId,jdbcType=INTEGER},
            </if>
            <if test="termTypeName != null" >
                term_type_name = #{termTypeName,jdbcType=VARCHAR},
            </if>
            <if test="adAudit != null" >
                ad_audit = #{adAudit,jdbcType=CHAR},
            </if>
            <if test="termGroupId != null" >
                term_group_id = #{termGroupId,jdbcType=INTEGER},
            </if>
            <if test="adFileType != null" >
                ad_file_type = #{adFileType,jdbcType=CHAR},
            </if>
            <if test="deviceType != null" >
                device_type = #{deviceType,jdbcType=CHAR},
            </if>
            <if test="appCode != null" >
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="appName != null" >
                app_name = #{appName,jdbcType=VARCHAR},
            </if>
            <if test="adPicPath != null" >
                ad_pic_path = #{adPicPath,jdbcType=VARCHAR},
            </if>
            <if test="isDisabled != null" >
                is_disabled = #{isDisabled,jdbcType=CHAR},
            </if>
            <if test="auditOpinion != null" >
                audit_opinion = #{auditOpinion,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert" >
        update coms_store_advert
        set ad_path = #{adPath,jdbcType=VARCHAR},
            file_name = #{fileName,jdbcType=VARCHAR},
            md5 = #{md5,jdbcType=VARCHAR},
            name = #{name,jdbcType=VARCHAR},
            ins_id = #{insId,jdbcType=INTEGER},
            ins_name = #{insName,jdbcType=VARCHAR},
            pub_time = #{pubTime,jdbcType=TIMESTAMP},
            cr_time = #{crTime,jdbcType=TIMESTAMP},
            description = #{description,jdbcType=VARCHAR},
            start_time = #{startTime,jdbcType=DATE},
            end_time = #{endTime,jdbcType=DATE},
            type = #{type,jdbcType=CHAR},
            factory_id = #{factoryId,jdbcType=INTEGER},
            factory_name = #{factoryName,jdbcType=VARCHAR},
            term_type_id = #{termTypeId,jdbcType=INTEGER},
            term_type_name = #{termTypeName,jdbcType=VARCHAR},
            ad_audit = #{adAudit,jdbcType=CHAR},
            term_group_id = #{termGroupId,jdbcType=INTEGER},
            ad_file_type = #{adFileType,jdbcType=CHAR},
            device_type = #{deviceType,jdbcType=CHAR},
            app_code = #{appCode,jdbcType=VARCHAR},
            app_name = #{appName,jdbcType=VARCHAR},
            ad_pic_path = #{adPicPath,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByCond" resultMap="BaseResultMap"
            parameterType="com.centerm.cpay.coms.dao.pojo.AdSelectCondition">
        select * from (
        select tmp.*, (@rownum := @rownum + 1) as ROWNUM
        from (
        select
        <include refid="Base_Column_List" />
        from coms_store_advert
        where ins_id in (select org.id from cpay_institution org where org.code in (${insList}))
        and device_type in ('3', #{deviceType, jdbcType=CHAR})
        and ad_audit = '1'
        and type = '4'
        and is_disabled = '0'
        <if test="termGroupId != 0">
            and (term_group_id = #{termGroupId, jdbcType=INTEGER} or term_group_id = 0)
        </if>
        <if test="termGroupId == 0">
            and term_group_id = 0
        </if>
        and start_time <![CDATA[ <= ]]> CURRENT_DATE()
        and end_time <![CDATA[>=]]> CURRENT_DATE()
        <if test="appCode != null">
            and app_code = #{appCode, jdbcType=VARCHAR}
        </if>
        order by pub_time desc
        ) as tmp,
        (select @rownum := 0) r
        ) as result
        where ROWNUM <![CDATA[>=]]> 1 and ROWNUM <![CDATA[ <= ]]> 5
    </select>
    <update id="adEnabled" parameterType="com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert">
        update coms_store_advert set is_disabled = #{isDisabled,jdbcType=CHAR},pub_time=#{pubTime} where id= #{id,jdbcType=INTEGER}
    </update>
</mapper>