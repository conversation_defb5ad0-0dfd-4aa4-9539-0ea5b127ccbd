package com.centerm.cpay.coms.dao.pojo;

import java.util.Date;

public class AgentMatters {
    private Integer id;

    private Integer insId;

    private Byte matterType;

    private String matterDesc;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInsId() {
        return insId;
    }

    public void setInsId(Integer insId) {
        this.insId = insId;
    }

    public Byte getMatterType() {
        return matterType;
    }

    public void setMatterType(Byte matterType) {
        this.matterType = matterType;
    }

    public String getMatterDesc() {
        return matterDesc;
    }

    public void setMatterDesc(String matterDesc) {
        this.matterDesc = matterDesc == null ? null : matterDesc.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}