package com.centerm.cpay.coms.service.impl;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.enums.DriverTypeEnum;
import com.centerm.cpay.coms.enums.FileUploadStatusEnum;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.service.FileUploadService;
import com.centerm.cpay.coms.util.storage.ApkFastFdsUtil;
import com.centerm.cpay.coms.util.xmlUtils.Config;
import com.centerm.cpay.coms.util.xmlUtils.Item;
import com.centerm.cpay.coms.util.xmlUtils.XmlToBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 *【文件上传】FastDFS
 * <AUTHOR>
 * @date 2024/3/12 09:55
 */
@Slf4j
@Service(value = "fastdfs")
public class UploadFileToFastDFS implements FileUploadService {

    @Resource
    private DriverUploadService driverUploadService;

    @Override
    public ResultMsg uploadDriverFile(ComsAppVersion entity) {
        String allPath = entity.getAppPath();
        String fileNamePre = entity.getFileName().substring(0,entity.getFileName().lastIndexOf("."));
        String iconPath = entity.getIconPath();
        Boolean invalid = validIconPath(entity.getIconPath());
        if (invalid) return new ResultMsg(3, "Image save failed", null);

        log.info("appPath:" + allPath);
        FileInfo fileInfo;
        ResultMsg result;
        String unzipDir = "";
        if (DriverTypeEnum.checkApkType(entity.getType())) {

            try {
                fileInfo = ApkFastFdsUtil.getSignedFileCustomer(entity.getAppPath(), entity.getFileName());
            } catch (Exception e) {
                log.info("【上传文件】FastDFS发生异常:「{}」", e.toString());
                return new ResultMsg(3, "Application upload failed. Please upload again", null);
            }
            if (CtUtils.isEmpty(fileInfo.getFileMd5()) || CtUtils.isEmpty(fileInfo.getFileSize()) ||
                    CtUtils.isEmpty(fileInfo.getFileSavePath())) {
                return new ResultMsg(3, "Application upload failed. Please upload again", null);
            }
            log.info("保存文件大小:『{}』", fileInfo.getFileSize());
            entity.setMd5(fileInfo.getFileMd5());
            entity.setAppPath(fileInfo.getFileSavePath());
            entity.setFileName(fileInfo.getFile_save_name());
            entity.setSize(fileInfo.getFileSize());
            entity.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());
            log.info("【上传文件】存储路径:「{}」", entity.getAppPath());
            result = driverUploadService.driverUploadAdd(entity);
        } else {
            log.info("oldFilePath:" + allPath);
            fileInfo = ApkFastFdsUtil.getSignedFileCustomer(entity.getAppPath(), entity.getFileName());
            if(CtUtils.isEmpty(fileInfo.getFileMd5()) || CtUtils.isEmpty(fileInfo.getFileSize()) || CtUtils.isEmpty(fileInfo.getFileSavePath())) {
                return new ResultMsg(3, "Package upload failed. Please upload again", null);
            }
            entity.setMd5(fileInfo.getFileMd5());
            entity.setSize(fileInfo.getFileSize());
            entity.setFileName(fileInfo.getFile_save_name());
            entity.setAppPath(fileInfo.getFileSavePath());
            entity.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());
            result = driverUploadService.driverUploadAdd(entity);
            if(result.getStatus() == ResultMsg.SUCCESS_CODE && DriverTypeEnum.checkSystemFile(entity.getType())) {
                log.info("开始进行系统镜像上传");
                Config config = null;
                List<Item> items = new ArrayList<>();
                unzipDir = unzipRootDir + "/" +fileNamePre;
                try {
                    //获取解压文件的settings信息
                    config = XmlToBean.getConfig(unzipDir + "/" + settingsHeader);
                    items = config.getItems().getItems();
                } catch (IOException | JAXBException e) {
                    e.printStackTrace();
                }
                log.info("系统镜像配置文件解析成功");

                int appVersionId = (int) result.getData();
                ComsAppVerDetail comsAppVerDetail = new ComsAppVerDetail();
                comsAppVerDetail.setAppVersionId(appVersionId);
                log.info("开始进行系统镜像差分包上传,差分包个数："+items.size());
                for(Item item : items){
                    log.info("oldFile: "+unzipDir+"/"+item.getFilename());
                    fileInfo = ApkFastFdsUtil.getSignedFileCustomer2(unzipDir+"/"+item.getFilename(), item.getFilename());
                    String[] versionArr = item.getVersion().split(",");
                    for(String version : versionArr){
                        comsAppVerDetail.setAppVersion(version);
                        comsAppVerDetail.setFileName(fileInfo.getFile_save_name());
                        comsAppVerDetail.setAppPath(fileInfo.getFileSavePath());
                        log.info("newFile: " + fileInfo.getFileSavePath());
                        comsAppVerDetail.setMd5(fileInfo.getFileMd5());
                        comsAppVerDetail.setSize(fileInfo.getFileSize());
                        driverUploadService.insertAppVerDetail(comsAppVerDetail);
                    }
                }
                log.info("系统镜像差分包上传完成");
            }
        }

        //【删除】临时文件
        deleteTempFile(result, allPath, unzipDir, iconPath);
        return result;
    }

    @Override
    public void deleteDriverFile(String appPath) {
        log.info("【开始删除】文件：路径「{}」", appPath);
        if(!CtUtils.isEmpty(appPath)){
            ApkFastFdsUtil.deleteFile(appPath);
            log.info("【删除成功】文件：路径「{}」", appPath);
        }
    }
}