<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsAppVersionDetailMapper" >

    <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="app_version_id" property="appVersionId" jdbcType="INTEGER" />
        <result column="TYPE" property="type" jdbcType="CHAR" />
        <result column="APP_VERSION" property="appVersion" jdbcType="VARCHAR" />
        <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
        <result column="MD5" property="md5" jdbcType="VARCHAR" />
        <result column="APP_PATH" property="appPath" jdbcType="VARCHAR" />
        <result column="SIZE" property="size" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List" >
      id,app_version_id,TYPE,APP_VERSION,FILE_NAME,MD5,APP_PATH,SIZE
    </sql>
    <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail">
        insert into
        COMS_APP_VERSION_DETAIL(
        app_version_id,TYPE,APP_VERSION,FILE_NAME,MD5,APP_PATH,SIZE
        ) values (
        #{appVersionId,jdbcType=INTEGER},#{type,jdbcType=CHAR},#{appVersion,jdbcType=VARCHAR},
        #{fileName,jdbcType=VARCHAR},#{md5,jdbcType=VARCHAR},#{appPath,jdbcType=VARCHAR},
        #{size,jdbcType=INTEGER}
        )
    </insert>
    <select id="selectByVersionId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from COMS_APP_VERSION_DETAIL
        where APP_VERSION_ID = #{appVersionId,jdbcType=INTEGER}
    </select>
</mapper>