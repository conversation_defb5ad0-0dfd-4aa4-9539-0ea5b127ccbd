
package com.centerm.cpay.coms.service;


import java.util.List;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.ComsAppVersionDetailMapper;
import com.centerm.cpay.coms.dao.pojo.ComsAppVerDetail;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;;

public interface DriverUploadService {
	EUDataGridResult getGridList(ComsAppVersion entity, int page, int rows);

	ResultMsg deleteById(Integer id);
	int appFileCount(String md5);

	ResultMsg insertEntity(ComsAppVersion entity);

	ResultMsg updateByEntity(ComsAppVersion entity);
	
	ResultMsg deleteByIds(List<String> ids);

	ResultMsg driverUploadAdd(ComsAppVersion entity);
	int insertAppVerDetail(ComsAppVerDetail entity);

	List<ComsAppVersion> selectByPrimaryKey(Integer id);
	List<ComsAppVersion> getAppVersions(ComsAppVersion entity);

	List<ComsAppVersion> selectConditions(ComsAppVersion entity);
	
	List<ComsAppVersion> selectRom(ComsAppVersion entity);

	ResultMsg driverUploadupdate(ComsAppVersion entity);

	ComsAppVersion queryByJobId(Integer id);

	ResultMsg updateDriverUpload(ComsAppVersion entity);
}
