package com.centerm.cpay.coms.dao.mapper;

import java.util.List;

import com.centerm.cpay.coms.dao.pojo.UserRole;

public interface UserRoleMapper {
	int deleteByPrimaryKey(String id);
    int deleteByUserId(Integer userId);
	List<Integer> selectByUserId(Integer userId);

	int insert(UserRole record);

	int insertSelective(UserRole record);

	UserRole selectByPrimaryKey(String id);

	int updateByPrimaryKeySelective(UserRole record);

	int updateByPrimaryKey(UserRole record);
}