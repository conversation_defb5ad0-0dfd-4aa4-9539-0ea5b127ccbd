<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.centerm.cpay.coms.dao.mapper.ComsCasTermWarrantyMapper" >
  <resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="term_seq" property="termSeq" jdbcType="VARCHAR" />
    <result column="warranty_id" property="warrantyId" jdbcType="INTEGER" />
    <result column="warranty_name" property="warrantyName" jdbcType="VARCHAR" />
    <result column="order_date" property="orderDate" jdbcType="TIMESTAMP" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="state" property="state" jdbcType="CHAR" />
  </resultMap>
  
  <sql id="INS_LIST" >
    (SELECT id from cpay_institution
 	where detail like  CONCAT(concat('%',(SELECT detail from cpay_institution where id=#{insId})),'%'))
  </sql>
  
  <sql id="Base_Column_List" >
    id, order_no, term_seq, warranty_id, order_date, start_date, end_date, state
  </sql>
  
  <sql id="Base_Acolumn_List" >
    a.id, a.order_no, a.term_seq, a.warranty_id, a.order_date, a.start_date, a.end_date, a.state
  </sql>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from coms_cas_term_warranty
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="selectByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from coms_cas_term_warranty
    where order_no = #{orderNo}
  </select>
  
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    select 
    <include refid="Base_Acolumn_List"/>,c.name AS warranty_name 
    from coms_cas_term_warranty a     
    INNER JOIN  coms_terminal_info b on a.term_seq = b.term_seq
    LEFT JOIN coms_cas_warranty c ON c.id = a.warranty_id
    where b.ins_id in <include refid="INS_LIST" /> 
    <if test="termSeq != null">
		and a.term_seq like concat (concat('%',#{termSeq}),'%')
	</if>
	<if test="orderNo != null">
		and a.order_no like concat (concat('%',#{orderNo}),'%')
	</if>
	 order by order_date desc
  </select>
  
  <select id="selectByEntity" resultMap="BaseResultMap" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    select 
    <include refid="Base_Column_List"/>
    from coms_cas_term_warranty
    where 1=1
    <if test="orderNo != null" >
          and  order_no=#{orderNo,jdbcType=VARCHAR}
      </if>
  </select>
  
  <delete id="deleteByIds" parameterType="java.util.List">
		delete from coms_cas_term_warranty where id in
		<foreach collection="list" index="index" open="("  separator=","
			close=")" item="id">
			#{id}
		</foreach>
  </delete>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from coms_cas_term_warranty
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    insert into coms_cas_term_warranty ( order_no, term_seq, 
      warranty_id, order_date, start_date, 
      end_date, state)
    values ( #{orderNo,jdbcType=VARCHAR}, #{termSeq,jdbcType=VARCHAR}, 
      #{warrantyId,jdbcType=INTEGER}, #{orderDate,jdbcType=TIMESTAMP}, #{startDate,jdbcType=TIMESTAMP}, 
      #{endDate,jdbcType=TIMESTAMP}, #{state,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    insert into coms_cas_term_warranty
    <trim prefix="(" suffix=")" suffixOverrides="," >
      
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="termSeq != null" >
        term_seq,
      </if>
      <if test="warrantyId != null" >
        warranty_id,
      </if>
      <if test="orderDate != null" >
        order_date,
      </if>
      <if test="startDate != null" >
        start_date,
      </if>
      <if test="endDate != null" >
        end_date,
      </if>
      <if test="state != null" >
        state,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="termSeq != null" >
        #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="warrantyId != null" >
        #{warrantyId,jdbcType=INTEGER},
      </if>
      <if test="orderDate != null" >
        #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        #{state,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    update coms_cas_term_warranty
    <set >
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="termSeq != null" >
        term_seq = #{termSeq,jdbcType=VARCHAR},
      </if>
      <if test="warrantyId != null" >
        warranty_id = #{warrantyId,jdbcType=INTEGER},
      </if>
      <if test="orderDate != null" >
        order_date = #{orderDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null" >
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        state = #{state,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty" >
    update coms_cas_term_warranty
    set order_no = #{orderNo,jdbcType=VARCHAR},
      term_seq = #{termSeq,jdbcType=VARCHAR},
      warranty_id = #{warrantyId,jdbcType=INTEGER},
      order_date = #{orderDate,jdbcType=TIMESTAMP},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      state = #{state,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByTermSeq" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List"/>
    from coms_cas_term_warranty       
    where  term_seq = #{termSeq,jdbcType=VARCHAR}
  </select>
</mapper>