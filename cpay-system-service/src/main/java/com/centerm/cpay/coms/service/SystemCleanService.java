package com.centerm.cpay.coms.service;

import java.util.List;
import java.util.Map;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.pojo.SystemClean;

public interface SystemCleanService {

	public EUDataGridResult getSystemCleanList(SystemClean systemClean, Integer page, Integer rows);

	public List<SystemClean> queryTableInfoById(Integer id);

	public void deleteInfo(Map map);

	public ResultMsg deleteSystemClean(List<String> idsList);

	public Integer getCleanAmount(Integer insId);

	public ResultMsg deleteInsOrTerm(SystemClean systemClean);

}
