
package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.EUDataGridResult;
import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.common.utils.PropertyUtil;
import com.centerm.cpay.coms.dao.mapper.ComsStoreAdvertMapper;
import com.centerm.cpay.coms.dao.pojo.AdSelectCondition;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.dao.pojo.ComsStoreAdvert;
import com.centerm.cpay.coms.dao.pojo.Institution;
import com.centerm.cpay.coms.dao.pojo.SysConfigure;
import com.centerm.cpay.coms.dao.pojo.Terminal;
import com.centerm.cpay.coms.dao.pojo.TerminalType;
import com.centerm.cpay.coms.response.bean.RomInfo;
import com.centerm.cpay.coms.service.AdvertService;
import com.centerm.cpay.coms.service.DriverUploadService;
import com.centerm.cpay.coms.service.InstitutionService;
import com.centerm.cpay.coms.service.SysConfigureService;
import com.centerm.cpay.coms.service.TerminalService;
import com.centerm.cpay.coms.service.TerminalTypeService;
import com.centerm.cpay.coms.util.SystemUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class AdvertServiceImpl implements AdvertService  {

	@Autowired
	private InstitutionService institutionService;
	@Autowired
	private TerminalService terminalService;
	@Autowired
	private ComsStoreAdvertMapper advertMapper;
	@Autowired
	private DriverUploadService driverUploadService;
	@Autowired
	private TerminalTypeService terminalTypeService;
	@Autowired
	private SysConfigureService sysConfigureService;

	@Override
	public ComsStoreAdvert getByPrimaryKey(Integer id){
		return advertMapper.selectByPrimaryKey(id);
	}

	@Override
	public EUDataGridResult getAdvertList(ComsStoreAdvert entity,
										  int page, int rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<ComsStoreAdvert> list = advertMapper.selectByCondition(entity);
		/*for(ComsStoreAdvert advert:list){
			Factory factory =factoryService.getFactoryById(advert.getFactoryId());
			TerminalType terminalType=terminalTypeService.selectByPrimaryKey(advert.getTermTypeId());
			if(factory != null){
				advert.setFactoryName(factory.getName());
			}
			if(terminalType != null){
				advert.setTermTypeName(terminalType.getRemark());
			}

		}*/
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<ComsStoreAdvert> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public EUDataGridResult getAdAuditList(ComsStoreAdvert entity,
										   int page, int rows) {
		// 分页处理
		PageHelper.startPage(page, rows);
		List<ComsStoreAdvert> list = advertMapper.selectADAuditByCondition(entity);
		/*for(ComsStoreAdvert advert:list){
			Factory factory =factoryService.getFactoryById(advert.getFactoryId());
			TerminalType terminalType=terminalTypeService.selectByPrimaryKey(advert.getTermTypeId());
			if(factory != null){
				advert.setFactoryName(factory.getName());
			}
			if(terminalType != null){
				advert.setTermTypeName(terminalType.getRemark());
			}

		}*/
		// 创建一个返回值对象
		EUDataGridResult result = new EUDataGridResult();
		result.setRows(list);
		// 取记录总条数
		PageInfo<ComsStoreAdvert> pageInfo = new PageInfo<>(list);
		result.setTotal(pageInfo.getTotal());
		return result;
	}

	@Override
	public ResultMsg deleteById(Integer id) {
		// TODO
		advertMapper.deleteByPrimaryKey(id);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg insertEntity(ComsStoreAdvert entity) {
		if(entity.getType().equals("2") && entity.getAdFileType().equals("4")){
			entity.setAdPath(entity.getAdPicPath());
		}

		if(entity.getType().equals("3") && entity.getAdFileType().equals("4")){
			entity.setAdPath(entity.getAdPicPath());
		}
		if(CtUtils.isEmpty(entity.getAdPath())){
			entity.setAdPath(entity.getAdPicPath());
		}
		if (CtUtils.isEmpty(entity.getTermGroupId())) {
			entity.setTermGroupId(0);
		}
		advertMapper.insertSelective(entity);
		// TODO
		return ResultMsg.success();
	}

	@Override
	public ResultMsg updateByEntity(ComsStoreAdvert entity) {
		if(CtUtils.isEmpty(entity.getAdPath())){
			entity.setAdPath(entity.getAdPicPath());
			entity.setAdPicPath(entity.getAdPicPath());
		}
		if("1".equals(entity.getAdFileType())){
			entity.setAdPath(entity.getAdPicPath());
		}
		advertMapper.updateByPrimaryKey(entity);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg deleteByIds(List<String> ids) {
		// TODO
		advertMapper.deleteByIds(ids);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg passByIds(List<String> ids) {
		// TODO
		advertMapper.passByIds(ids);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg rejectByIds(List<String> ids) {
		// TODO
		advertMapper.rejectByIds(ids);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg queryByAppType(String termSeq,String screenType) {

		Terminal terminal = terminalService.selectByTermSeq(termSeq);
		if(terminal == null){
			return ResultMsg.fail("无此终端信息");
		}
		Institution institution = institutionService.selectByInsId(terminal.getInsId());
		if(institution == null){
			return ResultMsg.fail("无此机构信息");
		}
		AdSelectCondition condition = new AdSelectCondition();
		condition.setTermGroupId(terminal.getTerminalGroupId());
		condition.setDeviceType(screenType);
		condition.setInsList(institution.getDetail());
		List<ComsStoreAdvert> adList = advertMapper.selectByCond(condition);
		if(adList == null ||adList.size() == 0){
			return ResultMsg.fail("无符合条件的广告信息");
		}
		return ResultMsg.success(SystemUtil.changeAdPojoListToAdReponseList(adList,getDownload()));
	}

	@Override
	public Integer getAdAuditAmount(Integer insId) {

		return advertMapper.getAdAuditAmount(insId);
	}

	@Override
	public void adEnabled(ComsStoreAdvert entity) {

		advertMapper.adEnabled(entity);
	}

	@Override
	public ResultMsg audit(ComsStoreAdvert entity) {
		advertMapper.updateByPrimaryKeySelective(entity);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg queryByRom(String termid,String osVersoin,String insCd) {

		//查询终端信息
		Terminal terminal = terminalService.selectByTermSeq(termid);
		if(terminal == null){
			return ResultMsg.fail("无此终端信息");
		}
		List<TerminalType> terminalType =terminalTypeService.selectTermTypeByCode(terminal.getTermTypeCode());
		if(terminalType.size()<=0){
			return ResultMsg.fail("终端机型有误信息");
		}


		ComsAppVersion comsAppVersion = new ComsAppVersion();
		comsAppVersion.setTermTypeCodeStr(terminalType.get(0).getCode());
		comsAppVersion.setRemark(insCd);
		comsAppVersion.setAppCode("full.os.rom");

		List<ComsAppVersion> AppVersion = driverUploadService.selectRom(comsAppVersion);
		//当前机构下该型号终端最新版本
		if(AppVersion.size()<=0){
			return ResultMsg.fail("当前版本已经是最新版本");
		}

		String highversion = AppVersion.get(0).getAppVersion();
		if(highversion.equals(osVersoin)){
			return ResultMsg.fail("当前版本已经是最新版本");
		}
		//开始查询差量包
		ComsAppVersion componentcomsAppVersion = new ComsAppVersion();
		componentcomsAppVersion.setTermTypeCodeStr(terminalType.get(0).getCode());
		componentcomsAppVersion.setRemark(insCd);
		componentcomsAppVersion.setAppCode("component.os.rom");

		String appPath = "";
		String pkgFlag = "0";
		String Version = "";
		List<ComsAppVersion> AppVersion1 = driverUploadService.selectRom(componentcomsAppVersion);
		for(int i=0;i<AppVersion1.size();i++){
			String ver[] = AppVersion1.get(i).getAppVersion().split("-");
			String ver1 =  ver[0];
			String ver2 =  ver[1];
			if(ver2.equals(highversion)){
				if(ver1.equals(osVersoin)){
					appPath = AppVersion1.get(i).getAppPath();
					Version = AppVersion1.get(i).getAppVersion();
					pkgFlag = "1";
				}

			}
		}
		if(appPath.equals("")){
			appPath = AppVersion.get(0).getAppPath();
			Version = AppVersion1.get(0).getAppVersion();
		}
		/**
		 * 往里面加要传的参数
		 */
		RomInfo romInfo = new RomInfo();
		romInfo.setOsVersoin(Version);
		romInfo.setPackageUrl(appPath);
		romInfo.setPkgFlag(pkgFlag);

		return ResultMsg.success(romInfo);
	}
	private String getDownload() {

		SysConfigure sysConfigure = sysConfigureService.getSysConfByKey("download_url_intranet");
		return sysConfigure.getValue();
	}
}