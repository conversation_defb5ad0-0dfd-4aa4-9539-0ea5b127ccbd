package com.centerm.cpay.coms.enums;

import java.util.Objects;

/**
 *【文件上传】策略
 */
public enum FileUploadStrategyEnum {
    //【文件上传策略】R2 CDN
    CLOUD_STORAGE("0", "r2"),
    //【文件上传策略】本地存储
    LOCAL_STORAGE("1", "local"),
    //【文件上传策略】本地存储
    FASTDFS_STORAGE("2", "fastdfs");

    //【上传策略】代码
    private final String code;

    //【上传策略】Service名称
    private final String name;

    //【构造方法】
    FileUploadStrategyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    /**
     *【读取】上传策略名称
     * @param code 上传策略代码
     * @return 上传策略名称
     */
    public static String getNameFromCode(String code) {
        for (FileUploadStrategyEnum index : FileUploadStrategyEnum.values()) {
            if (Objects.equals(index.code, code)) {
                return index.name;
            }
        }
        return LOCAL_STORAGE.name;
    }
}