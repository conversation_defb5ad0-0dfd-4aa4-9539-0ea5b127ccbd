package com.centerm.cpay.coms.service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.common.utils.ConfigInfo;
import com.centerm.cpay.common.utils.CtUtils;
import com.centerm.cpay.coms.dao.pojo.ComsAppVersion;
import com.centerm.cpay.coms.util.FilePathUtil;

import java.io.File;
import java.util.Objects;

/**
 *【文件上传】工具类
 * <AUTHOR>
 * @date 2024/3/12 09:42
 */
public interface FileUploadService {

    //【路径】文件分类路径
    String FILE_SAVE_PATH = FilePathUtil.getrelativePath("appsystempath");

    //【路径】文件头路径
    String FILE_HEAD_PATH = ConfigInfo.HEADER_PATH;

    //【配置文件】名称
    String settingsHeader = ConfigInfo.FILE_SETTING_HEADER;

    //【解压缩】文件路径
    String unzipRootDir = ConfigInfo.FILE_UPLOAD_TEMP;

    /**
     *【上传】系统固件/APK文件
     * @param entity 文件信息
     * @return 上传结果
     */
    ResultMsg uploadDriverFile(ComsAppVersion entity);

    /**
     *【删除】系统文件
     * @param appPath 文件路径
     */
    void deleteDriverFile(String appPath);

    /**
     *【删除】临时文件
     * @param result 更新结果
     * @param fullPath 文件完整路径
     * @param unzipDir 解压缩路基
     * @param iconPath 图标路径
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    default void deleteTempFile(ResultMsg result, String fullPath, String unzipDir, String iconPath) {
        if (result.getStatus() == ResultMsg.SUCCESS_CODE) {
            //【删除】文件路径
            File file = new File(fullPath);
            if (file.exists()) {
                file.delete();
            }

            //【删除】解压缩路径
            File files = new File(unzipDir);
            if (files.isDirectory()) {
                for (File temp : Objects.requireNonNull(files.listFiles())) {
                    temp.delete();
                }
                files.delete();
            }

            //【删除】图片路径
            if (iconPath != null) {
                File tmp = new File(iconPath);
                if (tmp.exists()) {
                    tmp.delete();
                }
            }
        }
    }

    /**
     *【验证】图标信息
     * @param iconPath 图标路径
     * @return 验证结果
     */
    default Boolean validIconPath(String iconPath) {
        if ((!CtUtils.isEmpty(iconPath)) && iconPath.indexOf("..") > 0) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}