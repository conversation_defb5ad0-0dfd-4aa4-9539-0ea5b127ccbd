package com.centerm.cpay.coms.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.LoginMapper;
import com.centerm.cpay.coms.dao.pojo.User;
import com.centerm.cpay.coms.service.LoginService;

@Service
public class LoginServiceImpl implements LoginService {
	@Autowired
	private LoginMapper loginMapper;


	@Override
	public User login(User user) {
		User result = loginMapper.login(user);
		return result;
	}

	@Override
	public User sendCode(User user) {
		return loginMapper.queryOne(user);
	}

}
