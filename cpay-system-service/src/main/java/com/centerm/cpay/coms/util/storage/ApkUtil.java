package com.centerm.cpay.coms.util.storage;

import com.centerm.cpay.common.utils.*;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import static com.centerm.cpay.common.utils.ConfigInfo.R2_DOWNLOAD_URL;

@Slf4j
public class ApkUtil {

	//【初始化】MD5解析
	protected static MessageDigest messageDigest = null;
	static {
		try {
			messageDigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			log.error("【初始化失败】MessageDigest不支持MD5Util「{}」", FileMD5.class.getName());
		}
	}

	/**
	 *【上传文件】本地存储
	 * @param file 文件信息
	 * @param fileName 文件名称
	 * @param targetPath 目标路径
	 * @return 文件信息
	 */
	public static FileInfo uploadFileToLocalStorage(File file, String fileName, String targetPath) {
		FileInfo fileInf = null;
		try (InputStream is = Files.newInputStream(file.toPath())) {
			fileInf = uploadFiles(is, fileName, targetPath);
		} catch (Exception e) {
			log.error("【新增失败】上传APK文件【{}】信息失败，原因为「{}」", fileName, e.getMessage());
		}
		return fileInf;
	}

	/**
	 *【上传文件】Cloudflare R2
	 * @param file 上传文件信息
	 * @param fileName 文件名称
	 * @return 文件信息
	 */
	public static FileInfo uploadFileToR2Storage(File file, String fileName) {
		//【上传】Cloudflare R2文件（分配文件名称）
		log.info("【上传文件】Cloudflare R2：文件名称「{}」", fileName);
		String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
		String fileSaveName = CtUtils.uuid() + "." + fileType;
		R2StorageAPI r2StorageAPI = R2StorageAPI.getInstance();
		r2StorageAPI.uploadFile(fileSaveName, file);

		//【配置】文件信息
		log.info("【上传】APK文件成功：文件名称「{}」", fileSaveName);
		FileInfo fileInfo = new FileInfo();
		try {
			fileInfo.setFile_absolute_path(R2_DOWNLOAD_URL + "/" + fileSaveName);
			fileInfo.setUuid(CtUtils.uuid());
			fileInfo.setFile_save_name(fileSaveName);
			fileInfo.setFileOldName(fileName);
			fileInfo.setFileSavePath(fileSaveName);
			fileInfo.setFileSize((int) file.length());
			fileInfo.setFileMd5(getFileMD5String(file));
			fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
		} catch (Exception e) {
			log.error("【新增失败】上传APK文件【{}】信息失败，原因为「{}」", fileName, e.getMessage());
		}

		//【返回】文件信息
		log.info("【新增成功】上传APK文件：文件详细信息「{}」", fileInfo);
		return fileInfo;
	}

	/**
	 *【上传文件】本地路径
	 * @param inputStream 文件流
	 * @param fileName 文件名称
	 * @param targetPath 目标路径
	 * @return 文件信息
	 * @throws Exception 异常
	 */
	@SuppressWarnings("ResultOfMethodCallIgnored")
	public static FileInfo uploadFiles(InputStream inputStream, String fileName, String targetPath) throws Exception {
		//【准备】文件信息
		byte[] b = new byte[10240];
		String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
		String fileSavePath = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
		File oldFile = new File(targetPath + fileName);

		//【生成】目标路径
		try {
			File f = new File(targetPath + fileSavePath);
			if (!f.isDirectory()) {
				f.mkdirs();
				f.setExecutable(true);
				f.setReadable(true);
				f.setWritable(true);
			}
		} catch (Exception e) {
			log.error("【生成】目标路径发生异常「{}」", e.toString());
		}
	    try {
			FileUtil.setFilePermissions(new File(targetPath + fileSavePath));
		} catch (Exception e) {
			log.error("【配置】目标路径权限发生异常「{}」", e.toString());
		}

		//【生成】目标文件
		String fileSaveName = CtUtils.uuid() + "." + fileType;
		fileSavePath += fileSaveName;
		File destFile = new File(targetPath + fileSavePath);
		if (!destFile.exists()) {
			destFile.createNewFile();
			destFile.setExecutable(true);
			destFile.setReadable(true);
			destFile.setWritable(true);
			Runtime.getRuntime().exec(Constants.SET_FILE_PERMISSIONS + destFile);
		}

		//【写入】文件信息
		int length;
		try (OutputStream outputStream = Files.newOutputStream(destFile.toPath())) {
			while ((length = inputStream.read(b)) > 0) {
				outputStream.write(b, 0, length);
			}
		} catch (Exception e) {
			log.error("【写入】文件发生异常「{}」", e.toString());
		} finally {
			oldFile.delete();
		}

		//【配置】文件信息
		FileInfo fileInfo = new FileInfo();
		fileInfo.setFile_absolute_path(targetPath + fileSavePath);
		fileInfo.setUuid(CtUtils.uuid());
		fileInfo.setFile_save_name(fileSaveName);
		fileInfo.setFileOldName(fileName);
		fileInfo.setFileSavePath(fileSavePath);
		fileInfo.setFileSize((int) destFile.length());
		fileInfo.setFileMd5(getFileMD5String(destFile));
		fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
		return fileInfo;
	}

	/**
	 *【计算】文件的MD5哈希值
	 * @param file 要计算MD5哈希值的文件
	 * @return 文件的MD5哈希值
	 * @throws SecurityException 安全异常
	 * @throws IllegalArgumentException 异常参数
	 */
	public static String getFileMD5String(File file) throws SecurityException, IllegalArgumentException, IOException {
		byte[] bt;
		try (FileInputStream in = new FileInputStream(file); FileChannel ch = in.getChannel()) {
			//【映射】将文件内容映射到内存中的ByteBuffer
			MappedByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0, file.length());

			//【更新】buffer对应的字节序列
			messageDigest.update(byteBuffer);

			//【计算】哈希值
			bt = messageDigest.digest();
		}
		return bufferToHex(bt);
	}

	/**
	 *【转换】将字节数组转换为十六进制字符串
	 * @param bytes 要转换的字节数组
	 * @return 转换后的十六进制字符串
	 */
	private static String bufferToHex(byte[] bytes) {
		StringBuilder sb = new StringBuilder(2 * bytes.length);
		for (byte aByte : bytes) {
			appendHexPair(aByte, sb);
		}
		return sb.toString();
	}

	/**
	 *【转换】十六进制-》并添加到字符串缓冲区
	 * @param bt 字节
	 * @param stringBuffer 字符串缓冲区
	 */
	private static void appendHexPair(byte bt, StringBuilder stringBuffer) {
		char c0 = Character.forDigit((bt & 0xf0) >> 4, 16);
		char c1 = Character.forDigit(bt & 0xf, 16);
		stringBuffer.append(c0);
		stringBuffer.append(c1);
	}

	/**
	 *【文件拷贝】添加方法如果路径不存在 先创建路径
	 * @param newFilePath 新文件目录
	 * @param newFileName 新文件名称
	 * @param oldFilePath 老文件目录
	 */
	@SuppressWarnings("ResultOfMethodCallIgnored")
	public static void fileCopy(String newFilePath,String newFileName, String oldFilePath) {
		//【新增】文件目录
		File newFileTemp = new File(newFilePath);
		if (!newFileTemp.isDirectory()) {
			newFileTemp.mkdirs();
			newFileTemp.setExecutable(true);
			newFileTemp.setReadable(true);
			newFileTemp.setWritable(true);
		}

		//【复制】文件信息
		File newFile = new File(newFilePath + newFileName);
		try (FileInputStream inputStream = new FileInputStream(oldFilePath);
			 FileOutputStream outputStream = new FileOutputStream(newFile)) {
			byte[] b = new byte[1024];
			int len;
			try {
				len = inputStream.read(b);
				while (len != -1) {
					outputStream.write(b, 0, len);
					len = inputStream.read(b);
				}
			} catch (IOException e) {
				log.error("【读取】文件发生异常「{}」", e.toString());
			}
		} catch (IOException e) {
			log.error("【IO】文件发生异常「{}」", e.toString());
		}
	}

	/**
	 *【上传文件】Cloudflare R2
	 * @param appPath 文件路径
	 * @param fileName 文件名称
	 * @return 文件信息
	 */
	public static FileInfo getSignedFileCustomer(String appPath, String fileName) {
		File apkFile = new File(appPath);
		log.info("「R2 Storage」【开始上传】文件路径:「{}」文件名称『{}』", appPath, fileName);
		FileInfo fileInfo = ApkUtil.uploadFileToR2Storage(apkFile, fileName);
		log.info("「R2 Storage」【上传成功】文件信息「{}」", fileInfo);
		return fileInfo;
	}

	/**
	 *【上传文件】本地路径
	 * @param savePath 目标路径
	 * @param appPath 临时文件路径
	 * @param fileName 文件名称
	 * @return 文件信息
	 */
	public static FileInfo getSignedFileCustomer(String savePath, String appPath, String fileName) {
		File apkFile = new File(appPath);
		log.info("『本地存储』【开始保存】文件路径:「{}」文件名称『{}』", appPath, fileName);
		FileInfo fileInfo = ApkUtil.uploadFileToLocalStorage(apkFile, fileName, savePath);
		log.info("「本地存储」【保存成功】文件路径:「{}」文件名称『{}』", appPath, fileName);
		return fileInfo;
	}
}