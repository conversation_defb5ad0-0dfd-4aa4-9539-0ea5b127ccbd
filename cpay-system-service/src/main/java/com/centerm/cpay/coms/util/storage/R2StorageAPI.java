package com.centerm.cpay.coms.util.storage;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

import static com.centerm.cpay.common.utils.ConfigInfo.*;

/**
 *【Cloudflare R2】工具类
 * <AUTHOR>
 * @date 2024/3/5 17:44
 */
@Slf4j
public class R2StorageAPI {

    //【AWS】方法类
    private AmazonS3 client;
    private final AwsClientBuilder.EndpointConfiguration endpointConfiguration;
    private final AWSStaticCredentialsProvider awsStaticCredentialsProvider;
    private final String bucket;

    //【单例】实体
    private static R2StorageAPI r2StorageAPI = null;

    /**
     *【创建】单例模式
     * @return 实体类
     */
    public static R2StorageAPI getInstance() {
        if (r2StorageAPI == null) {
            r2StorageAPI = new R2StorageAPI(R2_URL, R2_ACCESS_KEY, R2_SECRET_KEY, R2_BUCKET);
        }
        return r2StorageAPI;
    }

    /**
     *【初始化】R2存储接口
     * @param apiUrl 请求地址
     * @param accessKey Access Key
     * @param secretKey Secret Key
     * @param bucket 桶名称
     */
    private R2StorageAPI(String apiUrl, String accessKey, String secretKey, String bucket) {
        this.endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(apiUrl, "auto");
        this.awsStaticCredentialsProvider = new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, secretKey));
        this.bucket = bucket;
        this.initialize();
    }

    /**
     *【初始化】AWS S3 API
     */
    public void initialize() {
        client = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(this.endpointConfiguration)
                .withCredentials(this.awsStaticCredentialsProvider)
                .build();
    }

    /**
     *【上传文件】Cloudflare R2/AWS S3
     * @param fileFullName 完整文件名称
     * @param file 待上传文件
     */
    public void uploadFile(String fileFullName, File file) {
        log.info("【准备上传】Cloudflare R2：文件完整名称「{}」, 文件大小『{}』", fileFullName, (int) file.length());
        this.client.putObject(new PutObjectRequest(this.bucket, fileFullName, file));
        log.info("【上传成功】Cloudflare R2：文件完整名称「{}」, 文件大小『{}』", fileFullName, (int) file.length());
    }

    /**
     *【删除文件】Cloudflare R2/AWS S3
     * @param fileFullName 完整文件名称
     */
    public void deleteFile(String fileFullName) {
        log.info("【准备删除】Cloudflare R2：文件完整名称「{}」", fileFullName);
        this.client.deleteObject(this.bucket, fileFullName);
        log.info("【删除成功】Cloudflare R2：文件完整名称「{}」", fileFullName);
    }

    public static R2StorageAPI create(String apiUrl, String accessKey, String secretKey, String bucket) {
        return new R2StorageAPI(apiUrl, accessKey, secretKey, bucket);
    }
}