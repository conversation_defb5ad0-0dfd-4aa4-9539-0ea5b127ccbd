
package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.common.pojo.ResultMsg;
import com.centerm.cpay.coms.dao.mapper.RoleFunctionMapper;
import com.centerm.cpay.coms.dao.pojo.RoleFunction;
import com.centerm.cpay.coms.service.RoleFunctionService;

/**
 * @Title: RoleFunctionServiceImpl.java
 * @Description: TODO
 * <AUTHOR>
 * @date 2016年6月7日 下午5:26:48
 * @version V1.0
 */
@Service
public class RoleFunctionServiceImpl implements RoleFunctionService {
	@Autowired
	private RoleFunctionMapper roleFunctionMapper;

	@Override
	public ResultMsg deleteRoleFunction(Integer roleId) {
		roleFunctionMapper.deleteByRoleId(roleId);
		return ResultMsg.success();
	}

	@Override
	public ResultMsg insertRoleFunction(RoleFunction roleFunction) {
		roleFunctionMapper.deleteByRoleId(roleFunction.getRoleId());
		String[] functionArray = roleFunction.getFunctions().split(",");
		for (String functionId : functionArray) {
			if(functionId == ""){
				continue;
			}
			RoleFunction roleFuc = new RoleFunction();
			roleFuc.setFunctionId(Integer.valueOf(functionId));
			roleFuc.setRoleId(roleFunction.getRoleId());
			roleFunctionMapper.insert(roleFuc);
		}
		return ResultMsg.success();
	}

	@Override
	public List<RoleFunction> getRoleFucList(Integer roleId) {
		return roleFunctionMapper.getRoleFuntion(roleId);
	}

}
