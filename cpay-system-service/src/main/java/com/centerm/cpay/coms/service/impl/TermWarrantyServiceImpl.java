package com.centerm.cpay.coms.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.centerm.cpay.coms.dao.mapper.ComsCasTermWarrantyMapper;
import com.centerm.cpay.coms.dao.pojo.ComsCasTermWarranty;
import com.centerm.cpay.coms.service.TermWarrantyService;
@Service
public class TermWarrantyServiceImpl implements TermWarrantyService {

	@Autowired
	private ComsCasTermWarrantyMapper comsCasTermWarrantyMapper;
	@Override
	
	public List<ComsCasTermWarranty> queryTerminalWarrantyRelByTermSeq(String termSeq) {
		return comsCasTermWarrantyMapper.selectByTermSeq(termSeq);
	}

	

}
