package com.centerm.cpay.coms.util.storage;

import com.centerm.cpay.common.fastdfs_client.FastdfsClient;
import com.centerm.cpay.common.utils.*;
import com.centerm.cpay.coms.dao.pojo.FileInfo;
import com.centerm.cpay.coms.util.FilePathUtil;
import org.csource.common.MyException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.nio.ch.FileChannelImpl;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * apk工具类。封装了获取Apk信息的方法。
 * 
 * <AUTHOR>
 * 
 *         <p>
 *         <b>version description</b><br />
 *         V0.2.1 修改程序名字为从路径中获得。
 *         </p>
 */

public class ApkFastFdsUtil {
	public static final String VERSION_CODE = "versionCode";
	public static final String VERSION_NAME = "versionName";
	public static final String SDK_VERSION = "sdkVersion";
	public static final String TARGET_SDK_VERSION = "targetSdkVersion";
	public static final String USES_PERMISSION = "uses-permission";
	public static final String APPLICATION_LABEL = "application-label";
	public static final String APPLICATION_ICON = "application-icon";
	public static final String USES_FEATURE = "uses-feature";
	public static final String USES_IMPLIED_FEATURE = "uses-implied-feature";
	public static final String SUPPORTS_SCREENS = "supports-screens";
	public static final String SUPPORTS_ANY_DENSITY = "supports-any-density";
	public static final String DENSITIES = "densities";
	public static final String PACKAGE = "package";
	public static final String APPLICATION = "application:";
	public static final String LAUNCHABLE_ACTIVITY = "launchable-activity";
	protected static MessageDigest messageDigest = null;
	protected static char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e','f' };
	private static String signKeyPath =ConfigInfo.KEY_FILEPATH;
	private static String fdsFileSavePath = FilePathUtil.getValue("fdsFileSavePath");
	static Logger logger = LoggerFactory.getLogger(ApkFastFdsUtil.class);
	static {
		try {
			messageDigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			logger.info(FileMD5.class.getName() + "初始化失败，MessageDigest不支持MD5Util.");
		}
	}
	
	/*add 20180704*/
	static{
		
		try {
			FastdfsClient.init("fastdfs-client.properties");
		} catch (IOException | MyException e) {
			// TODO
			e.printStackTrace();
		}
		
	}
	/*add end*/
	
	





	public static FileInfo uploadFile3(File file, String fileName, String targetPath) {
		InputStream is = null;
		try {
			is = new FileInputStream(file);
		} catch (FileNotFoundException e1) {
			e1.printStackTrace();
		}
		FileInfo fileInf = null;
		try {
			fileInf = uploadFiles(is, fileName, targetPath);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
					is = null;
				} catch (IOException e) {
					e.printStackTrace();
				}

			}
			// file.delete();
		}

		return fileInf;
	}

	public static FileInfo uploadFiles(InputStream inputStream, String fileName, String targetPath) throws Exception {
		int length;
		byte b[] = new byte[10240];
		String basePath = targetPath;
		String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
		String fileSavePath = "/" + CtUtils.getCurrentTime("yyyyMMdd") + "/";
		File oldFile = new File(targetPath + fileName);
		try {
			File f = new File(basePath + fileSavePath);
			if (!f.isDirectory()) {
				f.mkdirs();
				f.setExecutable(true);
				f.setReadable(true);
				f.setWritable(true);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	    try {
			FileUtil.setFilePermissions(new File(basePath + fileSavePath));
		} catch (Exception e) {
			e.printStackTrace();
		}

		String fileSaveName = CtUtils.uuid() + "." + fileType;
		fileSavePath += fileSaveName;
		OutputStream outputStream = null;
		File destFile = new File(basePath + fileSavePath);
		if (!destFile.exists()) {
			destFile.createNewFile();
			destFile.setExecutable(true);
			destFile.setReadable(true);
			destFile.setWritable(true);
			Runtime.getRuntime().exec(Constants.SET_FILE_PERMISSIONS + destFile);
		}
		outputStream = new FileOutputStream(destFile);// new
														// FilebasePath+fileSavePath);
		try {
			while ((length = inputStream.read(b)) > 0) {
				outputStream.write(b, 0, length);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			oldFile.delete();
			outputStream.flush();
			if (outputStream != null) {
				outputStream.close();
				outputStream = null;
			}

			if (inputStream != null) {
				inputStream.close();
				inputStream = null;
			}
		}

		FileInfo fileInfo = new FileInfo();
		fileInfo.setFile_absolute_path(basePath + fileSavePath);
		fileInfo.setUuid(CtUtils.uuid());
		fileInfo.setFile_save_name(fileSaveName);
		fileInfo.setFileOldName(fileName);
		fileInfo.setFileSavePath(fileSavePath);
		fileInfo.setFileSize((int) destFile.length());
		fileInfo.setFileMd5(getFileMD5String(destFile));
		fileInfo.setRecCrtTm(CtUtils.getCurrentTime());

		return fileInfo;
	}

	/**
	 * 计算文件的MD5，重载方法
	 * 
	 * @param file
	 *            文件对象
	 * @return
	 * @throws IOException
	 * @throws SecurityException
	 * @throws NoSuchMethodException
	 * @throws InvocationTargetException
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	public static String getFileMD5String(File file) throws IOException, NoSuchMethodException, SecurityException,
			IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		FileInputStream in = null;
		FileChannel ch = null;
		MappedByteBuffer byteBuffer = null;
		byte[] bt = null;
		try {
			in = new FileInputStream(file);
			ch = in.getChannel();
			byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0, file.length());
			messageDigest.update(byteBuffer);
			bt = messageDigest.digest();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (ch != null) {
				ch.close();
				ch = null;
			}

			if (in != null) {
				in.close();
				in = null;
			}

			// unmap
			Method m = FileChannelImpl.class.getDeclaredMethod("unmap", MappedByteBuffer.class);
			m.setAccessible(true);
			m.invoke(FileChannelImpl.class, byteBuffer);
		}
		return bufferToHex(bt);

	}

	private static String bufferToHex(byte bytes[]) {
		return bufferToHex(bytes, 0, bytes.length);
	}

	private static String bufferToHex(byte bytes[], int m, int n) {
		StringBuffer stringbuffer = new StringBuffer(2 * n);
		int k = m + n;
		for (int l = m; l < k; l++) {
			appendHexPair(bytes[l], stringbuffer);
		}
		return stringbuffer.toString();
	}

	private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
		char c0 = hexDigits[(bt & 0xf0) >> 4];
		char c1 = hexDigits[bt & 0xf];
		stringbuffer.append(c0);
		stringbuffer.append(c1);
	}


	private static String uploadFile(File file, String fileName, String string) {
		String savePath = "";
		try {
			logger.info("开始执行Fdfs文件上传工作");
			/*del by wangzy 20180704
            Process ps = Runtime.getRuntime().exec(" "+fdsUploadCommand+" "+ file.getAbsolutePath());
            File destFile = new File(file.getAbsolutePath());
    		if (!destFile.exists()) {
    			Runtime.getRuntime().exec(Constants.SET_FILE_PERMISSIONS + destFile);
    		}
            BufferedReader br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            StringBuffer sb = new StringBuffer();
            String line;
            while ((line = br.readLine()) != null) {
                    sb.append(line).append("\n");
            }
            savePath = sb.toString();
            */
			savePath=FastdfsClient.uploadFile(file.getAbsolutePath(),FileUtil.getFileExtName(fileName));
            logger.info("文件上传完成，文件路径为："+savePath);
            logger.info("文件上传完成之后执行文件删除工作");
            file.delete();
            logger.info("文件删除完成");
		} catch (IOException | MyException e) {
			e.printStackTrace();
			logger.error("fastdfs上传文件失败",e.getMessage());
		}
		return savePath;
	}

	private static String uploadFile2(File file, String fileName, String string) {
		String savePath = "";
		try {
			logger.info("开始执行Fdfs文件上传工作");
			savePath=FastdfsClient.uploadFile(file.getAbsolutePath(),FileUtil.getFileExtName(fileName));
			logger.info("文件上传完成，文件路径为："+savePath);
		} catch (IOException | MyException e) {
			e.printStackTrace();
			logger.error("fastdfs上传文件失败",e.getMessage());
		}
		return savePath;
	}
	public static int deleteFile(String fileId){
		int result = -1;
		try {
			logger.info("开始删除文件fastdfs，文件路径为："+fileId);
			result = FastdfsClient.deleteFile(fileId);
			logger.info("fastdfs删除文件完成");
			return result;
		} catch (IOException | MyException  e) {
			e.printStackTrace();
			logger.error("fastdfs删除文件失败",e.getMessage());
			return result;
		}

	}
	public static FileInfo getSignedFileCustomer(String appPath,String fileName) {
		File apkFile = new File(appPath);
		FileInfo fileInfo = new FileInfo();
		try {
			fileInfo.setFileMd5(getFileMD5String(apkFile));
		} catch (NoSuchMethodException | SecurityException e) {
			// TODO
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			// TODO
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			// TODO
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			// TODO
			e.printStackTrace();
		} catch (IOException e) {
			// TODO
			e.printStackTrace();
		}
		fileInfo.setFileSize((int) apkFile.length());
		String savePath = ApkFastFdsUtil.uploadFile(apkFile, fileName, fdsFileSavePath);//开始执行FastFds文件上传，返回group等路径
		String relativePath = savePath.substring(savePath.indexOf("/") + 1);
		fileInfo.setFile_absolute_path(fdsFileSavePath + relativePath);//文件存储全路径
		fileInfo.setUuid(CtUtils.uuid());
		fileInfo.setFile_save_name(relativePath.substring(relativePath.lastIndexOf("/")+1));//获取文件名称
		fileInfo.setFileOldName(fileName);
		fileInfo.setFileSavePath(savePath);
		fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
		logger.info("新文件保存成功之后，开始删除临时文件");
//		apkFile.delete();
//		logger.info("临时文件删除成功");
//		logger.info("临时文件删除成功："+fileInfo.getFileSize());
		return fileInfo;
		
	}
	public static FileInfo getSignedFileCustomer2(String appPath,String fileName) {
		File apkFile = new File(appPath);
		FileInfo fileInfo = new FileInfo();
		try {
			fileInfo.setFileMd5(getFileMD5String(apkFile));
		} catch (NoSuchMethodException e) {
			// TODO
			e.printStackTrace();
		} catch (SecurityException e) {
			// TODO
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			// TODO
			e.printStackTrace();
		} catch (IllegalArgumentException e) {
			// TODO
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			// TODO
			e.printStackTrace();
		} catch (IOException e) {
			// TODO
			e.printStackTrace();
		}
		fileInfo.setFileSize((int) apkFile.length());
		String savePath = ApkFastFdsUtil.uploadFile2(apkFile, fileName, fdsFileSavePath);//开始执行FastFds文件上传，返回group等路径
		String relativePath = savePath.substring(savePath.indexOf("/") + 1);
		fileInfo.setFile_absolute_path(fdsFileSavePath + relativePath);//文件存储全路径
		fileInfo.setUuid(CtUtils.uuid());
		fileInfo.setFile_save_name(relativePath.substring(relativePath.lastIndexOf("/")+1));//获取文件名称
		fileInfo.setFileOldName(fileName);
		fileInfo.setFileSavePath(savePath);
		fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
		return fileInfo;

	}
	
	public static FileInfo uploadFileByFastFds(File file) {
		FileInfo fileInfo = new FileInfo();
		String filePath = file.getAbsolutePath();
		String savePath = ApkFastFdsUtil.uploadFile(file, filePath.substring(filePath.lastIndexOf("/")+1),fdsFileSavePath);//开始执行FastFds文件上传，返回group等路径
		String relativePath = savePath.substring(savePath.indexOf("/") + 1);
		fileInfo.setFile_absolute_path(fdsFileSavePath + relativePath);//文件存储全路径
		fileInfo.setUuid(CtUtils.uuid());
		fileInfo.setFile_save_name(relativePath.substring(relativePath.lastIndexOf("/")+1));//获取文件名称
		fileInfo.setFileOldName(filePath.substring(filePath.lastIndexOf("/")+1));
		fileInfo.setFileSavePath(savePath);
		fileInfo.setFileSize((int) file.length());
		fileInfo.setRecCrtTm(CtUtils.getCurrentTime());
//		logger.info("新文件保存成功之后，开始删除临时文件");
//		file.delete();
//		logger.info("临时文件删除成功");
		return fileInfo;
		
	}

}