<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.coms.dao.mapper.UserMapper">
	<resultMap id="BaseResultMap" type="com.centerm.cpay.coms.dao.pojo.User">
		<id column="id" property="id" jdbcType="INTEGER" />
		<result column="username" property="username" jdbcType="VARCHAR" />
		<result column="realname" property="realname" jdbcType="VARCHAR" />
		<result column="password" property="password" jdbcType="VARCHAR" />
		<result column="ins_id" property="insId" jdbcType="INTEGER" />
		<result column="ins_name" property="insName" jdbcType="VARCHAR" />
		<result column="telephone" property="telephone" jdbcType="VARCHAR" />
		<result column="email" property="email" jdbcType="VARCHAR" />
		<result column="roleNames" property="roleNames" jdbcType="VARCHAR" />
		<result column="login_ip" property="loginIp" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="TINYINT" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="login_num" property="loginNum" jdbcType="INTEGER" />
		<result column="pwd_errorNum" property="pwdChangeErrorNum" jdbcType="INTEGER" />
		<result column="last_up_pwd_time" property="lastUpPwdTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	<sql id="Base_Column_List">
		a.id, a.username, a.realname, a.password, a.ins_id,
		a.telephone, a.email,
		a.login_ip,
		a.status, a.create_time,
		a.update_time,login_num,pwd_errorNum,last_up_pwd_time
	</sql>
	<select id="selectById" parameterType="java.lang.Integer"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		,b.name as insName
		from cpay_user a left join cpay_institution b on
		a.ins_id=b.id
		where a.id = #{id,jdbcType=INTEGER}
	</select>
	<select id="onlyUser" parameterType="com.centerm.cpay.coms.dao.pojo.User"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from cpay_user a
		where 1=1
		<if test="username!=null">
			and username = #{username}
		</if>
		<if test="email!=null">
			and email = #{email}
		</if>
		<if test="telephone!=null">
			and telephone = #{telephone}
		</if>
	</select>
	<insert id="insertUser" parameterType="com.centerm.cpay.coms.dao.pojo.User" useGeneratedKeys="true" keyProperty="id">
		insert into cpay_user ( username, realname,
		password, ins_id,
		telephone,
		email, login_ip, status,
		create_time, update_time,pwd_errorNum,last_up_pwd_time)
		values
		(
		#{username,jdbcType=VARCHAR},
		#{realname,jdbcType=VARCHAR},
		#{password,jdbcType=VARCHAR},
		#{insId,jdbcType=INTEGER},
		#{telephone,jdbcType=VARCHAR},
		#{email,jdbcType=VARCHAR},
		#{loginIp,jdbcType=VARCHAR},
		#{status,jdbcType=TINYINT},
		#{createTime,jdbcType=TIMESTAMP},
		#{updateTime,jdbcType=TIMESTAMP},0,#{lastUpPwdTime,jdbcType=TIMESTAMP})
	</insert>

	<delete id="deleteById" parameterType="java.lang.Integer">
		delete from
		cpay_user
		where
		id = #{id,jdbcType=VARCHAR}
	</delete>
	<delete id="deleteByIds" parameterType="java.util.List">
		delete from cpay_user where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</delete>
	<select id="getUserByCondition" parameterType="com.centerm.cpay.coms.dao.pojo.User"
		resultMap="BaseResultMap">
		select * from cpay_user where 1=1
		<if test="telephone != null">
			and telephone=#{telephone}
		</if>
		<if test="email != null">
			and email=#{email}
		</if>
		order by create_time desc
	</select>
	<select id="selectByUer" resultMap="BaseResultMap"
		parameterType="com.centerm.cpay.coms.dao.pojo.User">
		SELECT
		<include refid="Base_Column_List" />
		,r.roleIds,
		r.roleNames,
		d.name AS insName
		FROM
		cpay_user a
		LEFT JOIN
		cpay_institution d ON a.ins_id = d.id
		LEFT JOIN
		(SELECT b.user_id,GROUP_CONCAT(CAST(c.id AS CHAR)) AS roleIds,GROUP_CONCAT(c.
		NAME) AS roleNames
		FROM cpay_user_role b LEFT JOIN cpay_role c ON
		b.role_id = c.id
		GROUP BY b.user_id
		) r ON r.user_id = a.id
		where 1=1
		<if test="username != null">
			and a.username LIKE concat ('%',#{username},'%')
		</if>
		<if test="realname !=null">
			and a.realname LIKE concat ('%',#{realname},'%')
		</if>
		<if test="insId != null">
			and a.ins_id IN (
			SELECT s1.id FROM cpay_institution s1
			WHERE
			s1.detail LIKE CONCAT('%',
			(SELECT s2.detail FROM cpay_institution s2
			WHERE s2.id = #{insId,jdbcType=INTEGER}
			),'%')
			)
		</if>
		<if test="roleId != null">
			and r.roleIds like concat ('%',#{roleId},'%')
		</if>
		order by a.create_time desc
	</select>
	<update id="updateBySelective" parameterType="com.centerm.cpay.coms.dao.pojo.User">
		update cpay_user
		<set>
			<!-- <if test="username != null" > -->
			<!-- username = #{username,jdbcType=VARCHAR}, -->
			<!-- </if> -->
			<if test="realname != null">
				realname = #{realname,jdbcType=VARCHAR},
			</if>
			<if test="password != null">
				password = #{password,jdbcType=VARCHAR},
			</if>
			<!-- <if test="insId != null" > -->
			<!-- ins_id = #{insId,jdbcType=INTEGER}, -->
			<!-- </if> -->
			<if test="telephone != null">
				telephone = #{telephone,jdbcType=VARCHAR},
			</if>
			<if test="email != null">
				email = #{email,jdbcType=VARCHAR},
			</if>
			<if test="loginIp != null">
				login_ip = #{loginIp,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=TINYINT},
			</if>
			<!-- <if test="createTime != null" > -->
			<!-- create_time = #{createTime,jdbcType=TIMESTAMP}, -->
			<!-- </if> -->
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="loginNum != null">
				login_num = #{loginNum,jdbcType=INTEGER},
			</if>
			<if test="pwdChangeErrorNum != null">
				pwd_errorNum = #{pwdChangeErrorNum,jdbcType=INTEGER},
			</if>
			<if test="lastUpPwdTime != null">
				last_up_pwd_time = #{lastUpPwdTime,jdbcType=TIMESTAMP}
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateByUser" parameterType="com.centerm.cpay.coms.dao.pojo.User">
		update cpay_user
		set
		username = #{username,jdbcType=VARCHAR},
		realname =
		#{realname,jdbcType=VARCHAR},
		password = #{password,jdbcType=VARCHAR},
		ins_id = #{insId,jdbcType=INTEGER},
		telephone =
		#{telephone,jdbcType=VARCHAR},
		email = #{email,jdbcType=VARCHAR},
		login_ip = #{loginIp,jdbcType=VARCHAR},
		status =
		#{status,jdbcType=TINYINT},
		create_time =
		#{createTime,jdbcType=TIMESTAMP},
		update_time =
		#{updateTime,jdbcType=TIMESTAMP},
		login_num = #{loginNum,jdbcType=INTEGER},
		code_num = #{codeNum,jdbcType=INTEGER},
		pwd_errorNum = 0
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="resetPwd" parameterType="com.centerm.cpay.coms.dao.pojo.User">
		update cpay_user set
		password = #{password,jdbcType=VARCHAR},
		pwd_errorNum = 0,
		login_num = 0
		where id=#{id,jdbcType=INTEGER}
	</update>
	<update id="startByIds" parameterType="java.util.List">
		update cpay_user set status=2 where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</update>

	<update id="stopByIds" parameterType="java.util.List">
		update cpay_user set status=0 where id in
		<foreach collection="list" index="index" open="(" separator=","
			close=")" item="id">
			#{id}
		</foreach>
	</update>
</mapper>